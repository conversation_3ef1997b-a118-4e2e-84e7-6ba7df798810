#  自动化 “信息收集+漏洞扫描” 系统开发   
 哈拉少安全小队   2023-11-30 19:28  
  
在开始之前，先问师傅们两个问题  
  
1：假设现在有一个项目，给了4个子域名，你会怎么进行漏洞挖掘呢？  
  
2：什么是信息收集，为什么要进行信息收集  
  
  
第一个问题 - 漏洞挖掘，我之前的做法  
  
1：创建一个文件夹，名为wadong，并进入此文件夹中。  
  
2：创建domain.txt，将4个子域名放入其中，通过子域名收集工具，如subfinder、oneforall、ksubdomain跑资产。  
  
3：将这三个工具跑出来的资产，合并在一起，在去重网站上进行去重后，结果放入alldomain.txt中。  
  
4：使用finger工具对alldomain.txt内容进行指纹识别，结果放入finger.xlsx中。  
  
5：使用urlfinder工具对alldomain.txt内容进行接口获取，结果放入interface.txt中。  
  
6：使用nuclei、xray等漏扫工具进行漏洞扫描，结果放入vul.txt中。  
  
7：将工具跑出来的结果，进行手动验证。  
  
8：将验证成功的漏洞，进行漏洞提交。  
  
  
那么就会产生一个问题，如果项目一下子有4、5个，要一直重复上面的操作吗？  
  
  
第二个问题 - 信息收集问题解答  
  
什么是信息收集？  
  
从第一个问题的做法中，可以看出，其中从第一步到第五步，都是一个信息收集的过程，信息收集就是通过各种方式获取所需要的目标信息，便于在后续更好的开展漏洞挖掘工作。  
  
  
为什么要信息收集？  
  
因为“漏洞挖掘的本质就是信息收集”，收集到目标的信息越多，漏洞挖掘的切入点就越多，挖到的漏洞也就越多。  
  
  
基于上面两个问题，由此我要做一个自动化“信息收集+漏洞扫描”系统。  
  
要实现的结果如下：  
  
1：通过WEB端去下发命令，指定扫描名称，扫描范围，扫描类型，扫描模式。  
  
2：自动创建自定义名称文件夹，并自动将工具跑出来的结果放入指定文件夹的文件中。  
  
3：自动处理工具跑出来的结果，过滤掉误报信息。  
  
4：定期对指定资产进行信息收集+漏洞扫描。  
  
5：在WEB端中显示资产跑出来的扫描结果。  
  
6：自动推送系统扫描阶段状态，以方便查询系统扫描进度。  
  
  
系统是由各个功能模块组成，开发的思路如下图所示.  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyVVibuiaiaBEEUhxAaUo372k0MNpQ31olu1ShfrWricWB3IYcKKBqUrFIBg/640?wx_fmt=png&from=appmsg "")  
  
<table><tbody><tr height="39" style="height:30pt;"><td style="word-break: break-all;" width="336"><p><span style="letter-spacing: 0.034em;font-size: 20px;">英文名称</span></p></td><td style="word-break: break-all;" width="364"><p><span style="font-size: 20px;">对应中文名称</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="336"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">Brosel_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">网络空间搜索引擎扫描模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="word-break: break-all;" width="336"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">Domain_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">综合性子域名工具扫描模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="word-break: break-all;" width="336"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">Verify_Scan</span></p></td><td style="" width="431"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">存活探测模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="336"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Port_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">端口扫描模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="336"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">Finger_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">指纹识别模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="336"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">Interface_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">接口扫描模块</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="336"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Vul_Scan</span></p></td><td style="" width="431"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">漏洞扫描模块</span></p></td></tr></tbody></table>```
Tips：
Brosel、Domain、Verify均为信息收集模块，当有指定的范围时，不用选择。
Finger、Interface、Vul均为漏洞扫描模块，可以自定义选择
```  
  
  
接下来为师傅们介绍该系统各个功能模块开发的思路。  
  
Brosel_Scan-域名收集  
  
首先是Brosel_Scan模块。  
  
这个模块是通过针对性脚本，对特定的网络空间搜索引擎进行爬虫操作，实现子域名收集。  
  
首推QAX的HUNTER，本人亲测，十分好用，可以将多个子域名放入，一起收集子域名信息。  
  
https://hunter.qianxin.com/  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnychByRGkbht8XnLPzGMlnHibb442NZ3oYCXKsuIhujTFia3yCrqfPwKPw/640?wx_fmt=png&from=appmsg "")  
  
  
其次推荐fofa和zoomeye  
  
https://fofa.info/     
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyxic8UZBNuh38b4kpRw2cPkFLFAeq5EDG4hgNgh88Go1StwKdUXzf1Tw/640?wx_fmt=png&from=appmsg "")  
  
 https://www.zoomeye.org/    
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnykf7AuzdCqib2Xw9qVB88dgFlqU9KbicZcBs6NQicVHmfGica0tjy4sWr6w/640?wx_fmt=png&from=appmsg "")  
  
经过工具调研，推荐下面两个工具  
```
HunterApi
https://github.com/akkuman/HunterApi
命令行hunter查询工具，爬取数据稳定。

fofax
https://github.com/xiecat/fofax
命令行 FoFa 查询工具，自定义 Fx 语法查询
```  
  
代码思路分享：写了一个Brosel_Scan类，里面一个函数代表一个工具的使用  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyMlgaOCrtnh59kpBWgEaPtno7tV7rOQY4BQXORUfLYRXUkU4JvibIib5A/640?wx_fmt=png&from=appmsg "")  
  
通过fofa_extract、hunter_extract、zoomeye_extract模块对各个工具跑出来的结果进行过滤筛选操作，下图为fofa_extract模块代码：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyNzMRYSSK60KCiaynfffzUPUCGQgjXibDQm6FLbL7Ya7N39BJJqjtzadQ/640?wx_fmt=png&from=appmsg "")  
  
  
Domain_Scan-域名收集  
  
这个模块通过综合性域名收集工具，对子域名进行域名收集，经过工具调研，推荐下面三个工具。  
```
OneForAll
https://github.com/shmilylty/OneForAll
集百家之长，功能强大的全面快速子域收集终极神器🔨。

subfinder
https://github.com/projectdiscovery/subfinder
内置获取子域名网址很多，收集信息很全面。

ksubdomain
https://github.com/knownsec/ksubdomain
无状态子域名爆破工具。
```  
  
  
写了一个Domain_Scan类，里面一个函数代表一个工具的使用。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnye4icxGzlXsVfmK71oxCkv0AIsI6GAlQUsBMGWdsnx7E2rlzrXu2G7MQ/640?wx_fmt=png&from=appmsg "")  
  
编写对应过滤脚本，对各个工具跑出来的子域名结果进行过滤筛选操作。  
下图为domai  
n  
_extract代码：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhny9LEh6nywJiaic6EKr2oGbofR2V7gu0pSygl4lbbIyFBrGP9OXvDuX4Bw/640?wx_fmt=png&from=appmsg "")  
  
关于数据去重的问题解答  
  
因为不同子域名收集工具跑出来的子域名结果，最后面肯定是合并去重后，放入一个文件中。  
  
经过工具调用，推荐anew工具进行数据去重。  
```
anew
https://github.com/tomnomnom/anew
去重速度快，稳定
```  
  
Verify_Scan-存活探测  
  
这个模块是通过httpx工具，对子域名收集的结果，进行存活探测操作。  
  
经过工具调研，推荐httpx工具。  
```
httpx
https://github.com/projectdiscovery/httpx
快速且多用途的 HTTP 工具包。
```  
  
写成了一个函数，并通过code_extract模块将200、403等不同状态码的网址进行一个分类。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhny8PObicZUpiaT4yRHD8pBwpgOa2EiaWicguibDHgch35r5TWPyjEJ6Vs2VVQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyoGGo8ia2cfXgAMmmLavPRvu76Mn5OkJZjxUaKqK0vnoGs6eMMBEuFKw/640?wx_fmt=png&from=appmsg "")  
  
  
Port_Scan-端口扫描  
  
这个模块是通过nmap工具，对资产进行端口扫描。  
  
经过工具调研，推荐nmap工具。  
```
nmap
https://github.com/nmap/nmap
老牌端口扫描工具，辅助参数很多，可以自行进行调试。
```  
  
写成了一个函数，调用nmap命令，将端口扫描的结果保存在一个txt文件中  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyqs3g5zRgjDsibX1pBxDCdZFrMdbwicmvibXibjoyP8LKVBqDS7In3sEjAQ/640?wx_fmt=png&from=appmsg "")  
  
Finger_Scan-指纹识别  
  
这个模块是通过finger工具，对合并去重过的子域名进行指纹识别操作。  
  
经过工具调研，推荐Finger工具。  
```
Finger
https://github.com/EASY233/Finger
可以在大量的资产中快速进行存活探测与重点攻击系统指纹探测工具。
```  
  
代码思路分享：写成了一个函数，调用finger命令，将指纹识别的结果保存在一个xlsx文件中。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnysq5tpl8EaC4wJMTCkOlHS79e5C0KCehdzZERWmicJ7njia43WfsqOFIA/640?wx_fmt=png&from=appmsg "")  
  
Interface_Scan-接口扫描  
  
这个模块是通过URLFinder工具，对合并去重过的子域名进行接口扫描操作。  
  
经过工具调研，推荐URLFinder工具。  
```
URLFinder
https://github.com/pingc0y/URLFinder
快速、全面、易用的页面信息提取工具。
```  
  
代码思路分享：写成了一个函数，通过urlfinder_extract和interface_extract，分别过滤urlfinder的接口结果，以及筛选想要的接口。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhny0hUcVG6xdftHh3xic0ibeIiauZ3q83OibSs1Y6ymYJWhPwlKy4tAMwuZAA/640?wx_fmt=png&from=appmsg "")  
  
  
Vul_Scan-漏洞扫描  
  
这个模块是通过nuclei、fscan工具，对合并去重过的子域名进行漏洞扫描操作。  
  
经过工具调研，推荐下面两个工具：  
```
nuclei
https://github.com/projectdiscovery/nuclei
基于简单的基于 YAML 的 DSL 的快速且可定制的漏洞扫描器。

fscan
https://github.com/shadow1ng/fscan
一款内网综合扫描工具，方便一键自动化。
```  
  
代码思路分享：写成两个函数，调用对应命令，将扫描结果，放入指定文件夹文件中。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyAXJdR4R7nrxFNgce0qCX9kUiaLvk4ribGaU28ee5SibCo0ia2SEwia53Ezg/640?wx_fmt=png&from=appmsg "")  
  
  
各个功能模块，利用到的工具，以及开发思路讲解完毕。  
  
  
接下来介绍内容如下：  
  
1：自动推送系统扫描状态代码分享。  
  
2：WEB端界面参数使用方法，  
  
3：不同场景中如何通过WEB端向后端下发命令的思路。  
  
  
自动推送系统扫描状态 - 代码分享  
  
当在使用该系统时，有一个问题：不知道系统的扫描进度怎么样了？  
  
针对这种情况，编写了一个msgpush.py，基于钉钉机器人自动推送系统扫描状态。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyWFW0ObPDzCpF32kMiaWT9gEtMjEDicRmT017zqNWMXRYSYzRYyjt3G0g/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhny5iacbbDQFfQtC8GWW63YwFAJnlYgiagepyEqmdonPCvsmicfTkMWm8sKg/640?wx_fmt=png&from=appmsg "")  
  
  
  
前端页面如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnygeUqyocJVpl705S3fVtE1KfFxicic6uvXcPd0LUhQFE9d1Q3e4guQIew/640?wx_fmt=png&from=appmsg "")  
  
  
下面的表格，用来介绍前端页面的参数使用方法  
  
<table><tbody><tr height="39" style="height:30pt;"><td style="" width="296"><p><span style="font-weight: bold;font-size: 20px;">名称</span></p></td><td style="" width="268"><p><span style="font-weight: bold;font-size: 20px;">作用</span></p></td><td style="" width="232"><p><span style="font-weight: bold;font-size: 20px;">可选性</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="296"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Scan_Name</span></p></td><td style="" width="268"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">扫描任务名称</span></p></td><td style="" width="232"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">必选</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="296"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Subdomain</span></p><p><span style="font-size: 17px;color: rgb(0, 0, 0);">AllDomain</span></p><p><span style="color: rgb(0, 0, 0);font-size: 17px;">AllIP</span></p></td><td style="" width="268"><p><span style="font-size:18pt;color:#000000;"><br/></span></p><p><span style="color: rgb(0, 0, 0);font-size: 17px;">扫描范围</span></p></td><td style="" width="232"><p><span style="font-size:18pt;color:#000000;"><br/></span></p><p><span style="color: rgb(0, 0, 0);font-size: 17px;">三选一</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="296"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Scan_Position</span></p></td><td style="" width="268"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">扫描类型</span></p></td><td style="" width="232"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">二选一</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="296"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">Scan_Type</span></p></td><td style="" width="268"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">扫描模式</span></p></td><td style="" width="232"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">自定义</span></p></td></tr></tbody></table>  
  
使用场景 - 自定义扫描模式  
  
**需求：**  
  
给出了指定的域名列表以及IP地址，并且想快速出漏洞。  
  
  
**用法如下：**  
  
1：指定Scan_Name名称。  
  
2：Alldomain框中填写域名，AllIP填写IP。  
  
3：Scan_Position选择为temping，代表临时扫描。  
  
4：Scan_Type选择自己想要的扫描模块，如这里我只想进行Vul_Scan，则只选择Vul即可，这里已经给出完整的范围，不用在使用信息收集模块。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyQNVMhib45iaBchuaRwzXxghIaMdzlP63IDRjph4wvVsqyQobJIg9pUjw/640?wx_fmt=png&from=appmsg "")  
  
  
**案例分享：**  
  
这里也是通过HTML+CSS写了一个漏洞信息查看的界面，如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhny6mdWq9G7ib3ibH1EVZzwOicF8xoCY5Y76yP0r310oMT5WY8uFbjZ44apg/640?wx_fmt=png&from=appmsg "")  
  
  
通过查看Fscan扫描的结果，发现扫描出一个站点的SpringBoot接口，其中包含了用户的认证信息，如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyn8VH8TPcManLdw0gKaNu8vYicHLvC7elU57FFG5lmPzH69DB6AUj1Ew/640?wx_fmt=png&from=appmsg "")  
  
  
这里访问一次网址，可以获取70-100显示条用户的登录凭证，通过编写脚本，发现跑两次脚本，获取到的authorizationv2参数值是不一样的，证明是可以获取所有用户登录凭证。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyHu7evYZyMnKtHTqE2PcNj7icTJBtUNfcribHgFXkMpqtTT9C8Dqkk0GQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnygKU43kY1EXuL5U2knuYH8b3KEJIJAl0Z8j0UwCg895SmsE8HRQaibCw/640?wx_fmt=png&from=appmsg "")  
  
这里泄露的用户登录凭证，经测试，可以进行所有用户的操作，下面放两个危害巨大的漏洞示例！  
  
  
漏洞一-->全站用户姓名、手机号、身份证信息泄露，漏洞发现过程：  
  
发现的接口如下，这里需要将authorizationv2参数值修改即可  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyI9Wv102m5l8RRia1xUXQPgfUI4uxssytBAbFReql29QlP0ib3cT57lag/640?wx_fmt=png&from=appmsg "")  
  
漏洞二-->全站用户注销  
  
![](https://mmbiz.qpic.cn/mmbiz_png/exqoVZK9pfl9gIZgfW1Bk22zzbNSRhnyQxLpwU42KViaubwCEKogsjft6obzZQsaNxxibPfJbZdk2vb4SRPicb6LA/640?wx_fmt=png&from=appmsg "")  
  
  
开发系统中遇到的问题以及解决方案  
<table><tbody><tr height="39" style="height:30pt;"><td style="" width="458"><p><span style="font-weight: bold;font-size: 20px;">问题</span></p></td><td style="" width="393"><p><span style="font-size: 20px;font-weight: bold;">解决方案</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="458"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">刚开始开发的时候，不知道用什么语言去进行编写</span></p></td><td style="" width="393"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">推荐python，易上手。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="458"><p><span style="color: rgb(0, 0, 0);font-size: 18px;">不知道使用哪些工具配合</span></p></td><td style="" width="393"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">可以试试我推荐的工具，都是经过测试的，比较好用的。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="458"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">不知道怎么去构思属于自己的框架</span></p></td><td style="" width="393"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">建议总结一下自己平时挖掘漏洞的思路，是怎么去操作的，通过python将这些操作，写成自动化脚本。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="458"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">不知道怎么通过前端跟后端进行交互</span></p></td><td style="" width="393"><p><span style="font-size: 18px;color: rgb(0, 0, 0);">推荐python的Flask模块，很方便部署WEB端。</span></p></td></tr></tbody></table>  
  
系统待完善的地方  
<table><tbody><tr height="34" style="height:26pt;"><td style="" width="328"><p><span style="font-weight: bold;font-size: 20px;">问题</span></p></td><td style="" width="541"><p><span style="font-size: 20px;font-weight: bold;">待解决方法</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">端口扫描的稳定精准性很难做到</span></p></td><td style="" width="541"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">通过不断调试，找到一个平衡点。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">指纹识别库的维护问题</span></p></td><td style="" width="541"><p><span style="color: rgb(0, 0, 0);font-size: 17px;">先写出来一个能自动添加指纹的操作。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">漏洞扫描的POC库维护问题</span></p></td><td style="" width="541"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">搭建一个数据库，将POC放入，并写跟SQL交互的脚本，进行更新。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">接口扫描的白名单问题，不去调用delete、update接口。</span></p></td><td style="" width="541"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">写一个配置文件，里面放入白名单接口，在未授权扫描时进行判断。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">针对指定POC，快速去打点指定范围。</span></p></td><td style="" width="541"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">慢慢扩展存储数据量，写一个模块，专门用于过滤出想要的数据范围，然后进行打点。</span></p></td></tr><tr height="39" style="height:30pt;"><td style="" width="328"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">自动根据输入的信息，选择最优解模式</span></p></td><td style="" width="541"><p><span style="font-size: 17px;color: rgb(0, 0, 0);">这个需要很强的算法，先把数学学好。</span></p></td></tr></tbody></table>  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
