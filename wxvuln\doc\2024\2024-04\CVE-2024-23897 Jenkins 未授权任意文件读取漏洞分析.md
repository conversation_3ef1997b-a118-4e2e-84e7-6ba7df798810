#  CVE-2024-23897 Jenkins 未授权任意文件读取漏洞分析   
 船山信安   2024-04-12 00:01  
  
## 漏洞描述  
  
Jenkins 是基于 Java 开发的一种持续集成工具。2024年1月25日，Jenkins 官方披露 CVE-2024-23897 Jenkins CLI 任意文件读取漏洞。Jenkins 受影响版本中使用 args4j 库解析CLI命令参数，攻击者可利用相关特性读取 Jenkins 控制器文件系统上的任意文件（如加密密钥的二进制文件），并结合其他功能等可能导致任意代码执行。  
## 影响版本  
  
Jenkins weekly <= 2.441Jenkins LTS <= 2.426.2  
## 环境搭建  
  
docker 起  
```
docker pull bitnami/jenkins:2.426.2-debian-11-r3  
docker run -e "JAVA_OPTS=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:5005" -d --name jenkins -p 8081:8080 -p 8777:5005 bitnami/jenkins:2.426.2-debian-11-r3

```  
  
管理员账号密码为:user/bitnami  
## 漏洞分析  
  
官方通告 https://www.jenkins.io/security/advisory/2024-01-24/  
  
在处理 CLI 命令时，Jenkins 使用 args4j 库来解析 Jenkins 控制器上的命令参数和选项。这个命令解析器有一个功能，可以用文件的内容（expandAtFiles）替换参数中的 @ 字符和文件路径。此功能默认启用  
  
接下来看一下 Jenkins 的 CLI 命令是什么 https://www.jenkins.io/zh/doc/book/managing/cli/  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISSgWial8hibz8ibSelzRZ818SJ60uZ7KMzEAR02ZueNBNDCAxsN34ibia45Q/640?wx_fmt=png&from=appmsg "")  
  
这里基本上已经可以打了，很简单的一个洞  
```
java -jar jenkins-cli.jar -s http://**************:8081 who-am-i '@/etc/passwd'

```  
  
patch  
  
https://github.com/jenkinsci/jenkins/compare/jenkins-2.441...jenkins-2.442#diff-5918f52b773cba16c7e0a74e139d637ae4b4f59e18435f1f1dc137979a46ae4c  
  
断点下在CLICommand#getCmdLineParser  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISv0p3zR27Iv9oLZeSfqEXPx5oRGDNAWbVBM0fjt327pxUMR5KsiaQwJA/640?wx_fmt=png&from=appmsg "")  
  
先获取到参数，往下看，判断了当前的指令类型  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISIzE76vLf8PvjouXxlzxibyrIgiaN2RRSKH9ppvB0UzcIEUeobFZlvIWA/640?wx_fmt=png&from=appmsg "")  
  
跟进parseArgument()对参数进行处理，getAtSyntax 默认为 true，跟进expandAtFiles方法  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISkvYrPtlFTl8kWjnic8sOPQMGaiaEcgwaciaKib6kPfUGhzCbD4oY3wDVRg/640?wx_fmt=png&from=appmsg "")  
  
后面就很明显了，判断参数是否为@符号开头，随后任意文件读取  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISXzrug0aia07cA9WWvwD7pJzKdLpoUuAXZKPCPbDMYDyBxVfe3HpAnuQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicPs6CKNFDvMktwSET14AaISOsQcT4Lo8Y49AiauicRzlKfyYXRZ5lgmc2QhZ9A5S6PR9qliasVkBs3Og/640?wx_fmt=png&from=appmsg "")  
  
来源：https://www.freebuf.com/  
  
