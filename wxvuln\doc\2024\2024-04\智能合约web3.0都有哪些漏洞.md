#  智能合约web3.0都有哪些漏洞   
原创 Fighter001  重生者安全   2024-04-13 08:46  
  
由于微信公众号推送机制的改变避免错过文章麻烦您将公众号  
设为星标  
感谢您的支持！![](https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/newemoji/Social.png "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/SEVwkT7gYkkHQA0nVKBpKOsiaG0J32krN4DJxTDwtr6DssNkjCAvNlBb4YkhpyfibFzOjfZEyo7NtBs1S8Bqly3w/640?wx_fmt=png&from=appmsg "")  
  
想要学习：【  
漏洞挖掘，内网渗透OSCP，车联网，二进制】的朋友欢迎加入知识星球一起学习。如果不满意，72小时内可在APP内无条件自助退款。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/SEVwkT7gYklprG8WAchaRgz0jbibibGtEDEoccibqeMBo4wM4DgGPdqfhUx5BiaM45LcH7ClKs7Yqp0ribONKmnNTTA/640?wx_fmt=png&from=appmsg "")  
  
![](https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/Expression/<EMAIL> "")  
  
-->进入正题啦  
  
智能合约漏洞名，下面分为英文和翻译后的推荐一个web3.0赏金猎人平台：https://dashboard.securr.tech/  
  
英文：  
```
Reentrancy
Front-running
Integer overflow/underflow
Denial-of-Service (DoS)
Solidity-specific vulnerabilities
Uninitialized storage pointers
Unchecked external calls
Access control issues
Ether withdrawal issues
Transaction-ordering dependence (TOD)
Frontrunning in decentralized exchanges
Oracle manipulation
Chain reorganizations
Inadequate auditing and testing
Gas-related vulnerabilities
Time-dependent vulnerabilities
Unintended token transfers
Inadequate exception handling
Unsafe delegatecall usage
Proxy contract vulnerabilities
Batch overflow vulnerabilities
Insecure token standards
Arbitrary data storage and retrieval
Insufficient input validation
Storage layout vulnerabilities
Ether lockup vulnerabilities
Logic flaws in upgradeable contracts
Incorrectly implemented token standards
Unpredictable external contract calls
Malicious contract dependencies
Solidity compiler bugs
Overflowing mapping storage
Non-standard fallback functions
Incorrect gas calculations
Uninitialized contract variables
Denial of service through block.timestamp
Excessive contract complexity
Vulnerable third-party libraries
Incorrect event log handling
Contract function visibility issues
Poorly implemented randomness generation
Lack of access control in upgradeable contracts
Vulnerable constructor functions
Race conditions in contract execution
Token supply vulnerabilities
Gas token vulnerabilities
Smart contract self-destruct vulnerabilities
Integer arithmetic vulnerabilities
Unprotected upgrades
Insufficient gas stipends for external calls
Flash loan attacks
Incorrect array handling
Short address attacks
Gas limit exhaustion
Stuck ether
Zero-day exploits
Web3.js vulnerabilities
Gas price manipulation
Transaction malleability
Resource depletion
Replay attacks
Immutable contract vulnerabilities
Compiler bugs
Proxy contract issues
Economic attacks
Governance flaws
Bridging vulnerabilities
Fork vulnerabilities
Smart contract upgrade risks
Supply chain attacks
Chainlink vulnerabilities
Security token standard issues
Asset pegging vulnerabilities
Sybil attacks
```  
  
中文：  
```
可重入性
抢先交易
整数上溢/下溢
拒绝服务 (DoS)
Solidity 特定的漏洞
未初始化的存储指针
未检查的外部呼叫
访问控制问题
以太币提现问题
交易顺序依赖性 (TOD)
去中心化交易所的抢先交易
甲骨文操纵
连锁重组
审核和测试不足
与天然气相关的漏洞
与时间相关的漏洞
意外的代币转移
异常处理不足
不安全的 delegatecall 使用
代理合约漏洞
批量溢出漏洞
不安全的代币标准
任意数据存储和检索
输入验证不足
存储布局漏洞
以太币锁定漏洞
可升级合约的逻辑缺陷
代币标准实施不正确
不可预测的外部合同调用
恶意合约依赖
Solidity 编译器错误
映射存储溢出
非标准后备函数
气体计算不正确
未初始化的合约变量
通过 block.timestamp 拒绝服务
合同过于复杂
易受攻击的第三方库
事件日志处理不正确
合约功能可见性问题
随机生成实施不当
可升级合约缺乏访问控制
易受攻击的构造函数
合约执行中的竞争条件
代币供应漏洞
Gas代币漏洞
智能合约自毁漏洞
整数算术漏洞
不受保护的升级
外部调用的燃气津贴不足
闪贷攻击
数组处理不正确
短地址攻击
气体极限耗尽
卡住乙醚
零日漏洞利用
Web3.js 漏洞
天然气价格操纵
交易延展性
资源枯竭
重放攻击
不可变的合约漏洞
编译器错误
代理合同问题
经济攻击
治理缺陷
弥补漏洞
分叉漏洞
智能合约升级风险
供应链攻击
Chainlink 漏洞
安全令牌标准问题
资产挂钩漏洞
女巫攻击
```  
  
**喜欢朋友可以点点赞转发转发。**  
****  
  
**免责声明：本公众号不承担任何由于传播、利用本公众号所发布内容而造成的任何后果及法律责任。未经许可，不得转载。**  
  
****  
