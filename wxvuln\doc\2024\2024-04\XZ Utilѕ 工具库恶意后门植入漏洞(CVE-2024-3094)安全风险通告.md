#  XZ Utilѕ 工具库恶意后门植入漏洞(CVE-2024-3094)安全风险通告   
奇安信CERT  代码卫士   2024-03-31 21:49  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="center" rowspan="1" colspan="4" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1.5em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 255, 255);letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">漏洞概述</span></strong><br style="outline: 0px;visibility: visible;"/></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;caret-color: red;letter-spacing: 0px;visibility: visible;">XZ Utilѕ 工具库恶意后门植入漏洞</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" rowspan="1" colspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">漏洞编号</strong></span></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-family: &#34;Helvetica Neue&#34;, Helvetica, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei&#34;, 微软雅黑, Arial, sans-serif;letter-spacing: 0.578px;text-decoration-style: solid;text-decoration-color: rgb(0, 0, 0);visibility: visible;">QVD-2024-11691</span>,CVE-2024-3094</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">2024-03-29</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">影响量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">万级</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);font-size: 17px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">奇安信评级</span></strong></span></p></td><td valign="middle" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高危</span></strong></span></p></td><td valign="middle" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">CVSS 3.1分数</span></strong></p></td><td valign="middle" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">10.0</strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">威胁类型</strong></span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">供应链攻击、后门</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">利用可能性</span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;caret-color: rgb(255, 0, 0);color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;">高</strong></span></strong></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未知<strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"></span></span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"><strong style="outline: 0px;letter-spacing: 0.544px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">部分公开</span></strong></span></span><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"></span></span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="4" rowspan="1" align="left" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">危害描述：</span></strong><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;text-align: justify;visibility: visible;">恶意代码可能允许攻击者通过后门版本的SSH非授权获取系统的访问权限。恶意代码修改了liblzma代码中的函数，该代码是XZ Utils软件包的一部分，链接到 XZ 库的任何软件都可以使用此修改后的代码，并允许拦截和修改与该库一起使用的数据。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响组件**  
  
XZ是一种高压缩比的数据压缩格式，由Tukaani项目开发，几乎存在于每个Linux发行版中，无论是社区项目还是商业产品发行版。它帮助将大文件格式压缩（然后解压缩）为更小、更易管理的大小，以便通过文件传输进行共享。liblzma是一个用于处理XZ压缩格式的开源软件库。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**漏洞描述**  
  
近日，奇安信CERT监测到**XZ Utilѕ 工具库恶意后门植入漏洞(CVE-2024-3094)**,3月29日有开发人员在安全邮件列表上发帖称，他在调查SSH性能问题时发现了涉及XZ包中的**供应链攻击**，进一步溯源发现SSH使用的上游liblzma库被植入了后门代码，恶意代码可能允许攻击者通过后门版本的SSH非授权获取系统的访问权限。恶意代码修改了liblzma代码中的函数，该代码是XZ Utils软件包的一部分，链接到 XZ 库的任何软件都可以使用此修改后的代码，并允许拦截和修改与该库一起使用的数据。  
  
  
****  
**鉴于此漏洞影响范围较大，建议客户尽快做好自查及防护。**  
  
  
**本次更新内容：**  
  
**新增部分IOC。**  
  
  
  
**02**  
  
**影响范围**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响版本**  
  
xz == 5.6.0   
  
xz == 5.6.1  
  
liblzma== 5.6.0   
  
liblzma== 5.6.1  
  
  
**>**  
**>**  
**>**  
**>**  
  
**其他受影响组件**  
  
使用了受影响版本XZ的操作系统或软件如openSUSE、Fedora 41、Liblzma、Debian非稳定的测试版 5.5.1alpha-0.1 到 5.6.1-1  
  
详情可在此查询：  
  
https://repology.org/project/xz/versions  
  
  
**03**  
  
**验证及处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**处置建议**  
  
目前 GitHub 已经关停了整个xz项目。  
  
<table><colgroup style="outline: 0px;"><col width="147" style="outline: 0px;width: 110.25pt;"/><col width="140" style="outline: 0px;width: 105pt;"/><col width="485" style="outline: 0px;width: 363.75pt;"/><col width="284" style="outline: 0px;width: 213pt;"/></colgroup><tbody style="outline: 0px;"><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" width="70" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">操作系统</td><td width="98" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">是否受影响</td><td width="202" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">影响版本</td><td width="213" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">官方公告</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Red Hat</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Fedora</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">Fedora 41 and Fedora Rawhide</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Debian<span style="outline: 0px;"> </span>所有稳定版</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://security-tracker.debian.org/tracker/CVE-2024-3094</td></tr><tr height="64" style="outline: 0px;height: 48pt;"><td height="48" width="113" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">Debian testing,<br style="outline: 0px;"/>unstable and<br style="outline: 0px;"/>experimental<br style="outline: 0px;"/>distributions</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">5.5.1alpha-0.1（于 2024 年 2 月 1 日上传）到 5.6.1-1</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://lists.debian.org/debian-security-announce/2024/msg00057.html</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Kali Linux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">在3月26日至3月29日期间更新过的任何Kali安装</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.kali.org/blog/about-the-xz-backdoor/</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">OpenSUSE</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">Tumbleweed snapshot &lt;= 20240328</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://news.opensuse.org/2024/03/29/xz-backdoor/</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Amazon Linux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="108" style="outline: 0px;height: 81pt;"><td height="81" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Alpine</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">5.6.0<br style="outline: 0px;"/>5.6.0-r0<br style="outline: 0px;"/>5.6.0-r1<br style="outline: 0px;"/>5.6.1<br style="outline: 0px;"/>5.6.1-r0<br style="outline: 0px;"/>5.6.1-r1</td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="32" style="outline: 0px;height: 24pt;"><td height="24" width="113" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">MACOS<br style="outline: 0px;"/>HomeBrew x64</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">MicroOS</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">3月7日至3月28日期间发行</td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">SUSE<span style="outline: 0px;"> <br style="outline: 0px;"/></span>全部版本</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.suse.com/security/cve/CVE-2024-3094.html</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">archlinux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://security.archlinux.org/CVE-2024-3094</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Alpine edge</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://pkgs.alpinelinux.org/package/edge/main/x86/xz</td></tr></tbody></table>  
可据此查看受影响的开源操作系统 https://repology.org/project/xz/versions  
自查脚本：  
  
#! /bin/bash  
  
set -eu  
  
# find path to liblzma used by sshd  
  
path="$(ldd $(which sshd) | grep liblzma | grep -o '/[^ ]*')"  
  
  
# does it even exist?  
  
if [ "$path" == "" ]  
  
then  
  
  echo probably not vulnerable  
  
  exit  
  
fi  
  
  
# check for function signature  
  
if hexdump -ve '1/1 "%.2x"' "$path" | grep -q f30f1efa554889f54c89ce5389fb81e7000000804883ec28488954241848894c2410  
  
then  
  
  echo probably vulnerable  
  
else  
  
  echo probably not vulnerable  
  
fi  
  
  
也可通过如下命令查看系统本地是否安装了受影响的XZ：  
  
$ xz --version  
  
xz (XZ Utils) 5.6.1  
  
iblzma 5.6.1  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icsuABj6MaiaW02vfdUO92UudCICqvNuRDPFl8tLVZdCqHt66OhIBk1lTNqxbxxPmne2UtAicYWJQqA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
目前官方尚无最新版本，需对软件版本进行降级5.4.X，请关注官方新版本发布并及时更新。  
  
Fedora Linux 40 用户需 xz 回退到 5.4.x 版本可参考：  
  
https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users  
  
https://bodhi.fedoraproject.org/updates/FEDORA-2024-d02c7bb266  
  
  
**04**  
  
**部分IOC**  
  
目前，奇安信CERT已发现部分IOC，如下所示：  
<table><colgroup><col width="295" style="width: 221.25pt;"/><col width="333" style="width: 249.75pt;"/></colgroup><tbody><tr height="18" style="height: 13.5pt;"><td height="13" width="296" x:str="" align="center" valign="top"><strong>MD5</strong></td><td width="241" x:str="" align="center" valign="top"><strong>文件名</strong></td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">4f0cf1d2a2d44b75079b3ea5ed28fe54</td><td x:str="" width="241">x86_64-linux-gnu-liblzma.so.5.6.0</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">d26cefd934b33b174a795760fc79e6b5</td><td x:str="" width="241">liblzma_la-crc64-fast-5.6.1.o</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">d302c6cb2fa1c03c710fa5285651530f</td><td x:str="" width="241">usr/lib/liblzma.so.5</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">212ffa0b24bb7d749532425a46764433</td><td x:str="" width="241">00000001.liblzma_la-crc64-fast.o</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">53d82bb511b71a5d4794cf2d8a2072c1</td><td x:str="" width="241">liblzma.so.5.6.1</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">35028f4b5c6673d6f2e1a80f02944fb2</td><td x:str="" width="241">bad-3-corrupt_lzma2.xz</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">9b6c6e37b84614179a56d03da9585872</td><td x:str="" width="241">bad-3-corrupt_lzma2.xz</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">540c665dfcd4e5cfba5b72b4787fec4f</td><td x:str="" width="241">good-large_compressed.lzma</td></tr><tr height="18" style="height: 13.5pt;"><td height="13" x:str="" width="68">89e11f41c5afcf6c641b19230dc5cdea</td><td x:str="" width="241">good-large_compressed.lzma</td></tr></tbody></table>  
  
  
**05**  
  
**参考资料**  
  
[1]https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users  
  
[2]https://www.openwall.com/lists/oss-security/2024/03/29/10   
  
[3]https://repology.org/project/xz/versions  
  
[4]https://www.cisa.gov/news-events/alerts/2024/03/29/reported-supply-chain-compromise-affecting-xz-utils-data-compression-library-cve-2024-3094   
  
[5]https://sysdig.com/blog/cve-2024-3094-detecting-the-sshd-backdoor-in-xz-utils/  
  
[6]https://github.com/byinarie/CVE-2024-3094-info  
  
  
代码卫士试用地址：  
https://codesafe.qianxin.com  
  
开源卫士试用地址：https://oss.qianxin.com  
  
  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMTBzmfDJA6rWkgzD5KIKNibpR0szmPaeuu4BibnJiaQzxBpaRMwb8icKTeZVEuWREJwacZm3wElt7vOtQ/640?wx_fmt=jpeg "")  
  
  
  
**推荐阅读**  
  
[在线阅读版：《2023中国软件供应链安全分析报告》全文](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517225&idx=1&sn=8154b433ae2be87ccbae15bc0fb09a00&chksm=ea94b543dde33c55c168c44e830d62b03e9b34ca072871d10156273a3f282cab7ccc42b9b430&scene=21#wechat_redirect)  
  
  
[奇安信入选全球《软件成分分析全景图》代表厂商](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247515374&idx=1&sn=8b491039bc40f1e5d4e1b29d8c95f9e7&chksm=ea948d84dde30492f8a6c9953f69dbed1f483b6bc9b4480cab641fbc69459d46bab41cdc4859&scene=21#wechat_redirect)  
  
  
[开源软件 LibreOffice 修复多个与宏、密码等相关的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247513283&idx=1&sn=5fbd02e0f95926cab449829326e0a8a1&chksm=ea9485a9dde30cbf0fb5e64dcbabdcbc1486306bbf9305df01d0f12022f30b84421fe09b167c&scene=21#wechat_redirect)  
  
  
[Linux 恶意软件攻击配置不当的云服务器](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247519011&idx=1&sn=17a70a9a2f2ffda628277cf2e0884282&chksm=ea94ba49dde3335f1ba768295ca8970e7a2a3d6080ee433e0eaa41f8b52859e0ae9f0acd6635&scene=21#wechat_redirect)  
  
  
[WiFi漏洞导致安卓和Linux设备易受攻击](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518892&idx=2&sn=21e7796662495b4b807b3393dafd9890&chksm=ea94bbc6dde332d07356a2e54be40ffbdc88a3cac47f21912107d477815155335555fe2c827d&scene=21#wechat_redirect)  
  
  
[Linux glibc 漏洞可导致攻击者在主要发行版本获得 root 权限](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518790&idx=1&sn=3a59b1cc8580a5f1c75bb61edc82557b&chksm=ea94bb2cdde3323acc4f1a49e39fec5304d1b53e15b8b2a5cb36bf885ddd6a7b1432e0addc60&scene=21#wechat_redirect)  
  
  
[PyPI 仓库存在116款恶意软件，瞄准 Windows 和 Linux 系统](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518385&idx=2&sn=9d0f5eba19662c208dce17056f8b6708&chksm=ea94b9dbdde330cd71a57d346d79ef4b39ec7ac907787ebe1f7a41fe47d6201ea4cfa4947e94&scene=21#wechat_redirect)  
  
  
[严重的蓝牙漏洞已存在多年，可用于接管安卓、iOS 和 Linux 设备](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518319&idx=1&sn=5714524d6170f4fef9f36a2a9801b556&chksm=ea94b905dde3301385c1f828c130d4e404acb8a7addf6ed7ea4155f5063d9b70b0d539ec90eb&scene=21#wechat_redirect)  
  
  
  
****  
**转载请注明“转自奇安信代码卫士 https://codesafe.qianxin.com”。**  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSf7nNLWrJL6dkJp7RB8Kl4zxU9ibnQjuvo4VoZ5ic9Q91K3WshWzqEybcroVEOQpgYfx1uYgwJhlFQ/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSN5sfviaCuvYQccJZlrr64sRlvcbdWjDic9mPQ8mBBFDCKP6VibiaNE1kDVuoIOiaIVRoTjSsSftGC8gw/640?wx_fmt=jpeg "")  
  
**奇安信代码卫士 (codesafe)**  
  
国内首个专注于软件开发安全的产品线。  
  
   ![](https://mmbiz.qpic.cn/mmbiz_gif/oBANLWYScMQ5iciaeKS21icDIWSVd0M9zEhicFK0rbCJOrgpc09iaH6nvqvsIdckDfxH2K4tu9CvPJgSf7XhGHJwVyQ/640?wx_fmt=gif "")  
  
   
觉得不错，就点个 “  
在看  
” 或 "  
赞  
” 吧~  
  
