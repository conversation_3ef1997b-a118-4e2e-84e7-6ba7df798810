#  GPT-4 竟然会自动实施漏洞攻击，成功率高达87%   
流苏  FreeBuf   2024-04-22 18:48  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR38jUokdlWSNlAjmEsO1rzv3srXShFRuTKBGDwkj4gvYy34iajd6zQiaKl77Wsy9mjC0xBCRg0YgDIWg/640?wx_fmt=gif "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ibPaXXzibTQWIAW7icz3iaAesgxGaYakplPOxbpyicHib2IquM73uQnOtVic4TzTXRT0uFLjgCbAyEgia4xQ/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ibPaXXzibTQWIAW7icz3iaAesgZA8Ny7KtPYM2wDOs06cu1ibK6tFQgVvkFhN8Ry8REku0Pq38Yz8Aq1w/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/0pygn8iaZdEfON2XFbCe9JWdCUFibfJFXHlqlVzic0xXYe22EAA0g7icMibR7ALIf45LwzknqdPvUVsjeiasNnD6gA7LVwmsibNzTlS/640?wx_fmt=svg&from=appmsg "")  
  
左右滑动查看更多  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/0pygn8iaZdEfON2XFbCe9JWdCUFibfJFXHlqlVzic0xXYe22EAA0g7icMibR7ALIf45LwzknqdPvUVsjeiasNnD6gA7LVwmsibNzTlS/640?wx_fmt=svg&from=appmsg "")  
  
  
  
近日，伊利诺伊大学香槟分校的研究团队揭示了一项关于人工智能模型进行黑客攻击的新研究：只需要阅读CVE漏洞描述，GPT-4就可以瞬间化身黑客，成功实施漏洞攻击，综合成功率达到了惊人的87%。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ibPaXXzibTQWIAW7icz3iaAesgZCV6lqbKsbfoZxslKaBP42NSq366NRJfBIXDocUq8kmG4CBsDMZSPg/640?wx_fmt=jpeg&from=appmsg "")  
  
  
在此次研究中，该团队共对包括GPT-4、GPT-3.5在内的10个AI大模型进行实验，结果发现只有GPT-4可以通过CVE漏洞信息成功发起攻击，且单次成本仅8.8美元 （折合人民币约63元），其他模型的成功率均为零。  
  
  
有「热心肠」的网友立马就对该研究进行了复现，并在社交平台上表示成功了。GPT-4的安全性引发网络安全圈的热议。虽然测试的15个漏洞中有三分之一是比较古老且容易利用的「XSS漏洞」，但是其超低的使用门槛和超高漏洞利用率依旧引起了许多人的担忧。如果实验结果被用于实际，那么庞大的「脚本小子」立马就可以进化成「AI大模型小子」，这对政府组织和企业来说，将会是一次极其严峻的考验。  
  
  
**AI大模型漏洞利用过程**  
  
  
  
**AI大模型：**  
GPT-4；  
  
**漏洞：**  
15个单日漏洞（One-day vulnerabilities），包括网站、容器管理软件和Python包漏洞；  
  
**架构：**  
黑客智能体，如下图所示：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ibPaXXzibTQWIAW7icz3iaAesg1OSIiaz6qiaTXnHdhzB3m4sicfHN9lPxs69rSxVZBoKwDc8ZxsLceDTYA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
**攻击过程**  
  
  
1. 发出指令：例如「使用ACIDRain（一种恶意软件）攻击这个网站」；  
  
1. GPT-4接收请求并使用工具和CVE漏洞数据库信息发起攻击；  
  
1. 根据系统反馈和历史记录调整攻击措施；  
  
1. 实现「double-spend attack」。  
  
**结果：**  
GPT-4成功利用13个CVE漏洞，两个漏洞未成功，分别是Iris XSS和Hertzbeat RCE，综合成功率约为87%；  
  
**所使****用的工具****：**  
网页浏览、访问终端、网页搜索结果、创建和编辑文件、代码解释器等；  
  
**所使用提示词：**  
共计1056个token；  
  
**代码量：**  
91行代码，包括调试和日志记录语句；  
  
**无 CVE 信息后进行实验：**  
GPT-4成功率约为7%，表明对于AI大模型，发现漏洞比利用漏洞更加困难。  
  
  
进一步分析发现，GPT-4能够在33.3%的情况下正确识别出存在的漏洞，但是即使识别出漏洞，它只能利用其中的一个。如果只考虑GPT-4知识截止日期之后的漏洞，它能够找到55.6%的漏洞。  
  
  
研究人员还发现有无CVE描述，智能体采取的行动步数相差并不大，分别为24.3步和21.3步。他们推测这可能与模型的上下文窗口长度有关，并认为规划机制和子智能体可能会提高整体性能。  
  
  
关于成本，研究计算得出GPT-4每次利用漏洞的平均成本为3.52美元，主要源于输入token的费用。考虑到整个数据集中40%的成功率，每次成功攻击的平均成本约为8.8美元。  
  
  
【  
FreeBuf粉丝交流群招新啦！  
  
在这里，拓宽网安边界  
  
甲方安全建设干货；  
  
乙方最新技术理念；  
  
全球最新的网络安全资讯；  
  
群内不定期开启各种抽奖活动；  
  
FreeBuf盲盒、大象公仔......  
  
扫码添加小蜜蜂微信回复“加群”，申请加入群聊  
】  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ich6ibqlfxbwaJlDyErKpzvETedBHPS9tGHfSKMCEZcuGq1U1mylY7pCEvJD9w60pWp7NzDjmM2BlQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/oQ6bDiaGhdyodyXHMOVT6w8DobNKYuiaE7OzFMbpar0icHmzxjMvI2ACxFql4Wbu2CfOZeadq1WicJbib6FqTyxEx6Q/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ61icYlLmBLDpdYEZ7nIzpGovpHjtxITB6ibiaC3R5hoibVkQsVLQfdK57w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
> https://mp.weixin.qq.com/s/l1FE38CV2USLhStE29aF5Q  
> https://www.theregister.com/2024/04/17/gpt4_can_exploit_real_vulnerabilities/  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ7JfyOicficFrgrD4BHnIMtgCpBbsSUBsQ0N7pHC7YpU8BrZWWwMMghoQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247493362&idx=1&sn=39c9b1c4d709e5ad0babb44995b0e412&chksm=ce1f1c6df968957be704d2843b3f448b252d2a2e1b5271efa486c3e57819849e0e287b04568b&scene=21#wechat_redirect)  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247493318&idx=1&sn=02dc5120e00a3d6759be8fcf1b49ec0a&chksm=ce1f1c59f968954fd868b2f8cefa0e8bc5dd703c36dd6db4fc03923be36783a7d4cc791c18b6&scene=21#wechat_redirect)  
  
[](https://mp.weixin.qq.com/s?__biz=MjM5NjA0NjgyMA==&mid=2651253272&idx=1&sn=82468d927062b7427e3ca8a912cb2dc7&scene=21#wechat_redirect)  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR3icF8RMnJbsqatMibR6OicVrUDaz0fyxNtBDpPlLfibJZILzHQcwaKkb4ia57xAShIJfQ54HjOG1oPXBew/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=webp "")  
  
