#  实战 ｜ 记一次金融项目的高危漏洞挖掘   
 F12sec   2023-12-16 10:48  
  
> **申明****：本次测试只作为学习用处，请勿未授权进行渗透测试，切勿用于其它用途！**公众号现在只对常读和星标的公众号才展示大图推送，建议大家把   
明暗安全 设为星标，否则可能就看不到啦！****  
  
## 1.漏洞背景  
  
    在我的最新项目中，我针对金融领域的一个复杂系统进行了深入探索。我的目标是识别潜在的安全弱点，为此，我采用了子域名模糊测试（fuzzing）的方法。这一策略不仅高效而且果然发挥了作用：我很快就在系统的一个后台发现了一个弱口令漏洞。   进一步的调查引导我深入了解系统的功能架构。我注意到，所有关键的交互和数据交换都集中在 /api/ 目录下。这个重要的发现立即启发了我：通过简单地搜索这些API接口，我可以快速了解整个系统的工作原理，无需逐一通读繁杂的JavaScript代码。   这种方法不仅大大节省了我的时间，也为我提供了更加直观和高效的方式来理解系统的内部逻辑。我很快就能够映射出整个系统的API架构，并且识别出关键的操作点。这些信息对于进一步的安全测试和潜在的风险评估至关重要。   在本文中，我将详细介绍这个过程，包括我是如何发现弱口令后台的，以及如何有效利用API接口来深入分析系统的。这不仅是一次关于技术深度的探索，也是一次关于如何在复杂系统中找到简单解决方案的思考。  
  
    通过对api搜索，找到了很多api接口。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/kq6gvfy0n7qtZFicGEQk2Hus0VNkNtJ0kN2UPBDD3RhmCnOqUr1EYFU94zMYz1NzibLMsPjg9sh1cX062nCON1Ng/640?wx_fmt=png "")  
api:  
  
![](https://mmbiz.qpic.cn/mmbiz_png/kq6gvfy0n7qtZFicGEQk2Hus0VNkNtJ0kyXiaEzK27wcyTB1O9nKbAr4SbiaV7xl0TOxMJFJmaXZvVNicsNqEkukfA/640?wx_fmt=png "")  
  
通过对api的fuzz，发现这么一处奇怪的报错。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/kq6gvfy0n7qtZFicGEQk2Hus0VNkNtJ0kPvHfYGRNicNDDNcoPSzdVLtGIJHsJbG31HJO1DOHMJaD1uZGIajMhMw/640?wx_fmt=png&from=appmsg "")  
  
    在我的最近一次安全测试中，我遇到了一个不寻常的发现。一开始，我认为在type参数中出现SQL注入的可能性非常小。然而，当我在该参数中添加了一个单引号作为测试，系统意外地提示我“没有这样的目录，访问xxxx目录失败”。  
  
    对于经验丰富的安全专家来说，这样的提示往往意味着潜在的更深层次漏洞。这次也不例外。在我进一步分析这个提示后，我意识到这实际上可能是一个任意文件读取的漏洞。激动之下，我立即尝试读取/etc/passwd文件，而结果正如我所预期：成功了！  
  
    这是我第一次在type参数中发现任意文件读取的漏洞。这个发现对于理解和加固网站的安全性至关重要，因为它揭示了即使是最不起眼的参数也可能隐藏着重大的安全风险。  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/kq6gvfy0n7qtZFicGEQk2Hus0VNkNtJ0kILo6mzQUPefRd26TNVHYySFlRUXRELVPBYlwZSAWXVFLdkDXwDfFog/640?wx_fmt=png&from=appmsg "")  
  
  
   
—————————————————————  
- **往期精彩推荐**  
  
[实战 ｜ 记一次Bugcrowd实战挖掘](http://mp.weixin.qq.com/s?__biz=MzkxMjYxODcyNA==&mid=2247483741&idx=1&sn=c5c89a2457162676f7ded4cf0ad60ce5&chksm=c10b64d6f67cedc0a0e78ace121952ffe8e19d5f512ecf8a59d93c90f1332cbea1380a59c53f&scene=21#wechat_redirect)  
[工具 ｜ burp被动扫描xss神器](http://mp.weixin.qq.com/s?__biz=MzkxMjYxODcyNA==&mid=2247483723&idx=1&sn=958a74524e740b397d2af6a3d1846543&chksm=c10b64c0f67cedd62434c5743ea0a3a2d65e630e63b70f81ea18ea53c6a7322ec13ccaf789fb&scene=21#wechat_redirect)  
[工具 ｜ 无回显变可回显rce](http://mp.weixin.qq.com/s?__biz=MzkxMjYxODcyNA==&mid=2247483708&idx=1&sn=28e86af2eb69e69cec8255fe487fffdf&chksm=c10b64b7f67ceda1f62361cdce50ce8d07da0a08970e5a385155df076d6cba61e43fccc8ab8c&scene=21#wechat_redirect)  
  
## ❤️爱心三连击  
  
1.关注公众号  
「明暗安全」！  
  
2.本文已收录在明暗官方网站：http://www.php1nf0.top/  
  
3.看到这里了就点个关注支持下吧，你的  
「关注」是我创作的动力。  
  
公众号：明暗安全  
  
官方网站：http://www.php1nf0.top/  
  
qq群：709068373  
  
微信群：公众号回复  
加群获取  
  
这是一个终身学习的团队，他们在坚持自己热爱的事情，欢迎加入明暗安全，和师傅们一起开心的挖洞～  
  
        关注和转发是莫大鼓励❤️  
  
