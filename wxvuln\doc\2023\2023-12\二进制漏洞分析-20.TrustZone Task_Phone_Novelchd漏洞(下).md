#  ​二进制漏洞分析-20.TrustZone Task_Phone_Novelchd漏洞(下)   
原创 haidragon  安全狗的自我修养   2023-12-10 11:00  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHL5u1UUice03iaOicUmNl5f96icPndfmZ63AGb3pvVVTDia1u8ib7710U2wib8wa7zEULTlTE2bKtKLb6Ng/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
   
# 二进制漏洞分析-1.华为Security Hypervisor漏洞  
# 二进制漏洞分析-2.揭示华为安全管理程序(上)  
# 二进制漏洞分析-3.揭示华为安全管理程序(下)  
# 二进制漏洞分析-4.华为安全监控漏洞(SMC SE 工厂检查 OOB 访问)  
  
[二进制漏洞分析-5.华为安全监控漏洞(SMC MNTN OOB 访问)](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247489932&idx=1&sn=8137e176025769acb9c31ba29a95ce3f&chksm=c13f2ac5f648a3d3fa0cb43ee3c7db8300b3a350cf12e51bc990e7015585a0bc53ed2c17562d&scene=21#wechat_redirect)  
  
# 二进制漏洞分析-6.Parallels Desktop Toolgate 漏洞  
# 二进制漏洞分析-7.华为TrustZone Vsim_Sw漏洞  
# 二进制漏洞分析-8.Huawei TrustZone VprTa漏洞  
# 二进制漏洞分析-9.华为TrustZone TEE_Weaver漏洞  
  
[二进制漏洞分析-10.华为TrustZone TEE_SERVICE_VOICE_REC漏洞](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490061&idx=1&sn=7d05728cc107b418453513eaeee259d6&chksm=c13f2944f648a052dad7bee5958a345195b843dc6473ce98311a857d4375e61cc4505b63950b&scene=21#wechat_redirect)  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490062&idx=1&sn=f5b234a3a0223dca7d990362dceff5fa&chksm=c13f2947f648a0518ab8c66eb32c694ee315043e954abc52a20c3b6d8eefc3a624575441401d&scene=21#wechat_redirect)  
# 二进制漏洞分析-11.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(上)  
# 二进制漏洞分析-12.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(下)  
# 二进制漏洞分析-13.华为TrustZone TEE_SERVICE_FACE_REC漏洞(一)  
# 二进制漏洞分析-14.华为TrustZone TEE_SERVICE_FACE_REC漏洞(二)  
# 二进制漏洞分析-15.华为TrustZone TEE_SERVICE_FACE_REC漏洞(三)  
# 二进制漏洞分析-16.华为TrustZone TEE_SERVICE_FACE_REC漏洞(四)  
# 二进制漏洞分析-17.华为TrustZone Tee_Fido_Main漏洞  
# 二进制漏洞分析-18.华为TrustZone TEE_EID漏洞  
  
[二进制漏洞分析-19.华为TrustZone TCIS漏洞](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490507&idx=1&sn=e2134c571be18442c52f062c2c30affe&chksm=c13f2882f648a194962d725a6613cfb988d4def0d1f0228e2c02acb0eb005b520d648ac0aaf3&scene=21#wechat_redirect)  
  
## 缺少长度和偏移检查NOVEL_CHDRM_Copyordecrypt¶  
  
该函数从用户提供的输入缓冲区中提取偏移量和长度，对其进行字节交换，并使用它们执行各种操作。特别是，许多对这些用户控制值的参数的调用和从这些用户控制的值派生的参数会导致 ION 缓冲区 OOB 写入、ION 缓冲区 OOB 读取，甚至堆栈缓冲区溢出。NOVEL_CHDRM_CopyordecryptTEE_ParamNOVEL_CHDRMw_MemcpyDRM_SVP_AES  
  
例如，下面是基于堆栈的缓冲区溢出：  
```
```  
```
int NOVEL_CHDRM_Copyordecrypt(TEE_Param params[4]) {
    /* [...] */
    int userprovided_license[4];

    /* [...] */
    ibuf3_addr = (uint32_t *)params[3].memref.buffer;
    ibuf3_int0 = bswap32(ibuf3_addr[0]); /* user-controlled */
    ibuf3_int1 = bswap32(ibuf3_addr[1]); /* user-controlled */
    if (ibuf3_int1)
        /* Stack-based buffer overflow */
        NOVEL_CHDRMw_Memcpy(userprovided_license, ibuf3_addr + 2, ibuf3_int1);
    /* [...] */
}

```  
  
以下是导致 ION 缓冲区 OOB 写入/OOB 读取的调用之一：NOVEL_CHDRMw_Memcpy  
```
```  
```
int NOVEL_CHDRM_Copyordecrypt(TEE_Param params[4]) {
    /* [...] */
    NOVEL_CHDRM_Mmap(params[0].memref.buffer, 0, params[0].memref.size, &ionmap0_addr, 1, 1);
    NOVEL_CHDRM_Mmap(params[2].memref.buffer, 0, params[0].memref.size, &ionmap2_addr, ibuf3_int0 == 0, 1);

    /* [...] */
    ibuf3_curr_ptr = ibuf3_addr + 7;
    before_datalen = 0;
    before_offset = 0;
    while (1) {
        offset = bswap32(ibuf3_curr_ptr[0]); /* user-controlled */
        datalen = bswap32(ibuf3_curr_ptr[1]); /* user-controlled */

        /* [...] */
        if (before_datalen + before_offset != offset)
            /* ION buffer OOB write/OOB read */
            NOVEL_CHDRMw_Memcpy(
                ionmap2_addr + before_datalen + before_offset,
                ionmap0_addr + before_datalen + before_offset,
                offset - (before_datalen + before_offset));

        /* [...] */
        before_datalen = datalen;
        before_offset = offset;
    }
    /* [...] */
}

```  
  
以下是导致 ION 缓冲区 OOB 写入/OOB 读取的调用之一：DRM_SVP_AES  
```
```  
```
int NOVEL_CHDRM_Copyordecrypt(TEE_Param params[4]) {
    /* [...] */
    ibuf3_curr_ptr = ibuf3_addr + 7;
    tmp_enc_datalen = 0;
    global_offset = 0;
    while (1) {
        offset = bswap32(ibuf3_curr_ptr[0]); /* user-controlled */
        datalen = bswap32(ibuf3_curr_ptr[1]); /* user-controlled */

        if (tmp_enc_datalen) {
            /* [...] */
        } else {
            enc_datalen = datalen;
        }

        if ((flag & 2) != 0) {
            /* [...] */
        } else if (enc_datalen <= 0xF) {
            /* [...] */
        } else {
            tmp_enc_datalen = enc_datalen & 0xF;
            if ((enc_datalen & 0xF) != 0) {
                /* [...] */
            } else {
                /* ION buffer OOB write/OOB read */
                DRM_SVP_AES(aes_op, aes_obj, flag,
                            ionmap0_addr + offset + global_offset,
                            ionmap2_addr + offset + global_offset,
                            enc_datalen);
                /* [...] */
            }
        }
    }
    /* [...] */
}

```  
  
我们确定了 7 个易受攻击的调用：NOVEL_CHDRMw_MemcpyNOVEL_CHDRM_Copyordecrypt  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1155" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1156" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1157" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="44876" msthash="1158" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8204</td><td msttexthash="697905" msthash="1159" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+108</td><td msttexthash="105261351" msthash="1160" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">基于堆栈的缓冲区溢出（如果大小为 &gt; 0x10）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="46410" msthash="1161" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8584</td><td msttexthash="702728" msthash="1162" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+488</td><td msttexthash="37350014" msthash="1163" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 写入/OOB 读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51935" msthash="1164" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x85c0</td><td msttexthash="705770" msthash="1165" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+4C4</td><td msttexthash="23861890" msthash="1166" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45825" msthash="1167" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8634</td><td msttexthash="700947" msthash="1168" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+538</td><td msttexthash="20614997" msthash="1169" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 写入</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53300" msthash="1170" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x864c</td><td msttexthash="698191" msthash="1171" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+550</td><td msttexthash="20614997" msthash="1172" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 写入</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45812" msthash="1173" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8724</td><td msttexthash="700934" msthash="1174" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+628</td><td msttexthash="23861890" msthash="1175" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45747" msthash="1176" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x8850</td><td msttexthash="700869" msthash="1177" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">NOVEL_CHDRM_Copyordecrypt+754</td><td msttexthash="23861890" msthash="1178" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">ION 缓冲液 OOB 读取</td></tr></tbody></table>  
我们还确定了 7 个易受攻击的调用：DRM_SVP_AESNOVEL_CHDRM_Copyordecrypt  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1180" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1181" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1182" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="52403" msthash="1183" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x86a4</td><td msttexthash="707135" msthash="1184" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+5A8</td><td msttexthash="37350014" msthash="1185" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 写入/OOB 读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="46865" msthash="1186" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8758</td><td msttexthash="36956634" msthash="1187" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_Copyordecrypt+65摄氏度</td><td msttexthash="37350014" msthash="1188" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">ION 缓冲液 OOB 写入/OOB 读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53729" msthash="1189" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x87e8</td><td msttexthash="743041" msthash="1190" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">NOVEL_CHDRM_Copyordecrypt+6ec</td><td msttexthash="37350014" msthash="1191" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">ION 缓冲液 OOB 写入/OOB 读取</td></tr></tbody></table>## 缺少长度签入NOVEL_CHDRM_SetDRMCertData¶  
  
NOVEL_CHDRM_SetDRMCertData  
从输入缓冲区中提取两个证书。在不检查其大小的情况下，该函数会将它们复制到两个堆栈分配的缓冲区中，分别为 和 。这可能导致基于堆栈的缓冲区溢出。server_Cert_pserver_CACert_pTEE_Paramserver_Cert_cpy_lenserver_CACert_cpy  
```
```  
```
int NOVEL_CHDRM_SetDRMCertData(int *ibuf0_addr, unsigned int ibuf0_size) {
    char server_CACert_cpy[2048];
    char server_Cert_cpy[2048];
    /* [...] */
    ibuf0_size_cpy = ibuf0_size;
    unpack_tlv_data(0x18, ibuf0_addr, &ibuf0_size_cpy, &server_Cert_p,
        &server_Cert_len);
    unpack_tlv_data(0x19, ibuf0_addr, &ibuf0_size_cpy, &server_CACert_p,
        &server_CACert_len);
    /* [...] */
    if (server_CACert_p)
        NOVEL_CHDRMw_Memcpy(server_CACert_cpy, server_CACert_p, server_CACert_len);
    /* [...] */
    if (server_Cert_p)
        NOVEL_CHDRMw_Memcpy(server_Cert_cpy, server_Cert_p, server_Cert_len);
    /* [...] */
}

```  
## 缺少长度签入NOVEL_CHDRM_SetRegisterResData¶  
  
包含相同的漏洞模式两次：NOVEL_CHDRM_SetRegisterResData  
- 它从输入缓冲区中的 TLV 数据中解析用户控制大小的缓冲区TEE_Param  
  
- 它调用此缓冲区作为输入，将固定大小的缓冲区作为输出DRM_AES_Encrypt_cbc  
  
```
```  
```
uint8_t Root_CA_Cert[0x800]; /* in the BSS */

int NOVEL_CHDRM_SetRegisterResData(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    char aes_outbuf[0x800];
    /* [...] */
    unpack_tlv_data(0x21, ibuf0_addr, &ibuf0_size, &aes_inbuf, &aes_inbuf_size);
    /* [...] */
    /* 1st Stack-based buffer overflow */
    DRM_AES_Encrypt_cbc(aes_inbuf, aes_inbuf_size, aes_outbuf, aes_key, aes_iv, 1);

    /* [...] */
    unpack_tlv_data(0x42, ibuf0_addr, &ibuf0_size, &root_ca_inbuf, &root_ca_inbuf_len);
    /* [...] */
    /* 2nd Stack-based buffer overflow */
    DRM_AES_Encrypt_cbc(root_ca_inbuf, root_ca_inbuf_len, Root_CA_Cert, aes_key, aes_iv, 1);
    /* [...] */
}

```  
  
此模式可能导致缓冲区溢出：  
- 对堆栈分配的缓冲区进行溢出的第一次调用DRM_AES_Encrypt_cbc  
  
- 第二次调用溢出位于 BSS 中的缓冲区DRM_AES_Encrypt_cbc  
  
## 呼叫时长度检查缺失/错误NOVEL_CHDRMw_MemCompare¶  
  
该函数不检查它传递到的用户控制的长度，允许在 BSS 缓冲区之后逐字节泄漏数据。NOVEL_CHDRM_SetRegisterResDataNOVEL_CHDRMw_MemComparesg_salt  
```
```  
```
int NOVEL_CHDRM_SetRegisterResData(int *ibuf0_addr, unsigned int ibuf0_size) {
    /* [...] */
    unpack_tlv_data(0x34, ibuf0_addr, &ibuf0_size, &salt_des, &salt_des_size);
    if (NOVEL_CHDRMw_MemCompare(salt_des, &sg_salt, salt_des_size)) {
        /* [...] */
        return 0xFFFF0000;
    }
    /* [...] */
}

```  
  
该函数检查传递给 的用户控制的长度，但仅在调用之后，从而导致 BSS 缓冲区的过度读取。NOVEL_CHDRM_SetDrmTimeNOVEL_CHDRMw_MemCompareOTPChipIDHex  
```
```  
```
int NOVEL_CHDRM_SetDrmTime(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    /* [...] */
    if (!unpack_tlv_data(0x36, ibuf0_addr, &ibuf0_size, &chipid_p, &chipid_size)
          && (NOVEL_CHDRMw_MemCompare(chipid_p, &OTPChipIDHex, chipid_size) || chipid_size != 0x20) ) {
        /* [...] */
    }
    /* [...] */
}

```  
  
我们在调用时发现了 11 个缺失/错误的长度检查（它们可能并非都可被利用）：NOVEL_CHDRMw_MemCompare  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1205" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1206" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1207" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="59150" msthash="1208" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x2ef8</td><td msttexthash="861146" msthash="1209" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_GetLicenseReqData+5d0</td><td msttexthash="25095317" msthash="1210" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45396" msthash="1211" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x3554</td><td msttexthash="836134" msthash="1212" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_DelLicenseReqData+228</td><td msttexthash="25095317" msthash="1213" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="50531" msthash="1214" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x40a0</td><td msttexthash="1202019" msthash="1215" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetDRMDataLicenseResData+A4C</td><td msttexthash="25095317" msthash="1216" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52325" msthash="1217" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x50d8</td><td msttexthash="1225666" msthash="1218" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetDRMDataLicenseResData+1A84</td><td msttexthash="25095317" msthash="1219" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45877" msthash="1220" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x7464</td><td msttexthash="883077" msthash="1221" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetRegisterResData+D4</td><td msttexthash="25095317" msthash="1222" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51194" msthash="1223" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x8c40</td><td msttexthash="521885" msthash="1224" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetDrmTime+E4</td><td msttexthash="25095317" msthash="1225" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="60710" msthash="1226" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x35c80</td><td msttexthash="500097" msthash="1227" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_DelLicenseForCEKID+38</td><td msttexthash="25095317" msthash="1228" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="61425" msthash="1229" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x35d38</td><td msttexthash="539097" msthash="1230" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_FindLicenseForCEKID+38</td><td msttexthash="25095317" msthash="1231" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="68679" msthash="1232" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x35fd4</td><td msttexthash="668473" msthash="1233" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_FindLicenseForCEKIDExt+38</td><td msttexthash="25095317" msthash="1234" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53534" msthash="1235" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x36170</td><td msttexthash="696423" msthash="1236" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_FindLocalLicenseByCEKID+24</td><td msttexthash="25095317" msthash="1237" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="55328" msthash="1238" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x36388</td><td msttexthash="611208" msthash="1239" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">DRM_FindMemLicenseByCEKID+24</td><td msttexthash="25095317" msthash="1240" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">缓冲区过度读取</td></tr></tbody></table>## 调用后缺少长度检查unpack_tlv_data¶  
  
该函数包含易受攻击模式的实例之一：NOVEL_CHDRM_SetDrmTime  
- 使用以下命令从输入缓冲区中解压缩值TEE_Paramunpack_tlv_data  
  
- 该值的使用方式就好像它的大小为 >= 4（在此示例中）  
  
```
```  
```
int NOVEL_CHDRM_SetDrmTime(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    /* [...] */
    unpack_tlv_data(1, ibuf0_addr, &ibuf0_size_, &data_p, &length_p);
    /* [...] */
    FUN(
        "%s %s: drmtime[%x],[%x],[%x]\n\n",
        "[Warning]",
        "NOVEL_CHDRM_SetDrmTime",
        data_p[0],
        data_p[1]),
        data_p[2]));
    DRM_Set_CurTime(*(uint32_t *)data_p);
    /* [...] */
}

```  
  
由于从不检查读取值的实际长度（例如，当预期有四个字节时读取单个字节），这可能会导致堆缓冲区过度读取（因为解压缩的值是从堆中分配的）。  
  
在调用 后，我们发现了 6 个缺失的长度检查，所有这些检查都可能导致堆缓冲区过度读取：unpack_tlv_data  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1247" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1248" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1249" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="58188" msthash="1250" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x2bb8</td><td msttexthash="838461" msthash="1251" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_GetLicenseReqData+290</td><td msttexthash="47666372" msthash="1252" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆分配的缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45318" msthash="1253" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x3418</td><td msttexthash="34548358" msthash="1254" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_DelLicenseReqData+欧共体</td><td msttexthash="47666372" msthash="1255" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆分配的缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59969" msthash="1256" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x39dc</td><td msttexthash="1189773" msthash="1257" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetDRMDataLicenseResData+388</td><td msttexthash="47666372" msthash="1258" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆分配的缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59358" msthash="1259" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x74ac</td><td msttexthash="923650" msthash="1260" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetRegisterResData+11c</td><td msttexthash="47666372" msthash="1261" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆分配的缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53300" msthash="1262" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x79a8</td><td msttexthash="903760" msthash="1263" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_SetRegisterResData+618</td><td msttexthash="47666372" msthash="1264" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆分配的缓冲区过度读取</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53027" msthash="1265" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x8b98</td><td msttexthash="533806" msthash="1266" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">NOVEL_CHDRM_SetDrmTime+3c</td><td msttexthash="47666372" msthash="1267" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">堆分配的缓冲区过度读取</td></tr></tbody></table>## 堆栈/堆/BSS 指针泄漏DRM_AES_Encrypt_xxx¶  
  
受信任的应用程序中使用了多个日志字符串，这些字符串泄漏了有关地址空间的信息，例如堆栈指针、堆指针和 bss 指针。  
  
下面是函数中此漏洞的一个实例的示例。DRM_AES_Encrypt_cbc  
```
```  
```
TEE_Result DRM_AES_Encrypt_cbc(
        char *buffer_input,
        int buffer_len,
        char *buffer_output,
        const void *key,
        int iv,
        int mode)
{
    /* [...] */
    tee_print(0,
        "%s %d:buffer_input = %p, buffer_len = %d,buffer_output = %p,key = %p ",
        "[error]",
        0x195,
        buffer_input,
        buffer_len,
        buffer_output,
        key);
    /* [...] */
}

```  
  
此函数泄漏作为参数传递的指针，从而生成可从 读取的日志。有关可能泄漏的指针类型的示例，我们可以查看对 in 的调用。tee_printlogcatDRM_AES_Encrypt_cbcNOVEL_CHDRM_SetRegisterResData  
```
```  
```
uint8_t Root_CA_Cert[0x800]; /* in the BSS */

int NOVEL_CHDRM_SetRegisterResData(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    /* [...] */
    uint8_t aes_key[8];
    /* [...] */
    unpack_tlv_data(0x42, ibuf0_addr, &ibuf0_size, &root_ca_inbuf, &root_ca_inbuf_len);
    /* [...] */
    DRM_AES_Encrypt_cbc(root_ca_inbuf, root_ca_inbuf_len, Root_CA_Cert, aes_key, aes_iv, 1);
    /* [...] */
}

```  
  
NOVEL_CHDRM_SetRegisterResData  
将以下参数传递给：DRM_AES_Encrypt_cbc  
- buffer_input => root_ca_inbuf  
，指向由unpack_tlv_data;  
  
- buffer_output => Root_CA_Cert  
，指向 BSS 缓冲区的指针;  
  
- key => aes_key  
，指向堆栈分配的缓冲区的指针。  
  
我们发现了 3 个泄漏指针的日志字符串（它们可能并非都可访问）：  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1277" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1278" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1279" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="53703" msthash="1280" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x33218</td><td msttexthash="435188" msthash="1281" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_AES_Encrypt_ecb+54</td><td msttexthash="21146437" msthash="1282" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆栈指针泄漏</td></tr><tr style="box-sizing: inherit;"><td msttexthash="56017" msthash="1283" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x3333C</td><td msttexthash="434603" msthash="1284" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_AES_Encrypt_cbc+54</td><td msttexthash="33460206" msthash="1285" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">堆栈/堆/BSS 指针泄漏</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53937" msthash="1286" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x33454</td><td msttexthash="450203" msthash="1287" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">DRM_AES_Encrypt_ctr+4C</td><td msttexthash="13215215" msthash="1288" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">指针泄漏</td></tr></tbody></table>## 整数下溢输入unpack_tlv_data¶  
  
unpack_tlv_data  
通过遍历输入缓冲区包含的 TLV 对象，从输入缓冲区中提取值。  
  
对于每个 TLV 对象，它检索长度和指向嵌入其中的数据的指针。然后，它会检查与对象关联的标记：  
- 如果它与参数匹配，则迭代将停止，并在提取的数据复制到其中之前分配堆缓冲区;tag  
  
- 否则，它将继续输入缓冲区内的下一个 TLV 对象，直到剩余大小等于或小于 4 个字节。  
  
```
```  
```
int unpack_tlv_data(
        uint32_t tag,
        uint8_t *inbuf,
        uint32_t *insize_p,
        void **data_p_p,
        uint32_t *length_p)
{
    /* [...] */
    // Remaining size in the input buffer
    uint32_t remaining_size = *insize_p;
    curr_offset = 0;
    while (1) {
        // TLV object length
        data_length = bswap32(*(uint32_t *)&inbuf[curr_offset + 1]);
        // TLV object data offset
        data_offset = curr_offset + 5;
        // Returns if the tag was found
        if (inbuf[curr_offset] == tag)
            break;
        // Computes the new data offset
        curr_offset = data_length + data_offset;
        /*
         * Computes how many bytes remain in the input buffer
         * The integer underflow occurs here.
         */
        remaining_size += -5 - data_length;
        // Unsigned comparison unable to detect the underflow
        if (remaining_size <= 4)
            return 0x7FFF0001;
    }
    if (data_length) {
        data_p = NOVEL_CHDRMw_Malloc(data_length);
        *data_p_p = data_p;
        NOVEL_CHDRMw_Memcpy(data_p, &inbuf[data_offset], data_length);
    }
    *length_p = data_length;
    return 0;
}

```  
  
但是，对剩余大小的比较是无符号的，并且没有考虑整数下溢。当数据长度和标头的大小从 中 的剩余大小中减去时，可能会变为负数。remaining_size += -5 - data_lengthremaining_size  
  
例如，在循环开始时，如果：  
- inbuf  
尺寸为 0x100;  
  
- remaining_size  
是0x10;  
  
- curr_offset  
是0xf0;  
  
- 从输入缓冲区中提取的是 。data_length0x1000  
  
更新这些值后，我们将得到：  
- curr_offset  
= 0xf0 + 5 + 0x1000 = 0x10f5;  
  
- remaining_size  
= 0x10 - 5 - 0x1000 = 0xfffff00b。  
  
我们可以看到，由于 上的比较是无符号的，因此它将通过检查，因为 .然后，循环将继续迭代，并开始提取之前输入之外的值。例如，长度将在 address 读取，这超出了我们的 0x100 字节输入缓冲区的范围。remaining_size0xfffff00b > 4inbuf + curr_offset + 1 = inbuf + 0x10f6  
## 整数下溢输入find_tlv_data¶  
  
find_tlv_data  
执行与 相同的操作，只是它不分配缓冲区来将 TLV 对象复制到其中。相反，它返回对象的偏移量和长度。因此，它容易受到与上一节中解释的漏洞类型相同的漏洞的影响。unpack_tlv_data  
```
```  
```
int find_tlv_data(
        uint32_t tag,
        uint8_t *inbuf,
        uint32_t *inbuf_size_p,
        uint32_t *data_offset_p,
        uint32_t *data_length_p)
{
    /* [...] */
    // Remaining size in the input buffer
    uint32_t remaining_size = *inbuf_size_p;
    curr_offset = 0;
    do {
        // TLV object length
        data_length = bswap32(*(uint32_t *)(inbuf + curr_offset + 1));
        // Check the TLV object type
        if (*(uint8_t *)(inbuf + curr_offset) == tag) {
            // Return the TLV object offset and length
            *data_offset_p = curr_offset;
            *data_length_p = data_length + 5;
            return 0;
        }
        /*
         * Computes how many bytes remain in the input buffer
         * The integer underflow occurs here.
         */
        curr_offset += data_length + 5;
        remaining_size += -5 - data_length;
    } while (remaining_size > 4); // Unsigned comparison unable to detect the underflow
    return 0x7FFF0001;
}

```  
## OOB 访问getvaluewithtypeandindex¶  
  
在该函数中，可以有多个 OOB 访问：getvaluewithtypeandindex  
- 在 ，由于（16 位值）由用户控制，因此访问最多可以超出 0xffff 个字节[1]key_length  
  
- 在 ，由于（8 位值）由用户控制，因此访问最多可以超出 0xff 个字节[2]rule_offset  
  
- at 和 ，因为（8 位值）和（8 位值）是用户控制的，所以访问最多可以越界 0xffff 字节[3][4]rule_offsetrule_count  
  
- 在 ，由于（8 位值）由用户控制，因此访问最多可以超出 0xff 个字节[5]some_offset  
  
```
```  
```
int getvaluewithtypeandindex(
        int type,
        int key_type,
        int inbuf,
        int insize,
        void *data_buf,
        unsigned int *length_p)
{
    /* [...] */
    offset = 0;
    value_offset = 0;
    value_length = 0;

    while (1) {
        // Outer TLV length
        length = bswap16(*(uint16_t *)(inbuf + offset + 2));

        // Check the outer TLV type
        if (*(uint8_t *)(inbuf + offset) == type) {
            value_offset = offset + 4;

            if (type == 1) {
                value_length = *(uint8_t *)(inbuf + value_offset + 8) + 9;
                goto FOUND_IT;

            } else if (type == 3) {
                key_length = bswap16(*(uint16_t *)(inbuf + value_offset + 1));
                /* [1]: The access below can be OOB by a maximum of 0xffff bytes */
                if (*(uint8_t *)(inbuf + value_offset + key_length + 3) == key_type) {
                    value_offset += 3;
                    value_length = key_length;
                    goto FOUND_IT;
                }

            } else if (type == 4) {
                rule_offset = *(uint8_t *)(inbuf + value_offset + 1);
                /* [2]: The access below can be OOB by a maximum of 0xff bytes */
                rule_count = *(uint8_t *)(inbuf + value_offset + rule_offset + 2);
                for (int i = 0; i /*signed*/< rule_count: i++) {
                    rule = inbuf + value_offset + rule_offset;
                    /* [3]: The access below can be OOB by a maximum of 0xffff bytes */
                    rule_type = *(uint8_t *)(rule + 3);
                    /* [4]: The access below can be OOB by a maximum of 0xffff bytes */
                    rule_length = *(uint8_t *)(rule + 4);
                    if (key_type == rule_type) {
                        value_offset = rule_offset + 5;
                        value_length = rule_length;
                        goto FOUND_IT;
                    }
                    rule_offset += 2 + rule_length;
                }

            } else if (type == 0xff) {
                if (key_type) {
                    some_offset = *(uint8_t *)(inbuf + value_offset + 1);
                    /* [5]: The access below can be OOB by a maximum of 0xff bytes */
                    value_length = *(uint8_t *)(inbuf + value_offset + some_offset + 3);
                    value_offset += 4 + some_offset;
                } else {
                    value_length = offset;
                    value_offset = 0;
                }
                goto FOUND_IT;
            }
        }

        // Increment the current offset
        offset += 4 + length;
        if (offset /*signed*/>= insize) {
            /* [...] */
            return -1;
        }
    }

FOUND_IT:
    // Copy the value into the argument buffer
    if (value_offset + value_length /*signed*/<= insize) {
        NOVEL_CHDRMw_Memcpy(data_buf, inbuf + value_offset, value_length);
        *length_p = value_length;
        return 0;
    } else {
        /* [...] */
        return 0xFFFFFFFD;
    }
}

```  
## 未经检查的 Malloc 返回值¶  
  
在 中，不检查调用的返回值（这是包装器）。因此，如果分配失败（例如，如果系统内存不足），则在使用此指针时将导致空指针取消引用。unpack_tlv_dataNOVEL_CHDRMw_MallocTEE_Malloc  
```
```  
```
int unpack_tlv_data(
        uint32_t tag,
        uint8_t *inbuf,
        uint32_t *insize_p,
        void **data_p_p,
        uint32_t *length_p)
{
    /* [...] */
    // Remaining size in the input buffer
    uint32_t remaining_size = *insize_p;
    curr_offset = 0;
    while (1) {
        // TLV object length
        data_length = bswap32(*(uint32_t *)&inbuf[curr_offset + 1]);
        // TLV object data offset
        data_offset = curr_offset + 5;
        // Returns if the tag was found
        if (inbuf[curr_offset] == tag)
            break;
        // Computes the new data offset
        curr_offset = data_length + data_offset;
        /*
         * Computes how many bytes remain in the input buffer
         * The integer underflow occurs here.
         */
        remaining_size += -5 - data_length;
        // Unsigned comparison unable to detect the underflow
        if (remaining_size <= 4)
            return 0x7FFF0001;
    }
    if (data_length) {
        data_p = NOVEL_CHDRMw_Malloc(data_length);
        *data_p_p = data_p;
        NOVEL_CHDRMw_Memcpy(data_p, &inbuf[data_offset], data_length);
    }
    *length_p = data_length;
    return 0;
}

```  
  
此外，还可以通过将负长度传递给 （在这种情况下，长度由用户控制）来强制分配失败。 然后，将作为指向 null 指针的指针，并在下次使用时崩溃。调用（这是包装器）也会失败，因为长度为负数，但由于其返回值也未选中，因此该函数将成功返回。NOVEL_CHDRMw_Mallocdata_p_pNOVEL_CHDRMw_Memcpymemcpy_s  
  
我们发现了 10 个未检查返回值的实例：NOVEL_CHDRMw_Malloc  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1316" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">地址</th><th msttexthash="5695157" msthash="1317" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">访客</th><th msttexthash="4085822" msthash="1318" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="46176" msthash="1319" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x1858</td><td msttexthash="30833907" msthash="1320" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_GetSecurityReqData+2直流</td><td msttexthash="25604150" msthash="1321" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51987" msthash="1322" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x6e54</td><td msttexthash="895869" msthash="1323" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_GetRegisterReqData+160</td><td msttexthash="25604150" msthash="1324" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="58760" msthash="1325" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x1c7c</td><td msttexthash="637026" msthash="1326" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">NOVEL_CHDRM_GetSignature+178</td><td msttexthash="25604150" msthash="1327" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="68120" msthash="1328" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x35ae4</td><td msttexthash="271622" msthash="1329" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">DRM_NewLicense+C</td><td msttexthash="25604150" msthash="1330" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="61880" msthash="1331" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x371c8</td><td msttexthash="651820" msthash="1332" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">Secure_Store_DataDecrypt+60</td><td msttexthash="25604150" msthash="1333" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="62777" msthash="1334" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x374f8</td><td msttexthash="695656" msthash="1335" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">Secure_Store_DataEncrypt+cc</td><td msttexthash="25604150" msthash="1336" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="60333" msthash="1337" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x37d30</td><td msttexthash="719316" msthash="1338" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">Secure_Store_EncryptWrite+9C</td><td msttexthash="25604150" msthash="1339" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="63154" msthash="1340" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x3849c</td><td msttexthash="621387" msthash="1341" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">Secure_Store_PlainWrite+9C</td><td msttexthash="25604150" msthash="1342" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="62998" msthash="1343" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">0x397c8</td><td msttexthash="331084" msthash="1344" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">unpack_tlv_data+64</td><td msttexthash="25604150" msthash="1345" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">空指针取消引用</td></tr><tr style="box-sizing: inherit;"><td msttexthash="63999" msthash="1346" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">0x3999c</td><td msttexthash="485511" msthash="1347" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">DRM_Hexstringtobyte+1c</td><td msttexthash="25604150" msthash="1348" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">空指针取消引用</td></tr></tbody></table>## 受影响的设备¶  
  
我们验证了这些漏洞是否影响了以下设备：  
- 麒麟990：P40 专业版 （ELS）  
  
请注意，其他型号可能已受到影响。  
## 补丁¶  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4389879" msthash="1354" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">名字</th><th msttexthash="4044495" msthash="1355" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">严厉</th><th msttexthash="7713706" msthash="1356" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">CVE漏洞</th><th msttexthash="5254223" msthash="1357" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">补丁</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">GetOCSPResponse</code></td><td msttexthash="4503603" msthash="1359" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">危急</td><td msttexthash="15881749" msthash="1360" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-46813 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 6 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度和偏移检查</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">NOVEL_CHDRM_Copyordecrypt</code></td><td msttexthash="4503603" msthash="1363" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">危急</td><td msttexthash="15881749" msthash="1364" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-46813 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 6 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">NOVEL_CHDRM_SetDRMCertData</code></td><td msttexthash="4503603" msthash="1367" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">危急</td><td msttexthash="15881749" msthash="1368" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-46813 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 6 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">DRM_Secure_Store_Read</code></td><td msttexthash="3607240" msthash="1371" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15879526" msthash="1372" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-40062 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 3 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">getvaluewithtypeandindex</code></td><td msttexthash="3607240" msthash="1375" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15880319" msthash="1376" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-40056 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 3 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入和</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">Secure_Store_EncryptWrite</code><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">Secure_Store_PlainWrite</code></td><td msttexthash="3607240" msthash="1379" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15880579" msthash="1380" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-40057 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 3 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">NOVEL_CHDRM_SetRegisterResData</code></td><td msttexthash="3607240" msthash="1383" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15880839" msthash="1384" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-40058 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 3 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">呼叫时长度检查缺失/错误</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">NOVEL_CHDRMw_MemCompare</code></td><td msttexthash="3607240" msthash="1387" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15879006" msthash="1388" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-40060 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 3 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">整数下溢输入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">find_tlv_data</code></td><td msttexthash="3607240" msthash="1391" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">高</td><td msttexthash="15881749" msthash="1392" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2021-46813 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 6 月</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">OOB 访问</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">getvaluewithtypeandindex</code></td><td msttexthash="7391163" msthash="1395" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">地中海</td><td msttexthash="15880267" msthash="1396" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">CVE-2022-39003 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">2022 年 9 月</td></tr><tr style="box-sizing: inherit;"><td msttexthash="36276318" msthash="1398" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">未经检查的 Malloc 返回值</td><td msttexthash="1847482" msthash="1399" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">低</td><td msttexthash="9161399" msthash="1400" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">不适用</td><td msttexthash="4465006" msthash="1401" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">固定</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">缺少长度签入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">pack_tlv_data</code></td><td msttexthash="1847482" msthash="1403" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">低</td><td msttexthash="9161399" msthash="1404" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">不适用</td><td msttexthash="4465006" msthash="1405" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">固定</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">调用后缺少长度检查</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">unpack_tlv_data</code></td><td msttexthash="1847482" msthash="1407" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">低</td><td msttexthash="9161399" msthash="1408" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">不适用</td><td msttexthash="4465006" msthash="1409" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">固定</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;"><span style="box-sizing: inherit;">堆栈/堆/BSS 指针泄漏</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">DRM_AES_Encrypt_xxx</code></td><td msttexthash="1847482" msthash="1411" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">低</td><td msttexthash="9161399" msthash="1412" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">不适用</td><td msttexthash="4465006" msthash="1413" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-color: rgb(219, 219, 219);border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;">固定</td></tr><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);"><span style="box-sizing: inherit;">整数下溢输入</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">unpack_tlv_data</code></td><td msttexthash="1847482" msthash="1415" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">低</td><td msttexthash="9161399" msthash="1416" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">不适用</td><td msttexthash="4465006" msthash="1417" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">固定</td></tr></tbody></table>## 时间线¶  
- 2021年12月02日 - 向华为PSIRT发送漏洞报告。  
  
- 2022年1月17日 - 华为PSIRT确认该漏洞报告。  
  
- 2022年9月1日 - 华为PSIRT表示，这些问题已在2022年3月、2022年6月和2022年9月的更新中修复。  
  
- 从 2022 年 11 月   
30 日至 2023 年   
7 月 19 日 - 我们定期交换有关公告发布的信息。  
  
- 二进制漏洞(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEH4eXCW471pNuHpGPzUKCkbyticiayoQ5gxMtoR1AX0QS7JJ2v1Miaibv1lA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 其它课程  
  
- windows网络安全防火墙与虚拟网卡（更新完成）  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERE5qcRgQueCyt3U01ySnOUp2wOmiaFhcXibibk6kjPoUhTeftn9aOHJjO6mZIIHRCBqIZ1ok5UjibLMRA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windows文件过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmvkF91T2mwk3lSlbG5CELC5qbib3qMOgHvow2cvl6ibicVH4KguzibAQOQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- USB过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHv0vyWxLx9Sb68ssCJQwXngPmKDw2HNnvkrcle2picUraHyrTG2sSK7A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 游戏安全(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHzEAlXtdkXShqbkibJUKumsvo65lnP6lXVR7nr5hq4PmDZdTIoky8mCg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- ios逆向  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmjrTM3epTpceRpaWpibzMicNtpMIacEWvJMLpKKkwmA97XsDia4StFr1Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windbg  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibv9VNhRI73qFehic91I5dsr3Jkh6EkHIRTPGibESZicD7IeA5ocHjexHhw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 恶意软件开发（更新中）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERGxhrr6IpiaZuqkGWyEJWPwXqHEYPEVp3gpDB73Pg81J9TdUQic0wn4NJQdbTCIDsgC2gqT4tkEkjsg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- 还有很多免费教程(限学员)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHDvveGEwLYBVsps1sH6rGrSnNZtjD2pzCk4EwhH3yeVNibMMSxsW5jkg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibR2Viaxgog8I2gicVHoXJODoqtq7tTVGybA8W0rTYaAkLcp8e2ByCd1QQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibDwwqQLTNPnzDQxtQUF6JjxyxDoNGsr6XoNLicwxOeYfFia0whaxu6VXA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
-   
-   
- 更多详细内容添加作者微信  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPMJPjIWnCTP3EjrhOXhJsryIkR34mCwqetPF7aRmbhnxBbiaicS0rwu6w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-    
  
-    
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPZeRlpCaIfwnM0IM4vnVugkAyDFJlhe1Rkalbz0a282U9iaVU12iaEiahw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
