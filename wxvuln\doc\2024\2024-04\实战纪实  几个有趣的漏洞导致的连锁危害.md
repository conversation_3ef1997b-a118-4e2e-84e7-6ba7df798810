#  实战纪实 | 几个有趣的漏洞导致的连锁危害   
原创 zbs  掌控安全EDU   2024-04-13 12:00  
  
扫码领资料  
  
获网安教程  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcrpvQG1VKMy1AQ1oVvUSeZYhLRYCeiaa3KSFkibg5xRjLlkwfIe7loMVfGuINInDQTVa4BibicW0iaTsKw/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/b96CibCt70iaaJcib7FH02wTKvoHALAMw4fchVnBLMw4kTQ7B9oUy0RGfiacu34QEZgDpfia0sVmWrHcDZCV1Na5wDQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
  
本文由掌控安全学院 -zbs投稿  
#### 前引：总的来说就是信息泄露+越权+爆破登录+xss这四个漏洞导致的连锁反应，因为他有默认密码123456，所以最终可以重置全校学生的密码，并都植入xss，危害十足。  
  
信息收集：和嘉名童鞋一起测试的，都是他收集的QAQ 主要通过百度贴吧搜集到了默认密码和学号信息，实在是牛皮  
## 漏洞复现：漏洞复现：  
#### 漏洞一：某接口没有对权限进行限制导致信息泄露  
  
登录后访问/stu/m/member/list  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fj47Bo1fnbiaC6C9kdicYwCnbY0FDzyRldiaMxuvxj1E8AKdHr8suNWHCA/640?wx_fmt=png&from=appmsg "")  
  
抓取数据包：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7f3foZtYad8PETWD9zUxzA5q9Tpm3Qk0J0V41GGVXHmqj2UtiaGF3ib2SQ/640?wx_fmt=png&from=appmsg "")  
  
可以看到其他同班同学的个人资料，有学号、电话、邮箱，并且因为默认密码为123456，所以获取其他人的学号后可以登录很多的账号；重要的是，该接口泄露了userid！这在接下来的漏洞二中有着很严重的危害：  
  
第一个接口泄露了同班同学的userid，第二个接口可以通过userid重置密码。第三个漏洞没防爆破，有默认密码可以爆破学号登录很多账号，导致可以重置学校大部分用户的密码  
#### 漏洞二：任意用户密码重置123456  
  
危害：只需要登录任一账号获取学生权限，便可以将任意用户的密码重置为默认密码123456：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fkhR2L8ciceWic7njAUrUnVb3FS5ibZHTGtnwnY5DsulmKFWy05nhRchmA/640?wx_fmt=png&from=appmsg "")  
  
POC：  
  
POST /stu/m/member/resetPassword HTTP/1.1Host: xxxxCookie: 【登录任意账号的cookie即可】User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/116.0Accept: application/json, text/javascript, /; q=0.01Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2Accept-Encoding: gzip, deflateContent-Type: application/x-www-form-urlencoded; charset=UTF-8X-Requested-With: XMLHttpRequestContent-Length: 14Origin: xxxxxReferer: xxxxxSec-Fetch-Dest: emptySec-Fetch-Mode: corsSec-Fetch-Site: same-originTe: trailersConnection: close  
  
userId=4841276  
  
漏洞复现：先访问/stu/m/member/resetPassword后抓包  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7f0aGBK0ibiamaGnc3AMYqUaRMXbsUE92KVwKqicrdoyib5ongKBTRVJtdRA/640?wx_fmt=png&from=appmsg "")  
  
改为post包，加一个参数userId=4841276【userId在漏洞一未授权访问里可以看到，我重置了我测试的账号】  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fHBt0ibrMk0DoL3spymRBibcJZh1GRhZHq5HLicYiazVJFFPsvL7WeXJ6BA/640?wx_fmt=png&from=appmsg "")  
  
重置成功，恢复为默认密码123456  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fR82sy9jGRqKKStibnE0DZ8DpKPViavV4B66fCzBvWq8cLbgibTFv5KSyA/640?wx_fmt=png&from=appmsg "")  
  
userid在漏洞一的未授权访问里获取：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fk8ZRv5Ye7DdQXe5iaxUn58ezB91TxaXz0fcVUI5IeI9R4NKhvaZ2mkg/640?wx_fmt=png&from=appmsg "")  
  
比如我们想登录这位07116224的同学账号，未授权访问获取userId=4841566  
  
登录随意一个账号后，构造如下数据包发送  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fBm2dW8Z2P1UicRbl618BQyw42wibqAqIlic38zdEUUFIE3K6J1AAicnFrQ/640?wx_fmt=png&from=appmsg "")  
  
登陆账号为xxxx+学号，默认密码123456  
  
=![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fRrT17gKFL0ibxsOTFJn6NWDuG8lbQTIJnIBjBibVgBUmP9FA3ayP3S7Q/640?wx_fmt=png&from=appmsg "")  
  
  
登陆成功  
#### 漏洞三：登录处无验证码，因为默认密码为123456，所以可爆破用户名漏洞  
  
利用登陆账号为xxxx+学号，默认密码123456，爆破学号  
  
抓取登录数据包：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fvBAiatqZPvibbueDE8djQiaicwKG8LZ3803OgEP5fO0uVXazlhhxEpwchA/640?wx_fmt=png&from=appmsg "")  
  
我这里就爆破后四位数字：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fKyxG4PDW5aWDuuyaQcbbsRRgxHMXF9cWCSFwich3OicyF9oHtJPafljg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fDLXDPdr5I6shYQU1N0dW92eUYpjoF3jbcjTAjrPNDanUictdXAmgYaw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fnsDqEWoJ9Vuic1HibMoJQOwibMMcjE10roTowCnibRekGOPsCFcribKrBkg/640?wx_fmt=png&from=appmsg "")  
随便测四位数都登陆成功了几百个账号，如果从八位数学号都爆破，估计涉及 成千上万账号；  
  
而且结合漏洞一和漏洞二，可以重置全校的账号为默认密码123456，你说危害大不大  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fEGHUz5tmjnMFVxmPSjUDDJVYxNbgdATxJUicgAxKDd9iafJRut45VEibw/640?wx_fmt=png&from=appmsg "")  
  
登陆账号涉及支付功能  
#### 漏洞四：存储型xss  
  
个人资料上传照片，抓取数据包：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fQ9ZZON5WicrHjyeqqs5TVeu3sZ2s2TApkYlS6wkP9Qc0Sia6LqMQx73A/640?wx_fmt=png&from=appmsg "")  
  
图片路径更新这条数据包—可以更换图片路径参数  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpHvbRduUABc5hmx6ZGwl7fumqe3JT3OibDqX0HcXKqS1micP0klvxKwBBCprdOcTuaChCSNGqWK9zw/640?wx_fmt=png&from=appmsg "")  
  
漏洞1-3可登录学校百分之八九十的账户，漏洞4可导致同学账户被上存储型xss…  
```
```  
  
  
