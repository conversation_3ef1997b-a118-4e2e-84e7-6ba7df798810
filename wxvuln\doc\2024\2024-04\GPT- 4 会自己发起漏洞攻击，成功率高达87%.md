#  GPT- 4 会自己发起漏洞攻击，成功率高达87%   
 天融信教育   2024-04-28 17:32  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/58icFqQRdJX8s4f75JWLBL12w6BQRyt4ApGEjE3tIIpp0Os9hw8ahvgibx2GevzmlwB3XO94ggYY74IoVG6icBYpw/640?wx_fmt=gif "")  
  
近日，伊利诺伊大学香槟分校的研究团队揭示了一项关于人工智能模型进行黑客攻击的新研究：只需  
要阅读CVE漏洞描述，GPT- 4就可以瞬间化身黑客，成功实施漏洞攻击，综合成功率达到了惊人的87%。  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/58icFqQRdJXicezjyqd8vu6tDKVEYgZweEVic0rhia7BnSK70OlU1HQN6re4q419FNnp7cG9KkyYgeNGKKjGtpAIbQ/640?wx_fmt=jpeg&from=appmsg "")  
  
  
在此次研究中，该团队共对包括GPT- 4、GPT- 3.5在内的10个AI大模型进行实验，结果发现只有GPT- 4可以通过CVE漏洞信息成功发起攻击，且单次成本仅8.8美元 （折合人民币约63元），其他模型的成功率均为零。  
  
有“热心肠”的网友立马就对该研究进行了复现，并在社交平台上表示成功了。GPT- 4的安全性引发网络安全圈的热议。虽然测试的15个漏洞中有三分之一是比较古老且容易利用的“XSS漏洞”，但是其超低的使用门槛和超高漏洞利用率依旧引起了许多人的担忧。如果实验结果被用于实际，那么庞大的“脚本小子”立马就可以进化成“AI大模型小子”，这对政府组织和企业来说，将会是一次极其严峻的考验。  
## AI大模型漏洞利用过程  
  
AI大模型：GPT- 4；  
  
漏洞：15个单日漏洞（One-day vulnerabilities），包括网站、容器管理软件和Python包漏洞；  
  
架构：黑客智能体，如下图所示：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/58icFqQRdJXicezjyqd8vu6tDKVEYgZweEjaD68HfVPK52iaQIRQMLVgHdOdxGstJafKv36t0oY4jJGjuRCj89ohQ/640?wx_fmt=png&from=appmsg "")  
  
**攻击过程：**  
  
1. 发出指令：例如“使用ACIDRain（一种恶意软件）攻击这个网站”；  
  
1. GPT- 4接收请求并使用工具和CVE漏洞数据库信息发起攻击；  
  
1. 根据系统反馈和历史记录调整攻击措施；  
  
1. 实现“double-spend attack”。  
  
结果：GPT- 4成功利用13个CVE漏洞，两个漏洞未成功，分别是Iris XSS和Hertzbeat RCE，综合成功率约为87%；  
  
所使用的工具：网页浏览、访问终端、网页搜索结果、创建和编辑文件、代码解释器等；  
  
所使用提示词：共计1056个token；  
  
代码量：91行代码，包括调试和日志记录语句；  
  
无 CVE 信息后进行实验：GPT- 4成功率约为7%，表明对于AI大模型，发现漏洞比利用漏洞更加困难。  
  
进一步分析发现，GPT- 4能够在33.3%的情况下正确识别出存在的漏洞，但是即使识别出漏洞，它只能利用其中的一个。如果只考虑GPT- 4截止日期之后的漏洞，它能够找到55.6%的漏洞。  
  
研究人员还发现有无CVE描述，智能体采取的行动步数相差并不大，分别为24.3步和21.3步。他们推测这可能与模型的上下文窗口长度有关，并认为规划机制和子智能体可能会提高整体性能。  
  
关于成本，研究计算得出GPT- 4每次利用漏洞的平均成本为3.52美元，主要源于输入token的费用。考虑到整个数据集中40%的成功率，每次成功攻击的平均成本约为8.8美元。  
  
  
参考链接：https://www.freebuf.com/news/398651.html  
  
**相关阅读**  
  
[1.2024数据安全大赛正式启动，开始报名啦！](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247517787&idx=1&sn=1cbaf1fc9c2fa9a336d6218677620979&chksm=fb1d0c00cc6a8516115354aa82bfb1556a0151f8c3ed05a9ff14208d2a59f845670efba9e543&scene=21#wechat_redirect)  
  
  
[2.《加快数字人才培育支撑数字经济发展行动方案（2024－2026年）》](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247517700&idx=1&sn=c93ca3b91ea2de229147bec988a53c96&chksm=fb1d0c5fcc6a8549e0da14235056755406472b3d7efc5ea3f56f7740d4ed1b516ba9b5981808&scene=21#wechat_redirect)  
  
  
[3.看过来|2024年度CISP证书维持方案！](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247516731&idx=1&sn=994b0850607fad7ba870e14cce537020&chksm=fb1d0860cc6a8176606da85c4b0d82d296abbf8168210a0014008eba213a484444f3fc0171cc&scene=21#wechat_redirect)  
  
  
[4.](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247515891&idx=1&sn=f79780ce0a5600cbd679c5f051b8aedb&chksm=fb1d34a8cc6abdbe15ab0d79148bbee6a12bb472b98ac50d307d65b025f18276172989976c46&scene=21#wechat_redirect)  
[北京新增网络空间安全职称评审专业](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247517214&idx=1&sn=ef446465df8c82043d403dc10bbe9b5a&chksm=fb1d0e45cc6a87531d9ff6c04cd6432dbb155f434989214c6b8a5a4b4fdb6153b57ae0f48d69&scene=21#wechat_redirect)  
  
[](http://mp.weixin.qq.com/s?__biz=MzU0MjEwNTM5Ng==&mid=2247515891&idx=1&sn=f79780ce0a5600cbd679c5f051b8aedb&chksm=fb1d34a8cc6abdbe15ab0d79148bbee6a12bb472b98ac50d307d65b025f18276172989976c46&scene=21#wechat_redirect)  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/58icFqQRdJX9X6tn3YhsYqia3AXy9cRicotMUGtsdibKiaXZ5Fq9wSibtTaVtt3iclm6iaSId0ymhZkS9hzTaXkia6TV1ag/640?wx_fmt=gif&from=appmsg&wxfrom=5&wx_lazy=1&tp=wxpic "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/jpT0KhEzjYW2n4ebCB9MqOTfGrT2hlI9uicyTFia0szM5y82bia8Y1moAsuiatb5YhZj9PlkdeQhg2BKIILzo9vYPw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
**官网：http://www.topsec-edu.cn**  
  
**热线：400-017-0077**  
  
**邮箱：<EMAIL>**  
  
**客服：topsec-sky**  
  
