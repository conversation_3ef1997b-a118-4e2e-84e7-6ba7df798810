#  原创 | 首个国内自主挖掘的RISC-V处理器设计中危漏洞研究   
原创 CISRC  网络安全应急技术国家工程中心   2024-04-24 14:51  
  
CNCERT国家工程研究中心  
  
作者 | 胡伟 西北工业大学教授  
  
张家琦 网络安全应急技术国家工程研究中心  
  
**摘要：**  
处理器安全是近年来备受关注的热点研究方向。2021年，国内首个处理器硬件安全领域的国家重点研发计划项目——纳米级芯片硬件综合安全评估关键技术研究成功立项，国家计算机网络与应急技术处理协调中心（以下简称“CNCERT”）与清华大学、西北工业大学、中科院微电子所等单位合作开展处理器硬件漏洞方面的研究和挖掘工作。2024年4月，项目联合承研单位西北工业大学研究团队首次在RISC-V SonicBOOM处理器上挖掘出寄存器端口争用漏洞。该漏洞是国内漏洞数据库首次收录的RISC-V处理器设计上可远程利用的中危漏洞。本报告就该漏洞的机理及影响进行分析。此次漏洞挖掘和验证工作由西北工业大学胡伟教授团队完成，该团队长期从事硬件设计安全验证、安全漏洞与恶意逻辑检测、密码应用安全、硬件安全设计自动化工具等方面的研究。  
# 一、背景  
  
处理器硬件漏洞是指由处理器逻辑设计引入的，会对处理器功能正确性或安全性造成影响的设计缺陷。早期学术界研究的硬件漏洞以密码侧信道和恶意硬件为主。这两类漏洞通常难以直接通过软件远程利用。2018年，Google团队首次公布了两组由处理器微架构瞬态执行机制导致的硬件漏洞，即人们熟知的“幽灵”（Spectre，CVE-2017-5753/CVE-2017-5715）和“熔断”（Meltdown，CVE-2017-5754）。这两个漏洞是CVE收录的首批处理器微架构硬件漏洞。  
  
目前，国内外披露的RISC-V处理器相关漏洞比较少。CVE中与BOOM处理器相关漏洞包括CVE-2020-29561（负载转换异常）,CVE-2022-26296（瞬态执行攻击）,CVE-2022-34636（地址转换PMA违规的异常类型错误），CVE-2022-34641（地址转换PMP违规的异常类型错误）。此外，CVE也披露了一些其他开源处理器漏洞如OpenRISC处理器的控制器对EEAR（异常有效地址寄存器）写入逻辑错误实现、ALU单元减法指令进位标志更新漏洞等。  
  
表1、CVE目前已收录的RISC-V处理器相关的漏洞情况  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GoUrACT176mjtIjjhL0BdMliaEj51R9picQksARNd88K5uQBsuVrGNWJbmfLKgu0We3qC2uA6QXKGrEXb4vUrGSQ/640?wx_fmt=png&from=appmsg "")  
# 二、漏洞技术分析  
  
**1．漏洞产品分析**  
  
**本漏洞在RISC-V SonicBOOM开源代码中发现。**  
SonicBOOM是加州大学伯克利分校开发的第三代 RISC-V 超标量乱序处理器，旨在为高性能处理器研究及应用提供先进的开源平台。代码链接如下：https://github.com/riscv-BOOM/riscv-BOOM  
  
SonicBOOM处理器架构如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GoUrACT176mjtIjjhL0BdMliaEj51R9picbic0LaniaaHn8AdlG4mHutiadzCjUajVDxnA7mXFQhicT167CeAeUyk06g/640?wx_fmt=png&from=appmsg "")  
  
图1  SonicBOOM的处理器架构设计（见"RISC-V BOOM: Documentation and Source", Berkeley Architecture Research Group, 2024年。）  
  
**2．漏洞成因分析**  
  
在SonicBOOM处理器中，ALU（含加法）、乘法和除法单元使用相同的写端口来访问寄存器文件。在写端口争用的情况下，存在固定优先级仲裁，其中ALU具有最高优先级，而除法单元具有最低优先级。这意味着由于端口争用，除法指令可能被较年轻的乘法或加法指令延迟，导致除法指令执行时间的差异，从而造成时间侧信道。  
  
**3．漏洞利用途径**  
  
该漏洞利用原理是在瞬态执行之前进行除法运算，然后，在瞬态时间窗口中，尝试使用与机密信息相关的分支，如果探测值正好等于机密信息，则执行乘法和加法运算。由于除法运算本身优先级较低，与此时较年轻的乘法和加法运算构成端口争用，此时整个代码执行时间由于除法指令执行变长而随之变长。攻击者可以通过测量执行代码所需的时间来探测机密信息。  
  
该漏洞可作为一种新型的侧信道攻击方式，结合处理器中瞬态攻击对微架构状态的影响，成为新型的瞬态攻击方式。  
  
**4．漏洞利用效果**  
  
该漏洞主要影响机密性。利用该漏洞，攻击者可以绕过处理器的边界检查，内存访问权限检查等安全措施，通过端口争用导致的时间差异直接推测机密信息，将瞬态执行中访问的数据通过侧信道方式泄露出来。  
  
**5．与现有微架构漏洞对比分析**  
  
当前的幽灵类、熔断类攻击，无论是Spectre-v1（利用处理器模式历史表），Spectre-v2（利用处理器分支目标缓冲器），Spectre-v3（利用处理器返回堆栈缓冲），Spectre-v4（利用存储加载），还是熔断类攻击，在触发瞬态执行后的信息恢复阶段大多采用Cache侧信道攻击泄露信息。此次发现的端口争用漏洞可以替代Cache侧信道攻击，作为一种新型侧信道攻击方式。端口争用漏洞可以结合不同的瞬态执行机制构成新型处理器安全漏洞。  
# 三、漏洞影响分析  
  
近年来，RISC-V 指令集架构迅速发展，其开源、简洁、模块化、易于扩展的特性引来广泛关注。RISC-V 国际公司的首席执行官Calista Redmond 在2022 年7 月的嵌入式世界展览会上表示RISC-V 出货量突破100 亿。特别的，RISC-V被视为我国芯片自主可控的一次弯道超车的机会，因此国内出现大量RISC-V架构的处理器内核和芯片产品。  
  
RISC-V主要有两类应用场景，一是独立的RISC-V处理器芯片，二是作为处理器核集成至大型片上系统，执行各类计算和控制任务。截至2022年末，我国大约有50款不同型号的国产RISC-V芯片量产，集中在工业控制、电源管理、无线连接、存储控制、物联网等嵌入式场景。随着近几年生态不断演进，RISC-V也有从嵌入式场景拓展到工业控制、自动驾驶、人工智能、通信、数据中心等对算力要求更高场景的趋势。  
  
SonicBOOM作为开源平台，在上述两类场景中都有应用，是许多国内商用RISC-V处理器开发的基础。虽然在开发过程中，企业会对原始代码进行修改优化，然而其指令架构仍有相似之处，因此在SonicBOOM上发现的安全风险仍值得国内处理器厂商和处理器应用方关注。  
  
  
  
转载  
请注明来源：CNCERT国家工程研究中心  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=other&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
