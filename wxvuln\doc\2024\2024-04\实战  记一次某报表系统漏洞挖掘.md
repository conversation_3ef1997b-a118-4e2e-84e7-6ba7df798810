#  实战 | 记一次某报表系统漏洞挖掘   
原创 Xxia0hei04  小黑说安全   2024-04-08 00:03  
  
## 0x01 前言  
##   
  
这是近期朋友发过来的一个站，  
快  
速帮忙  
过了一遍，内容比较简单。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5O2tWD17r0icz6D2gBxArxEO1hics21yJcNnY9ZR4KCiaN8Jr3mjpn8R0Tg/640?wx_fmt=png&from=appmsg "")  
  
## 0x02 分析与利用       
  
访问网站提示以下信息。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5O8vQmE8QbQ8LBrLXlr59ibZxR7S0NYgzQxYVBLkykbcJmmJaM8bTIAww/640?wx_fmt=png&from=appmsg "")  
  
搜索关键字获取到代码。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OonHk3VtTxzaYZTmqOcmONp1ic2u1gVO8Wqn6vV6sVoY4P4N4SaZjhcA/640?wx_fmt=png&from=appmsg "")  
  
拿到项目简单过了一遍，跑了一遍。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OP8QLK2BOicOL4wkIHTO1jicq8icShRCsHib5YPL1GqO7Fq3QBZ81qhsKyw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5O4jnRSPLHhfjZrUu2E7RTJmXACVFf3yeVLqnQZXtq1QpxwibaoIFZ7Gw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5O3YudvArlxpp6cdDYicRqYJ9ITBOibdlma4Anycbt8sI9MOa78uibElvTw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5O6V6fSZdxicCXcrHDQgx83WLU4icKs0aCKhFf97R12g4RcQzZuOvibcvmg/640?wx_fmt=png&from=appmsg "")  
  
其实这里多个接口都能玩，这里就先看一个InputServlet吧，运行脚本发现在raqsoftReport.jar。  
```
import os
import zipfile
import sys

# 确保有足够的命令行参数传入
if len(sys.argv) < 3:
    print("Usage: python tqu.py <directory_to_search> <text_to_search>")
    sys.exit(1)

# 第一个命令行参数是要搜索的目录
directory_to_search = sys.argv[1]
# 第二个命令行参数是要搜索的文本
text_to_search = sys.argv[2]

# 遍历目录
for foldername, subfolders, filenames in os.walk(directory_to_search):
    for filename in filenames:
        if filename.endswith('.jar'):
            # 构造完整的文件路径
            full_path = os.path.join(foldername, filename)
            try:
                # 打开JAR文件
                with zipfile.ZipFile(full_path, 'r') as jarfile:
                    # 遍历JAR文件内的每个文件
                    for name in jarfile.namelist():
                        # 打开JAR文件内的文件
                        with jarfile.open(name) as file:
                            if text_to_search.encode() in file.read():
                                print(f"Found '{text_to_search}' in {full_path}")
                                break  # 找到文本后跳出循环
            except zipfile.BadZipFile:
                print(f"Bad zip file: {full_path}")
```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OicteLiaoONYXoctkAc7dmB0yCCBwkHrSibgBul8Q2hCz7yeIPiajotKtDg/640?wx_fmt=png&from=appmsg "")  
  
  
代码很简单，根据action参数的值来判断走哪块逻辑。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OWoQniavbDkOsthcN8TAZ0yX9HvEhRbnYPf0Mu99HPYicNuiaPBlxsJtWw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OndKgvBySseiaMlRDgnRzTRGiahqS3j3yv7sibVJD6BmcZcrlSPxLdzGww/640?wx_fmt=png&from=appmsg "")  
  
这里的上传路径实际只有upFileName可控，经过下方代码拼接之后，  
大概是类似"  
var/cache/tmp_12345_example.txt  
"的样子。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OWzh1aPCa0JbSrkZy8SN1qxoxOINpBBjE3NVh7fwbWVEOcmDhib9X7XA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5Oia53CYGFmERuSa4Msl1c78IuAXRra7BZ49icKpIqolF1epj7WWhg4XmQ/640?wx_fmt=png&from=appmsg "")  
  
这里习惯性的测试../，发现可能是被过滤掉了。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OY0BiaNtXiagiaM6MOhyibox8en6WTM7njQFCdutUQX5181J4e2ficxYBSbA/640?wx_fmt=png&from=appmsg "")  
  
然后fuzz了一下，发现当输入"/\"的时候居然将路径暴了出来，同时"/\"居然被解释成了"\\"。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5On4b2Z2qDIGZphM9AuicFyoI47fTfPibafKVKRMKxkEldOfJc14fQ5DcA/640?wx_fmt=png&from=appmsg "")  
  
于是尝试使用"..\\"跳三次，居然成功了。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OlKNIGcl794PK3ZobRXQ8kJnZdM0vjlCOM5iamHdEDtTD9Rskt6o56ww/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OeCb6qQLuwR2X8GrrOkAx3JNPkp6qxBVcX9OA44LMTAAH7Uc1lISOhA/640?wx_fmt=png&from=appmsg "")  
  
最后帮到底吧，上了个马发过去了。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OADHE2pTkx4EwcE2vHIXTKg48uiankRFOZbnlMsmrv2UibEhc9QbEL3Gg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5Ou3pdPb6EnpcEK48GAoqIGZnuCjTsvwlVD3874IX4V0O2ibbgV6NZ9Rg/640?wx_fmt=png&from=appmsg "")  
## 0x03 小密圈‍‍‍‍‍‍‍‍  
## 最后送你一张优惠券，欢迎加入小密圈，好朋友。  
##   
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/9zZrDr2DM8PvBSs6vhNytd5lYKAYUx5OATvUOl41ol2OSCv3xvE7iaKsfOCgQIZhRO0uZOEBmqfXyc9dNvEnH6g/640?wx_fmt=jpeg&from=appmsg "")  
  
