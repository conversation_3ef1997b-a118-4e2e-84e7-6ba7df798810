#  欧盟就《网络弹性法案》达成一致，数字产品该如何落实漏洞报告机制？   
 网络安全应急技术国家工程中心   2023-12-06 15:11  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176nEnT38tYeVgnliav3LQRSA7Aoam00hy1dHe3m94HCsrFwHJuiczVUG6RIpKVTEnKlZCqO9Yb6hkCxA/640?wx_fmt=jpeg&from=appmsg "")  
  
欧盟立法者就《网络弹性法案》在技术和政治层面均达成一致，下一步欧洲议会和欧盟理事会通过后将成为法律。  
  
12月5日消息，欧盟政策制定者于11月30日达成了关于《网络弹性法案》的政治协议，弥合了在最后几个悬而未决问题上的分歧。  
  
《网络弹性法案》是一项立法提案，为从智能玩具到工业机械等各类联网设备引入安全要求。欧盟委员会、欧洲议会和欧盟理事会通过“三方对话”会议最终敲定这一法案。下一步需要欧洲议会和欧盟理事会正式通过，才能成为法律。  
  
该协议此前在技术层面上已经基本敲定，提案的许多方面在政治会议期间得到认可。欧盟谈判代表经过激烈讨论之后解决了最后的政治障碍。  
  
牵头此事的欧洲议会议员Nicola Danti表示，“《网络弹性法案》将加强联网产品的网络安全，解决硬件和软件中的漏洞问题，让欧洲大陆更安全、更有弹性。欧洲议会已经立法保护供应链，并将保护路由器、杀毒软件等关键产品列为网络安全优先事项。”  
# 漏洞处理机制  
  
法案规定，如果制造商知道联网设备存在可能被黑客利用的重大漏洞，不得将此类产品投放市场。一旦发现这些潜在入口，在产品公开的支持期限内，他们必须处理这些问题。  
  
一旦发现安全事件或被积极利用漏洞，制造商必须向有关当局报告，并告知他们采取了哪些行动减轻安全风险。  
  
被积极利用漏洞是一类极其敏感的网络威胁情报，表明黑客入口仍然没有被修补。因此，谁应该负责处理这些敏感信息成为谈判的焦点。  
  
欧盟部长理事会将这项任务从欧盟网络安全局（ENISA）移交给各国计算机安全事件响应团队（CSIRTs）。根据修订的《网络和信息系统指令》（NIS2），这些团队本就担负类似的任务。  
  
然而，欧洲议会坚持让ENISA参与，避免国家机构在保留这些高度敏感信息方面拥有过多自主权。  
  
后来，双方达成妥协，决定要求制造商通过单一报告平台同时向有关CSIRT和ENISA发送通知。但是，欧盟成员国认为，考虑网络安全事宜，他们应该有权利对发送给ENISA的信息加以限制。  
  
各方对这些限制的条件进行激烈讨论，达成的条件十分“狭窄”。具体而言，如涉及产品主要存在于国内市场且不对其他欧盟国家构成风险，所在国CSIRT有权限制向ENISA发送通知。  
  
其次，成员国当局不会被强制要求，向ENISA披露他们认为与保护基本安全利益相关的任何信息。这一限制性条款符合欧盟条约。  
  
第三，如制造商自认为信息进一步传播会立即带来风险，并在提交给CSIRT的通知中加以说明，所在国也有权对发送给ENISA的信息加以限制。  
  
另一方面，欧洲议会议员争取到如下权利：ENISA仍将获得部分信息，用以监测欧盟单一市场的任何系统性风险。ENISA将了解相关制造商和产品信息，以及有关漏洞利用的一般信息。  
  
欧洲议会议员们还推动在法案中明确，ENISA应获得足够的资源应对新任务。虽然这未能成为法案的一部分，但它将纳入欧盟主要机构发布的联合声明。  
# 开源软件  
  
谈判的另一大焦点是如何处理集成到商业产品中的开源软件。就此，各方已在技术层面达成一项协议，并在政治层面得到认可。  
  
法案仅涵盖在商业活动背景下开发的软件，并专门针对开源软件管理者在文档编制和漏洞处理方面制定规则。  
  
根据外媒Euractiv看到的最终文本，法案排除了那些在市场上销售开源软件但将所有收入重新投资于非营利活动的非营利组织。  
# 其他热点话题  
  
法案还包括其他热点话题的条目，如国家安全豁免、次级立法、罚款收入分配等。  
  
成员国专门为国家安全或国防目的开发或修改的任何产品不受法案限制。  
  
欧盟立法阶段反复讨论次级立法类型。如果是委托法规（delegated acts），需要欧洲议会参与，而执行法规（implementing acts）无需欧洲议会参与。支持期限将在委托法规下详细定义，而特殊产品类别将在执行法规下定义。  
  
欧洲议会推动加入措辞，要求《网络弹性法案》的罚没款项用于增强网络安全能力的活动。这一点没有写入法案文本，但将在法案总则中提及。  
  
**参考资料：**  
  
euractiv.com  
  
  
  
原文来源：安全内参  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
