#  【漏洞预警】Apache Solr Operator身份验证凭据泄漏漏洞 CVE-2024-31391   
cexlife  飓风网络安全   2024-04-15 20:11  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg9JY3N0e9gw0oAzYNyUfTXW3RJvwMoK3k0Lor6prvaLP0RjASiaZvdjww/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**Solr Operator是在Kubernetes内管理 Apache Solr 生态系统的官方方式,当SolrOperator配置为启用基本身份验证（.solrOptions.security.authenticationType=basic）并要求在运行状况检查探测端点上进行身份验证（.solrOptions.security.probesRequireAuth=true）时（默认参数）,该漏洞会导致Solr Operator在探测失败时,在Kubernetes事件日志泄露Solr以及"k8s-oper"账户的用户名和密码。**影响范围:**github.com/apache/solr-operator[0.3.0, 0.8.1)**修复方案:**将org.apache.solr:solr-core升级至0.8.0及以上版本设置“.solrOptions.security.probesRequireAuth=false”禁用健康检查探针上的身份验证**参考链接:**https://lists.apache.org/thread/w7011s78lzywzwyszvy4d8zm99ybt8c7  
  
