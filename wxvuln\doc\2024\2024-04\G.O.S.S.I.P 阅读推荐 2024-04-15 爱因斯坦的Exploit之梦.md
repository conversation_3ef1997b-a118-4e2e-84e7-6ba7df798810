#  G.O.S.S.I.P 阅读推荐 2024-04-15 爱因斯坦的Exploit之梦   
原创 G.O.S.S.I.P  安全研究GoSSIP   2024-04-15 21:25  
  
先来提一个问题，你知道下面这篇论文是什么时候出版的？  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvmjx19BcxK4IGgtiagcsp7nCSGRwCmz5rhP8VXC1YIxKtjShb50gMic8A/640?wx_fmt=png&from=appmsg "")  
  
没想到吧，上面这篇论文是1987年发表在IEEE S&P（那时候还没有四大，只有这一家）上的。也许是最早研究Data-only Attack的文献（尽管这篇论文更主要的目标是讨论引入code verification的必要性——然而时间过去了快40年也没怎么成功），因为在这篇论文中作者介绍了一个奇怪的安全攻击（在那个年代，大家对安全攻击都很陌生，我们上回介绍的《杜鹃蛋》【[G.O.S.S.I.P 阅读推荐 2024-02-17 天文学博士和电脑黑客的猫鼠游戏](http://mp.weixin.qq.com/s?__biz=Mzg5ODUxMzg0Ng==&mid=2247497290&idx=1&sn=c76796d8d2f719e7377ed9387fe69940&chksm=c063d893f7145185746b1a6b361edce287bb168b3f9d3b69538a10b42745657c632fdd10878b&scene=21#wechat_redirect)  
】要再过两年才出版）。如果你对这个攻击有兴趣，可以看看另一个网页上的演示PPT:  
> https://www.cse.msu.edu/~enbody/overflow.htm  
  
  
别退出，继续读，我们并不是要考古，在今天介绍的这篇USENIX Security 2024论文 Practical Data-Only Attack Generation 中，作者延续了37年的研究历史，提出了一种实用的自动化生成data-only attack相关exploit的技术。印象里好像从Brumley教授的Automatic Exploit Generation工作和DARPA的CGC竞赛过后，这个领域就有点沉寂了（都怪AI ！！！）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvT43aXhvv5KRB3qib9uBolAhj51Vq9bHuHTohypVjicFdNLQswIMYiagoA/640?wx_fmt=png&from=appmsg "")  
  
再次考古！回到差不多20年前的2005年，在那一年的USENIX Security会议上有一篇论文叫做Non-Control-Data Attacks Are Realistic Threats，差不多是第一次系统性地介绍了data-only attack（论文里面叫做control-data attack，也就是对一些关键性的数据变量进行overwrite产生的攻击）对现实世界应用程序的威胁：  
> https://www.usenix.org/legacy/publications/library/proceedings/sec05/tech/full_papers/chen/chen_html/index.html.bak.html![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvfcRu34POqW0VibHBVzIJFjB2qeDwYR0Lo7WjojaJNopKKfGLSLxApNg/640?wx_fmt=png&from=appmsg "")  
  
  
  
本文的作者指出，尽管过去了快20年，data-only attack的安全威胁依然存在，而且现实中的软件在这方面的安全防护做得并不好，像上图中这种漏洞实例（其实就是web应用程序常见的数据注入攻击）遍地都是。只不过大部分挖掘这类漏洞的尝试都高度依赖人工，而本文的主要贡献就是提出了一个叫做“爱因斯坦”的系统，用来自动化构造data-only attack的exploit（为什么叫Einstein呢？因为作者之一也就是大名鼎鼎的Hebert Bos此前在2017年CCS上发表的另一个工作——针对control flow hijacking的分析系统叫做Newton，主要用来发现code reuse所需要的gadget）。  
  
最近这些年的各种系统安全研究，似乎都遇到很多困难，因为前人设定的那些目标太宏大（动不动就要统一量子力学和引力），所以现在的研究有一个特点是给目标做减法，比如今天的这个Einstein系统，它之所以能更好地生成data-only attack相关的exploit，也是因为作者首先做了一些简化性的假定：  
1. 放弃复杂的约束求解，更多地依赖动态数据流污点追踪分析；  
  
1. 放弃exploit的完备性（计算上的图灵完备性），转而追求“够用就好”，例如只要能执行关键系统调用（例如execve）就行；  
  
1. 不需要寻找什么额外的数据传播路径，假定程序中总是存在可被复用的“数据注入-数据执行”路径；  
  
1. 假定程序中的很多关键数据变量（例如保存关键文件名的变量）在初始化以后就不会变了，因而只需要overwrite一次即可，不需要再去反复考虑其他约束。  
  
作者还把Einstein和已有的一些同类工作进行了对比，主打一个强调Einstein的实际可用性。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvtqbjKV3NjSQjCAArbAvhKrNS6dicAyia5WLx9uqMWNEOPB77DJwdicia6g/640?wx_fmt=png&from=appmsg "")  
  
下图展示了Einstein的基本工作流程，看起来好像很容易理解：首先构建一个动态代码插桩分析环境，用DTA（Dynamic Taint Analysis）引擎来追踪程序的执行（假定测试用例是完备的，因此程序状态能够正常初始化）；然后执行第一趟污点分析（这时候假定已经知道了有一个任意内存写的漏洞，标记所有可能会被这个漏洞影响到的内存区域；接下来执行第二趟污点分析，这时候要求程序接受（可能被攻击者控制的）用户输入，然后检查用户输入是否能够执行到敏感的系统调用，如果存在这样的数据流，就把它标记为一个candidate gadget；最后就是分析这两趟污点分析对应的数据流能不能连接起来，如果能，就想办法构造相关的data-only attack！  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvibeniaNw0lbOpNBaYufkRcbhCuT1SA2oR3eO1Q59ibY26NDDSmM14sxMA/640?wx_fmt=png&from=appmsg "")  
  
在Einstein的分析假设中，有一个很关键的简化假设，即针对前面步骤中不同的两趟污点分析标记的数据流，假定其中之一的source是另一条数据流的sink（通过比较内存中的value是否相同），作者把这种情况称为identity flow，这个假定其实就极大简化了构造gadget的难度。当然作者用了很多调查结果来证明这种简化在现实中还是可行的。  
  
作者针对httpd、lighttpd、nginx、postgres、redis这些  
长期被云厂商白嫖的知名的网络应用程序进行了分析，首先发现了大量的data-only attack相关的gadget：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvACz2ic6UZDs9KOJv71z1pGMkY5vbeia66Swdf5Sdut3xd31jEmRTexGg/640?wx_fmt=png&from=appmsg "")  
  
在论文中，作者列举了Einstein工具针对nginx构造的exploit，其中有一个能够任意代码执行的exploit，其余的exploit的能力包括可以往系统中特定位置写入文件等等。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvKic8FeMkasSia0kzxiavb9RCPYznOVkyYmh0qeOrFVJUfg1pY9KibZ0kIg/640?wx_fmt=png&from=appmsg "")  
  
作者给出了针对nginx的具体的exploit细节（看不清楚的读者可以去原文里面看高清矢量图）：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvhGJSwu3YicMmkWqC1xXq5rRruU5sEyQPHgk2SV5Hk9FNaadAfQRVn9Q/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21GAzc7xfMdXOeHUUedDfmzvNMUjiaQwUjNjbTbjzCNGvpFaLry4XgVicRYAr0MhiaiaoWd4pEtJSrc7tA/640?wx_fmt=png&from=appmsg "")  
  
作者还指出，Einstein构造的这些exploit，实际上是可以使用的，而且还能绕过常见的一些防护（忽而想到WAF绕过）~~~  
  
最后，提到data only atatck，我们不得不cite一下老朋友——“非尝咸鱼贩”的主笔CC，他是一个真正的data only attack构造大师，期待他与Einstein一决高下！  
> 论文：https://download.vusec.net/papers/einstein_sec24.pdfGitHub：https://github.com/vusec/einstein  
  
  
  
