#  【已复现】Jenkins任意文件读取漏洞(CVE-2024-23897)安全风险通告   
原创 QAX CERT  奇安信 CERT   2024-01-26 15:12  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
  
<table><tbody><tr><td valign="middle" align="center" rowspan="1" colspan="4" style="background-color: #4676d9;border-color: #4676d9;"><p style="line-height: 1em;"><span style="color: #ffffff;letter-spacing: 0px;"><strong><span style="font-size: 13px;letter-spacing: 0px;">漏洞概述</span></strong><br/></span></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size: 13px;letter-spacing: 0px;"><strong><span style="font-size: 13px;letter-spacing: 0px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: #4676d9;"><p style="line-height: 1em;"><span style="color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;letter-spacing: 0px;display: inline !important;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">Jenkins任意文件读取漏洞</span></p></td></tr><tr><td valign="middle" align="left" rowspan="1" colspan="1" style="border-color: #4676d9;" width="126"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;"><strong>漏洞编号</strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: #4676d9;"><p style="line-height:1em;"><span style="color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;display: inline !important;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">QVD-2024-3674,CVE-2024-23897</span></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">公开时间</span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="185"><p style="line-height: 1em;"><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">2024-01-25</span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="153"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">影响量级</span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="93"><p style="line-height: 1em;"><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">十万级</span></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">奇安信评级</span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="185"><p style="line-height: 1em;"><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family:微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #ff0000;font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">高危</span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="153"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family:微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;">CVSS 3.1分数</strong></span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="93"><p style="line-height: 1em;"><strong><span style="text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-size: 13px;color: #ff0000;display: inline !important;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">9.8</span></strong></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size:13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">威胁类型</span></strong><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;"></span></strong></span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="185"><p style="line-height: 1em;"><span style="font-size:13px;">信息泄露,代码执行</span></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="153"><p style="line-height:1em;"><strong><span style="font-size:13px;">利用可能性</span></strong></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="93"><p style="line-height: 1em;"><span style="color: #ffae28;"><strong><span style="font-size: 13px;">中</span></strong></span></p></td></tr><tr><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="185"><p style="line-height: 1em;"><span style="color: #ff0000;font-size: 13px;"><strong><span style="color: #ff0000;font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">已公开</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="153"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="93"><p style="line-height: 1em;"><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">未发现</span></p></td></tr><tr><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="126"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="185"><p style="line-height: 1em;"><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="153"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="93"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="color: #ff0000;font-size: 13px;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">已公开</span></strong></span></p></td></tr><tr><td valign="middle" colspan="4" rowspan="1" align="left" style="border-color: #4676d9;"><p style="line-height:1em;"><strong><span style="font-size:13px;">利用条件：</span></strong><span style="color: rgba(0, 0, 0, 0.9);font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-decoration-thickness: initial;display: inline !important;font-family:system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;">经过身份验证或Jenkins配置了“Allow anonymous read access”或”Anyone can do anything”。</span></p></td></tr></tbody></table>  
  
  
**（注：奇安信CERT的漏洞深度分析报告包含此漏洞的POC及技术细节，订阅方式见文末。）**  
  
  
**0****1**  
  
**漏洞详情**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响组件**  
  
Jenkins是一个开源软件项目，是基于Java开发的一种持续集成工具，用于监控持续重复的工作，旨在提供一个开放易用的软件平台，使软件项目可以进行持续集成。Jenkins 有一个内置的命令行界面（CLI），可从脚本或 shell 环境访问 Jenkins。处理 CLI 命令时， Jenkins 使用args4j库解析 Jenkins 控制器上的命令参数和选项。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**漏洞描述**  
  
近日，奇安信CERT监测到Jenkins官方发布新版本修复多个漏洞，其中包括**Jenkins任意文件读取漏洞(CVE-2024-23897)**  
。Jenkins处理CLI命令的命令解析器中的expandAtFile功能存在任意文件读取漏洞，未经身份认证的远程攻击者利用该漏洞可以读取部分文件的有限行内容，攻击者经过身份验证或目标Jenkins更改了默认”Security”配置可以通过该漏洞读取任意文件，攻击者进一步利用该漏洞并结合其他功能可能导致任意代码执行。  
  
  
**鉴于该漏洞影响范围较大，建议客户尽快做好自查及防护。**  
  
  
  
**02**  
  
**影响范围**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响版本**  
  
Jenkins <= 2.441  
  
Jenkins LTS <= 2.426.2  
  
  
**>**  
**>**  
**>**  
**>**  
  
**其他受影响组件**  
  
无  
  
  
  
**03**  
  
**复现情况**  
  
目前，  
奇安信CERT已成功复现Jenkins任意文件读取漏洞(CVE-2024-23897)，截图如下：  
  
未经身份验证可以读取文件有限行内容：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzrZK0qSAmXFLKTtKuRzrWgZwRMmr3qtOicf4bEx7OJUTbApVH1Mg4xUWg/640?wx_fmt=png&from=appmsg "")  
  
默认情况下需要用户认证才能读取文件中全部内容：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzrV5AuzDpS7dMMwXI2IC6o4xhcgaTuPBLkJ3U8fog2XpzeP6zqkoZZTQ/640?wx_fmt=png&from=appmsg "")  
  
如开启以下配置，不需要用户认证便能读取文件中全部内容：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzr4JawKMOgXqpXbwpHudJ12lybN1A1bhiaaicIYavRoEM4kqaZnI2j6PRw/640?wx_fmt=png&from=appmsg "")  
  
  
  
**04**  
  
**受影响资产情况**  
  
奇安信鹰图资产测绘平台数据显示，Jenkins任意文件读取漏洞(CVE-2024-23897)关联的国内风险资产总数为74335个，关联IP总数为69551个。国内风险资产分布情况如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzr20AQMLmaMfpSOJNzNQvcNUvNxo0UAnaic5qvenGHbMibLwYr5e4diaQ1Q/640 "")  
  
  
Jenkins任意文件读取漏洞(CVE-2024-23897)关联的全球风险资产总数为213715个，关联IP总数为186974个。全球风险资产分布情况如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzrkUdATF4hw0dC3JILqwsXicKVwmBWMN3RepePGBqQVaPmwrnEH7LFp2w/640 "")  
  
  
  
  
**05**  
  
**处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**安全更新**  
  
目前官方已有可更新版本，建议受影响用户升级至：  
  
Jenkins >= 2.442   
  
Jenkins LTS >= 2.426.3  
  
下载链接：https://www.jenkins.io/download/   
  
  
**>**  
**>**  
**>**  
**>**  
  
**缓解措施**  
  
**禁用CLI或限制访问：**  
如果不需要使用Jenkins CLI，可以在Jenkins配置中禁用CLI功能。如果需要使用CLI，请限制对CLI的访问权限，并仅授权给受信任的用户。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**通用建议**  
  
- 最小权限原则：按照最小权限原则，为用户和插件分配适当的权限级别，仅提供其所需的操作和功能。  
  
  
  
- 安全配置：仔细配置Jenkins的安全设置，包括启用CSRF保护、禁用不必要的功能和插件、限制对重要文件和密钥的访问等。  
  
- 密钥管理：妥善管理和保护Jenkins中使用的密钥，包括限制对密钥文件的访问权限、定期更换密钥、使用安全的密钥存储等。  
  
- 审查日志：定期审查Jenkins的日志文件，以及监控和检测任何可疑活动或异常行为。  
  
  
  
  
**>**  
**>**  
**>**  
**>**  
  
**产品解决方案**  
  
**奇安信网站应用安全云防护系统已更新防护特征库**  
  
奇安信网神网站应用安全云防护系统已全局更新所有云端防护节点的防护规则，支持对Jenkins任意文件读取漏洞(CVE-2024-23897)的防护。  
  
  
**奇安信开源卫士已支持**  
  
奇安信开源卫士20240126.517版本已支持对Jenkins任意文件读取漏洞(CVE-2024-23897)的检测。  
  
  
**奇安信天眼检测方案**  
  
奇安信天眼新一代安全感知系统已经能够有效检测针对该漏洞的攻击，请将规则版本升级到3.0.0126.14185或以上版本。规则ID及规则名称：  
0x604b，Jenkins任意文件读取漏洞(CVE-2024-23897)。奇安信天眼流量探针规则升级方法：系统配置->设备升级->规则升级，选择“网络升级”或“本地升级”。  
  
  
**奇安信网神网络数据传感器系统产品检测方案**  
  
奇安信网神网络数据传感器（NDS5000/7000/9000系列）产品，已具备该漏洞的检测能力。规则ID为：52573 ，建议用户尽快升级检测规则库至2401261630以上。  
  
  
  
**06**  
  
**参考资料**  
  
[1]https://www.jenkins.io/security/advisory/2024-01-24/#SECURITY-3314  
  
  
  
**07**  
  
**时间线**  
  
2024年1月26日，奇安信 CERT发布安全风险通告。  
  
  
  
**08**  
  
**漏洞情报服务**  
  
奇安信ALPH  
A威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icK81IN6CIrB19EFqlicHRzruIqOpGY2PSS6nalSvibZsvqJWqhkhywjibjg2L4uaN7HgnLr7fOmsM2g/640 "漏洞订阅上线.png")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640 "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs48xXg4MDyEhibLMiaE34I7EWiazktrG9u3RmIDSvoC9MfFt0BrcjHibKcWKicXATicfXUlycfw1H4IxBrxA/640 "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到**ALPHA威胁分析平台**  
订阅更多漏洞信息。  
  
