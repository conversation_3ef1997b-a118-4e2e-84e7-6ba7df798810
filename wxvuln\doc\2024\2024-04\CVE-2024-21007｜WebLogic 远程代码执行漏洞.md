#  CVE-2024-21007｜WebLogic 远程代码执行漏洞   
alicy  信安百科   2024-04-20 21:36  
  
**0x00 前言**  
  
****  
WebLogic是美国Oracle公司出品的一个application server，确切的说是一个基于JAVAEE架构的中间件，WebLogic是用于开发、集成、部署和管理大型分布式Web应用、网络应用和数据库应用的Java应用服务器。将Java的动态功能和Java Enterprise标准的安全性引入大型网络应用的开发、集成、部署和管理之中。  
  
  
  
**0x01 漏洞描述**  
  
  
未经身份验证的威胁者可通过 T3、IIOP 进行网络访问来破坏 Oracle WebLogic Server，成功利用可能导致对关键数据的未授权的访问或对所有Oracle WebLogic Server可访问数据的完全访问，造成任意代码执行，从而获取远程服务器权限。  
  
  
  
**0x02 CVE编号**  
  
  
CVE-2024-21007  
  
  
  
**0x03 影响版本**  
  
  
WebLogic Server 12.2.1.3.0  
  
WebLogic Server 12.2.1.4.0  
  
WebLogic Server 14.1.1.0.0  
  
  
  
**0x04 漏洞详情**  
  
  
https://www.oracle.com/security-alerts/cpuapr2024.html  
  
  
  
**0x05 参考链接**  
  
  
https://www.oracle.com/security-alerts/cpuapr2024.html  
  
  
[【复现】WebLogic T3/IIOP 远程代码执行漏洞（CVE-2024-21007）的风险通告](http://mp.weixin.qq.com/s?__biz=MzkxMDQyMTIzMA==&mid=2247484573&idx=1&sn=fd18eea55ae9cd68173825003e1a888d&chksm=c12af91cf65d700a6b02d2062a901bcf475d5f7154df4f83a58f619bab78b12d9bd74f12c8a6&scene=21#wechat_redirect)  
  
  
  
  
  
推荐阅读：  
  
  
[CVE-2024-29202｜JumpServer JINJA2注入代码执行漏洞（POC）](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247485118&idx=1&sn=71c347bd5af7c9ae26602f892ddbaa97&chksm=cea96f67f9dee6711ccb836b0f407787640eb8609e9688348cd8a19205b80f7de40c3343f78a&scene=21#wechat_redirect)  
  
  
  
[CVE-2024-20767｜Adobe ColdFusion 任意文件读取漏洞（POC）](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247485096&idx=2&sn=dd0048ec7cb3b7ca77d29b9d446f8f8d&chksm=cea96f71f9dee66720c13cb780e66b29b79fee5856b80c7df95371c75b1befb0a4eaeabec812&scene=21#wechat_redirect)  
  
  
  
[N/A｜RuoYi v4.7.8若依后台管理系统RCE漏洞（POC）](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247485027&idx=2&sn=772181c520afb25f337f5849526c3b1f&chksm=cea96fbaf9dee6ac6d72ca6ad50c3a19c66b150bba018fb80b60124cadad16b3443727693e90&scene=21#wechat_redirect)  
  
  
  
  
  
Ps：国内外安全热点分享，欢迎大家分享、转载，请保证文章的完整性。文章中出现敏感信息和侵权内容，请联系作者删除信息。信息安全任重道远，感谢您的支持![](https://mmbiz.qpic.cn/mmbiz_png/Whm7t4Je6urTIficI8UhQibwpYWx4ic7Bk40AJlXrgx3icofWCbd5cbJFheld132R8exvlHnicn0AUjHLmVok4wV9qA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
！！！  
  
  
**本公众号的文章及工具仅提供学习参考，由于传播、利用此文档提供的信息而造成任何直接或间接的后果及损害，均由使用者本人负责,本公众号及文章作者不为此承担任何责任。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/Whm7t4Je6uqQ24S6worK6npevNP8p1uPc9jQeMAib2iaibBnibOzFaIbD0KlvsEtUAmL3xdbJJnWk74Y1KfBcIazzw/640?wx_fmt=png "")  
  
  
