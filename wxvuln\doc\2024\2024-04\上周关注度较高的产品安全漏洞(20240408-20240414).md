#  上周关注度较高的产品安全漏洞(20240408-20240414)   
原创 CNVD  CNVD漏洞平台   2024-04-15 17:01  
  
**一、境外厂商产品**  
**漏洞**  
  
**1、Dell OpenManage Enterprise路径遍历漏洞**  
  
Dell OpenManage Enterprise是美国戴尔（Dell）公司的一款用于IT基础架构管理的易于使用的一对多系统管理控制台。Dell OpenManage Enterprise
v4.0及之前版本存在路径遍历漏洞，未经身份验证的攻击者可利用该漏洞会对存储在服务器文件系统上的文件进行未经授权的访问。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16912  
  
**2、Dell ECS不正确访问控制漏洞**  
  
Dell ECS是美国戴尔（Dell）公司的一款可扩展、易于管理且具有弹性的企业级对象存储解决方案。Dell ECS存在不正确访问控制漏洞，攻击者可利用此漏洞访问命名空间内的所有存储桶及其数据。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16930  
  
**3、Dell vApp Manager操作系统命令注入漏洞**  
  
Dell vApp Manager是美国戴尔（Dell）公司的一个虚拟应用程序管理器。Dell vApp Manager 9.2.4.9之前版本存在操作系统命令注入漏洞，攻击者可利用此漏洞在系统上执行任意命令。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16927  
  
**4、Google Chrome安全绕过漏洞（CNVD-2024-16875）**  
  
Google Chrome是美国谷歌（Google）公司的一款Web浏览器。Google Chrome存在安全绕过漏洞，该漏洞源于iOS中存在执行不当问题。攻击者可利用此漏洞绕过安全限制。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16875  
  
**5、Google Chrome代码执行漏洞（CNVD-2024-16937）**  
  
Google Chrome是美国谷歌（Google）公司的一款Web浏览器。Google Chrome存在代码执行漏洞，该漏洞是由于在Accessibility中免费使用造成的。攻击者可利用此漏洞在系统上执行任意代码。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16937  
  
  
**二、境内厂商产品漏洞**  
  
**1、浙江大华技术股份有限公司DSS存在SQL注入漏洞（CNVD-2024-14629）**  
  
浙江大华技术股份有限公司，是全球领先的以视频为核心的智慧物联解决方案提供商和运营服务商。浙江大华技术股份有限公司Digital Surveillance System存在SQL注入漏洞，攻击者可利用该漏洞获取数据库敏感信息。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14629  
  
**2、Foxit PDF Reader AcroForm Annotation类型混淆代码执行漏洞**  
  
Foxit PDF Reader是一款PDF文档阅读器和打印器，拥有快捷的启动速度和丰富的功能。Foxit PDF Reader AcroForm
Annotation存在类型混淆漏洞，攻击者可以利用该漏洞提交特殊的文件请求，诱使用户解析，可使应用程序崩溃或以应用程序上下文执行任意代码。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-17004  
  
**3、Tenda AC10U fromAddressNat函数堆栈缓冲区溢出漏洞**  
  
Tenda AC10U是中国腾达（Tenda）公司的一款无线路由器。Tenda AC10U
15.03.06.49_multi_TDE01版本存在缓冲区溢出漏洞，该漏洞源于fromAddressNat函数的Entrys/mitInterface/page参数未能正确验证输入数据的长度大小，攻击者可利用该漏洞使缓冲区溢出并在系统上执行任意代码。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16941  
  
**4、北京亚控科技发展有限公司KingSCADA存在命令执行漏洞**  
  
KingSCADA是一款面向中、高端市场的SCADA产品，具有集成化管理、模块式开发、可视化操作、智能化诊断及控制等特点。北京亚控科技发展有限公司KingSCADA存在命令执行漏洞，攻击者可利用该漏洞执行任意命令。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14159  
  
**5、Foxit PDF Reader AcroForm代码执行漏洞**  
  
Foxit PDF Reader是一款PDF文档阅读器和打印器，拥有快捷的启动速度和丰富的功能。Foxit PDF Reader AcroForm存在代码执行漏洞，远程攻击者可以利用该漏洞提交特殊的文件请求，诱使用户解析，可使应用程序崩溃或以应用程序上下文执行任意代码。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-17007  
  
  
说明：关注度分析由CNVD根据互联网用户对CNVD漏洞信息查阅情况以及产品应用广泛情况综合评定。  
  
