#  【漏洞预警】XZ-Utils 5.6.0-5.6.1版本存在后门风险CVE-2024-3094   
cexlife  飓风网络安全   2024-04-01 17:51  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03xnxx1g6erJAKiaLkA6GwlBLibwibwvicoARUTXLxzJohJdds2FFlYyWuDY8czkj1hnyAVr7wkDcAjvw/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**XZ-Utils是Linux、Unix等POSIX兼容系统中广泛用于处理.xz文件的套件,包含liblzma、xz等组件,已集成在Debian、Ubuntu、CentOS等发行版仓库中,开发者JiaT75在其GitHub仓库中发布的5.6.0和5.6.1版本（github.com/tukaani-project/xz/releases/）加入了后门逻辑,使得构建的liblzma包含额外的代码注入逻辑,systemd依赖liblzma,导致sshd等依赖于systemd的应用程序也受到影响,后门针对sshd通过特定的证书签名进行公钥认证时,可执行其中的任意命令,存在后门版本的xz-utils官方发布于2024年2月24日,被Debian unstable分支、Fedora Rawhide、Fedora 40、ArchLinux等少数仓库集成,暂未被大规模应用,CentOS/Redhat/Ubuntu/Debian/Fedora等stable仓库不受影响。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03xnxx1g6erJAKiaLkA6GwlBjhlueC7wibWa5PJh16MPib33IicSptIo9YS1ibUkXLlMCOEGvXfqUSUaHQ/640?wx_fmt=png&from=appmsg "")  
  
**影响范围:**xz-utils[5.6.0, 5.6.1]xz-utils(-∞, 5.6.1+really5.4.5-1)xz-utils(-∞, 5.6.1+really5.4.5-1)**修复方案:**将xz-utils降级至5.6.0以前版本或在应用中替换为7zip等组件将组件 xz-utils 升级至 5.6.1+really5.4.5-1 及以上版本**参考链接:**https://www.openwall.com/lists/oss-security/2024/03/29/4https://salsa.debian.org/debian/xz-utils/-/blob/debian/unstable/m4/build-to-host.m4?ref_type=heads#L63https://github.com/tukaani-project/xz/releaseshttps://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users  
  
