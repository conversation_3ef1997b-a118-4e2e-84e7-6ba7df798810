#  【风险通告】Jenkins CLI 任意文件读取漏洞（CVE-2024-23897）   
风险通告  阿里云应急响应   2024-01-26 09:48  
  
2024年1月25日，Jenkins 官方披露 CVE-2024-23897 Jenkins CLI 任意文件读取漏洞。  
  
0  
1  
  
漏  
洞  
描  
述  
  
  
Jenkins是基于Java开发的一种持续集成工具。  
2024年1月25日，Jenkins 官方披露 CVE-2024-23897 Jenkins CLI 任意文件读取漏洞致。  
Jenkins 受影响版本中使用 args4j 库解析CLI命令参数，攻击者可利用相关特性读取 Jenkins 控制器文件系统上的任意文件（如加密密钥的二进制文件），并结合其他功能等可能导致任意代码执行。  
  
官方已发布安全更新修复该漏洞。  
Jenkins  
 官  
方评级严重。  
请 Jenkins   
客户尽快升级。  
  
  
0  
2  
  
漏  
洞  
评  
级  
  
  
CVE-2024-23897 Jenkins CLI 任意文件读取漏洞  
   
严  
重  
  
  
0  
3  
  
安全版  
本  
  
  
Jenkins weekly 2.442   
  
Jenkins LTS 2.426.3  
   
  
  
04  
  
安  
全  
建  
议  
  
  
1  
、  
升  
级  
至  
安  
全  
版  
本  
及  
其  
以  
上  
。  
  
2  
、  
利  
用  
安  
全  
组  
设  
置 Jenkins  
   
仅  
对  
可  
信  
地  
址  
开  
放  
。  
  
3  
、  
阿  
里  
云  
云  
安  
全  
中  
心  
应  
用  
漏  
洞  
已于2024年1月25日  
支  
持  
该  
漏  
洞  
检  
测  
。  
  
  
05  
  
  
相  
关  
链  
接  
  
  
https://avd.aliyun.com/detail?id=AVD-2024-23897  
  
https://www.jenkins.io/security/advisory/2024-01-24/#SECURITY-3314  
  
  
  
