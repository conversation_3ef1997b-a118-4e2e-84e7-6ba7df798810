#  多款中文输入法存在重大安全漏洞，暴露了超过10亿用户的击键行为   
 安全客   2024-04-25 12:17  
  
基于云的拼音键盘应用程序中发现的安全漏洞可能会被利用，向不法分子泄露用户的击键内容。  
  
该调查结果来自公民实验室，该实验室发现了百度、荣耀、科大讯飞、OPPO、三星、腾讯、Vivo和小米等供应商的九款应用程序中的八款存在缺陷。唯一一家键盘应用程序不存在任何安全缺陷的供应商是华为。  
  
研究人员杰弗里·诺克尔 (Jeffrey Knockel)、莫娜·王 (Mona Wang) 和佐伊·赖克特 (<PERSON><PERSON><PERSON>ert)表示，这些漏洞可能被用来“完全泄露用户在传输过程中的击键内容”。  
  
此次披露是基于多伦多大学跨学科实验室的先前研究，该实验室去年八月发现了腾讯搜狗输入法的密码缺陷。  
  
总体而言，估计有近 10 亿用户受到此类漏洞的影响，其中搜狗、百度和科大讯飞的输入法编辑器 (IME) 占据了很大一部分市场份额。  
  
已发现的问题摘要如下：  
1. 腾讯QQ拼音：易受CBC padding oracle攻击，可恢复明文  
  
1. 百度输入法：由于 BAIDUv3.1 加密协议中的错误，允许网络窃听者解密网络传输并提取 Windows 上键入的文本  
  
1. iFlytek IME：其 Android 应用程序允许网络窃听者恢复未充分加密的网络传输的明文  
  
1. Android 上的三星键盘：通过普通、未加密的 HTTP 传输击键数据  
  
1. 小米：预装了百度、科大讯飞和搜狗的键盘应用程序（因此容易受到上述相同缺陷的影响）  
  
1. OPPO：预装了百度和搜狗的键盘应用程序（因此容易受到上述相同缺陷的影响）  
  
1. Vivo：预装了搜狗输入法（因此容易受到上述相同缺陷的影响）  
  
1. Honor：预装了百度输入法（因此容易受到上述相同缺陷的影响）  
  
成功利用这些漏洞可以让攻击者完全被动地解密中国移动用户的击键，而无需发送任何额外的网络流量。经过负责任的披露，截至 2024 年 4 月 1 日，除荣耀和腾讯（QQ拼音）外，所有键盘应用程序开发商均已解决了这些问题。  
  
研究人员建议用户保持应用程序和操作系统最新，并切换到完全在设备上运行的键盘应用程序，以缓解这些隐私问题。  
  
“考虑到这些漏洞的范围、用户在设备上输入内容的敏感性、这些漏洞被发现的难易程度，以及五眼联盟之前曾利用中国应用程序中的类似漏洞进行监视，因此此类用户的击键也可能受到大规模监视，”研究人员说。  
  
  
**来**  
  
**领**  
  
**资**  
  
**料**  
  
**【免费领】**  
**网络安全专业入门与进阶学习资料，轻松掌握网络安全技能！**  
  
****![](https://mmbiz.qpic.cn/sz_mmbiz_png/Ok4fxxCpBb4N2VUg5icoU6eUKJ14GUznZiaB5GRRWfKMn3k9mc03BRO6zB0LoPzN4UFb1vIKXwibvsEkPLy6ozj8Q/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
  
