#  供应链后门检测方式以xz xz-utils为例（CVE-2024-3094）   
原创 高渐离  落水轩   2024-04-18 11:24  
  
人类唯一能从历史中吸取的教训就是，人类从不会从历史中吸取到教训  
  
xz xz-utils后门事件（CVE-2024-3094），来的很快，当天很多安全公司都发文了，都是一番分析（翻译），包括我。去的也快，也才两周时间，已经不是安全圈的热点了。  
  
你要说从这个事件学到了啥？除了我们在使劲恐吓老板外，我们能做一些提高么？我并未从这嘈杂的内外网讨论中，看到多少营养的例子。  
  
俨然历史上的供应链攻击并未给我们带来多少经验教训，只是给大家带来了不少茶余饭后的谈资而已。  
  
从SolarWinds到xz-utils，面临这些供应链大家的第一反应都是，这个事情防不了。保持躺平。  
  
其实也不尽然。  
  
我们能在供应链攻击中获得一点点先机么？  
  
我们ThreatRadar团队，一个专注于事件情报跟踪分析团队，带来我们的第一篇博客《  
XZ供应链后门检测方案(CVE-2024-3094)  
》。该博客也得到了某不具名资深Linux老专家的指导。  
  
参见：  
```
https://blog.threatradar.cn
```  
  
欢迎大家批评，希望能跟大家一起探讨，做最认真的防御。  
  
