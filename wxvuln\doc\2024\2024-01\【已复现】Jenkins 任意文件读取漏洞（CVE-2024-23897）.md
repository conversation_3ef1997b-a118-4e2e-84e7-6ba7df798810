#  【已复现】Jenkins 任意文件读取漏洞（CVE-2024-23897）   
长亭应急响应  黑伞安全   2024-01-26 17:29  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicR8k25DW2icibDib3jQzkoZVQED3wS6XicL7p1f4LnM0AeTyE775b4Gic8gRu259qNfQRTF5K8U74exeCg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
Jenkins是一个开源CI/CD工具，用于自动化开发流程，包括构建、测试和部署软件。  
2024年1月，互联网公开了一个Jenkins的任意文件读取漏洞。鉴于该漏洞易于利用，存在危害扩大的风险，且该系统数据较为敏感且影响范围广泛，建议所有使用Jenkins的企业尽快进行修复，以确保系统安全。  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因命令行接口文件读取： Jenkins内置的命令行接口（CLI）存在一个特性，允许在命令参数中用@字符后跟文件路径来替换为文件内容。这导致攻击者能够读取Jenkins控制器文件系统上的任意文件。权限绕过： 拥有Overall/Read权限的攻击者可以读取完整文件，而没有该权限的攻击者也可以读取部分文件内容。漏洞影响任意文件读取：拥有Overall/Read权限的攻击者可以读取整个文件。（默认情况下）没有Overall/Read权限的攻击者可以读取文件的前几行。可读取的行数取决于可用的CLI命令。远程代码执行及其它影响：攻击依赖攻击者能通过漏洞读取到二进制形式密钥以及其它前提条件，虽然攻击者可以读取包含二进制数据的文件，但是由于功能限制，某些字节无法成功读取并被占位符值替换，导致攻击者在读取部分密钥后仍然需要推算一定数量的字节（字节数根据Jenkins » System Information中file.encoding值的不同而存在差异）。虽然条件较多，但是仍存在一定利用可能性。利用特征使用POST请求/cli接口，且包体中含有help、who-am-i命令以及@文件名。长亭安全研究员发现，除了官方给出的help、who-am-i两个命令外，还可以调用下面一批命令（默认匿名环境下）来进行任意文件读取：keep-buildrestartshutdownsafe-shutdowndisable-jobenable-job上述命令是通过另一种方式加载的，相关的 main 方法位于 CLIRegisterer 类中。这个CLIRegisterer#main 方法与 CLICommand#main 方法的主要区别在于处理顺序：CLIRegisterer#main 会先对参数进行解析，然后进行权限校验，而 CLICommand#main 则是先进行权限校验，再对参数进行解析。因此，通过引发参数解析阶段的错误，可以在权限校验之前获取到文件读取的部分结果。这揭示了程序逻辑上的一个缺陷。同时值得注意的是Jenkins CLI 支持三种访问模式，分别是 SSH、HTTP、Websocket，因此如果要依赖流量特征缓解此漏洞，可能会相对繁琐。  
  
**检测工具**  
  
 Detec  
tion   
  
  
  
**02**  
  
****  
****  
  
xpoc远程检测工具xpoc -r 419 -t http://xpoc.org工具获取方式https://github.com/chaitin/xpochttps://stack.chaitin.com/tool/detail/1036  
  
  
  
  
牧云本地检测工具访问百川云平台，通过「牧云·服务器漏洞管理」进行扫描：https://rivers.chaitin.cn/landing/sentinel  
  
  
  
  
**影响版本**  
  
 Affected Version   
  
  
  
**03******  
  
  
  
  
version <= Jenkins 2.441version <= LTS 2.426.2  
  
解决方案 Solution 04临时缓解方案访问控制：加强服务器和应用的访问控制，仅允许可信IP进行访问。另外如非必要，不要将该系统开放在互联网上。临时禁用CLI访问：暂时禁用CLI访问，直到部署更新。升级修复方案Jenkins 2.442， LTS 2.426.3已禁用相关命令解析器特性。建议更新至这些版本以解决漏洞。若升级后遇到问题，可以通过设置Java系统属性hudson.cli.CLICommand.allowAtSyntax为true来禁用此变更，但这种做法不建议在可由非管理员用户访问CLI的网络上使用（会重新引入漏洞）。漏洞复现 Reproduction 05产品支持 Support06云图：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。洞鉴：预期1月26日发布引擎版本，支持检测该漏洞的检测。雷池：默认支持HTTP利用方式的检测。全悉：已发布规则升级包，支持检测该漏洞的利用行为。牧云：使用管理平台 23.05.001 及以上版本的用户可通过升级平台下载应急漏洞情报库升级包（EMERVULN-24.01.026）“漏洞应急”功能支持该漏洞的检测；其它管理平台版本请联系牧云技术支持团队获取该功能。  
**时间线**  
  
 Timeline   
  
  
  
**07**  
1月25日 互联网公开漏洞情报1月25日 长亭应急响应实验室复现漏洞1月26日 长亭安全应急响应中心发布通告  
参考资料：  
[1].https://www.jenkins.io/security/advisory/2024-01-24/  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
  
#2024漏洞风险提示  
4  
  
  
#2024漏洞风险提示 · 目录  
  
  
上一篇  
【原创0day】用友YonBIP ServiceDispatcher远程代码执行漏洞  
  
  
  
  
