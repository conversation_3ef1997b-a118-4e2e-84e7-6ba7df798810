#  微软2024年4月补丁日多个产品安全漏洞风险通告   
 奇安信 CERT   2024-04-10 10:51  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="center" rowspan="1" colspan="4" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1.5em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 255, 255);letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">漏洞概述</span></strong><br style="outline: 0px;visibility: visible;"/></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;caret-color: red;letter-spacing: 0px;visibility: visible;">微软2024年4月补丁日多个产品安全漏洞</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" rowspan="1" colspan="1" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;visibility: visible;">影响产品</strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">SQL Server、Microsoft Office、Windows Defender等。</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="162" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">2024-4-10</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="190" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">影响对象数量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="101" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">千万级</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">奇安信评级</span></strong></p></td><td valign="middle" align="left" width="162" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高危</span></strong></p></td><td valign="middle" align="left" width="190" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">利用可能性</span></strong></p></td><td valign="middle" align="left" width="101" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 169, 0);visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="color: rgb(255, 169, 0);cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><strong style="letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高</span></strong></span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="162" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="190" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="101" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">部分发现</span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="104" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="162" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="190" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="101" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="4" rowspan="1" align="left" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">危害描述：</span></strong><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;visibility: visible;">攻击者利用这些漏洞，可造成权限提升、远程代码执行等。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
  
本月，微软共发布了149个漏洞的补丁程序，修复了SQL Server、Microsoft Office Outlook、Windows Kernel等产品中的漏洞。经研判，以下17个重要漏洞值得关注（包括3个紧急漏洞、14个重要漏洞），如下表所示：  
  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td align="center" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 255, 255);">编号</span></strong></p></td><td align="center" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 255, 255);">漏洞名称</span></strong></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 255, 255);">风险等级</span></strong></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 255, 255);">公开状态</span></strong></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 255, 255);">利用可能</span></strong></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26234</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">代理驱动程序欺骗漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">公开</span></span></strong></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><strong><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">在野利用</span></span></strong></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-21323</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">一般</span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-29053</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">紧急</span></span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">一般</span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-21322</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">紧急</span></span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">一</span><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">般</span></span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-28903</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">安全启动安全功能绕过漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-28921</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">安全启动安全功能绕过漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26241</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Win32k 权限提升漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></p></td></tr><tr style="outline: 0px;"><td align="left" valign="middle" width="103" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26158</span></p></td><td align="left" valign="middle" width="262" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Microsoft 安装服务权限提升漏洞</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td align="center" valign="middle" width="64" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);"><p style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></p></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);">CVE-2024-26230</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);">Windows 启用电话服务器权限提升漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span><br/></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-26209<span style="display: none;line-height: 0px;">‍</span></span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Microsoft 本地安全认证子系统服务信息泄露漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;color: rgb(255, 0, 0);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-202</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">4-26185</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Windows 压</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">缩文</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">件夹篡改漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-26218</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Windows 内核权限提升漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-26212</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">DHCP 服务器服务拒绝服务漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-26211</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Windows 远程访问连接管理器权限提升漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-29056</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Windows 身份验证权限提升漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-26256</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">Libarchive 远程代码执行漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr><tr style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">CVE-2024-29988</span></td><td align="left" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">SmartScreen 提示安全功能绕过漏洞</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">重</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">要</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">未</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">公</span><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 14px;letter-spacing: normal;">开</span></td><td align="center" valign="middle" colspan="1" rowspan="1" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;margin-bottom: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-align: -webkit-center;text-wrap: wrap;background-color: rgb(255, 255, 255);">较大</span></td></tr></tbody></table>  
  
  
  
**02**  
  
**重点关注漏洞**  
  
**>**  
**>**  
**>**  
**>**  
  
**更容易被利用漏洞**  
  
以下14个漏洞被微软标记为“Exploitation More Likely”  
或“Exploitation More Likely”，这代表这些漏洞更容易被利用：  
  
  
- CVE-2024-26212  
	DHCP 服务器服务拒绝服务漏洞  
  
- CVE-2024-26256  
	Libarchive 远程代码执行漏洞  
  
- CVE-2024-26158  
	Microsoft 安装服务权限提升漏洞  
  
- CVE-2024-26209  
	Microsoft 本地安全认证子系统服务信息泄露漏洞  
  
- CVE-2024-29988  
	SmartScreen 提示安全功能绕过漏洞  
  
- CVE-2024-26241  
	Win32k 权限提升漏洞  
  
- CVE-2024-26218  
	Windows 内核权限提升漏洞  
  
- CVE-2024-26239  
	Windows 启用电话服务器权限提升漏洞  
  
- CVE-2024-26230  
	Windows 启用电话服务器权限提升漏洞  
  
- CVE-2024-29056  
	Windows 身份验证权限提升漏洞  
  
- CVE-2024-26211  
	Windows 远程访问连接管理器权限提升漏洞  
  
- CVE-2024-28903  
	安全启动安全功能绕过漏洞  
  
- CVE-2024-28921  
	安全启动安全功能绕过漏洞  
  
  
  
- CVE-2024-26234  
	代理驱动程序欺骗漏洞  
  
  
  
CVE-2024-26195 DHCP 服务器服务远程代码执行漏洞、CVE-2024-26208 Microsoft 消息队列 (MSMQ) 远程代码执行漏洞由奇安信代码安全实验室安全研究员发现并提交。  
  
CVE-2024-29064 Windows Hyper-V 拒绝服务漏洞由奇安信天工实验室安全研究员发现并提交。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs48cgDunqmibyiaPqvxDMKXDLSfAiaSRvvXCwAnM1b2lHHBHt6qpOGAXok9htfX41rRyDd90HI0QVhekw/640?wx_fmt=png&from=appmsg "")  
  
  
**鉴于这些漏洞危害较大，建议客户尽快安装更新补丁。**  
  
  
**>**  
**>**  
**>**  
**>**  
  
**重点关注漏洞详情**  
  
经研判，以下17个漏洞值得关注，漏洞的详细信息如下：  
  
  
**1、CVE-2024-26234 代理驱动程序欺骗漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="451" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">代理驱动程序欺骗漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="71" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">欺骗</span></p></td><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="68" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26234</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="91" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;color: red;letter-spacing: 0px;">公开</span></strong></span></p></td><td width="79" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="245" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="outline: 0px;color: red;letter-spacing: 0px;">已发现</span></strong></span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="431" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">通过滥用 Microsoft Windows 硬件兼容性计划 (WHCP)签名，经过攻击者利用签名的后门可被Windows视为合法白文件执行。</span><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-size: 14px;outline: 0px;color: red;letter-spacing: 0px;">该漏洞已存在在野利用。</span></strong></span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26234</span></p></td></tr></tbody></table>  
  
**2、CVE-2024-21323 用于 IoT 的 Microsoft Defender 远程代码执行漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="84" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="433" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="61" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="49" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="90" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-21323</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="81" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="85" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="237" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="413" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">经过身份认证并获得启动更新过程所需的权限的远程攻击者可利用此漏洞，使用路径遍历可向 Defender for IoT 传感器发送 tar 文件，提取过程完成后，攻击者可以发送未签名的更新包并覆盖他们选择的任何文件，从而造成远程代码执行。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-21323</span></p></td></tr></tbody></table>  
  
**3、CVE-2024-29053 用于 IoT 的 Microsoft Defender 远程代码执行漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="451" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="79" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="46" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td width="72" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="106" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-29053</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="248" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="431" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞的存在是由于 Microsoft Defender for IoT 中的绝对路径遍历问题所致。经过身份验证的攻击者如果可以进行文件上传，则可以通过将恶意文件上传到服务器上的敏感位置来利用此路径遍历漏洞。成功利用此漏洞允许远程攻击者在目标系统上执行任意代码。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-29053</span></p></td></tr></tbody></table>  
  
**4、CVE-2024-21322 用于 IoT 的 Microsoft Defender 远程代码执行漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="442" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">用于 IoT 的 Microsoft Defender 远程代码执行漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="63" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td width="64" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-21322</span></p></td></tr><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="88" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="249" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="422" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于 Microsoft Defender for IoT 中的存在命令注入所致。远程管理员可以将特制数据传递给应用程序并在目标系统上执行任意命令。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-21322</span></p></td></tr></tbody></table>  
  
**5、安全启动安全功能绕过漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="423" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">安全启动安全功能绕过漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="94" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">安全特性绕过</span></p></td><td width="73" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="40" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="99" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-28903</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-28921</span></p></td></tr><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="73" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="229" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="403" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">由于安全启动存在保护机制失效问题，本地高权限的攻击者成功利用这些漏洞可以绕过安全启动。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-28903</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-28921</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://support.microsoft.com/help/5025885</span></p></td></tr></tbody></table>  
  
**6、CVE-2024-26241 Win32k 权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="440" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Win32k 权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="77" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="69" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="47" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="65" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="107" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26241</span></p></td></tr><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="87" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="247" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="420" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">由于Win32K存在释放后重用，具有低权限的本地攻击者成功利用此漏洞可以获得系统特权。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26241</span></p></td></tr></tbody></table>  
  
**7、CVE-2024-26158 Microsoft 安装服务权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="437" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Microsoft 安装服务权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="80" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="88" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="54" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="81" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26158</span></p></td></tr><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="103" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="78" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="221" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="417" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">由于Microsoft 安装服务存在文件访问前的不正确链接解析，具有低权限的本地攻击者成功利用此漏洞可以获得系统特权。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26158</span></p></td></tr></tbody></table>  
  
**8、Windows 启用电话服务器权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="433" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Windows 启用电话服务器权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="84" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="67" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="68" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="52" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="129" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26239</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26230</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="64" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="81" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="251" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="413" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26239是由于 Windows Telephony Server 中存在基于堆的缓冲区溢出，CVE-2024-26230是由于 Windows Telephony Server 中存在释放后重用。具有低权限的本地攻击者成功利用这些漏洞可以获得系统特权。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26239</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26230</span></p></td></tr></tbody></table>  
  
  
**9、CVE-2024-26209 Microsoft 本地安全认证子系统服务信息泄露漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="451" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Microsoft 本地安全认证子系统服务信息泄露漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="71" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">信息泄露</span></p></td><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="68" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26209</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="91" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="79" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="245" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="431" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于Microsoft 本地安全认证子系统服务中存在使用未初始化的资源所致。如果攻击者成功利用此漏洞，则可以泄露未初始化的内存信息。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26209</span></p></td></tr></tbody></table>  
  
**10、CVE-2024-26218 Windows 内核权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="84" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="433" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Windows 内核权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="61" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="49" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="90" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26218</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="81" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="85" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="237" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="104" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="413" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于Windows内核中存在检查时间和使用时间（TOCTOU）竞争条件，具有低权限的本地攻击者成功利用该漏洞可以获得系统特权。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26218</span></p></td></tr></tbody></table>  
  
**11、CVE-2024-26212 DHCP 服务器服务拒绝服务漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="451" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">DHCP 服务器服务拒绝服务漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="66" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">拒绝服务</span></p></td><td width="79" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="46" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="72" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="106" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26212</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="248" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="431" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于 DHCP 服务器服务中存在不受控制的资源消耗。未经身份验证的远程攻击者可以向服务器发送特制的流量触发该漏洞，成功利用该漏洞将造成服务器崩溃。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26212</span></p></td></tr></tbody></table>  
  
**12、CVE-2024-26211 Windows 远程访问连接管理器权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="442" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Windows 远程访问连接管理器权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="63" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="64" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26211</span></p></td></tr><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="88" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="249" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="95" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="422" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于 Windows 远程访问连接管理器中存在基于堆的缓冲区溢出造成的。具有低权限的本地攻击者成功利用该漏洞可以获得系统特权。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26211</span></p></td></tr></tbody></table>  
  
**13、CVE-2024-29056 Windows 身份验证权限提升漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="423" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Windows 身份验证权限提升漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="94" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td width="73" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="40" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="57" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="99" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-29056</span></p></td></tr><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="73" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="86" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="229" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="114" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="403" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><span style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-wrap: wrap;background-color: rgb(255, 255, 255);">该漏洞由于Windows 身份验证中存在使用有风险的加密算法所致。具有来自跨组织信任用户的攻击者成功利用该漏洞可能获得组织中所有用户授予的访问权限。成功利用此漏洞的攻击者可以查看一些敏感信息，但并非受影响组件中的所有资源都会泄露给攻击者。攻击者无法对泄露的信息进行更改或限制对资源的访问。</span></span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-29056</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://support.microsoft.com/zh-cn/help/5037754</span></p></td></tr></tbody></table>  
  
**14、CVE-2024-26256 Libarchive 远程代码执行漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="440" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">Libarchive 远程代码执行漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="77" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td width="69" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="47" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="65" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="107" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-26256</span></p></td></tr><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="75" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="87" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="247" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="97" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="420" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞是由于Libarchive中存在基于堆的缓冲区溢出所致。未经授权的攻击者需要诱导受害者将恶意文件保存到本地，等待用户启动连接后触发该漏洞造成任意代码执行。</span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-26256</span></p></td></tr></tbody></table>  
  
**15、CVE-2024-29988 SmartScreen 提示安全功能绕过漏洞**  
<table><tbody style="outline: 0px;"><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></p></td><td colspan="5" width="437" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">SmartScreen 提示安全功能绕过漏洞</span></p></td></tr><tr style="outline: 0px;"><td width="80" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></p></td><td width="88" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">安全特性绕过</span></p></td><td width="70" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></p></td><td width="55" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td width="54" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞ID</span></strong></p></td><td width="81" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">CVE-2024-29988</span></p></td></tr><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></p></td><td width="103" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td width="78" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></p></td><td colspan="3" width="221" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr style="outline: 0px;"><td width="100" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></p></td><td colspan="5" width="417" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">由于SmartScreen 提示安全功能保护机制失效，未经身份认证的远程攻击者通过发送带有压缩文件的利用载荷后诱骗受害者与之交互，可以绕过&#34;网页标记&#34;（Mark of the Web，MotW）功能，从而在目标系统上执行恶意代码。</span></p><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">该漏洞与微软24年2月补丁日修复的在野漏洞CVE-2024-21412相似，</span><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 14px;letter-spacing: normal;text-wrap: wrap;background-color: rgb(255, 255, 255);"><span style="font-size: 14px;outline: 0px;color: red;letter-spacing: 0px;">微软目前并未将其列为已被利用的漏洞，但该漏洞更有可能存在在野利用。</span></strong></span></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><strong style="outline: 0px;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></p></td></tr><tr style="outline: 0px;"><td colspan="6" style="outline: 0px;word-break: break-all;hyphens: auto;"><p style="outline: 0px;line-height: 1.6em;"><span style="outline: 0px;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2024-29988</span></p></td></tr></tbody></table>  
  
**03**  
  
**处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**安全更新**  
  
**使用奇安信天擎的客户可以通过奇安信天擎控制台一键更新修补相关漏洞，也可以通过奇安信天擎客户端一键更新修补相关漏洞。**  
  
  
也可以采用以下官方解决方案及缓解方案来防护此漏洞：  
  
**Windows自动更新**  
  
Windows系统默认启用 Microsoft Update，当检测到可用更新时，将会自动下载更新并在下一次启动时安装。还可通过以下步骤快速安装更新：  
  
1、点击“开始菜单”或按Windows快捷键，点击进入“设置”  
  
2、选择“更新和安全”，进入“Windows更新”（Windows Server 2012以及Windows Server 2012 R2可通过控制面板进入“Windows更新”，步骤为“控制面板”-> “系统和安全”->“Windows更新”）  
  
3、选择“检查更新”，等待系统将自动检查并下载可用更新  
  
4、重启计算机，安装更新  
  
系统重新启动后，可通过进入“Windows更新”->“查看更新历史记录”查看是否成功安装了更新。对于没有成功安装的更新，可以点击该更新名称进入微软官方更新描述链接，点击最新的SSU名称并在新链接中点击“Microsoft 更新目录”，然后在新链接中选择适用于目标系统的补丁进行下载并安装。  
  
  
**手动安装补丁**  
  
另外，对于不能自动更新的系统版本，可参考以下链接下载适用于该系统的4月补丁并安装：  
  
https://msrc.microsoft.com/update-guide/releaseNote/2024-Apr  
  
  
**>**  
**>**  
**>**  
**>**  
  
**产品解决方案**  
  
**奇安信天擎终端安全管理系统解决方案**  
  
奇安信天擎终端安全管理系统并且有漏洞修复相关模块的用户，可以将补丁库版本更新到：  
2024.4.10.1及以上版本，对内网终端进行补丁更新。  
  
推荐采用自动化运维方案，如果控制中心可以连接互联网的用户场景，建议设置为自动从奇安信云端更新补丁库至  
2024.4.10.1版本。  
  
控制中心补丁库更新方式：每天04:00-06:00自动升级，升级源为从互联网升级。  
  
纯隔离网内控制中心不能访问互联网，不能下载补丁库和补丁文件，需使用离线升级工具定期导入补丁库和文件到控制中心。  
  
  
  
**04**  
  
**参考资料**  
  
https://msrc.microsoft.com/update-guide/releaseNote/2024-Apr  
  
  
  
**05**  
  
**时间线**  
  
2024年4月10日，奇安信 CERT发布安全风险通告。  
  
  
  
**06**  
  
**漏洞情报服务**  
  
奇安信ALPHA威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs49bPiaZuqqYYtC3YCxCdNwxaHtIlcwywGr2SZSOibMUxJpVajoCdBQWbMkcWMUpS0se30JzdBYkMB8g/640?tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640?tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs49bPiaZuqqYYtC3YCxCdNwxaq09vOpl6nxZHvibSgPaHme8URcKtcBORgxialKylWicXQqxBes9xteoNw/640?tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到**ALPHA威胁分析平台**  
订阅  
更多漏洞信息。  
  
