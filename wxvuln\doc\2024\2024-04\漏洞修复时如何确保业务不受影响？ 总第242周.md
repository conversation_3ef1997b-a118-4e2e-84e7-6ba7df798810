#  漏洞修复时如何确保业务不受影响？| 总第242周   
原创 群秘  君哥的体历   2024-04-22 07:36  
  
‍‍  
  
‍‍  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/yXsxtS2cfwYLicju4TyAeQhibftSnibn1R9dnxB7tCR0JyCicooUTh4rDmWsBv1wBniaFHVGdaNmMeJOl1hVIicPKkzg/640?wx_fmt=gif "")  
  
**0x1本周话题**  
  
****  
话题：漏洞修复时，怎么最大程度确保不产生对业务的影响？  
  
A1：只要动生产就要遵循变更流程，漏洞修复和日常的投产变更要求是一样的，都需要做好回归测试。本质都是变更，做好最坏影响分析，如果不接受“万一失败就断业务维修时间”的风险，那备机就备好，失败就切回，成功就继续灰度。漏洞升级最大的成本其实是测试。  
  
A2：修漏洞是运维的本职工作，不是安全的本职工作；保密是自己的事，泄密了不是保密办的责任，他们负责抓人、审计、风险、质量，都是一样的。安全告知这个漏洞可能引发的最坏结果，修漏洞是机器负责人和模块负责人的本职。  
  
回归怎么最大程度确保不对业务产生影响：  
运维日常要做好容灾，修漏洞也要遵守变更灰度，少量服务器离线，容灾要能兜住  
。  
常态化演习。  
  
A3：应用的漏洞，做好开关，发现问题可恢复。与生产环境最接近测试环境先修复，全流程功能执行一遍，再到生产修复。  
  
A4：业务应用本身的安全漏洞由研发流程保障，由业务本身发行版本测试流程保障。系统漏洞由运维sop流程保障，测试验证后没问题后灰度修复。  
不过很多公司没有灰度环境，甚至测试环境和生产环境一致性都很难保证。  
  
A5：  
整个流程中安全的角色是问题检测和发现者，问题解决由研发、运维各自的标准流程保障稳定性，安全可以提出解决方案，不负责解决问题，研发运维可以选择接受风险不修复，但需要对产生的风险买单。  
  
A6：  
研发去修复，然后测试，回归。  
把漏洞当业务问题看待。  
业务问题也一样有可能导致业务受影响，所以不用过分紧张，真出问题就回滚，走通用流程。  
  
A7：备份，双活设计加热迁移。我们重要实时性高的系统一般先在测试、验证或预发环境搞，业务测试观察没问题之后再生产系统。  
  
A8：  
一是有原厂服务的尽量找原厂来做。二是使用成熟的解决方案，感觉棘手地充分调研，不打无准备之仗。三是在测试环境充分测试，尤其涉及配置和源代码修改的。有条件准生产的就多测一轮。四是准备修复之前充分评估，有风险提前跟领导汇报说清楚。五是上线时间选择周末晚上凌晨，万一有风险也能避开业务高峰。六是应急回退预案要做好准备。  
  
A9：  
一般是有三个环境：  
一个测试环  
境，一个准生产环境，一个生产环境。  
测试环境搞得动静大点没事，  
先在测试环境进行全面测试、验证等，模拟多个业务场景等等；准生产环境要尽量跟测试环境一致，在准生产上跑个1个月再把变动同步到生产环境，且  
提  
前做好备份，最后避开  
业务高峰期进行漏洞修复。  
  
A10：漏洞风险评估，灰度升级测试，单点服务能不修的不修，用其他手段做风险减弱缓解措施，待时机成熟再升级。版本比对外，还要注意开源转收费。  
  
Q：漏洞如果没发现被利用，责任大家怎么界定？如果已经报了漏洞，超时不修复被利用，是业务责任，这个容易理解。但没发现，比如一些难发现的逻辑漏洞，你们有责任界定规则没？  
  
A11：  
逻辑漏洞属于业务安全，看业务安全有没有或者谁负责了，如果没有，那就是业务自身的问题。  
  
A12：  
想到一  
个类比：  
一个人有病，需要吃药，但是药有副作用，极小概率会导致吃药死亡。  
作为医生，建议他有病要吃药，一般情况下没问题，但是有时候运气不好，病人吃了之后挂了，医生有责任  
吗？  
  
Q：如果已经投产，又出现组件漏洞，系统不是自研，有没有好的解决办法？厂商的应用可能并不支持组件升级到新版本。  
  
A13：继续让厂商修复，同时用其他措施做风险补偿，第一时间做补救措施，具体问题具体分析了。后续的合同里面也可以加上厂商的漏洞修复责任，SLA之类的条款。  
  
Q：如果厂商配合不够好，是不是可以在采购时提出约束？不过对某些垄断领域厂商，好像不太实用。  
  
A14：厂商可能会不认可，该类系统是垄断。我们尽力这样去做，已经在采购部分加条款了。  
  
A15：有些第三方系统很难修复，要看看这个系统是不是在外网，访问控制如何？谁可以访问，能不能加IP限制等等。如果是内网系统控制方法更多，有漏洞影响也可能比较小。  
  
A16：有测试环境的话测试环境先测试；业务低峰开展，做好回退措施；如果有多个系统有相同漏洞，按重要性从低往高修复；连续性要求高的又时不时需要修复漏洞的，考虑做主备或双活；虚拟补丁之类临时使用。有预算的单位，还是做好冗余吧。  
  
Q：咨询下大佬们对于开源软件怎么做上线前检测？除了漏扫外。或者说引入一个开源软件，都是从哪些方面来提高安全性呢？  
  
A17：引入阶段就需要评审，是否和现有技术栈匹配，该组件处于生命周期的哪个阶段，版本比对是否存在安全隐患。个人理解和自己开发的应该逻辑一样把SDL，做静态动态代码检查 对比收集涉及组件版本 bom表，对比版本。  
  
A18  
：  
若  
考虑对外暴露有大量客户的系统，且漏洞至少是高危以上的情况。  
需要立刻修复，没办法走dev-st-uat-prd这种常规的发布流程了。  
因为缺乏足够的测试所以担心修复的动作影响业务。  
  
A19：根据我这边的经验，公司内部的软件研发、测试、部署流程管控到位的话，暴露到外的这种立刻需要整改的漏洞往往是以下的情况：1、测试账户未删除；2、弱口令；3、前段文件暴露信息、存在暗链；4、外购系统暴露的一些非业务功能组件，如nacos、swagger、actuator。这几种情况直接启动紧急修复流程，对业务几乎无影响。弱口令我们会找业务人员联系客户解决同时要求系统增强密码复杂度校验规则。对于业务功能出现的漏洞几乎都是要存在一系列的前置条件，这种我们就按照正常的漏洞闭环管理流程处置。  
  
A20：我见过很多核心数据库业务不敢打补丁，都搞的虚拟补丁或缓解措施，如果确实要修的前提下就是用高可用弄，应用上的发版的情况和这一样，他们是用的啥红蓝发版和金丝雀发版啥的来解决。  
  
A21：我认为核心数据库这种完全可以通过网络限制访问来源缓解风险，通过数据库的升级计划来修复漏洞。打补丁的成本有点高了。  
  
A22：工信部来现场检查，直接把线插到核心交换机做扫描，跳开防火墙了，不打补丁通不过检查的。核心数据库肯定是网络白名单的方式访问，担心的就是攻击是白名单来。  
  
A23：南北横向网络限制没通路，下游有漏洞才能下穿。数据库和应用肯定是两个区域网络隔离的。  
如果前提数据库做好账户口令和权限控制，没怎么见过远程rce，包括mssql、mysql、oracle。  
  
一般外部扫描我见过比较多的是服务识别和版本识别。  
  
A24：  
事实上攻击手法还是远古的  
sa/sa xp_cmdshell  
，打补丁是件困难的事，  
oracle  
的补丁都没那么好拿。  
问题症结还在安全跟业务或者开发的角度不同，拿  
log4j  
来说，升级应该是放在最后的，前面有控制先控制，  
waf  
挡一道解决外网风险，  
jar  
启动禁用  
lookup  
再加一道，最后再考虑补丁，哪种方法适合我觉得安全需要依据情况因地制宜。  
  
**0x2 群友分享**  
  
  
**【安全资讯】**  
  
  
[5项网络安全国家标准获批发布](http://mp.weixin.qq.com/s?__biz=MzkyMjM2NDY1NA==&mid=2247487716&idx=1&sn=ffa20f12534e893825932dc3926f30a1&chksm=c1f42f65f683a67318ad5cf8c9dbf9180b8f9a47e4e2cf6f54906717a81be2faef8bce79bdfc&mpshare=1&scene=21&srcid=0322XrqAwLM2T4CMKQAZEqHl&sharer_shareinfo=a926588fa129dd9318e253f232a0d167&sharer_shareinfo_first=b5996d588c1bbad2c7df20648ef7bfd5#wechat_redirect)  
  
  
  
[下架关停！未备案App下月起禁止安装，怎么办？](http://mp.weixin.qq.com/s?__biz=Mzg2MDE0MTQ1NA==&mid=2247505582&idx=1&sn=87ca55961057697b920e1227e6017fce&chksm=ce28744ff95ffd595ed164a0b4c4ea3796d2a9339f5cfdaa85a951fc05d81c5c1b6afcdc1f0c&mpshare=1&scene=21&srcid=0319vr5tyJoSTpODjPu1ZmeE&sharer_shareinfo=a2b113438f31dd766df82f9b8b988be4&sharer_shareinfo_first=a2b113438f31dd766df82f9b8b988be4#wechat_redirect)  
  
  
  
[国家互联网信息办公室公布《促进和规范数据跨境流动规定》](http://mp.weixin.qq.com/s?__biz=MzAwMjU0MjIyNw==&mid=2651472429&idx=1&sn=e7f2755e72df1a873d409e7408cf2821&chksm=81363dd1b641b4c78785a7653b59d670d9cf3457ad17ab3d81e57c680fcf10ec72d0e57b47e5&mpshare=1&scene=21&srcid=0322Y1dXpibcRqbcT8lG1qRR&sharer_shareinfo=fa9a4e9c0519f846c71c71b03204ad1d&sharer_shareinfo_first=fa9a4e9c0519f846c71c71b03204ad1d#wechat_redirect)  
  
  
  
[《银行保险机构数据安全管理办法》公开征求意见及答记者问](http://mp.weixin.qq.com/s?__biz=MzkyMTUwMjIwNA==&mid=2247492112&idx=1&sn=3d898009e1e82b3fd494cbdf6543f3d4&chksm=c1800a83f6f78395c91ce5d0cd7457597dc552b4d8668642da2e298ad10a6cd8eaa9f6b37985&mpshare=1&scene=21&srcid=03232bhc9ytBLaVirzGze3en&sharer_shareinfo=a91cf257a2d33a55bfdc3abbdb8ed775&sharer_shareinfo_first=f6b1b1c59d9fa378d54dc808c4f9345b#wechat_redirect)  
  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MDE0MTQ1NA==&mid=2247505582&idx=1&sn=87ca55961057697b920e1227e6017fce&chksm=ce28744ff95ffd595ed164a0b4c4ea3796d2a9339f5cfdaa85a951fc05d81c5c1b6afcdc1f0c&mpshare=1&scene=21&srcid=0319vr5tyJoSTpODjPu1ZmeE&sharer_shareinfo=a2b113438f31dd766df82f9b8b988be4&sharer_shareinfo_first=a2b113438f31dd766df82f9b8b988be4#wechat_redirect)  
  
重磅！  
GB/T 43697-2024《数据安全技术 数据分类分级规则》发布  
  
  
[国务院办公厅关于印发《扎实推进高水平对外开放更大力度吸引和利用外资行动方案》的通知](http://mp.weixin.qq.com/s?__biz=MzA4MDA0MzcwMA==&mid=2652673094&idx=1&sn=234786bdcf044667a401997a087ba9ae&chksm=84427f9db335f68b67eaf5912300644f7dcf3507e7738312bdf4676fef357f895d5aa59f9d19&mpshare=1&scene=21&srcid=0319NgvkzGzKCloCXa8E2dxl&sharer_shareinfo=dc979a0b806746d50154fa50b609a3fe&sharer_shareinfo_first=61cb9250a9c156b1cf955c9ea09d4979#wechat_redirect)  
  
  
  
[国家互联网信息办公室发布《数据出境安全评估申报指南（第二版）》和《个人信息出境标准合同备案指南（第二版）》](http://mp.weixin.qq.com/s?__biz=MzAwMjU0MjIyNw==&mid=2651472431&idx=1&sn=ae2aebb93009771b4e8330d321514d5e&chksm=81363dd3b641b4c5196443e7a0e769077c7b38a77243476b2649c0ea82b42408b5b4c3ea7c16&mpshare=1&scene=21&srcid=0322OTdpdeQC219oejAp7pgp&sharer_shareinfo=0395bf661427dac76b33bfd43d578a74&sharer_shareinfo_first=0395bf661427dac76b33bfd43d578a74#wechat_redirect)  
  
  
----------------------------------------------------------------------------  
‍‍‍  
  
【金融业企业安全建设实践群】和【企业安全建设实践群】每周讨论的精华话题会同步在本公众号推送（每周）。**根据话题整理的群周报完整版——每个话题甲方朋友们的****展开讨论内容——每周会上传知识星球**  
，方便大家查阅。  
  
**往期群周报：**  
  
[钓鱼邮件如何确定office附件宏的打开者身份？| 总第241周](http://mp.weixin.qq.com/s?__biz=MzI2MjQ1NTA4MA==&mid=2247491257&idx=1&sn=70c6a83f4dc721e3ce9b19fb969df700&chksm=ea4bb4fedd3c3de8bbb110d1000cff6aa3648794942d5deaca0a25462a6eb84343cb57dbdf93&scene=21#wechat_redirect)  
  
****  
  
[关于数据库加解密平台讨论及如何保证临时token的安全性？| 总第240周](http://mp.weixin.qq.com/s?__biz=MzI2MjQ1NTA4MA==&mid=2247491249&idx=1&sn=2e754e1b39dc3967cbff4483fe0f7c78&chksm=ea4bb4f6dd3c3de0bbde64ea16da65be65c8a70b141b5b4c771186be4da6f4d740ba1354cb15&scene=21#wechat_redirect)  
  
****  
  
[关于SOC关联分析暨从VPN问题看安全管理的策略与权衡的讨论 | 总第239周](http://mp.weixin.qq.com/s?__biz=MzI2MjQ1NTA4MA==&mid=2247491248&idx=1&sn=6a9f523de16e0a9d25a5577a14e4d341&chksm=ea4bb4f7dd3c3de1864f483795123d040aa3780829f163f29f12230d0784f63906dd56bbd42c&scene=21#wechat_redirect)  
  
## 如何进群？  
  
**如何下载群周报完整版？**  
  
**请见下图：******  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/yXsxtS2cfwbppZu5PBSictiaObD2Bnru4z5nSyfMrsqjPO0micwA8CsIDUxRb73kIPomrYtYpWuWqPwMU17LHAIpg/640?wx_fmt=jpeg "")  
  
  
