#  CVE-2024-21633 - " Apktool 中的任意文件写入漏洞"   
 Ots安全   2024-01-06 16:45  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/bL2iaicTYdZn7gtxSFZlfuCW6AdQib8Q1onbR0U2h9icP1eRO6wH0AcyJmqZ7USD0uOYncCYIH7ZEE8IicAOPxyb9IA/640?wx_fmt=gif "")  
  
CVE-2024-21633 是一个由 Apktool 引发的安全漏洞，该漏洞被识别为 CVE 标识符，CVSS 评分为 7.8。这个漏洞主要涉及到 Apktool 在解码过程中处理资源文件的方法，特别是当资源文件的名称被更改为可能导致路径遍历的字符串时。  
  
Apktool 是一种常用的 Android 应用程序反编译工具，它可以用来解码 APK 文件并将其重新打包。Apktool 的工作方式是将 APK 文件解码为可读的文件结构，然后可以对这些文件进行修改。这些修改可以包括添加或修改代码，更改资源等。然后，Apktool 将修改后的文件重新打包成 APK 文件。这种工具在 Android 应用程序的开发和逆向工程中非常有用。  
- 然而，Apktool 在处理资源文件的路径时存在一个安全问题。Apktool 根据资源文件的名称推断资源文件的输出路径，格式为 [output-dir]/res/[type]/[resource-name]+[extension of (resource-file)]  
。  
  
- 例如，路径为“res/raw/bar”的名为“foo”的资源通常会被提取到 res/raw/foo  
。  
  
- 然而，如果资源名称被更改为可能导致路径遍历的字符串（例如“../../../../../../../../../../../../tmp/poc”），那么“res/raw/bar”文件就会被放置在 Linux 系统中的 /tmp/poc  
 目录中。这个漏洞也存在于 Windows 中，也可能存在于 macOS 中，但后者尚未得到明确证实。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48tafZ8GZEcZsU5Dkp3CYPsgicWjlXXVibrBv2cxZJ5aKAq7eNHG2bQo0a407ysiaSSCKetGDNPUfIn3rUw/640?wx_fmt=png&from=appmsg "")  
  
  
这个漏洞允许攻击者写入或覆盖用户有权访问的任何文件。  
在某些情况下，这甚至可能扩展到关键系统文件，例如 shell 初始  
化文件或授权密钥。  
一个 APK 可能包含多达 65535 个原始资源，为攻击者提供了充分的机会暴力破解目标文件。  
  
解决这个问题的一种方法是限制 Apktool 只能在指定的目录中创建文件，而不能访问系统的其他部分。此外，还需要在处理资源文件名称时进行更严格的检查，以防止路径遍历攻击。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48tafZ8GZEcZsU5Dkp3CYPsgicWF9h1Y7jj5YJZWEPCKNtHmRicXFjGjGTU5PO3xXvbldETYbTUBFXxENQ/640?wx_fmt=png&from=appmsg "")  
### 影响  
- 攻击者可以写入/覆盖用户具有写入权限的任何文件（例如，在某些条件下* shell 初始化文件或授权密钥可以被覆盖）  
*用户名已知或 cwd 位于用户文件夹下（请注意，apk 可能包含 0xFFFF (65535 ）原始资源，这允许攻击者暴力破解他的目标文件）  
  
  
  
感谢您抽出  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycNnFvFYVgXoExRy0gqCkqvrAghf8KPXnwQaYq77HMsjcVka7kPcBDQw/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycd5KMTutPwNWA97H5MPISWXLTXp0ibK5LXCBAXX388gY0ibXhWOxoEKBA/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycU99fZEhvngeeAhFOvhTibttSplYbBpeeLZGgZt41El4icmrBibojkvLNw/640?wx_fmt=gif "")  
  
来阅读本文  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWge7Mibiad1tV0iaF8zSD5gzicbxDmfZCEL7vuOevN97CwUoUM5MLeKWibWlibSMwbpJ28lVg1yj1rQflyQ/640?wx_fmt=gif "")  
  
**点它，分享点赞在看都在这里**  
  
