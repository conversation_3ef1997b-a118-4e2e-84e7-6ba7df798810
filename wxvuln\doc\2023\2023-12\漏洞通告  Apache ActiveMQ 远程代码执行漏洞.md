#  漏洞通告 | Apache ActiveMQ 远程代码执行漏洞   
原创 微步情报局  微步在线研究响应中心   2023-11-30 09:44  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMKNkm4Pg1Ed6nv0proxQLEKJ2CUCIficfAwKfClJ84puialc9eER0oaibMn1FDUpibeK1t1YvgZcLYl3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
01 漏洞概况****  
  
  
  
Apache ActiveMQ 是美国阿帕奇（Apache）基金会的一套开源的消息中间件，它实现了 Java Message Service (JMS) 规范。作为一个消息中间件，它充当了应用程序之间的通信桥梁，允许不同的应用程序在分布式环境中进行可靠的异步通信。微步漏洞团队于2022年7月通过“X 漏洞奖励计划”获取到Apache ActiveMQ jolokia 远程代码执行漏洞情报。经过身份认证后的攻击者可通过发送HTTP请求，修改配置文件后写入恶意文件，进而完全控制主机。  
  
  
微步漏洞团队在获取该漏洞情报后  
联系官方修复，并获得官方致谢（  
CVE-2022-41678）。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMLzw4LB1sIiaqAettedfiaq9zuaCrByM7mSI05knF3zMLDlpHibiaSCBpw34OPmnupeGn9FNItdn49L3w/640?wx_fmt=png&from=appmsg "")  
  
该漏洞需要以下利用条件：**1.Web控制台可访问（默认端口8161）2.需要登录（通常结合弱口令/密码泄露）**  
**建议受影响的用户，根据以上利用条件，酌情处置。**  
  
02 漏洞处置优先级（VPT）  
  
  
  
**综合处置优先级：**  
**中**  
****  
  
<table><tbody style="visibility: visible;"><tr style="height: 23.3pt;visibility: visible;"><td width="123" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞编号</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">微步编号</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">XVE-2022-23035</span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="143" valign="top" rowspan="6" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞评估</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">危害评级</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><strong style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(181, 15, 26);">中危</span></strong><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">漏洞类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">RCE</span><br/></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;">公开程度</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">PoC已公开</span><span style="font-size: 14px;"></span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">利用条件</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">需要低权限</span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-family: 黑体;font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">交互要求</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">0-click</span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">威胁类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">远程</span></p></td></tr><tr style="height:26.0500pt;"><td width="143" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>利用情报</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">微步已捕获攻击行为</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">暂无</span></p></td></tr><tr><td width="143" valign="top" rowspan="4" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>影响产品</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">产品名称</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">Apache ActiveMQ</span></section><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="mso-spacerun:&#39;yes&#39;;font-family:微软雅黑;mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;"><span style="font-family:微软雅黑;"></span></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">受影响版本</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">5.17.0 &lt;= version &lt; 5.17.4</span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">5.16.0 &lt;= version &lt; 5.16.6</span><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">影响范围</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">万级</span></p></td></tr><tr style="height:26.7000pt;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有无修复补丁</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有<br/></span></p></td></tr></tbody></table>  
### 03 漏洞复现 04 修复方案 1、官方修复方案：Apache官方已发布安全更新，建议尽快前往官网（https://activemq.apache.org/）升级至安全版本：(1)5.16.x 系列更新至5.16.6（安全）以上(2)5.17.x 系列更新至5.17.4（安全）以上(3)5.18.x系列和6.0.x系列不受影响2、临时修复方案：(1)开启Web控制台认证，并修改默认口令；(2)使用防护类设备对相关资产进行防护；(3)对Apache ActiveMQ Web端口，尤其是/api/jolokia/路径，强化访问控制策略。05 微步在线产品侧支持情况  （1）微步在线威胁感知平台TDP已支持检测，规则ID为S3100029224。（2）微步在线安全情报网关OneSIG已支持防护，规则ID为3100029224。（3）微步在线安全情报社区X已支持资产查询，规则为app=Apache-ActiveMQ。06 时间线 2022.07.21 微步“X漏洞奖励计划”获取该漏洞相关情报2023.11.28 漏洞公开2023.11.30 微步发布报告---End---微步漏洞情报订阅服务微步提供漏洞情报订阅服务，精准、高效助力企业漏洞运营提供高价值漏洞情报，具备及时、准确、全面和可操作性，帮助企业高效应对漏洞应急与日常运营难题；可实现对高威胁漏洞提前掌握，以最快的效率解决信息差问题，缩短漏洞运营MTTR；提供漏洞完整的技术细节，更贴近用户漏洞处置的落地；将漏洞与威胁事件库、APT组织和黑产团伙攻击大数据、网络空间测绘等结合，对漏洞的实际风险进行持续动态更新。X 漏洞奖励计划“X漏洞奖励计划”是微步X情报社区推出的一款针对未公开漏洞的奖励计划，我们鼓励白帽子提交挖掘到的0day漏洞，并给予白帽子可观的奖励。我们期望通过该计划与白帽子共同努力，提升0day防御能力，守护数字世界安全。  
  
