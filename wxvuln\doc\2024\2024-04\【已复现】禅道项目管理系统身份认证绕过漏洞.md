#  【已复现】禅道项目管理系统身份认证绕过漏洞   
长亭应急  黑伞安全   2024-04-26 13:33  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicR0t2WZK7d25gHvvYbb4BRlJy0r83XNr0u7DVy1hchcl7vZz66kfg0Bm2oWWrEtNJvicKiamO1vtT7g/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
禅道项目管理系统（以下简称禅道系统）是一个开源的项目管理软件，专门设计用于敏捷开发，提供了需求管理、任务管理、缺陷跟踪、测试管理和项目绩效等功能，帮助团队更高效地进行项目规划和执行。2024年4月，互联网披露禅道系统存在身份认证绕过的漏洞情报。经分析，确认该漏洞利用简单，并可能在绕过权限后利用后台其他漏洞实现远程代码执行，建议受影响的客户尽快修复漏洞。  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因禅道系统某些API设计为通过特定的鉴权函数进行验证，但在实际实现中，这个鉴权函数在鉴权失败后并不中断请求，而是仅返回一个错误标志，这个返回值在后续没有被适当处理。此外，该系统在处理某些API时未能有效检查用户身份，允许未认证的用户执行某些操作，从而绕过鉴权机制。漏洞影响该漏洞允许未授权用户绕过正常认证流程，创建管理员账户并接管后台，进而可能利用其他后台漏洞实现远程代码执行，严重威胁系统安全，可能导致数据泄露、勒索或更广泛的网络攻击。检测工具 Detection 02X-POC远程检测工具检测方法：xpoc -r 422 -t http://xpoc.org工具获取方式：https://github.com/chaitin/xpochttps://stack.chaitin.com/tool/detail/1036影响范围 Affects 03v16.x <= 禅道 < v18.12 （开源版）v6.x <= 禅道 < v8.12 （企业版）v3.x <= 禅道 < v4.12 （旗舰版）解决方案 Solution 04临时缓解方案如非必要，不要将禅道系统放置在公网上。或通过网络ACL策略限制访问来源，例如只允许来自特定IP地址或地址段的访问请求。升级修复方案官方已发布新版本修复漏洞，建议尽快访问官网（https://www.zentao.net/）或联系官方售后支持获取新版本安装包，升级至不受影响的安全版本。漏洞复现 Reproduction 05获取合法session访问需要鉴权的API  
**产品支持**  
  
   
Support   
  
  
  
**06**  
云图：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。洞鉴：以自定义POC的形式支持该漏洞的原理检测。  
  
  
**时间线**  
  
   
Timeline   
  
  
  
**07**  
4月25月 长亭科技监测到漏洞情报4月25月 长亭应急响应实验室漏洞分析与复现4月26日 长亭安全应急响应中心发布通告  
参考资料：  
  
[1].https://www.zentao.net/  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
  
#2024漏洞风险提示  
12  
  
  
#2024漏洞风险提示 · 目录  
  
  
上一篇  
【已复现】Primeton EOS Platform jmx反序列化致远程代码执行漏洞  
  
  
