#  实战中遇到的一些莫名奇妙的逻辑漏洞   
点击关注👉  马哥网络安全   2024-04-04 12:01  
  
# 前言  
  
真实案例，文中所写漏洞现已经被修复，厚码分享也是安全起见，请各位大佬见谅哈！  
# 案例一：奇怪的找回密码方式  
  
某次漏洞挖掘的过程中，遇到某单位的登录框，按以往的流程测试了一下发现并没有挖掘出漏洞，在我悻悻地点开找回密码的功能并填入了基础信息之后，我发现他的业务流程与常见的安全的找回密码的方式并没有什么区别，都是需要接收到手机验证码或者邮箱验证码或者回答正确安全问题才能进行下一步重置密码。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwrPPwdwiaW191D48QHlQghHM9krsnZ8GjL2qjE1zHAIZ7MTd9n6Qw98GA/640?wx_fmt=png&from=appmsg "")  
  
但是敏锐的我，发现了右上角存在一个验证方式不可用？的功能，我点开发现竟然直接跳到重置认证方式里面去了：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwrGjaNn77vjE7CjvS6XFNlib79qAJa6NKdWkY2fcmA2p7g9a2K88GbqhQ/640?wx_fmt=png&from=appmsg "")  
  
然后我把原号主的安全问题重置之后，更令我意外的是直接跳转到重置密码的步骤了（令人哭笑不得2333）：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwrYuEV038IXPUEaK1tFuAFNwPoKBagThjpZBmT4AHsQQtdSLCTD3mmRQ/640?wx_fmt=png&from=appmsg "")  
  
就这样我就修改了原号主的密码成功进入了系统进行后续安全测试：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5Mwr3X8T7cDPKFEOM1MdibFkaEDiblJhy2kDjtzM5oDiaSATJHZj0j5lm9veg/640?wx_fmt=png&from=appmsg "")  
# 案例二：且丘世专  
  
在某次SRC挖掘过程中，我发现了一个站点可以进行任意用户注册，在往下继续挖掘的过程中，发现了一个比较有趣的越权。  
  
当我成功登录的时候，我抓到了一个数据包大致如下(已脱敏)：  
```
GET /XXX?id=%E4%B8%94%E4%B8%98%E4%B8%96%E4%B8%93 HTTP/1.1
Host: XXX
Accept: /
......
Connection: close
```  
  
我敏锐的直觉告诉我这里很有可能存在越权的漏洞，但是这个id看起来实在确实很抽象，经验丰富的师傅可能一眼就看出来是URL编码，我们拿去解码一番：  
  
发现解码出来我们的id居然是：且丘世专  
  
有趣，经过我的一番FUZZ后我发现有下面的对应规则：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwrEaWWlX0urvcdgdibpmBR5x85KrSOpLsYccOiaIma23lh10AALkriaEjEA/640?wx_fmt=png&from=appmsg "")  
  
所以且丘世专  
对应着我的id也就是4863。  
  
然后我将对应的id值进行遍历一下就越权查看了大量用户的信息：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwrWd1KfcQTNbWSiaNNmNZibfekE9yyxxelYxlrhmQnsXsC4qyugmzagxpA/640?wx_fmt=png&from=appmsg "")  
# 案例三：1 + 1 + 1 ？  
  
在另外一次SRC挖掘中，同样是找回密码功能处，我选择直接重置admin账户的密码  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5MwreOHMP61Gy8srMS3j0pPiaaZVpXruvbLDD6RgHnLUIdldHib1XCNlKd0w/640?wx_fmt=png&from=appmsg "")  
  
 该找回密码的逻辑是输入正确的密保问题，于是我随便输入了三个2来测试：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5Mwribpgraj5f5cc0Vl9o6U1HKyhVWPxaNeTkpFqGQgwfnyGZatIXM0b5Nw/640?wx_fmt=png&from=appmsg "")  
  
 确实很安全，那么3个1呢？  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5Mwr0bzFh9vPtyHT5YbVSIl6r3Ue6FmpgSb3BnpVI410oSKmpBtGVhVrrg/640?wx_fmt=png&from=appmsg "")  
  
居然直接进入了重置密码的界面。。。。。。  
  
由于是admin账户，我没有贸然去修改密码，后续厂商也是承认了该漏洞。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/UkV8WB2qYAk4pIGujhOgpMDED51s5Mwrvdqia3m6MLU6rFgm5BWQItyuibYIgGibzEzeAFNIqvBiajJCpRibgzOmrsA/640?wx_fmt=png&from=appmsg "")  
# 总结  
  
有些时候有些漏洞确实非常莫名其妙，本文其实并没有太深的技术支持，但是也算是一种思路的补充吧！  
  
```
文章转自先知社区，原作者1572282084220041（侵删）
原文链接：https://xz.aliyun.com/t/13790?time__1311=mqmxnQKGwx9AD%2FD0Dx2DUEbfbPAKGOUWBeD
```  
  
   
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/INa3lxHH4I2aV3zCmfiaj4cXeQ2HQd6s53wJS36HYI65ib48fujDK8najfWiahicsljzsdT3dfVS8HHyxaviaSd8g2g/640?wxfrom=5&wx_lazy=1&wx_fmt=png&wx_co=1 "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/b96CibCt70iaZVWLqKWYqToBBoLTeNc2Nmly4DBgnj2omUrnWukGfmCCQtViaLiajKHp4cCUM9FWg8s14UORhccUBg/640?wx_fmt=gif&wxfrom=5&wx_lazy=1 "")  
  
  
[● 1748页CTF竞赛入门指南，有点牛！](http://mp.weixin.qq.com/s?__biz=MzkxMzMyNzMyMA==&mid=2247553347&idx=1&sn=229d9be6cce405fd34bdaba4729cb0ae&chksm=c17d04f4f60a8de2201898bbe32e0f1fdc6ecbc571819f0c7175e2cd24f252e8c385ce5fd243&scene=21#wechat_redirect)  
  
  
  
[● 网安人们，可以开始考虑新的出路了！](http://mp.weixin.qq.com/s?__biz=MzkxMzMyNzMyMA==&mid=2247552544&idx=1&sn=0df94e7d9f61b945b0f260ba42b26199&chksm=c17d1b97f60a92815f26035de41375efe0c06c9587388f420cb726ed4e483d30155ca60e9b4d&scene=21#wechat_redirect)  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/UkV8WB2qYAkZUWAdRpPOBGYEK7UyRbNsayG2YZrib6HdH9e1tUzvFsbI8vDTmcAcFJicDibol6p94E3PH6qpmM4GQ/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
