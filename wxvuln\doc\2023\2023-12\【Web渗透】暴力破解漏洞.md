#  【Web渗透】暴力破解漏洞   
Whoami  晨曦安全团队   2023-12-18 23:59  
  
- “**暴力破解**  
”是一种攻击手段，在web攻击中，一般会使用这种手段对应用系统的认证信息进行获取。其过程就是使用大量的认证信息在认证接口进行尝试登录，直到得到正确的结果。  
  
- 为了提高效率，暴力破解一般会使用带有字典的工具进行自动化操作。理论上来说，大多数系统都是可以被暴力破解的，只要攻击者有足够强大的计算能力和时间，所以判断一个系统是否存在暴力破解漏洞，其条件也不是绝对的。  
  
- 我们说一个web应用系统存在暴力破解漏洞，一般是指该web应用系统没有采用或者采用了比较比较弱的认证安全策略，导致其被暴力破解的“可能性”变的比较高。这里的认证安全策略，包括：   
  
1. 是否要求用户设置复杂的密码；  
  
1. 是否每次认证都使用安全的验证码（想想你买火车票时输的验证码）或者手机otp（一次新密码）；  
  
1. 是否对尝试登录的行为进行判断和限制（如：连续5次错误登录，进行账号锁定或IP地址锁定等）；  
  
1. 是否采用了双因素认证；...等等。  
  
- 千万不要小看暴力破解漏洞，往往这种简单粗暴的攻击方式带来的效果时超出预期的。  
  
从来没有哪个时代的黑客像今天一样热衷于猜解密码 ---奥斯特洛夫斯基  
  
###### 个人理解  
  
- 暴力破解，顾名思义，暴力+破解，是不是可以理解成傻瓜式的一个个的去尝试、去猜解；只不过是利用一系列的手段，字典也好、收集的信息也好，“暴力”的使用方法进行破解你想要的东西（如账号、密码等，这只是暴力破解中的一种而已）。  
  
###### 原理  
  
- 两个字：**撞库**  
，三个字：**枚举法**  
。  
  
- 攻击者借助计算机的高速计算不停枚举所有可能的用户名和密码，直到尝试出正确的组合，成功登录系统。  
  
- 理论上，**只要字典足够大，破解总是会成功的**  
。  
  
###### 危害  
  
  
目的主机账号猜解成功后，攻击者通常利用该账号做如下操作：  
  
- **攻击者通过泄露账户非法登录主机，盗取用户数据；**  
  
- 攻击者通过泄露账户非法登录主机，注入病毒程序，执行挖矿或勒索，如Goblelmposter勒索病毒、蠕虫；  
  
- 攻击者通过泄露账户非法登录主机，作为跳板机攻击其他主机。  
  
###### 测试流程  
  
-  **流程**  
   
  
1.  确认登录接口的脆弱性   
  
```
确认目标是否存在暴力破解的漏洞。（确认被暴力破解的“可能性”）。
比如：尝试登录-抓包-观察验证元素和response信息，判断是否存在被暴力破解的可能。
```  
  
   
1.  对字典进行优化   
  
根据实际情况对字典进行优化，提高暴破过程的效率。  
  
   
1.  工具自动化操作   
  
配置自动化工具（比如线程、超时时间、重试次数等），进行自动化操作。  
  
   
-  **字典优化技巧**  
   
  
1.  根据注册提示信息优化   
  
对目标站点进行注册，搞清楚账号密码的一些限制，比如目标站点要求密码是6位数字以上、字母数字组合，则可以按照此优化字典(去掉不符合要求的密码)  
  
   
1.  如果暴破的是管理后台，往往这种系统的管理员是admin/administrator/root的几率比较高，可以使用这三个账号随便一个密码，尝试登录，观察返回的结果，确定用户名。   
  
例如：  
  
   
  
输入xxx/yyy返回 “用户名或密码错误”；  
  
   
  
输入admin/yyy返回 “密码错误”；  
  
   
  
则基本可以确定用户名是admin；  
  
   
  
因此只对密码进行暴破即可，提高效率。  
  
   
  
###### 验证码  
  
-  **认证流程**  
   
  
1.  客户端request登录页面，后台生成验证码：  
（1）后台使用算法生成图片，并将图片response给客户端；  
（2）同时将算法生成的值全局赋值存到session中；   
  
1.  校验验证码：  
（1）客户端将认证信息和验证码一同提交；  
（2）后台对提交的验证码与session里面的进行比较；   
  
1.  客户端重新刷新页面，再次生成新的验证码：  
（1）验证码算法中一般包含随机函数，所以每次刷新都会改变。   
  
   
-  **不安全的验证码-on client-常见问题**  
   
  
1. 使用前端js实现验证（纸老虎）；  
  
1. 将验证码在cookie中泄露，容易被获取；  
  
1. 将验证码在前端源代码中泄露，容易被获取。  
  
   
-  **不安全的验证码-on server-常见问题**  
   
  
1. 验证码在后台不过期，导致可以长期被使用；  
  
1. 验证码校验不合格，逻辑出现问题；  
  
1. 验证码设计的太过简单和有规律，容易被猜解。  
  
   
  
###### token  
  
-  **一个简单的token实例**  
   
  
```
// 生成一个token，以当前微妙时间+一个5位的前缀
function set_token(){
	if(isset($_SESSION['token'])){
		unset($_SESSION['token']);
	}
	$_SESSION['token'] = str_replace('.','',uniqid(mt_rand(10000,99999),true));
}
```  
  
   
-  **一般的做法**  
：   
  
1. 将token以 “type = ‘hiden’ ” 的形式输出在表单中；  
  
1. 在提交认证的时候一起提交，并在后台对其进行校验。  
  
   
-  **但**  
，由于其token值输出在了前端源码中，容易被获取，因此也就失去了防暴力破解的意义。一般token在防止CSRF上有较好的功效。   
  
###### 防范  
  
- 设计安全的验证码（安全带流程 复杂而又可用的图形）  
  
- 对认真错误的提交进行计数并给出限制，比如连续5次密码错误，锁定俩小时；  
  
- 必要情况下，使用双因素认证。  
  
###### 优缺点  
  
- 优点：破解成功率100%（理论上来说，利用密码全集尝试是这样的）；  
  
- 非常耗时间，甚至有些不人性化。  
  
但是在常规工作和具体测试环境中从来不会这么搞，大都是通过从全集挑选出一些常用的或者常见的密码进行组合的方式来尝试破解，这种成功率也很高，又称”字典攻击“。是否能成功破解的条件：就是密码字典里边一定包含有一个正确的密码，否则再大再多的密码都失败。  
  
   
  
**注**  
：所以说，破解的速度和成功率关键不是取决于你的技术水平，而是取决于**[字典的密码耦合度]**  
问题。  
  
