#  实战 | 前端加密的用户名枚举漏洞怎么从低危变为中高危？   
 渗透安全团队   2023-12-30 23:16  
  
## 0x01 阅读须知  
  
**此文所提供的信息只为网络安全人员对自己所负责的网站、服务器等（包括但不限于）进行检测或维护参考，未经授权请勿利用文章中的技术资料对任何计算机系统进行入侵操作。利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责。本文所提供的工具仅用于学习，禁止用于其他！！！**  
  
0x02 漏洞描述  
  
**获取验证码时，无图形验证码校验，破解前端编码后，可批量爆破用户名，获取用户姓名、编号与个人手机号**  
  
0x03 漏洞详情  
  
**1、 app打开后获取验证码时抓包，发现请求数据被加密**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UAAgTXic6IBuE2OOibian0AgplfJmhvlmUIde27ibexjep1IxMO1b0JoH4A/640?wx_fmt=png&from=appmsg "")  
  
2、  
**从手机登录页点击新账号激活时，抓包，在响应包发现其url地址，使用电脑浏览器访问，可以直接访问，则手机端测试转为pc端测试。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UOP1WBtZI2BNwc9dibHZV6bQ6fIHdohbZtsyuAlMYlNKIoO0UU4EkcBA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UAvdYhApurANSvBT8exeHIIA3GUbKkJkZaXUsbnCyc5nNbZJj1MD8hw/640?wx_fmt=png&from=appmsg "")  
  
**3、**  
**查看前端js代码，发现名为security.js的文件**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UZtPibn0xq7p6mDEp7yhy6HrhBRkVtdl0JxoMOrHEjQACbriaVf7pibHmQ/640?wx_fmt=png&from=appmsg "")  
  
**4、 发现31行代码就是第1步的请求数据**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UvVxgK4cWWGC4uaeqqnKOTR3PzhHUK8WvPwmtGzeOH9le13ibXRBicF5g/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961Uicp2VYZ7ZetgqCtibT1VTIQicZyzVPu9qvpor8zfZFTuyIquPjO1z0vJg/640?wx_fmt=png&from=appmsg "")  
  
**5、向上追溯变量，发现变量传递为data->allParam->signCode->postParam**![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961U1ZGT7wuxdxBKGibNrEq21UrSkGTYA3CXKhwFibj7QF9CGXxc3Mnm4KRw/640?wx_fmt=png&from=appmsg "")  
  
  
**6、 而data变量在html代码里搜索此url的代码也能找到**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UFGicpVzWialLWr1YsqHozoqXtQ1Ixic5U7Ors29ZPhS6HHfdqh36HJvWg/640?wx_fmt=png&from=appmsg "")  
  
**7、由上述代码可知，后续会将json格式转为字符串，即data=’
loginName=base64encode(#loginName)’,尝试用常规的base64编码，结果失败**  
  
**8、 又发现一个名为securityencode.js的文件里有base64编码的方法，由此看出，此处自定义了base64编码，所以常规的base64不行。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961U9uu5DHLSnAj8DMXteVmUAeaPibFLeqP2GR0mDZzv9PvDWXbyMgNEOng/640?wx_fmt=png&from=appmsg "")  
  
**9、调用第8点js中的编码方法，和第5步的逻辑，成功将明文用户名编码成第1步中请求包的加密值，以此逻辑编写python代码，批量枚举用户名**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UD2u7LsibrTtHGbN9padDba1oEaSoFgXPPrrxjlkAmiculo99yReWs69g/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UiaVMePQErKic3yMMJic6PFBQCHluFG76hjXU8QUZTlMLQND32Dxey5t7w/640?wx_fmt=png&from=appmsg "")  
  
10、  
   
**使用爆出的用户名去首页登录，到输入验证码的地方，可以在请求查看到明文手机号和authenWorkNo号**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961UkXDXgVsDOVBJZrsQSSAK8OKUic2WL24FNcoannBTTpa6bzwzp4OPDiaQ/640?wx_fmt=png&from=appmsg "")  
  
**可以直接pc端访问此url**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhdmw1hZsKSX5MNOO1c961Ufa3ffYhcumJOay2cscHT2ZMLAsEaL3ia4yha2aJkicsaVnrpMQW2tEJg/640?wx_fmt=png&from=appmsg "")  
  
**因此，整个用户名枚举漏洞转变为可批量获取用户姓名+手机号的漏洞，提升为中危漏洞**  
  
0x03 修复建议  
  
**1、 增加图形验证码校验**  
  
**2、 对客户端ip进行频次限制**  
  
**3、 将手机号传参在请求包加密或隐藏**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.09738205945672873&wxfrom=5&wx_lazy=1&tp=wxpic "")  
  
**翻到文章最底部点击“阅读原文”下载链接**  
  
  
**★**  
  
**付费圈子**  
  
  
**欢 迎 加 入 星 球 ！**  
  
**代码审计+免杀+渗透学习资源+各种资料文档+各种工具+付费会员**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.4447566002908574&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**进成员内部群**  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/pPVXCo8Wd8AQHAyOTgM5sLrvP6qiboXljGWG0uOdvcNR8Qw5QJLxSVrbFds2j7MxExOz1ozb9ZoYwR68leoLdAg/640?wx_fmt=jpeg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.09738205945672873&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**星球的最近主题和星球内部工具一些展示******  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/pPVXCo8Wd8Doq0iczyRiaBfhTQyfzqSGuia4lfHfazabEKr2EDe7sGVoxUhLrNRA4FbI1yef6IkWdmzxvZrTiaJncg/640?wx_fmt=jpeg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8BmE6FAA8Bq7H9GZIRt1xYZpmYNWxrrzolt71FtX5HyM03H0cxkiaYelv7ZSajLtibEdBXUpCibdItXw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8ADSxxicsBmvhX9yBIPibyJTWnDpqropKaIKtZQE3B9ZpgttJuibibCht1jXkNY7tUhLxJRdU6gibnrn0w/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8DKZcqe8mOKY1OQN5yfOaD5MpGk0JkyWcDKZvqqTWL0YKO6fmC56kSpcKicxEjK0cCu8fG3mLFLeEg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8CKksEIzZyEb3tEFGzGYSXfribrG4jKOkRKGKYb7zk7MTNZPT6Wp3bLd2BPhuFHddIL6sqrg1d2qHQ/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8D0bS8ibc3XhFcDYkVusFvc3c6onthQpPGZn4v32rpOp7CeFiamGdeC7JBk0mGVsiciazLp3z0SIJAtnQ/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8B96heXWOIseicx7lYZcN8KRN8xTiaOibRiaHVP4weL4mxd0gyaWSuTIVJhBRdBmWXjibmcfes6qR1w49w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8DKZcqe8mOKY1OQN5yfOaD5MpGk0JkyWcDKZvqqTWL0YKO6fmC56kSpcKicxEjK0cCu8fG3mLFLeEg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8AqNwoQuOBy9yePOpO5Kr6aHIxj7d0ibfAuPx9fAempAoH9JfIgX4nKzCwDyhQzPrRIx4upyw5yT4Q/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
****  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.4447566002908574&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**加入安全交流群**  
  
  

								[                ](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489372&idx=1&sn=5e14ba5fa59059fb1ee405e56ef90d40&chksm=c175eaf3f60263e5ef5415a8a9fc134f0890fdb9c25ab956116d17109baf98b3bd6bed572a2d&scene=21#wechat_redirect)  

			                  
  
  
**关 注 有 礼**  
  
  
  
关注下方公众号回复“  
666  
”可以领取一套领取黑客成长秘籍  
  
![](https://mmbiz.qpic.cn/mmbiz_png/XOPdGZ2MYOeSsicAgIUNHtMib9a69NOWXw1A7mgRqqiat1SycQ0b6e5mBqC0pVJ3oicrQnCTh4gqMGiaKUPicTsUc4Tw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
 还在等什么？赶紧点击下方名片关注学习吧！![](https://mmbiz.qpic.cn/mmbiz_png/XOPdGZ2MYOeSsicAgIUNHtMib9a69NOWXw1A7mgRqqiat1SycQ0b6e5mBqC0pVJ3oicrQnCTh4gqMGiaKUPicTsUc4Tw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ndicuTO22p6ibN1yF91ZicoggaJJZX3vQ77Vhx81O5GRyfuQoBRjpaUyLOErsSo8PwNYlT1XzZ6fbwQuXBRKf4j3Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
  
**推荐阅读**  
  
  
  
[干货｜史上最全一句话木马](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489259&idx=1&sn=b268701409ad4e8785cd5ebc23176fc8&chksm=c175eb44f60262527120100bd353b3316948928bd7f44cf9b6a49f89d5ffafad88c6f1522226&scene=21#wechat_redirect)  
  
  
  
[干货 | CS绕过vultr特征检测修改算法](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247486980&idx=1&sn=6d65ae57f03bd32fddb37d7055e5ac8e&chksm=c175f3abf6027abdad06009b2fe964e79f2ca60701ae806b451c18845c656c12b9948670dcbc&scene=21#wechat_redirect)  
  
  
  
[实战 | 用中国人写的红队服务器搞一次内网穿透练习](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247488628&idx=1&sn=ff2c617cccc00fe262ed9610c790fe0e&chksm=c175e9dbf60260cd0e67439304c822d28d510f1e332867e78a07d631ab27143309d14e27e53f&scene=21#wechat_redirect)  
  
  
  
[实战 | 渗透某培训平台经历](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247488613&idx=1&sn=12884f3d196ac4f5c262a587590d516d&chksm=c175e9caf60260dcc0d5d81a560025d548c61fda975d02237d344fd79adc77ac592e7e562939&scene=21#wechat_redirect)  
  
  
  
[实战 | 一次曲折的钓鱼溯源反制](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489278&idx=1&sn=5347fdbf7bbeb3fd37865e191163763f&chksm=c175eb51f602624777fb84e7928bb4fa45c30f35e27f3d66fc563ed97fa3c16ff06d172b868c&scene=21#wechat_redirect)  
  
  
  
**免责声明**  
  
由于传播、利用本公众号渗透安全团队所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，公众号渗透安全团队及作者不为**此**  
承担任何责任，一旦造成后果请自行承担！如有侵权烦请告知，我们会立即删除并致歉。谢谢！  
  
好文分享收藏赞一下最美点在看哦  
  
