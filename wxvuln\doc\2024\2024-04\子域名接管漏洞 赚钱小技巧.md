#  子域名接管漏洞: 赚钱小技巧   
 迪哥讲事   2024-03-31 22:00  
  
# 本文将介绍实际接管“易受攻击”的子域的过程。  
  
我注意到，多个漏洞赏金计划开始明确接收子域名接管漏洞。同时，说实话，奖励也相当丰厚：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMoiao3VDhqQ3QdMJEDR4n8iaNm8FwiaUk4OibQLyYfJibLrx5EmTCgBsuns7lpDYCFdia9Wsz8XNtABwz1A/640?wx_fmt=png&from=appmsg "")  
  
  
尽管许多漏洞赏金计划很慷慨，但它们要求您提供确实可能进行接管的概念验证。  
  
在本文中，我将解释如何验证子域名接管是否可能，并为您提供**step-by-step instructions for PoC creation **-逐步POC构造（或标准操作程序）说明。  
  
正如您可能知道的那样，子域名接管通常（但并非必然）与云服务提供商相关联 - 本文将针对三个最容易发生接管的云服务提供商进行解释。  
> 更新：  
> 请参考“can-i-takeover-xyz”作为子域名接管概念验证的主要项目。  
> 本文仅充当文档扩展，提供了屏幕截图和更深入的解释。  
> https://github.com/EdOverflow/can-i-take-over-xyz  
  
  
在我们开始之前，您应该熟悉子域名接管的基本原理。GitHub 上有许多工具可用于提供子域名接管验证：  
- aquatone  
  
https://github.com/michenriksen/aquatone  
  
- SubOver  
  
https://github.com/Ice3man543/SubOver  
  
- subjack  
  
https://github.com/haccer/subjack  
  
尽管这些工具提供了关于可能子域名接管的良好启发，但由于各个云服务提供商的几项限制，它们有时会产生误报。  
  
在下面的各章节中，为每个域名提供人工验证片段。不同类型域名的命名惯例如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMoiao3VDhqQ3QdMJEDR4n8iaNuzCjtdUGZM7ibdhBVlg4XvicScAQwTtHHSkXC655DGttMeUsqvrIgquA/640?wx_fmt=png&from=appmsg "")  
  
### Amazon S3   
  
Amazon S3 是一个与桶的概念配合使用的存储服务。桶是存储的逻辑单位。创建桶后，为其生成一个唯一的子域名。听起来很熟悉吗？  
  
Amazon S3 基本上采用与CloudFront相同的虚拟主机概念。您可以在这里阅读有关S3虚拟主机的信息。  
  
https://docs.aws.amazon.com/AmazonS3/latest/dev/VirtualHosting.html  
  
我只想提一件事。S3桶可能被配置为网站托管来提供静态内容，就像Web服务器一样。  
  
如果规范的域名中包含有website，则指定的S3桶将被指定为网站托管。我怀疑非网站和已配置为网站的桶会由单独的负载均衡器处理，因此它们彼此之间不起作用。唯一的区别将在创建桶时，如果需要，需要设置正确的网站标志。  
#### Verification  
  
CNAME记录应该看起来像这样：  
```
```  
  
我使用以下正则表达式来匹配正确的规范名称：  
```
```  
  
要验证是否可能发生子域名接管，请运行：  
```
```  
  
I use httpie instead of curl. You should too.  
  
httpie: https://httpie.org/  
#### Takeover  
  
（假设您已创建了 AWS 账户。）  
  
好的，以下是每个步骤的分点翻译：  
1. 前往 S3 面板  
  
1. 点击“创建桶”  
  
1. 将桶名称设置为源域名（即您想要接管的域名）  
  
1. 点击多次“下一步”以完成  
  
1. 打开创建的桶  
  
1. 点击“上传”  
  
1. 选择将用于 PoC 的文件（HTML 或 TXT 文件）。我建议将其命名为与 index.html 不同的名称；您可以使用 poc（不带扩展名）  
  
1. 在权限选项卡中选择“授予此对象（们）公共读取访问权限”  
  
1. 上传后，选择文件，点击“更多”->“更改元数据”  
  
1. 点击“添加元数据”，选择“内容类型”，值应反映文档的类型。如果是 HTML，则选择 text/html，等等。  
  
1. （可选）如果桶已配置为网站  
  
1. 切换到“属性”选项卡  
  
1. 点击“静态网站托管”  
  
1. 选择“使用此桶托管网站”  
  
1. 作为索引，选择您上传的文件  
  
1. 点击“保存”  
  
如果一切顺利，没有任何错误，恭喜您，域名接管已完成。文件将存在于相应的 URL 路径上。  
  
如果您上传了名为 poc 的文件，那么它将位于  
  
http://sub.example.com/poc  
  
等路径下。  
### GitHub Pages   
  
GitHub提供了免费的Web托管服务，使用的是他们的GitHub Pages项目。这种Web托管通常用于项目文档、技术博客或开源项目的支持网页。GitHub Pages支持自定义域名，除了默认域名以外，还可以使用.github.io域名。  
#### Verification  
  
CNAME记录应该看起来像这样：  
```
```  
  
我使用以下正则表达式来检查正确的云前置的域名别名：  
```
```  
  
要验证是否可能发生子域接管，请运行：  
```
```  
#### Takeover  
  
（假设您已创建了 GitHub 账户。）  
1. 前往新存储库页面  
  
1. 将存储库名称设置为规范域名（即，来自CNAME记录的{something}.github.io）  
  
1. 点击“创建存储库”  
  
1. 使用git将内容推送到新创建的存储库。GitHub本身提供了实现此操作的步骤  
  
1. 切换到“设置”选项卡  
  
1. 在GitHub Pages部分，选择主分支作为来源  
  
1. 点击“保存”  
  
1. 保存后，将“自定义域”设置为源域名（即，您想要接管的域名）  
  
1. 点击“保存”  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMoiao3VDhqQ3QdMJEDR4n8iaNRhXJ2biaPoyxhciarGU5EpznooascYeev9qcEkyTTWk7ajGNp1nZplWQ/640?wx_fmt=png&from=appmsg "")  
  
  
如果一切顺利，没有任何错误，恭喜，接管已完成。  
### Heroku   
  
eroku 是一家流行的平台即服务提供商。  
  
在我们的上下文中，它具有与其他云提供商相同的虚拟主机概念。  
  
各种 *.herokudns.com 子域名会响应相同的一组 A 记录。  
  
HTTP 主机头对于正确的域名解析很重要（就像其他提供商一样）。  
  
还有一种可能性是上传自己的证书以在自定义域上工作（例如，GitHub Pages 不支持此功能，因此您无法在设置自定义域时启用 HTTPS）。  
#### Verification  
  
CNAME记录应该类似于以下内容：  
```
```  
  
我使用以下正则表达式来检查正确的云前置的域名别名：  
```
```  
  
要验证是否可能发生子域名接管，请运行：  
```
```  
#### Takeover  
  
（假设您已创建了Heroku账户。）  
1. 打开新的Heroku应用。  
  
https://dashboard.heroku.com/new-app  
  
1. 选择名称和地区（对接管没有影响）。  
  
1. 使用git将PoC应用推送到Heroku。该过程在“部署”选项卡中有描述。  
  
1. 切换到“设置”选项卡。  
  
1. 滚动到“域名和证书”。  
  
1. 点击“添加域名”。  
  
1. 提供您想要接管的域名，然后点击“保存更改”。  
  
1. 设置可能需要一些时间来传播。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMoiao3VDhqQ3QdMJEDR4n8iaNXEPZH639f2ibBteLs6C9q0Lh56zYpTptNyvCKLOxYu8mjqdfeGe5wLA/640?wx_fmt=png&from=appmsg "")  
  
### Readme.io   
  
Readme.io 是一个用于托管文档的流行服务。事实证明，他们还提供自定义域支持，更重要的是，可以使用 readme.io 域进行子域接管。  
#### Verification  
  
CNAME记录应该类似于以下内容：  
```
```  
  
我使用以下正则表达式来检查正确的云前置的域名别名  
```
```  
  
要验证是否可能发生子域名接管，请运行：  
```
```  
#### Takeover  
  
（假设您已创建了Readme.io账户）  
1. 前往仪表板。  
  
https://dash.readme.io/  
  
1. 设置项目名称及其子域名。子域名无需与您尝试接管的域名匹配。  
  
1. 在左侧边栏中，转到“常规设置” -> “自定义域”。  
  
1. 将自定义域设置为您想要接管的域名。  
  
1. 点击“保存”。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMoiao3VDhqQ3QdMJEDR4n8iaNcknng2oPBW0ESiafSz4SKTqj088C3fzfjj0vOichJZ6LK5xw3KHzfSMw/640?wx_fmt=png&from=appmsg "")  
  
### 提交报告   
  
在最终报告中，不要忘记提及所有的技术部分。您还可以指定概括的概念验证创建过程。  
  
关于风险，我建议您阅读我另一篇帖子，并在报告中提供链接。由于子域接管是一个相对较新的攻击向量，您还应该为受影响方提供缓解策略。  
  
希望本文为您提供了对带有自定义域名的云服务内部工作原理的良好理解。如果CNAME记录指向它们，许多其他云服务（在这里找到的一个）可以用于概念验证创建。  
  
该过程应类似于上面解释的一个服务的过程。  
  
如果你是一个长期主义者，欢迎加入我的知识星球，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款前面有同学问我有没优惠券，这里发放100张100元的优惠券,用完今年不再发放  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7N5nMaJbtnMPVw96ZcVbWfp6SGDicUaGZyrWOM67xP8Ot3ftyqOybMqbj1005WvMNbDJO0hOWkCaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
