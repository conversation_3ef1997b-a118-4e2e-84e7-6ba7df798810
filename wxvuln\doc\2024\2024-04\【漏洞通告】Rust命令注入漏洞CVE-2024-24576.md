#  【漏洞通告】Rust命令注入漏洞CVE-2024-24576   
深瞳漏洞实验室  深信服千里目安全技术中心   2024-04-11 16:53  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALQkyzxYIEOaFtc8r2lICmPnzAiaicjyLibRk6CmRpgdAMjcbLPrgdyUIQg/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
Rust命令注入漏洞(CVE-2024-24576)  
  
**组件名称：**  
  
Rust  
  
**影响范围：**  
  
Rust < 1.77.2  
  
**漏洞类型：**  
  
远程代码执行  
  
**利用条件：**  
  
1、用户认证：否  
  
2、前置条件：默认配置  
  
3、触发方式：远程/本地均可利用  
  
**综合评价：**  
  
<综合评定利用难度>：容易，无需授权即可远程代码执行。  
  
<综合评定威胁等级>：严重，能造成远程代码执行。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALJLfQJxm4dTLBE6nCvdGcwdDACd7D3CCaiaFBQ4eC3Kf5Xm6LicLXlN7g/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
Rust 是一种开源的系统编程语言，它专注于安全性、并发性和性能，旨在提供内存安全和线程安全的同时，保持高性能和控制力。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALJLfQJxm4dTLBE6nCvdGcwdDACd7D3CCaiaFBQ4eC3Kf5Xm6LicLXlN7g/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2024年4月11日，深瞳漏洞实验室监测到一则Rust组件存在命令注入漏洞的信息，漏洞编号：CVE-2024-24576，漏洞威胁等级：严重。  
  
该漏洞是由于Rust 转义Windows上批处理文件的参数时存在错误，**攻击者可利用该漏洞在未经授权的情况下，构造恶意数据执行命令注入攻击，最终获取服务器最高权限。**  
  
  
  
**影响范围**  
  
目前受影响的Rust版本：  
  
Rust < 1.77.2  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALJLfQJxm4dTLBE6nCvdGcwdDACd7D3CCaiaFBQ4eC3Kf5Xm6LicLXlN7g/640?wx_fmt=gif&from=appmsg "")  
  
**如何检测组件系统版本**  
  
  
在Windows cmd 或 Powershell中执行命令：  
  
rustc --version  
  
回显即为所安装的rust版本  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALJLfQJxm4dTLBE6nCvdGcwdDACd7D3CCaiaFBQ4eC3Kf5Xm6LicLXlN7g/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
当前官方已发布最新版本，建议受影响的用户及时更新升级到最新版本。链接如下：  
  
https://github.com/rust-lang/rust  
  
  
**参考链接**  
  
  
https://github.com/rust-lang/rust/security/advisories/GHSA-q455-m56c-85mh  
  
  
**时间轴**  
  
  
  
**2024/4/11**  
  
深瞳漏洞实验室监测到Rust命令注入漏洞(CVE-2024-24576)攻击信息。  
  
  
**2024/4/11**  
  
深瞳漏洞实验室发布漏洞通告。  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALzFUTlicbqNBZZN9KGO2atd1HAlXtjLd7RtKHjKI9g2qOYVaLZ4iavia2Q/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5y3kKRF0QgoCjFUVSicucQALdjEcqZEtQys8QATgeib46IKUYzao5wVLTg29uh5O0Gb9XZtEQAcyaLA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
  
