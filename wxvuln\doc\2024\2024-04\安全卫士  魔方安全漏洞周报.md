#  安全卫士 | 魔方安全漏洞周报   
原创 ASM&CVM都找  魔方安全   2024-04-22 18:30  
  
**4.15 - 4.21**  
  
**漏洞周报**  
  
**本期速览**  
  
  
  
NEWS   
  
🔹 泛微Ecology OA发布安全公告  
  
🔹   
SmartBI  
发布新补丁  
  
🔹   
Oracle发布安全公告  
  
🔹 Vmware发布安全通告  
  
🔹 Palo Alto Networks PAN-OS GlobalProtect 命令注入漏洞  
  
🔹 Apache Solr Operator信息泄露漏洞  
  
🔹 Apache Kafka ACL配置错误漏洞  
  
🔹 IP-guard WebServer权限绕过漏洞  
  
🔹 kkFileView任意文件上传导致远程执行漏洞  
  
🔹 Jenkins CLI客户端中的Terrapin SSH漏洞通告  
  
魔方安全提醒您：保护企业网络空间资产安全至关重要！以下漏洞请重点关注。  
  
  
  
  
**泛微****发布安全公告**  
  
  
  
  
近日，泛微Ecology OA发布了安全补丁以修复安全漏洞。  
  
泛微Ecology OA是一款企业级办公自动化软件，是泛微网络科技有限公司开发的一款业务流程管理(BPM)和协同办公(Collaboration)平台。  
  
  
**影响版本**  
  
- **泛微 Ecology 9.x 补丁版本号 ＜ v10.61**  
  
- **泛微 Ecology 8.x 补丁版本号 ＜ v10.61**  
  
  
  
**补丁详情**  
  
**补丁类型：**安全更新  
  
**补丁等级：**高危  
  
**补丁详情：**修复安全漏洞  
  
**修复建议**  
  
**官方已发布了产品更新补丁，相关用户可下载安装。**  
  
****  
**参考链接：**  
  
https://www.weaver.com.cn/cs/securityDownload.html#  
  
  
  
  
**SmartBI官方发布新补丁**  
  
  
  
  
近日，SmartBI官方发布了新补丁，修复了存在的安全漏洞，漏洞编号：暂无。  
  
SmartBI是广州思迈特软件有限公司的核心产品，是一款基于Web的企业级BI平台，可帮助企业快速构建数据仪表板、报表、数据分析和数据挖掘等功能，以便更好地管理和利用企业数据。  
  
  
**影响版本**  
  
- **SmartBI v7及以下系列**  
  
- **SmartBI v8**  
  
- **SmartBI v9及以上系列**  
  
  
  
**补丁详情**  
  
**补丁类型：**安全加固  
  
**补丁等级：**高危  
  
**补丁详情：**修复某种特定情况下越权漏洞  
  
**修复建议**  
  
**官方已发布了相关安全补丁，受影响用户可使用官方补丁工具包下载、安装补丁。**  
  
****  
**参考链接：**  
  
https://www.smartbi.com.cn/patchinfo  
  
  
  
  
**Oracle发布安全公告**  
  
  
  
  
近日，Oracle发布了安全公告，修复了多个产品中的漏洞，具体查看Oracl安全公告页面，漏洞详情中列出了较为重要的漏洞。  
  
  
**影响版本**  
  
Oracle WebLogic Server、MySQL Server等，具体查看Oracle安全公告页面。  
  
**漏洞详情**  
  
**漏洞名称：**Oracle WebLogic Server信息泄露漏洞  
  
**漏洞类型：**信息泄露  
  
**漏洞等级：**高危  
  
**CVE编号：**CVE-2024-21006  
  
**漏洞详情：**  
  
Oracle WebLogic Server 中存在信息泄露漏洞。未经身份验证的远程攻击者可通过该漏洞获取敏感信息。  
  
**漏洞名称：**Oracle WebLogic Server信息泄露漏洞  
  
**漏洞类型：**信息泄露  
  
**漏洞等级：**高危  
  
**CVE编号：**CVE-2024-21007  
  
**漏洞详情：**  
  
Oracle WebLogic Server 中存在信息泄露漏洞。未经身份验证的远程攻击者可通过该漏洞获取敏感信息。  
  
**修复建议**  
  
**目前官方已发布相关安全补丁，受影响用户可及时下载更新。**  
  
****  
**参考链接：**  
  
https://www.oracle.com/security-alerts/cpuapr2024.html  
  
  
  
  
**Vmware发布安全通告**  
  
  
  
  
近日，Vmware发布了安全通告，修复存在的安全漏洞，漏洞编号：CVE-2024-22246、CVE-2024-22247、CVE-2024-22248。  
  
SD-WAN 是可对 WAN 连接进行虚拟化的基于软件的网络技术应用。SD-WAN 将网络软件服务与底层硬件分离，以创建虚拟化网络叠加。SD-WAN 使位于任何位置的用户能够连接到位于任何位置的应用，从而提供灵活性、简便性、性能、安全性和云规模。它还具有易于部署、集中管理和控制以及有保证的应用性能等优势。  
  
  
**影响版本**  
  
**CVE-2024-22246**  
- VMware SD-WAN (Edge) 5.x ＜ 5.0.1  
  
- VMware SD-WAN (Edge) 4.5.x ＜ 4.5.1  
  
**CVE-2024-22247**  
- VMware SD-WAN (Edge) 5.x(未安装KB97391)  
  
- VMware SD-WAN (Edge) 4.5.x(未安装KB97391)  
  
**CVE-2024-22248**  
- VMware SD-WAN (Orchestrator) 5.x ＜ 5.0.1  
  
  
  
**漏洞详情**  
  
**漏洞名称：**SD-WAN Edge中的未经身份验证的命令注入漏洞  
  
**漏洞类型：**命令注入  
  
**漏洞等级：**高危  
  
**CVE编号：**CVE-2024-22246  
  
**漏洞详情：**  
  
在激活过程中能够本地访问边缘路由器UI的恶意攻击者可能能够执行命令注入攻击，从而完全控制路由器。  
  
**漏洞名称：**SD-WAN Edge中缺少身份验证和保护机制漏洞  
  
**漏洞类型：**其他  
  
**漏洞等级：**中危  
  
**CVE编号：**CVE-2024-22247  
  
**漏洞详情：**  
  
在激活期间对SD-WAN Edge设备具有物理访问权限的恶意行为者可能会利用此漏洞访问BIOS配置。此外，恶意参与者可能能够利用所配置的默认靴子优先级。  
  
**漏洞名称：**SD-WAN Orchestrator中的开放重定向漏洞  
  
**漏洞类型：**重定向  
  
**漏洞等级：**高危  
  
**CVE编号：**CVE-2024-22248  
  
**漏洞详情：**  
  
由于不正确的路径处理导致敏感信息泄露，恶意攻击者可能能够将受害者重定向到攻击者控制的域。  
  
  
**修复建议**  
  
**目前官方已发布相关安全补丁、安全版本，受影响用户可下载安装。**  
  
****  
**下载链接：**  
  
https://www.vmware.com/security/advisories/VMSA-2024-0008.html  
  
  
**Palo Alto Networks PAN-OS GlobalProtect 命令注入漏洞**  
  
**漏洞类型：**命令执行  
  
**漏洞等级：**高危  
  
**CVE编号：**CVE-2024-3400  
  
**漏洞详情：**  
  
Palo Alto Networks PAN-OS 软件的 GlobalProtect 功能中针对特定 PAN-OS 版本和不同功能配置下，未经身份验证的攻击者可能利用此漏洞在防火墙上以root权限执行任意代码。  
  
**修复建议**  
  
**1、官方已发布安全版本，受影响用户可升级。**  
  
  
**下载链接：**  
  
https://security.paloaltonetworks.com/CVE-2024-3400  
  
  
**2、缓解措施**  
  
拥有Threat Prevention订阅的客户可以使用Threat ID 95187（在应用程序和威胁内容版本8833-8682及更高版本中提供）阻止针对此漏洞的攻击。要应用威胁ID 95187，客户必须确保已将漏洞保护应用于其GlobalProtect接口，以防止在其设备上利用此问题。请参阅https：//live.paloaltonetworks.com/t5/globalprotect-articles/applying-vulnerability-protection-to-globalprotect-interfaces/ta-p/340184了解更多信息。  
  
  
**3、禁用设备直至升级**  
  
  
**Apache Solr Operator信息泄露漏洞**  
  
**漏洞类型：**信息泄露  
  
**漏洞等级：**中危  
  
**CVE编号：**CVE-2024-31391  
  
**漏洞详情：**  
  
Apache Solr Operator中将敏感信息插入日志文件。默认情况下，操作员将用于这些探测器的Solr API配置为免于身份验证，但用户也可以特别请求在探测器端点上需要身份验证。 当其中一个探测失败时，如果正在使用身份验证，Solr操作员将创建一个包含“k8s-oper”帐户的用户名和密码的Kubernetes“事件”。  
  
**修复建议**  
  
**官方已发布安全版本，受影响用户可下载安装。**  
  
  
**下载链接：**  
  
https://solr.apache.org/operator/  
  
  
**Apache Kafka ACL配置错误漏洞**  
  
**漏洞类型：**访问控制不当  
  
**漏洞等级：**中危  
  
**CVE编号：**CVE-2024-27309  
  
**漏洞详情：**  
  
Apache Kafka受影响版本中，当Apache Kafka集群从 ZooKeeper 模式迁移到 KRaft模式时，如果管理员删除ACL（访问控制列表），并且与删除的ACL相关联的资源在删除后仍有两个或更多其他ACL关联时，Kafka会将该资源视为在删除后仅具有一个与其关联的 ACL，导致ACL可能无法正确执行，该漏洞可能导致访问控制失效或未授权访问、拒绝服务攻击、合法用户无法正常访问等。漏洞具体影响取决于配置的 ACL。如果在迁移期间仅配置了 ALLOW ACL，则影响将仅限于可用性影响。如果配置了 DENY ACL，则影响可能包括机密性和完整性影响，因为在迁移过程中DENY ACL可能会被忽略。  
  
**修复建议**  
  
**官方已发布安全版本，受影响用户可下载安装。**  
  
  
**下载链接：**  
  
https://github.com/apache/kafka/tags  
  
  
**IP-guard WebServer权限绕过漏洞**  
  
**漏洞类型：**权限绕过  
  
**漏洞等级：**高危  
  
**CVE编号：暂无**  
  
**漏洞详情：**  
  
由于访问控制存在逻辑错误，导致可以绕过权限验证，调用后台接口进行任意文件读取、删除。攻击者可利用该漏洞读取数据库配置信息，进而接管数据库。  
  
**修复建议**  
  
**1、升级至安全版本或联系官方获取修复方式。**  
  
****  
**下载链接：**  
  
https://www.ip-guard.net/  
  
  
**2、缓解措施**  
  
1）使用防护类设备对相关资产进行防护  
  
2）避免将IP-guard WebServer暴露在互联网  
  
  
**kkFileView任意文件上传导致远程执行漏洞**  
  
**漏洞类型：**任意文件上传  
  
**漏洞等级：**高危  
  
**CVE编号：暂无**  
  
**漏洞详情：**  
  
在v4.2.0版本的更新中，由于前台上传功能在处理压缩包时，从仅获取文件名改为获取文件名及其目录，导致出现了Zip Slip漏洞。这使得攻击者可上传包含恶意代码的压缩包并覆盖系统文件，随后通过调用这些被覆盖的文件实现远程代码执行。  
  
**修复建议**  
  
**1、目前官方暂未发布安全版本，仅在开发分支中修复**  
  
****  
**参考链接：**  
  
https://github.com/kekingcn/kkFileView  
  
  
**2、缓解措施**  
  
1）开启 file.upload.disable=true 参数，禁用首页的文件上传功能，关闭演示入口来规避问题。  
  
2）如非必要，不要将该系统放置在公网上或限制白名单访问  
  
  
**Jenkins CLI客户端中的Terrapin SSH漏洞通告**  
  
**漏洞类型：**其它  
  
**漏洞等级：**中危  
  
**CVE编号：**CVE-2023-48795  
  
**漏洞详情：**  
  
Jenkins 2.451及更早版本、LTS 2.440.2及更早版本中的CLI客户端（jenkins-cli.jar）捆绑了易受CVE-2023-48795（Terrapin攻击）影响的Apache MINA SSHD库版本。 此漏洞允许中间机器攻击者降低SSH连接的安全性(这只会在使用-ssh连接模式时影响Jenkins CLI客户端，这不是默认模式)。  
  
**修复建议**  
  
**官方已发布了部分插件的安全版本，受影响用户可下载安装。**  
  
  
**下载链接：**  
  
https://www.jenkins.io/  
  
  
**检测方式**  
   
  
魔方外部攻击面管理系统EASM（SaaS）、魔方网络资产攻击面管理系统CAASM、魔方漏洞管理系统CVM均已收录以上漏洞影响资产的指纹识别规则，详情请登录系统查看。  
  
  
**END**  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/ZTu8ibzLzefGpU43QwiaerAvqxvEFhek3MJibdwc7Kib2w4WGECnOw5WW6ibCT7A4ndpU1xiaxib3gGZbtd3kWsSwasuw/640?wx_fmt=gif "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZTu8ibzLzefGpU43QwiaerAvqxvEFhek3Mc29vZ1DcGV6dTK3emdIQB7saV7StoV7AakFnNbicRQeTqne77mIbsOA/640?wx_fmt=png "")  
  
  
