#  【漏洞通告】OpenSSH命令注入漏洞CVE-2023-51385   
深瞳漏洞实验室  深信服千里目安全技术中心   2023-12-26 16:20  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJib2nYrw8rK73ibpsdtenKLicFdAdNJySDz5FypjDBZ6HZvkibalwFeX4JQ/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
OpenSSH命令注入漏洞(CVE-2023-51385)  
  
**组件名称：**  
  
OpenSSH  
  
**影响范围：**  
  
OpenSSH < 9.6  
  
**漏洞类型：**  
  
命令注入  
  
**利用条件：**  
  
1、用户认证：是  
  
2、前置条件：默认配置  
  
3、触发方式：远程  
  
**综合评价：**  
  
<综合评定利用难度>：较难，需要主动引用或拉取带有shell元字符的主机名。  
  
<综合评定威胁等级>：高危，能造成远程代码执行。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJPWFfq5ZcNlau8GB5zFWicbSNDLMK4gDzBMMSTO7ZS6F7UB9tApfqDsg/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
OpenSSH 是 SSH （Secure SHell） 协议的免费开源实现。SSH协议族可以用来进行远程控制，或在计算机之间传送文件。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJPWFfq5ZcNlau8GB5zFWicbSNDLMK4gDzBMMSTO7ZS6F7UB9tApfqDsg/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2023年12月26日，深瞳漏洞实验室监测到一则OpenSSH组件存在命令注入漏洞的信息，漏洞编号：CVE-2023-51385，漏洞威胁等级：高危。  
  
该漏洞是由于OpenSSH处理主机名称中的特殊符号不严谨，如果用户名或主机名中含有shell元字符（如 | ‘ “等），并且ssh_config中ProxyCommand、LocalCommand指令或”match exec”谓词通过%u、%h或类似的扩展标记引用用户或主机名时，可能会发生命令注入。**攻击者可利用该漏洞在获得权限或钓鱼攻击的情况下，构造恶意数据执行命令注入攻击，最终获取服务器最高权限。**  
  
  
**影响范围**  
  
目前受影响的OpenSSH版本：  
  
OpenSSH<9.6  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJPWFfq5ZcNlau8GB5zFWicbSNDLMK4gDzBMMSTO7ZS6F7UB9tApfqDsg/640?wx_fmt=gif&from=appmsg "")  
  
**修复建议**  
  
1.如何检测组件系统版本  
  
    执行命令  
  
    ssh -V  
  
    回显即为OpenSSH 版本  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJ3A9VuSwzI2H4QGmlwp8mg40NL0GiclRyv88gKnSoSuTTVNg6VW3aLKw/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJPWFfq5ZcNlau8GB5zFWicbSNDLMK4gDzBMMSTO7ZS6F7UB9tApfqDsg/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
当前官方已发布最新版本，建议受影响的用户及时更新升级到最新版本。链接如下：  
  
https://www.openssh.com/openbsd.html  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJPWFfq5ZcNlau8GB5zFWicbSNDLMK4gDzBMMSTO7ZS6F7UB9tApfqDsg/640?wx_fmt=gif&from=appmsg "")  
  
**深信服解决方案**  
  
  
**1.风险资产发现**  
  
支持对OpenSSH的主动检测，可**批量检出**业务场景中该事件的受影响资产情况，相关产品如下：  
  
**【深信服主机安全检测响应平台CWPP】**已发布资产检测方案。  
  
**【深信服云镜YJ】**已发布资产检测方案。  
  
**【****深信服漏洞评估工具TSS****】**已发布资产检测方案。  
  
  
****  
**2.漏洞主动扫描**  
  
支持对OpenSSH命令注入漏洞(CVE-2023-51385)的主动扫描，可**批量快速检出**业务场景中是否存在**漏洞风险**，相关产品如下：  
  
**【****深信服云镜YJ****】**预计2023年12月31日发布扫描方案。  
  
**【深信****服漏洞评估工具TSS****】**已发布扫描方案。  
  
**【****深信服安全托管服务MSS****】**已发布  
扫描方案。（需要具备TSS组件能力）。  
  
**【****深信服安全检测与响应平台XDR****】**预计2023年12月31日发布扫描方案。（需要具备云镜组件能力）。  
  
  
**参考链接**  
  
  
https://vin01.github.io/piptagole/ssh/security/openssh/libssh/remote-code-execution/2023/12/20/openssh-proxycommand-libssh-rce.html  
  
  
**时间轴**  
  
  
  
**2023/12/25**  
  
2023/12/25  深瞳漏洞实验室监测到OpenSSH命令注入漏洞(CVE-2023-51385)攻击信息。  
  
  
**2023/12/26**  
  
深瞳漏洞实验室发布漏洞通告。  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJqUybLfHrm2ATLd1GIcAwsoRw8KOONETlBNZugr7IMGh3U3jibQcicV0A/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5yLmNnNhqK6VHwuyrSLibvpJ8zPfuibGawtSicocFvdyPFIicKARkg4MvfYhyORzACWJ8DWooxh3ZzaaQ/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
