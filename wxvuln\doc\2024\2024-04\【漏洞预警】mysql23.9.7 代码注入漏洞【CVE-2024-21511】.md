#  【漏洞预警】mysql2<3.9.7 代码注入漏洞【CVE-2024-21511】   
cexlife  飓风网络安全   2024-04-24 22:43  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu025WOeXJDD9ib9emKB3QQRyVAS6ToMQ1JTpyzprsibpc69euQMa6ib11Jh0qOibjmRRzWD4hxjyaTe4icA/640?wx_fmt=png&from=appmsg "")  
****  
**漏洞描述:**mysql2是用于操作 MySQL 数据库的高性能Node.js库，可兼容 Node MySQL API、并提供预编译语句、扩展编码等功能。受影响版本的 readCodeFor 函数在调用 readDateTimeString 函数处理日期时拼接时区参数 timezone，导致代码注入漏洞。攻击者可构造恶意的时区参数（如：`'); payload//` ）在mysql2客户端中远程执行任意代码。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu025WOeXJDD9ib9emKB3QQRyV7X5AR6l4GBpmPeykGgaRGTXfqnjaoMFnWX2apibPIVqSLOwUibqqwAzw/640?wx_fmt=png&from=appmsg "")  
  
**影响范围:**mysql2[0.0.1, 3.9.7)**修复方案:**升级mysql2到 3.9.7 或更高版本**参考链接:**https://github.com/sidorares/node-mysql2/commit/7d4b098c7e29d5a6cb9eac2633bfcc2f0f1db713  
  
