#  CNNVD关于Fortinet FortiClient代码注入漏洞的通报   
 网安百色   2024-04-13 19:32  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/1QIbxKfhZo6TLA19pviaCFfbrwwfDkd81KlLEPjVUhNmpUTv82EJhu2QnczPmf7nU0UicVQhD3icJZp2vicGaWur0w/640?wx_fmt=gif "")  
  
  
  
近日，国家信息安全漏洞库（CNNVD）收到关于Fortinet FortiClient 代码注入漏洞(CNNVD-202404-1054、CVE-2023-45590)情况的报送。未经身份验证的攻击者可通过诱导用户访问恶意网站来触发该漏洞从而导致任意代码执行。Fortinet FortiClientLinux 7.2.0版本、7.0.6至7.0.10版本和7.0.3至7.0.4版本均受此漏洞影响。目前，Fortinet官方已发布新版本修复了该漏洞，建议用户及时确认产品版本，尽快采取修补措施。  
  
**一、漏洞介绍**  
  
Fortinet FortiClient是美国飞塔（Fortinet）公司的一套移动终端安全解决方案。该方案与FortiGate防火墙设备连接时可提供IPsec和SSL加密、广域网优化、终端合规和双因子认证等功能。未经身份验证的攻击者可通过诱导FortiClientLinux用户访问恶意网站来触发该漏洞，从而导致任意代码执行。  
  
**二、危害影响**  
  
Fortinet FortiClientLinux 7.2.0版本、7.0.6至7.0.10版本和7.0.3至7.0.4版本  
均受此漏洞影响。  
  
**三、修复建议**  
  
目前，Fortinet官方已发布新版本修复了该漏洞，建议用户及时确认产品版本，尽快采取修补措施。官方参考链接：  
  
https://www.fortiguard.com/psirt/FG-IR-23-087  
  
本通报由CNNVD技术支撑单位——北京华云安信息技术有限公司、西安交大捷普网络科技有限公司、北京时代新威信息技术有限公司等技术支撑单位提供支持。  
  
CNNVD将继续跟踪上述漏洞的相关情况，及时发布相关信息。如有需要，可与CNNVD联系。联系方式: <EMAIL>  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/1QIbxKfhZo6M60aLu6MNdy20VjcnyaGECz7d9mYhdbclWg7wibJsickPUrnmNyFcvsjSYUqq5OPVPEXfW1SwkXCw/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/1QIbxKfhZo57Spb4ibrib8VUZd2ibdF9wHbvr4RwYJ4H2z6571icFIdSZXIpNH2YfW16ETwHh3ict3gtpW3W2fJqDmw/640?wx_fmt=gif "")  
  
长按添加关注，为您保驾护航！  
  
  
