#  支付类漏洞挖掘技巧   
中铁13层打工人  迪哥讲事   2024-04-05 22:18  
  
## 目录  
- 前言：  
  
- 支付逻辑漏洞成因：  
  
- 支付逻辑漏洞挖掘技巧：  
  
- 一、更改支付金额  
  
- 二、更改支付状态  
  
- 三、修改支付类型  
  
- 四、更改订单信息  
  
- 五、更改数量实现优惠支付  
  
- 六、重复支付，突破限购  
  
- 七、优惠券多次使用  
  
- 八、遍历隐藏或者下架优惠id获取优惠链接  
  
- 九、利用小数点精度四舍五入  
  
支付类逻辑漏洞在漏洞挖掘中是常常出现的问题之一。此类漏洞挖掘思路多，奖励高，是炙手可热的漏洞。此篇文章主要围绕挖掘支付逻辑漏洞时的一些思路分享。  
## 前言：  
  
支付类逻辑漏洞在漏洞挖掘中是常常出现的问题之一。此类漏洞挖掘思路多，奖励高，是炙手可热的漏洞。此篇文章主要围绕挖掘支付逻辑漏洞时的一些思路分享。  
## 支付逻辑漏洞成因：  
  
支付漏洞可能由以下原因造成：  
1. 前端验证不充分：在前端页面上，没有进行足够的验证和限制，使得用户可以通过修改页面元素或发送自定义请求来篡改支付金额、支付类型、支付状态等。  
  
1. 客户端数据不可信：客户端（如移动应用）在进行支付时，没有对传输的数据进行完整性验证和加密，导致恶意用户可以直接修改数据包中的支付金额、订单号等与订单有关的参数。  
  
1. 服务器端  
验证不严格：支付请求在到达服务器端时，没有进行足够的验证和校验，使得攻击者能够更改支付相关参数并绕过服务器端的验证机制。  
  
1. 不安全的存储和传输：支付金额数据在存储或传输过程中未经适当的加密保护，导致黑客可以窃取或篡改数据。  
  
## 支付逻辑漏洞挖掘技巧：  
  
在实际漏洞挖掘中，一般最先尝试的就是更改数据包发包内容，可以直接修改支付金额、更改支付状态、更改支付类型、更改提交订单支付的时候其中的订单信息等等，当然也会有一些新奇的功能点可以测试。这些在测试中会遇到的操作可以分为以下几类：  
#### 一、更改支付金额  
  
在支付流程中，可以修改支付价格的步骤有很多，包括订购、确认信息、付款等。在涉及到价格的步骤中都可以尝试修改，如果网站在某一环节存在逻辑上的漏洞，就可以利用该漏洞对支付价格进行修改。可以直接修改提交订单中的价格字段，一般可尝试0.01，1.00，1等  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObSa9KGXZHGjtTTq3ndHVJUNMVSbQkUC9upuBV0T5PIsW8NojxYABgCg/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
#### 二、更改支付状态  
  
在测试中有的时候订单得支付状态是由用户提交订单时的某个数据包参数决定的，服务端通过支付状态判断订单支付与否，这时我们可以尝试找到这个参数（可以通过正常支付订单的数据包进行对比），对支付状态进行修改。或者还有一种情况是通过检查订单是否支付，这个时候可以通过抓取已支付的订单数据包将其中的订单编号改为未支付的编号，实现绕过。  
  
1、直接修改为已支付状态  
  
2、修改未支付的订单号为已支付订单号  
#### 三、修改支付类型  
  
通常在提交订单付款时，这里的type一般是对支付方式的判断，可能会存在开发人员测试的时候遗留的无需支付的type值，根据支付方式判断支付与否。可以通过  
fuzz  
特定值去实现绕过。比如比较常见的值0（这里需要结合实际进行测试不同的处理方式type值不同），可以实现不需要付款订单就会自动生成。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUOb1Thhq3UPItibibDicKs0UiaUqjaClr4YniaGicOTYpUjDgurics9eVV9DqFkg/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
#### 四、更改订单信息  
  
服务端只检查支付是否完成，并没有确认订单金额与银行支付金额是否相同，过分信任客户端提交的数据。此时可以通过替换支付订单号、更换商品id的方式，来完成花少钱买更贵的东西。同时生成两个订单号，一个贵的一个便宜，首先支付便宜的，银行往回返回的时候，替换订单号，然后就可以完成两个订单的同时支付。  
  
常见位置在生成订单、生成支付链接等。  
  
1、修改商品编号  
  
直接在生成的订单中替换商品编号。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObsgOSGf0kOicppSfUuRq4ELqdDrpZLkZtYhSqHQWc6IlK82esaVCuYYQ/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
2、修改订单号  
  
将金额不同的订单进行替换，可以支付一个金额较少的订单，然后将订单号修改为金额较大的订单，少付实际金额。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUOb5x60uFrQia9KCtNIibeeNSsbmDObzibClASc7QBKiaOUboO1TrVuibvmBnw/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
3、越权使用他人优惠券、越权使用他人积分等  
#### 五、更改数量实现优惠支付  
  
支付金额是由购买数量乘以商品单价决定的，这时我们在数据包中修改购买数量，将其修改为负数或者小数，如果站点后台对此没有进行过滤，就有可能存在支付漏洞。  
  
1、将正常的数量值修改至最小值0.01，可以实现低价购买。比如：原价300修改修量为0.01后实付金额变为3。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUOb2ibsb8t6Ugib1GOA8X06kiakMeBJgfWvYI12wEx5ykiatzYLxiccrHvzQLA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
2、未对负数做检验的还可以将数量改为负数。（这里需要注意，因为后端大部分会校验不允许实付金额小于0或者0.01等，所以有的时候要想实现订单成功生成需要结合实际修改价格）  
  
生成订单时有参数表示商品数量，修改为-1  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObc0fGvicnTj9qL4OA2EicJp7Jb4hGQzzT7s6m0g76GZdqqY4PVdrjT90w/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
修改数量为-1后会发现，此时金额为负数。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObRPdvyicqmK4tPnuW2I5MsxItwskCIlictMbXQX0WycCiazjUpeUVpomqw/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
在提交订单支付的时候，为保证支付成功需要修改金额。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObtbucBvxkiajG478pmjwRBqRbLlW1rr3Bx1YMWhZE7eqUxj1hDA3OM4A/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
3、对数量没有做负数校验的时候也可以巧用负数抵消实现0元购  
  
在计算价格时，没有对负数进行验证，通过修改某个商品数量为-1实现与1的抵消实现0元购。  
  
同时购买两件商品，修改两件商品其中价格低的商品的金额为负数，实现价格的抵消，低价购买商品。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObsKXBTpFxttdkq5DacmvDSBhWoFt0dvozmHKSwRHicI6Iphiap449ibibCQ/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
4、手动增加订单中商品相关的多个参数以达到少付多买的目的。  
  
有的时候在提交订单时抓取数据包可以看到只有一套商品的信息，尝试多添加几套同样的参数订单是否会有变化。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObfQvM3BL6PZiaomcuJoKU5wC0KeXGzLibwwNmrTqM4fVdqJqE2yc9r0MA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
尝试在提交订单的时候多添加几个此类参数  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObfr4cr6UyYPt3tXKCbLhC2icibIzXxQrh2WTiah3OrRBh3geGHXx3Nk1mA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
提交订单实际支付金额未变仍是一个商品的价格，但是实际套餐已经变成了四个。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObvcP9kgMacQYPzZ5TZKFhsjstKeEs2qY3Vq7QibcGH0WsKVHm372DdBg/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
#### 六、重复支付，突破限购  
  
在支付系统中，服务端没有做好相关验证，比如订单状态被错误更新或者未更新，未对订单多重提交进行校验。那么就可以并发订单实现优惠订单多次提交。需要注意的是这里有的时候会根据实际支付订单判断，并发了多个订单也可能只有一个优惠订单可以正常支付。  
  
并发订单，多台设备同时提交优惠订单。  
  
常见于限购，一个账号仅许购买一次等  
  
1、限制一个优惠订单时直接并发生成多个优惠订单  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUObqiccHqrxu5Cx85fvMKGmokdt7VMJMca0RwzA8wm6wiaGOCsIOUyRkl9A/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
2、使用多台设备、多个浏览器、多种支付方式（wx、支付宝等）购买优惠订单  
  
常见于购买会员，会员第一个月往往会有优惠价。生成一个优惠订单后不支付，打开多个设备或者虚拟器设备，同时提交生成优惠订单，再分别支付，有的时候会发现会员截至日期顺延，突破限制以优惠价格购买会员。  
  
3、退款处并发。退款的时候可以发起同一订单多次退款，达到多退款的目的。  
#### 七、优惠券多次使用  
  
常见于涉及优惠券的订单中。可以在提交订单的时候修改发包中优惠券的值尝试使用大额优惠券，或者按照原数据包中优惠券的构造参数手工添加几张优惠券，达到优惠券叠用的目的。有优惠券面值参数的也可以直接修改数据包中优惠券的面值。  
  
1、在一个订单中叠加使用优惠券  
  
2、修改优惠券标识，尝试使用其他商品中的大额优惠券  
  
3、直接修改优惠券的面值。实际金额计算会扣除优惠的部分，此时修改优惠券面值可以实现低价购买。  
#### 八、遍历隐藏或者下架优惠id获取优惠链接  
  
漏洞常见位置：会员处、商品处（隐藏商品，已下架商品，开发测试低价商品等）  
  
1、遍历隐藏优惠券  
  
一般会有一些开发时测试的大额优惠券，或者已经过期下架的优惠券，通过遍历可以被使用。  
  
2、遍历商品id从而fuzz到已下架的商品  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rf8EhNshONTKVdIVEsvFQ9KXnZ09zUOb4A1jDRySqW4xmUiaicrMqyfpcjL4Ss85pyeWErFRb3gnMcdpd1ReQgsA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
#### 九、利用小数点精度四舍五入  
  
0.019=0.02（比如充值0.019元，第三方支付截取到分也就是0.01元，但是系统四舍五入为0.02）。  
  
如果你是一个长期主义者，欢迎加入我的知识星球，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款前面有同学问我有没优惠券，这里发放100张100元的优惠券,用完今年不再发放  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7N5nMaJbtnMPVw96ZcVbWfp6SGDicUaGZyrWOM67xP8Ot3ftyqOybMqbj1005WvMNbDJO0hOWkCaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
  
原文:https://forum.butian.net/share/2778  
  
  
