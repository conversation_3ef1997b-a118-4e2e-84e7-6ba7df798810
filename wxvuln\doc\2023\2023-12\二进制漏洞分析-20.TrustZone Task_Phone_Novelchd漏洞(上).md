#  二进制漏洞分析-20.TrustZone Task_Phone_Novelchd漏洞(上)   
原创 haidragon  安全狗的自我修养   2023-12-10 11:00  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHL5u1UUice03iaOicUmNl5f96icPndfmZ63AGb3pvVVTDia1u8ib7710U2wib8wa7zEULTlTE2bKtKLb6Ng/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
   
# 二进制漏洞分析-1.华为Security Hypervisor漏洞  
# 二进制漏洞分析-2.揭示华为安全管理程序(上)  
# 二进制漏洞分析-3.揭示华为安全管理程序(下)  
# 二进制漏洞分析-4.华为安全监控漏洞(SMC SE 工厂检查 OOB 访问)  
  
[二进制漏洞分析-5.华为安全监控漏洞(SMC MNTN OOB 访问)](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247489932&idx=1&sn=8137e176025769acb9c31ba29a95ce3f&chksm=c13f2ac5f648a3d3fa0cb43ee3c7db8300b3a350cf12e51bc990e7015585a0bc53ed2c17562d&scene=21#wechat_redirect)  
  
# 二进制漏洞分析-6.Parallels Desktop Toolgate 漏洞  
# 二进制漏洞分析-7.华为TrustZone Vsim_Sw漏洞  
# 二进制漏洞分析-8.Huawei TrustZone VprTa漏洞  
# 二进制漏洞分析-9.华为TrustZone TEE_Weaver漏洞  
  
[二进制漏洞分析-10.华为TrustZone TEE_SERVICE_VOICE_REC漏洞](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490061&idx=1&sn=7d05728cc107b418453513eaeee259d6&chksm=c13f2944f648a052dad7bee5958a345195b843dc6473ce98311a857d4375e61cc4505b63950b&scene=21#wechat_redirect)  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490062&idx=1&sn=f5b234a3a0223dca7d990362dceff5fa&chksm=c13f2947f648a0518ab8c66eb32c694ee315043e954abc52a20c3b6d8eefc3a624575441401d&scene=21#wechat_redirect)  
# 二进制漏洞分析-11.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(上)  
# 二进制漏洞分析-12.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(下)  
# 二进制漏洞分析-13.华为TrustZone TEE_SERVICE_FACE_REC漏洞(一)  
# 二进制漏洞分析-14.华为TrustZone TEE_SERVICE_FACE_REC漏洞(二)  
# 二进制漏洞分析-15.华为TrustZone TEE_SERVICE_FACE_REC漏洞(三)  
# 二进制漏洞分析-16.华为TrustZone TEE_SERVICE_FACE_REC漏洞(四)  
# 二进制漏洞分析-17.华为TrustZone Tee_Fido_Main漏洞  
# 二进制漏洞分析-18.华为TrustZone TEE_EID漏洞  
  
[二进制漏洞分析-19.华为TrustZone TCIS漏洞](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490507&idx=1&sn=e2134c571be18442c52f062c2c30affe&chksm=c13f2882f648a194962d725a6613cfb988d4def0d1f0228e2c02acb0eb005b520d648ac0aaf3&scene=21#wechat_redirect)  
  
  
此通报包含有关以下漏洞的信息：  
- CVE-2021-46813 漏洞 GetOCSPResponse 中缺少长度检查  
  
- CVE-2021-46813 漏洞 NOVEL_CHDRM_Copyordecrypt中缺少长度和偏移检查  
  
- CVE-2021-46813 漏洞 NOVEL_CHDRM_SetDRMCertData中缺少长度检查  
  
- CVE-2021-40062 漏洞 缺少长度检查DRM_Secure_Store_Read  
  
- CVE-2021-40056 漏洞 getvaluewithtypeandindex 中缺少长度检查  
  
- CVE-2021-40057 漏洞 Secure_Store_EncryptWrite 和 Secure_Store_PlainWrite 中缺少长度检查  
  
- CVE-2021-40058 漏洞 NOVEL_CHDRM_SetRegisterResData中缺少长度检查  
  
- CVE-2021-40060 漏洞 调用NOVEL_CHDRMw_MemCompare时长度检查缺失/错误  
  
- CVE-2021-46813 漏洞 find_tlv_data中的整数下溢  
  
- CVE-2022-39003 漏洞 getvaluewithtypeandindex 中的 OOB 访问  
  
- HWPSIRT-2022-77114 未经检查的 Malloc 返回值  
  
- HWPSIRT-2021-84851 缺少长度检入pack_tlv_data  
  
- HWPSIRT-2021-40855 调用unpack_tlv_data后缺少长度检查  
  
- HWPSIRT-2021-36582 堆栈/堆/BSS 指针泄漏DRM_AES_Encrypt_xxx  
  
- HWPSIRT-2021-78954 unpack_tlv_data中的整数下溢  
  
我们发现多个漏洞影响华为TASK_PHONE_NOVELCHD可信应用程序。为了使报告简明扼要，我们尝试对类似的漏洞进行重新组合。我们最终得到了下面给出的类别。  
- 缺少长度检查导致TEE_Param输出缓冲区的 39 个缓冲区溢出pack_tlv_data  
  
- 缺少长度检查导致 25 个缓冲区溢出（23 个堆栈分配，2 个堆分配）DRM_Secure_Store_Read  
  
- 缺少长度检查，导致 13 个堆栈缓冲区溢出getvaluewithtypeandindex  
  
- 缺少长度检查导致 14 个堆栈缓冲区溢出GetOCSPResponse  
  
- 缺少长度签入并导致 12 个堆栈缓冲区溢出Secure_Store_EncryptWriteSecure_Store_PlainWrite  
  
- 缺少长度和偏移检查，导致：NOVEL_CHDRM_Copyordecrypt  
  
- 1 堆栈缓冲区溢出  
  
- 6 次 ION 缓冲液 OOB 写入  
  
- 3 个 ION 缓冲液 OOB 读数  
  
- 缺少长度检查导致 2 个堆栈缓冲区溢出NOVEL_CHDRM_SetDRMCertData  
  
- 缺少长度检查导致：NOVEL_CHDRM_SetRegisterResData  
  
- 1 堆栈缓冲区溢出  
  
- 1 BSS 缓冲区溢出  
  
- 调用后缺少长度检查，导致 6 堆缓冲区溢出unpack_tlv_data  
  
- 堆栈/堆/bss 指针泄漏DRM_AES_Encrypt_xxx  
  
- 整数下溢导致 OOB 读取/缓冲区过度读取unpack_tlv_data  
  
- 整数下溢导致 OOB 读取/缓冲区过度读取find_tlv_data  
  
- 未经检查的 malloc 返回值导致 10 个空指针取消引用  
  
## 缺少长度签入pack_tlv_data¶  
  
该函数用于将 TLV 数据打包到输出缓冲区中。输出缓冲区的大小由用户控制，在写入之前从不检查，如果缓冲区不够大，无法写入其中的数据，则会导致缓冲区溢出。pack_tlv_data  
```
```  
```
void pack_tlv_data(uint8_t type, uint8_t *value, uint32_t length, uint8_t *outbuf, uint32_t *outbuf_size) {
    outbuf[0] = type;
    *(uint32_t *)(outbuf + 1) = bswap32(length);
    if (length)
        NOVEL_CHDRMw_Memcpy(outbuf + 5, value, length);
    *outbuf_size = length + 5;
}

```  
  
例如，我们将查看对 in 的易受攻击的调用，这是命令 ID #0x1 的处理程序。pack_tlv_dataNOVEL_CHDRM_GetDeviceID  
```
```  
```
TEE_Result TA_InvokeCommandEntryPoint(
        void *sessionContext,
        uint32_t commandID,
        uint32_t paramTypes,
        TEE_Param params[4])
{
    /* [...] */
    if (commandID == 1) {
        DRM_CheckParamType(paramTypes, 5, 6, 0, 0);
        /* [...] */
        NOVEL_CHDRM_GetDeviceID(params[1].memref.buffer, &params[1].memref.size);
    }
    /* [...] */
}

```  
  
NOVEL_CHDRM_GetDeviceID  
传递输出缓冲区及其大小，而无需事先验证。无论其实际大小如何，这都会写入 0x20 个字节。例如，如果我们提供大小为 0x8 字节的输出缓冲区，则会导致 0x18 字节的缓冲区溢出。TEE_Paramobuf1_addrobuf1_sizepack_tlv_dataobuf1_addrTEE_Param  
```
```  
```
int NOVEL_CHDRM_GetDeviceID(uint8_t *obuf1_addr, uint32_t *obuf1_size) {
    pack_tlv_data(END_OF_CONTENT, &OTPChipIDHex, 0x20, obuf1_addr, obuf1_size);
    return 0;
}

```  
  
在这个特定示例中，TA 不会崩溃。但是有些调用具有可控大小，然后能够跨越页面边界，导致崩溃。pack_tlv_data  
  
我们确定了 39 个易受攻击的调用，所有调用都写入了输出缓冲区的 OOB：pack_tlv_dataTEE_Param  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="790" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="791" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="792" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="43979" msthash="793" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1420</td><td msttexthash="528086" msthash="794" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDeviceID+20</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x20 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44395" msthash="796" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1540</td><td msttexthash="780624" msthash="797" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSerialNumber+10c</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45630" msthash="799" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1638</td><td msttexthash="32626100" msthash="800" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+公元前</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x10 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52624" msthash="802" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x165c</td><td msttexthash="884585" msthash="803" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+E0</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x20 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="53196" msthash="805" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x169c</td><td msttexthash="900679" msthash="806" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+120</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">4 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44655" msthash="808" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1740</td><td msttexthash="911378" msthash="809" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+1C4</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45708" msthash="811" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1774</td><td msttexthash="914979" msthash="812" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+1F8</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="53586" msthash="814" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x199c</td><td msttexthash="902161" msthash="815" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+420</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="50739" msthash="817" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1a44</td><td msttexthash="914940" msthash="818" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+4C8</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="50362" msthash="820" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1d30</td><td msttexthash="654303" msthash="821" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSignature+22c</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="43914" msthash="823" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x2004</td><td msttexthash="1210196" msthash="824" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetAttestationSignature+244</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45864" msthash="826" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x2828</td><td msttexthash="491127" msthash="827" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMTime+40</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">4 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="51766" msthash="829" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x28d0</td><td msttexthash="596102" msthash="830" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetTAVersion+90</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="51597" msthash="832" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x2f90</td><td msttexthash="842959" msthash="833" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+66 8</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="58942" msthash="835" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x30ec</td><td msttexthash="847834" msthash="836" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+7C4</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x14 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44876" msthash="838" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3154</td><td msttexthash="41126579" msthash="839" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+82摄氏度</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x10 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45305" msthash="841" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3184</td><td msttexthash="41128061" msthash="842" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+85摄氏度</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">4 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="58643" msthash="844" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x31bc</td><td msttexthash="843375" msthash="845" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+894</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52364" msthash="847" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x31e8</td><td msttexthash="846287" msthash="848" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+8C0</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">0x20 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="59020" msthash="850" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x35ac</td><td msttexthash="835042" msthash="851" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_DelLicenseReqData+280</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44824" msthash="853" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x5134</td><td msttexthash="1268852" msthash="854" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+1ae0</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="59631" msthash="856" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x5ff8</td><td msttexthash="674284" msthash="857" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+234</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44031" msthash="859" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6110</td><td msttexthash="682201" msthash="860" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+34C</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="44304" msthash="862" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6220</td><td msttexthash="37939889" msthash="863" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+45摄氏度</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45968" msthash="865" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6348</td><td msttexthash="677885" msthash="866" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+584</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52806" msthash="868" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x644c</td><td msttexthash="680199" msthash="869" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+688</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45747" msthash="871" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6554</td><td msttexthash="677352" msthash="872" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+790</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="51077" msthash="874" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6e30</td><td msttexthash="920868" msthash="875" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+13c</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="65754" msthash="877" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6ebc</td><td msttexthash="906620" msthash="878" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+1C8</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45409" msthash="880" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7028</td><td msttexthash="897416" msthash="881" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+334</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">2 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52845" msthash="883" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x70f8</td><td msttexthash="896389" msthash="884" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+404</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45344" msthash="886" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7154</td><td msttexthash="897351" msthash="887" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+460</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">2 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52663" msthash="889" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x724c</td><td msttexthash="901498" msthash="890" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+558</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="45760" msthash="892" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7274</td><td msttexthash="898859" msthash="893" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+580</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">20 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="52195" msthash="895" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x72d4</td><td msttexthash="921674" msthash="896" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+5e0</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="58409" msthash="898" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7db4</td><td msttexthash="876759" msthash="899" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+124</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">n 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="59605" msthash="901" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7df8</td><td msttexthash="880763" msthash="902" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+168</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="58851" msthash="904" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7f0c</td><td msttexthash="903539" msthash="905" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+27c</td><td style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr><tr style="box-sizing: inherit;"><td msttexthash="59150" msthash="907" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7fa8</td><td msttexthash="879255" msthash="908" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+318</td><td style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;"><span style="box-sizing: inherit;">1 字节缓冲区溢出</span><code style="padding: 0.25em 0.5em;box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;white-space-collapse: preserve;border-radius: 5px;">params[1]</code></td></tr></tbody></table>## 缺少长度签入DRM_Secure_Store_Read¶  
  
DRM_Secure_Store_Read  
使用或 从安全存储中读取数据，具体取决于正在读取的文件。Secure_Store_PlainReadSecure_Store_EncryptRead  
```
```  
```
unsigned int DRM_Secure_Store_Read(void* data, uint32_t *data_len, uint32_t store_type)
{
    if (store_type - 0x10 <= 0x4F) {
        // drmCipherData
        return Secure_Store_EncryptRead("64726D43697068657244617461.cli",
            data, data_len, store_type, &version);
    }

    if (store_type - 0x60 <= 0x9F) {
        // drmPlainData
        return Secure_Store_PlainRead("64726D506C61696E44617461.cli",
            data, data_len, store_type, &version);
    }

    if (store_type <= 0xF) {
        // drmCipherData
        ret1 = Secure_Store_EncryptRead("64726D43697068657244617461.cli",
            data, &outsize, store_type, &version);
        *data_len = outsize;
        // drmPlainData
        ret2 = Secure_Store_PlainRead("64726D506C61696E44617461.cli",
            data + outsize, &outsize, store_type, &version);
        *data_len += outsize;
        return ret1 | ret2;
    }

    // [...]
}

```  
  
这里将：Secure_Store_EncryptRead  
- 读取文件filename;  
  
- 解密其中的数据;  
  
- 根据值检索 TLV 对象store_type;  
  
- 将结果复制到输出缓冲区中。data  
  
```
```  
```
unsigned int Secure_Store_EncryptRead(uint8_t *filename, void *data,
    uint32_t *outsize, uint8_t store_type, uint32_t *version) {
    /* [...] */
    // Reads the file from the file
    HiTEE_FlashRead(filename, &rpmb_data, 0x2000, &rpmb_data_size);
    rpmb_data_size = _byteswap_ulong(rpmb_data.size);
    /* [...] */
    // In-place RPMB data decryption.
    Secure_Store_DataDecrypt(rpmb_data.data, &rpmb_data_size);
    /* [...] */
    // Extracts data from the file according to the `store_type` argument.
    unpack_tlv_data(store_type, rpmb_data.data, &rpmb_data_size,
        &unpacked_tlv_buf_p, &unpacked_tlv_len);
    /* [...] */
    // Copies the result into the `data` output buffer provided to the function.
    NOVEL_CHDRMw_Memcpy(data, unpacked_tlv_buf_p, unpacked_tlv_len);
    /* [...] */
}

```  
  
注意：除了解密过程外，执行相同的操作。Secure_Store_PlainRead  
  
输出缓冲区首先作为参数传递给 ，然后传播到 和/或 。但是，从不检查其大小，这可能会导致在将数据复制到其中时缓冲区溢出。dataDRM_Secure_Store_ReadSecure_Store_PlainReadSecure_Store_EncryptRead  
  
例如，我们将查看对 in 的易受攻击的调用，这是命令 ID #0x17 的处理程序。DRM_Secure_Store_ReadNOVEL_CHDRM_GetSerialNumber  
```
```  
```
TEE_Result TA_InvokeCommandEntryPoint(
        void *sessionContext,
        uint32_t commandID,
        uint32_t paramTypes,
        TEE_Param params[4])
{
    /* [...] */
    if (commandID == 0x17) {
        DRM_CheckParamType(paramTypes, 5, 6, 0, 0);
        /* [...] */
        NOVEL_CHDRM_GetSerialNumber(params[1].memref.buffer,
            &params[1].memref.size);
    }
    /* [...] */
}

```  
  
NOVEL_CHDRM_GetSerialNumber  
从安全存储中读取类型为 0x60 的文件。读取的数据将写入大小为 2048 的堆栈分配缓冲区。DRM_Secure_Store_Read  
```
```  
```
int NOVEL_CHDRM_GetSerialNumber(uint8_t *obuf1_addr, uint32_t obuf1_size) {
    /* [...] */
    uint8_t cert[2048];
    memset(cert, 0, sizeof(cert));
    /* [...] */
    DRM_Secure_Store_Read(cert, &cert_size, 0x60);
    /* [...] */
}

```  
  
但是，用户可以调用以将与类型 0x60 对应的值写入安全存储。文件数据和大小来自对输入缓冲区（由用户控制）进行操作的调用。因此，应该可以写入超过 2048 个字节，从而在使用 读取此数据时导致缓冲区溢出。NOVEL_CHDRM_SetDRMCertDataDRM_Secure_Store_Writeunpack_tlv_dataTEE_ParamDRM_Secure_Store_Read  
```
```  
```
int NOVEL_CHDRM_SetDRMCertData(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    /* [...] */
    if (!unpack_tlv_data(0x16, ibuf0_addr, &ibuf0_size, &cert, &cert_size)) {
        DRM_Secure_Store_Write(cert, cert_size, 0x60);
        /* [...] */
    }
    /* [...] */
}

```  
  
我们确定了 25 个易受攻击的调用，所有这些调用都可能导致缓冲区溢出：DRM_Secure_Store_Read  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="923" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="924" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="925" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="52936" msthash="926" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x149c</td><td msttexthash="739024" msthash="927" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSerialNumber+68</td><td msttexthash="112047039" msthash="928" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52494" msthash="929" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x16c8</td><td msttexthash="928213" msthash="930" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+14c</td><td msttexthash="112047039" msthash="931" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52156" msthash="932" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x16e4</td><td msttexthash="906867" msthash="933" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+168</td><td msttexthash="112047039" msthash="934" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51857" msthash="935" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x17b4</td><td msttexthash="905840" msthash="936" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+238</td><td msttexthash="102644048" msthash="937" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 4&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53170" msthash="938" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x19e8</td><td msttexthash="930709" msthash="939" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSecurityReqData+46c</td><td msttexthash="131545999" msthash="940" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 8，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="56823" msthash="941" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x1bb0</td><td msttexthash="20941908" msthash="942" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetSignature+交流</td><td msttexthash="112047039" msthash="943" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="44876" msthash="944" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x2344</td><td msttexthash="803855" msthash="945" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_VerifySignature+2b8</td><td msttexthash="112047039" msthash="946" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="57889" msthash="947" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x2fe0</td><td msttexthash="864695" msthash="948" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+6b8</td><td msttexthash="112047039" msthash="949" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51792" msthash="950" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x303c</td><td msttexthash="838942" msthash="951" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetLicenseReqData+714</td><td msttexthash="112047039" msthash="952" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="46202" msthash="953" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3974</td><td msttexthash="1181479" msthash="954" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+320</td><td msttexthash="112047039" msthash="955" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51948" msthash="956" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x53c4</td><td msttexthash="27574417" msthash="957" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMCertData+1直流</td><td msttexthash="112047039" msthash="958" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52975" msthash="959" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x546c</td><td msttexthash="679523" msthash="960" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMCertData+284</td><td msttexthash="112047039" msthash="961" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59904" msthash="962" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x5f9c</td><td msttexthash="11037884" msthash="963" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+1天8</td><td msttexthash="112047039" msthash="964" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="50908" msthash="965" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x60b0</td><td msttexthash="719030" msthash="966" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+2ec</td><td msttexthash="112047039" msthash="967" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51181" msthash="968" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x61c0</td><td msttexthash="690391" msthash="969" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+3FC</td><td msttexthash="112047039" msthash="970" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52078" msthash="971" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x62d4</td><td msttexthash="672828" msthash="972" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+510</td><td msttexthash="112047039" msthash="973" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52494" msthash="974" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x63f4</td><td msttexthash="674180" msthash="975" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+630</td><td msttexthash="112047039" msthash="976" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53248" msthash="977" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x64f8</td><td msttexthash="676494" msthash="978" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetDRMCertData+734</td><td msttexthash="83872932" msthash="979" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆的缓冲区溢出（任何大小）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53053" msthash="980" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6d98</td><td msttexthash="877799" msthash="981" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+A4</td><td msttexthash="112047039" msthash="982" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52130" msthash="983" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x6e64</td><td msttexthash="896376" msthash="984" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterReqData+170</td><td msttexthash="125723637" msthash="985" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆的缓冲区溢出（如果大小为 256，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="58851" msthash="986" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7cf4</td><td msttexthash="853788" msthash="987" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+64</td><td msttexthash="112047039" msthash="988" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52117" msthash="989" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7d64</td><td msttexthash="860522" msthash="990" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+D4</td><td msttexthash="112047039" msthash="991" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52299" msthash="992" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7e28</td><td msttexthash="882245" msthash="993" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+198</td><td msttexthash="112047039" msthash="994" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52052" msthash="995" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7e90</td><td msttexthash="874224" msthash="996" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+200</td><td msttexthash="112047039" msthash="997" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59280" msthash="998" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x7f3c</td><td msttexthash="24951667" msthash="999" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_GetRegisterStatus+2交流</td><td msttexthash="112047039" msthash="1000" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr></tbody></table>## 缺少长度签入getvaluewithtypeandindex¶  
  
getvaluewithtypeandindex  
分析输入缓冲区以提取具有给定类型和键类型的值。它首先在输入缓冲区内定位值，计算其偏移量和长度。然后，使用 将该值复制到输出缓冲区中。值长度受输入缓冲区大小的限制。从不检查输出缓冲区长度，从而导致潜在的缓冲区溢出。inbufNOVEL_CHDRMw_Memcpyinsize  
```
```  
```
uint32_t getvaluewithtypeandindex(
        uint32_t type,
        uint32_t key_type,
        void *inbuf,
        uint32_t insize,
        void *outbuf,
        uint32_t *outsize_p)
{
    /* [...] */
    /*
     * Parses the input buffer `inbuf` to compute `value_size` and `value_offset`.
     * A value is then extracted from `inbuf` and written into `outbuf`.
     */
    if (insize >= value_size + value_offset) {
        NOVEL_CHDRMw_Memcpy(outbuf, inbuf + value_offset, value_size);
        *outsize_p = value_size;
        return 0;
    }
    /* [...] */
}

```  
  
例如，我们将查看对 in 的易受攻击的调用，这是命令 ID #0xE 的处理程序。getvaluewithtypeandindexNOVEL_CHDRM_SetDRMDataLicenseResData  
  
在 中，首先从 中提取缓冲区（大小不受限制）并将其复制到 中。然后，此缓冲区用作对 的调用的输入。此调用的输出是大小为 4 的堆栈变量。如上所述，可以将其超过 4 个字节复制到其输出中，从而导致缓冲区溢出。NOVEL_CHDRM_SetDRMDataLicenseResDataibuf0_addrdata1_pgetvaluewithtypeandindexOutputProtectiongetvaluewithtypeandindex  
```
```  
```
unsigned int NOVEL_CHDRM_SetDRMDataLicenseResData(
        uint8_t *ibuf0_addr,
        uint32_t ibuf0_size,
        uint8_t *obuf1_addr,
        uint32_t *obuf1_size)
{
    /* [...] */
    unpack_tlv_data(0xD, ibuf0_addr, &ibuf0_size, &data1_p, &data1_size);
    /* [...] */
    OutputProtection = 0;
    OutputProtection_size = 0;
    getvaluewithtypeandindex(4, 6, data1_p, data1_size,
                             &OutputProtection, &OutputProtection_size);
    /* [...] */

```  
  
我们确定了 13 个易受攻击的调用，所有这些调用都可能导致堆栈缓冲区溢出：getvaluewithtypeandindex  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1006" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="1007" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="1008" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="57408" msthash="1009" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3aa4</td><td msttexthash="1183806" msthash="1010" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+450</td><td msttexthash="135519800" msthash="1011" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 512，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="50674" msthash="1012" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b14</td><td msttexthash="1210716" msthash="1013" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+4c0</td><td msttexthash="112047039" msthash="1014" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 2048，则&gt;</td></tr><tr style="box-sizing: inherit;"><td msttexthash="51675" msthash="1015" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b84</td><td msttexthash="1183208" msthash="1016" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+530</td><td msttexthash="135520866" msthash="1017" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 128，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="58422" msthash="1018" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3c3c</td><td msttexthash="1217242" msthash="1019" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+5e8</td><td msttexthash="135520866" msthash="1020" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 128，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52052" msthash="1021" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3f74</td><td msttexthash="1184911" msthash="1022" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+920</td><td msttexthash="104376285" msthash="1023" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 &gt; 256）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="46046" msthash="1024" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x4188</td><td msttexthash="1193036" msthash="1025" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+B34</td><td msttexthash="133531684" msthash="1026" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 32，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52325" msthash="1027" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x42c8</td><td msttexthash="1195948" msthash="1028" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+C74</td><td msttexthash="133531684" msthash="1029" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 32，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52429" msthash="1030" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x443c</td><td msttexthash="1207102" msthash="1031" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+DE8</td><td msttexthash="133531684" msthash="1032" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 32，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="45175" msthash="1033" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x4570</td><td msttexthash="1203124" msthash="1034" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+F1C</td><td msttexthash="133531684" msthash="1035" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 32，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="52143" msthash="1036" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x48e0</td><td msttexthash="1226056" msthash="1037" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+128C</td><td msttexthash="133531684" msthash="1038" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 32，则&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="57993" msthash="1039" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x4a1c</td><td msttexthash="1226498" msthash="1040" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+13C8</td><td msttexthash="102644048" msthash="1041" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 4&gt;）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="57330" msthash="1042" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x4ad0</td><td msttexthash="1226628" msthash="1043" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+147C</td><td msttexthash="104376285" msthash="1044" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 &gt; 256）</td></tr><tr style="box-sizing: inherit;"><td msttexthash="58227" msthash="1045" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x4be4</td><td msttexthash="1216800" msthash="1046" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">NOVEL_CHDRM_SetDRMDataLicenseResData+1590</td><td msttexthash="133532334" msthash="1047" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出（如果大小为 16，则&gt;）</td></tr></tbody></table>## 缺少长度签入GetOCSPResponse¶  
  
NOVEL_CHDRM_SetDRMCertData  
是命令 ID #0xF 的处理程序，该处理程序将输入缓冲区及其大小作为参数。它首先调用从输入缓冲区中提取一个值，该值由用户控制，然后再将其传递给函数。TEE_Paramunpack_tlv_dataGetOCSPResponse  
```
```  
```
int NOVEL_CHDRM_SetDRMCertData(uint8_t *ibuf0_addr, uint32_t ibuf0_size) {
    /* [...] */
    ibuf0_size_cpy = ibuf0_size;
    rsp_status_t rsp_status;
    /* [...] */
    if (!unpack_tlv_data(0x1C, ibuf0_addr, &ibuf0_size_cpy, &data_p, &data_size)) {
        GetOCSPResponse(data_p, data_size, &rsp_status);
        /* [...] */
    }
    /* [...] */
}

```  
  
GetOCSPResponse  
解析位于输入缓冲区中的 ASN.1 数据。在此函数中，有许多以下模式的实例：data_p  
- 一个调用，它递增数据指针并检索当前标记的长度（无界）asn1_get_tag  
  
- 调用 that 将标记数据从输入缓冲区复制到输出缓冲区NOVEL_CHDRMw_Memcpyrsp_status_p  
  
```
```  
```
int GetOCSPResponse(uint8_t *data_p, uint32_t data_size, rsp_status_t *rsp_status) {
    /* [...] */
    asn1_get_tag(&data_p, seq0_end, &length, OID);
    /* [...] */
    NOVEL_CHDRMw_Memcpy(&rsp_status->field_14, data_p, length);
    data_p += length;
    /* [...] */
}

```  
  
由于标记的长度是在没有任何检查的情况下给出的，这将导致缓冲区溢出（这是堆栈分配的缓冲区）。我们确定了 14 个易受攻击的模式，所有这些模式都会导致堆栈缓冲区溢出：NOVEL_CHDRMw_Memcpyrsp_status_pGetOCSPResponsersp_status_p  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1054" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="1055" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="1056" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="67002" msthash="1057" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3a7d0</td><td msttexthash="8804497" msthash="1058" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+1e0</td><td msttexthash="36953072" msthash="1059" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="75920" msthash="1060" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3a8ec</td><td msttexthash="8817549" msthash="1061" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+2fc</td><td msttexthash="36953072" msthash="1062" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="61256" msthash="1063" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3a994</td><td msttexthash="8804991" msthash="1064" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+3a4</td><td msttexthash="36953072" msthash="1065" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="73671" msthash="1066" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3abc4</td><td msttexthash="8806135" msthash="1067" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+5d4</td><td msttexthash="36953072" msthash="1068" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="67106" msthash="1069" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3ac84</td><td msttexthash="8796294" msthash="1070" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+694</td><td msttexthash="36953072" msthash="1071" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="82069" msthash="1072" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3acec</td><td msttexthash="8818433" msthash="1073" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+6fc</td><td msttexthash="36953072" msthash="1074" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="66781" msthash="1075" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3ad54</td><td msttexthash="8795813" msthash="1076" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+764</td><td msttexthash="36953072" msthash="1077" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="66976" msthash="1078" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3ae18</td><td msttexthash="8796086" msthash="1079" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+828</td><td msttexthash="36953072" msthash="1080" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="73736" msthash="1081" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3aee0</td><td msttexthash="8806278" msthash="1082" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+8f0</td><td msttexthash="36953072" msthash="1083" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59943" msthash="1084" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b084</td><td msttexthash="8805797" msthash="1085" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+a94</td><td msttexthash="36953072" msthash="1086" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="59982" msthash="1087" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b138</td><td msttexthash="8805836" msthash="1088" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+b48</td><td msttexthash="36953072" msthash="1089" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="67938" msthash="1090" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b1f8</td><td msttexthash="345839" msthash="1091" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSPResponse+c08</td><td msttexthash="36953072" msthash="1092" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="74724" msthash="1093" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b2bc</td><td msttexthash="8827676" msthash="1094" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+ccc</td><td msttexthash="36953072" msthash="1095" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="60268" msthash="1096" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3b338</td><td msttexthash="8806278" msthash="1097" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">GetOCSP响应+d48</td><td msttexthash="36953072" msthash="1098" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr></tbody></table>## 缺少长度签入和Secure_Store_EncryptWriteSecure_Store_PlainWrite¶  
  
Secure_Store_EncryptWrite  
，可以使用用户控制的 和 参数 via 调用，首先作为 TLV 对象打包到大小为 + 5 的堆分配缓冲区中。然后，它将文件的 0x2000 字节读入堆栈分配的结构中。然后，此函数多次表现出相同的易受攻击模式，即调用：DRM_Secure_Store_Writebufferbuffer_sizebufferbuffer_tlvbuffer_sizerpmb_dataNOVEL_CHDRMw_Memcpy  
- rpmb_data.data  
作为目的地;  
  
- buffer_tlv  
作为来源;  
  
- buffer_tlv_size  
作为长度。  
  
```
```  
```
TEE_Result Secure_Store_EncryptWrite(
        char *filename,
        void *buffer,
        uint32_t buffer_size,
        uint32_t store_type,
        uint32_t a5)
{
    /* [...] */
    rpmb_data_t rpmb_data;
    /* [...] */
    buffer_tlv = NOVEL_CHDRMw_Malloc(buffer_size + 5);
    pack_tlv_data(store_type, buffer, buffer_size, buffer_tlv, &buffer_tlv_size);
    ret = HiTEE_FlashRead(filename, &rpmb_data, 0x2000, &rpmb_data_size);
    if (ret == 0xFFFF7106) {
        /* [...] */
        NOVEL_CHDRMw_Memcpy(&rpmb_data.data[secure_store_len], buffer_tlv, buffer_tlv_size);
        secure_store_len += buffer_tlv_size;
    }
    /* [...] */
}

```  
  
由于大小为 0x2000，并且由用户控制，因此会发生堆栈缓冲区溢出。rpmb_databuffer_tlv_size  
  
注意：包含与 相同的易受攻击的模式。这两个函数的调用方式与DRM_Secure_Store_Read中的缺失长度检查部分中所述相同。Secure_Store_PlainWriteSecure_Store_EncryptWrite  
  
总而言之，我们确定了 12 个易受攻击的模式实例，这些实例会导致堆栈缓冲区溢出：  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1107" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="1108" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="1109" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="60944" msthash="1110" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x37e60</td><td msttexthash="775073" msthash="1111" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+1cc</td><td msttexthash="36953072" msthash="1112" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="54691" msthash="1113" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x38058</td><td msttexthash="740402" msthash="1114" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+3C4</td><td msttexthash="36953072" msthash="1115" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="60203" msthash="1116" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x380a0</td><td msttexthash="36988627" msthash="1117" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+40摄氏度</td><td msttexthash="36953072" msthash="1118" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="62023" msthash="1119" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x380d8</td><td msttexthash="734201" msthash="1120" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+444</td><td msttexthash="36953072" msthash="1121" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="53014" msthash="1122" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x38120</td><td msttexthash="742794" msthash="1123" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+48C</td><td msttexthash="36953072" msthash="1124" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="54106" msthash="1125" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x38190</td><td msttexthash="748982" msthash="1126" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_EncryptWrite+4FC</td><td msttexthash="36953072" msthash="1127" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr></tbody></table><table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4354064" msthash="1128" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">地址</th><th msttexthash="5695157" msthash="1129" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">访客</th><th msttexthash="4085822" msthash="1130" style="padding: 0.5em 0.75em;border-width: 0px 0px 2px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;">冲击</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td msttexthash="61906" msthash="1131" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x385c4</td><td msttexthash="640471" msthash="1132" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+1C4</td><td msttexthash="36953072" msthash="1133" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="55172" msthash="1134" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x38764</td><td msttexthash="635869" msthash="1135" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+364</td><td msttexthash="36953072" msthash="1136" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="69823" msthash="1137" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x387ac</td><td msttexthash="20964710" msthash="1138" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+3交流</td><td msttexthash="36953072" msthash="1139" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="62504" msthash="1140" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x387e4</td><td msttexthash="655421" msthash="1141" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+3e4</td><td msttexthash="36953072" msthash="1142" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="62634" msthash="1143" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x3882c</td><td msttexthash="34875568" msthash="1144" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+42摄氏度</td><td msttexthash="36953072" msthash="1145" style="padding: 0.5em 0.75em;border-top-width: 0px;border-right-width: 0px;border-left-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr><tr style="box-sizing: inherit;"><td msttexthash="56147" msthash="1146" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">0x38878</td><td msttexthash="638404" msthash="1147" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">Secure_Store_PlainWrite+478</td><td msttexthash="36953072" msthash="1148" style="padding: 0.5em 0.75em;border-width: 0px;border-color: rgb(219, 219, 219);box-sizing: inherit;vertical-align: top;text-align: inherit;">基于堆栈的缓冲区溢出</td></tr></tbody></table>  
- 二进制漏洞(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEH4eXCW471pNuHpGPzUKCkbyticiayoQ5gxMtoR1AX0QS7JJ2v1Miaibv1lA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 其它课程  
  
- windows网络安全防火墙与虚拟网卡（更新完成）  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERE5qcRgQueCyt3U01ySnOUp2wOmiaFhcXibibk6kjPoUhTeftn9aOHJjO6mZIIHRCBqIZ1ok5UjibLMRA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windows文件过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmvkF91T2mwk3lSlbG5CELC5qbib3qMOgHvow2cvl6ibicVH4KguzibAQOQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- USB过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHv0vyWxLx9Sb68ssCJQwXngPmKDw2HNnvkrcle2picUraHyrTG2sSK7A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 游戏安全(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHzEAlXtdkXShqbkibJUKumsvo65lnP6lXVR7nr5hq4PmDZdTIoky8mCg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- ios逆向  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmjrTM3epTpceRpaWpibzMicNtpMIacEWvJMLpKKkwmA97XsDia4StFr1Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windbg  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibv9VNhRI73qFehic91I5dsr3Jkh6EkHIRTPGibESZicD7IeA5ocHjexHhw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 恶意软件开发（更新中）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERGxhrr6IpiaZuqkGWyEJWPwXqHEYPEVp3gpDB73Pg81J9TdUQic0wn4NJQdbTCIDsgC2gqT4tkEkjsg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- 还有很多免费教程(限学员)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHDvveGEwLYBVsps1sH6rGrSnNZtjD2pzCk4EwhH3yeVNibMMSxsW5jkg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibR2Viaxgog8I2gicVHoXJODoqtq7tTVGybA8W0rTYaAkLcp8e2ByCd1QQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibDwwqQLTNPnzDQxtQUF6JjxyxDoNGsr6XoNLicwxOeYfFia0whaxu6VXA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
-   
-   
- 更多详细内容添加作者微信  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPMJPjIWnCTP3EjrhOXhJsryIkR34mCwqetPF7aRmbhnxBbiaicS0rwu6w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-    
  
-    
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPZeRlpCaIfwnM0IM4vnVugkAyDFJlhe1Rkalbz0a282U9iaVU12iaEiahw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
