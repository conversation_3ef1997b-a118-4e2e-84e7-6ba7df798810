#  【复现】Confluence Data Center & Server 远程代码执行漏洞（CVE-2023-22527）风险通告   
原创 赛博昆仑CERT  赛博昆仑CERT   2024-01-20 21:22  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/iaZ7t7b9Dodvib7ddpGMC6vx4COAy4sBoGbGCkwVUIJSHBPI0z1Utrp1h5ys6ygT3albl3PgjejJcRRRiaDFFbMBA/640?wx_fmt=gif "")  
  
  
-  
赛博昆仑漏洞安全通告-  
  
Confluence Data Center & Server 远程代码执行漏洞（CVE-2023-22527）风险通告   
  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/7j1UQofaR9fsNXgsOXHVKZMJ1PCicm8s4RHQVjCJEjX63AsNibMx3So4wSMAvubEOoU2vLqYY7hIibIJbkEaPIDs5A4ianh5jibxw/640?wx_fmt=svg "")  
  
  
  
****  
**漏洞描述**  
  
Confluence是一个专业的企业知识管理与协同软件，也可以用于构建企业wiki。使用简单，它强大的编辑和站点管理特征能够帮助团队成员之间共享信息、文档协作、集体讨论，信息推送。  
  
近日，赛博昆仑CERT监测到Confluence Data Center & Server 远程代码执行漏洞（CVE-2023-22527）的漏洞情报。未授权的攻击者可构造恶意请求模板注入，从而在受影响的 Confluence 服务器上执行远程代码。  
  
<table><colgroup><col width="182"/><col width="182"/><col width="182"/><col width="182"/></colgroup><tbody><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞名称</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;"><p><span style="color: rgb(0, 122, 170);">Confluence Data Center &amp; Server 远程代码执行漏洞</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞公开编号</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;"><p><span style="color: rgb(0, 122, 170);">CVE-2023-22527</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>昆仑漏洞库编号</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;height: 39px;"><span style="color: rgb(0, 122, 170);">CYKL-2023-025019</span></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞类型</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">代码执行</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>公开时间</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">2024-01-16</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞等级</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">严重</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>评分</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">10</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞所需权限</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">无权限要求</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞利用难度</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">低</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>PoC</strong><strong>状态</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>EXP</strong><strong>状态</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞细节</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>在野利用</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td></tr></tbody></table>  
**影响版本**  
  
  
  
Atlassian Confluence Data Center and Server 8.0.x  
  
Atlassian Confluence Data Center and Server 8.1.x  
  
Atlassian Confluence Data Center and Server 8.2.x  
  
Atlassian Confluence Data Center and Server 8.3.x  
  
Atlassian Confluence Data Center and Server 8.4.x  
  
Atlassian Confluence Data Center and Server 8.5.0-8.5.3  
  
  
**利用条件**  
  
  
无需任何利用条件。  
  
  
  
  
  
  
  
**漏洞复现**  
  
目前赛博昆仑CERT已确认漏洞原理，复现截图如下：  
  
复现版本为 confluence 8.5.3  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/iaZ7t7b9DodsKicqCcwEzG7zmdwyibmY9VJQOMy5yLr5wUqbzW73YqO8T9hnCCxicZmnHt42WibHtFMVworLwFppb4Q/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/iaZ7t7b9DodsKicqCcwEzG7zmdwyibmY9VJrXB1qiaz5beJChvuvlWg60u3QDZZiakHZNM12M6DkD4geAQgs2C1QEnw/640?wx_fmt=png&from=appmsg "")  
  
**防护措施**  
  
  
  
 目前，官方已发布修复建议，建议受影响的用户尽快升级至安全版本。  
  
 下载地址：https://www.atlassian.com/zh/software/confluence/download-archives  
  
  
**技术咨询**  
  
赛博昆仑支持对用户提供轻量级的检测规则或热补方式，可提供定制化服务适  
配多种产品及规则，帮助用户进行漏洞检测和修复。  
  
赛博昆仑CERT已开启年订阅服务，付费客户(可申请试用)将获取更多技术详情，并支持适配客户的需求。  
  
联系邮箱：<EMAIL>  
  
公众号：赛博昆仑CERT  
  
**参考链接**  
  
https://confluence.atlassian.com/security/cve-2023-22527-rce-remote-code-execution-vulnerability-in-confluence-data-center-and-confluence-server-1333990257.html  
  
  
  
**时间线**  
  
 2024年01月16日，Confluence 官方发布漏洞通告  
  
 2024年01月20日，赛博昆仑CERT公众号发布漏洞风险通告  
  
  
