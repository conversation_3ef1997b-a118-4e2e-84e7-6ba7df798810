#  【漏洞预警】Spring Framework拒绝服务漏洞CVE-2024-22233   
cexlife  飓风网络安全   2024-01-23 23:51  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu01sqYhxJ7Otp618k3YyYLb23pte92riapxDV6f14P66bq5DXiakvjsictWuVIEy0VWq5VGOqrV5KYUzA/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**  
  
Spring Framework 是一个功能强大的 Java 应用程序框架，旨在提供高效且可扩展的开发环境。近日监测到Spring Framework中修复了一个拒绝服务漏洞（CVE-2024-22233），该漏洞的CVSSv3评分为7.5,Spring Framework 版本 6.0.15 和 6.1.2 中，当应用程序使用Spring MVC,且classpath类路径中包含Spring Security 6.1.6+ 或 6.2.1+时，威胁者可发送特制HTTP请求导致拒绝服务。**影响范围:**Spring Framework 版本6.0.15Spring Framework 版本6.1.2**注意**，通常SpringBoot应用程序需要org.springframework.boot:spring-boot-starter-web和org.springframework.boot:spring-boot-starter-security依赖项来满足上述条件,Spring Framework 版本**6.0.15**和**6.1.2**分别被 Spring Boot **3.1.7**和 **3.2.1**使用。**安全措施:**升级版本目前该漏洞已经修复，受影响用户可升级到以下版本:Spring Framework 版本6.0.15：可升级到 6.0.16Spring Framework 版本6.1.2：可升级到 6.1.3**下载链接：**https://github.com/spring-projects/spring-framework/releases**临时措施:**Spring Boot用户可升级到Spring Boot 3.1.8（升级到Spring Framework 6.0.16）、3.2.2（升级到Spring Framework 6.1.3）或更高版本**下载链接:**https://github.com/spring-projects/spring-boot/releases**参考链接:**https://spring.io/security/cve-2024-22233/https://spring.io/projects/spring-framework/  
  
