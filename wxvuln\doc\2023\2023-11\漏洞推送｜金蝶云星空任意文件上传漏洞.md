#  漏洞推送｜金蝶云星空任意文件上传漏洞   
alicy  信安百科   2023-11-26 17:17  
  
**0x00 前言**  
  
  
金蝶云星空是一款云端企业资源管理（ERP）软件，为企业提供财务管理、供应链管理以及业务流程管理等一体化解决方案。  
  
  
  
**0x01 漏洞描述**  
  
  
ScpSupRegHandler接口存在任意文件上传漏洞。攻击者可在无需登录的情况下利用此漏洞上传任意文件，最终可能导致远程执行恶意命令，控制服务器。  
  
  
  
**0x02 CVE编号**  
  
  
无  
  
  
**0x03 影响版本**  
  
****  
金蝶云星空企业版私有云  
  
企业版私有云（订阅）  
  
标准版私有云（订阅）  
  
  
涉及版本：  
  
V6.2(含17年12月补丁) 至 V8.1(含23年9月补丁)  
  
  
  
**0x04 漏洞详情**  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/Whm7t4Je6upulljHgO6mr5VMMJwGUSFMUwSofUicPNS7oqFngBvFDrXEHKR4MfrcQ97nic7ACCO8h7ofveterUsA/640?wx_fmt=png&from=appmsg "")  
  
  
  
**0x05 参考链接**  
  
```
https://vip.kingdee.com/article/505394681531036160
```  
  
  
  
  
推荐阅读：  
  
  
[CVE-2023-38286｜Spring Boot Admin 远程代码执行漏洞](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247484419&idx=1&sn=fbc03d7719084ac98d3835a42108b91a&chksm=cea96ddaf9dee4cc15faa4a64faf8618083c32f98c4c5d3732d1e5f9f1ee93877df1eb559c62&scene=21#wechat_redirect)  
  
  
  
[CVE-2023-46750｜Apache Shiro开放重定向漏洞](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247484717&idx=1&sn=5781298c53829a03e8679e83263a0b3f&chksm=cea96cf4f9dee5e267dc72e818fb1d956f9b8a118d787f8e51911b356aa4ea6a153ff1822fe4&scene=21#wechat_redirect)  
  
  
  
[CVE-2023-22518｜Atlassian Confluence 远程代码执行漏洞](http://mp.weixin.qq.com/s?__biz=Mzg2ODcxMjYzMA==&mid=2247484706&idx=2&sn=1e4cc8b0fab03e030ff59f1f34d9343a&chksm=cea96cfbf9dee5eda8fe5caa234a42a977a8d3f52bbe12861708ffd51c61d7f18552882ac374&scene=21#wechat_redirect)  
  
  
  
  
  
Ps：国内外安全热点分享，欢迎大家分享、转载，请保证文章的完整性。文章中出现敏感信息和侵权内容，请联系作者删除信息。信息安全任重道远，感谢您的支持![](https://mmbiz.qpic.cn/mmbiz_png/Whm7t4Je6urTIficI8UhQibwpYWx4ic7Bk40AJlXrgx3icofWCbd5cbJFheld132R8exvlHnicn0AUjHLmVok4wV9qA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
！！！  
  
  
**本公众号的文章及工具仅提供学习参考，由于传播、利用此文档提供的信息而造成任何直接或间接的后果及损害，均由使用者本人负责,本公众号及文章作者不为此承担任何责任。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/Whm7t4Je6uqQ24S6worK6npevNP8p1uPc9jQeMAib2iaibBnibOzFaIbD0KlvsEtUAmL3xdbJJnWk74Y1KfBcIazzw/640?wx_fmt=png "")  
  
  
