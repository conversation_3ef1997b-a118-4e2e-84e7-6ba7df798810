#  微软4月补丁星期二值得关注的漏洞：4个0day及更多   
综合编译  代码卫士   2024-04-10 15:43  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Az5ZsrEic9ot90z9etZLlU7OTaPOdibteeibJMMmbwc29aJlDOmUicibIRoLdcuEQjtHQ2qjVtZBt0M5eVbYoQzlHiaw/640?wx_fmt=gif "")  
  
   
聚焦源代码安全，网罗国内外最新资讯！  
  
**编译：代码卫士**  
  
**本月微软共发布147个CVE漏洞，加上本月所提到的第三方CVE，总数达到了155个。这些漏洞中并不包含Pwn2Own 温哥华大赛中发布的漏洞。**  
  
  
  
在这些漏洞中，仅有3个被评级为“严重”，142个是“重要”级别，2个是“中危”级别。这是微软今年以来也是至少自2017年以来发布补丁最多的星期二。目前尚不清楚是否因工作囤积还是漏洞报送剧增造成的。  
  
  
**两个已遭利用的 0day**  
  
  
微软最初并未将这两个漏洞标记为“已遭活跃利用”，不过 Sophos 和 ZDI均分享了这些漏洞如何遭活跃利用的情况。  
  
**CVE-2024-26234****是位于 Proxy 驱动中的欺骗漏洞。**Sophos 公司表示，该漏洞是通过有效的“微软硬件发布者证书 (Microsoft Hardware Publisher Certificate)”签名的一个恶意驱动。该驱动用于部署此前由 Stairwell 披露的一个后门。  
  
团队负责人 Christopher Budd 表示，此前报送给微软的驱动漏洞并未获得CVE漏洞但微软却发布了安全公告。目前尚不清楚为何微软今日未发布该驱动的CVE漏洞，除非是认为该驱动经过了上述有效证书的签名。  
  
**CVE-2024-29988****是位于SmartScreen 中的提示安全特性绕过漏洞。**该漏洞是对 CVE-2024-21412漏洞补丁的绕过，而后者也是对 CVE-2023-26025漏洞补丁的绕过。该漏洞可导致打开文件时，微软 Defender Smartscreen 提示遭绕过。威胁行动者以压缩包文件的格式发送利用，躲避EDR/NDR检测，之后组合利用其它漏洞绕过MotW。该漏洞被Water Hydra 黑客组织用于攻击 Forex 交易论坛和Telegram 股票交易的多个频道，牟取经济利益。  
  
  
**其它2个0day漏洞**  
  
  
Varonis 公司的研究人员也发现了两个 SharePoint 0day 漏洞，从服务器中下载文件时这两个漏洞更难以被检测到。微软并未发布相关的CVE编号，只是增加到后续打补丁工作中，目前也并未公布修复日期。  
  
  
**其它值得关注的漏洞**  
  
  
**CVE-2024-20678****是RPC 运行时远程代码执行漏洞。**RPC 利用在野出现历史悠久，因此任何可导致代码执行后果的RPC漏洞均会引人关注。该漏洞利用虽然需要认证但无需任何权限提升。任何认证用户均可利用它。目前尚不清楚如果认证为 Guest 或匿名用户是否会遭攻击。快速搜索后发现，TCP 端口为135的130万个系统被暴露到互联网。预计很多人会开始寻求该利用。  
  
**CVE-2024-20670****是Outlook 欺骗漏洞。**该漏洞虽然被列为欺骗漏洞，但基于利用结果来看，它是信息泄露漏洞。所泄露的信息是NTLM哈希，它后续可用于欺骗目标用户。不论如何，用户需要点击邮件中的内容触发该漏洞。预览面板并非攻击向量。然而，几个月来我们已经发现了不少NTLM中继漏洞。Outlook 的用户更多，因此未来几个月该漏洞可能会被利用。  
  
**CVE-2024-26221****是Windows DNS Server 远程代码执行漏洞。**该漏洞是本月修复的七个DNS RCE漏洞之一，且这些漏洞较为相似。如果攻击者具有查询DNS服务器的权限，则可导致在受影响DNS服务器上实现RCE。这里也有一个时机因素，但如果DNS查询时机合适，那么攻击者就能够在目标服务器上执行任意代码。尽管并未特别说明，但从逻辑上来看代码执行可能会发生在DNS服务层面，这是提升后的结果。众所周知DNS服务器是重点目标，因此必须严肃对待该漏洞并迅速部署补丁。  
  
  
****  
代码卫士试用地址：  
https://codesafe.qianxin.com  
  
开源卫士试用地址：https://oss.qianxin.com  
  
  
  
  
  
  
  
  
  
  
  
  
**推荐阅读**  
  
[微软2月补丁星期二值得关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518852&idx=1&sn=118503a8f9ad674c456c7a1beb026af7&chksm=ea94bbeedde332f84c2d0ebf024af1f8cb2ab422a17c266b9120bc7bbb81c05e8a27b9fc035b&scene=21#wechat_redirect)  
  
  
[微软补丁星期二值得关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518639&idx=1&sn=5eb41017915be58b56c7eef48e7dc4de&chksm=ea94b8c5dde331d3020ca525a644211fa78e7fed0b6f4329c4ed79f060f65c97659843449eae&scene=21#wechat_redirect)  
  
  
[微软12月补丁星期二值得关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518349&idx=1&sn=7d0a830340114bfe063e58557ea01613&chksm=ea94b9e7dde330f1808becf6b96f023942a253bc69753c951113741277557c3cc163d081b25d&scene=21#wechat_redirect)  
  
  
[微软11月补丁星期二需要关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518139&idx=1&sn=941a9da7093fb53ada0f874db9d3ef39&chksm=ea94b6d1dde33fc740734b8dc6fbbcd0d0ea7446e8a8bfa3c8bdb1423e2e2c01536e8adf2960&scene=21#wechat_redirect)  
  
  
[十月补丁星期二值得关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=**********&idx=1&sn=996f3399f60a91c25395c5c879a049e1&chksm=ea94b7fbdde33eedc9257fadac74eda5188d3e34694eb6e3eb872b3d35e3d410c2e0cf04f03a&scene=21#wechat_redirect)  
  
  
  
  
**原文链接**  
  
  
https://www.zerodayinitiative.com/blog/2024/4/9/the-april-2024-security-updates-review  
  
https://www  
.bleepingcomputer.com/news/microsoft/microsoft-april-2024-patch-tuesday-fixe  
s-150-security-flaws-67-rces/  
  
  
题图：  
Pexels  
 License  
  
****  
**本文由奇安信编译，不代表奇安信观点。转载请注明“转自奇安信代码卫士 https://codesafe.qianxin.com”。**  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSf7nNLWrJL6dkJp7RB8Kl4zxU9ibnQjuvo4VoZ5ic9Q91K3WshWzqEybcroVEOQpgYfx1uYgwJhlFQ/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSN5sfviaCuvYQccJZlrr64sRlvcbdWjDic9mPQ8mBBFDCKP6VibiaNE1kDVuoIOiaIVRoTjSsSftGC8gw/640?wx_fmt=jpeg "")  
  
**奇安信代码卫士 (codesafe)**  
  
国内首个专注于软件开发安全的产品线。  
  
   ![](https://mmbiz.qpic.cn/mmbiz_gif/oBANLWYScMQ5iciaeKS21icDIWSVd0M9zEhicFK0rbCJOrgpc09iaH6nvqvsIdckDfxH2K4tu9CvPJgSf7XhGHJwVyQ/640?wx_fmt=gif "")  
  
   
觉得不错，就点个 “  
在看  
” 或 "  
赞  
” 吧~  
  
