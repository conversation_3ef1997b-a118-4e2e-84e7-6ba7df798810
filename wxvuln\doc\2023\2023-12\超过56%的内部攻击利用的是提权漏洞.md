#  超过56%的内部攻击利用的是提权漏洞   
Bill Toulas  代码卫士   2023-12-11 17:12  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Az5ZsrEic9ot90z9etZLlU7OTaPOdibteeibJMMmbwc29aJlDOmUicibIRoLdcuEQjtHQ2qjVtZBt0M5eVbYoQzlHiaw/640?wx_fmt=gif "")  
  
   
聚焦源代码安全，网罗国内外最新资讯！****  
  
**编译：代码卫士**  
  
**CrowdStrike 分析2021年1月至2023年4月之间收集的数据指出，企业内鬼在网络进行越权活动，不管是出于恶意目的还是以危险方式下载有风险的工具，最常用的漏洞类型是提权漏洞。**  
  
  
分析报告指出，内部威胁活动正在增长，使用提权漏洞是越权活动的重大组成部分。在CrowdStrike公司所记录的内部威胁活动中，55%依赖的是提权漏洞，而余下45%通过下载或滥用攻击性工具引入风险。  
  
恶意的内部人员一般会出于经济利益、怨恨或与上级的不合而针对所在企业。CrowdStrike 公司将并非针对企业的恶意攻击如使用 exp 安装软件或平台安全测试的事件归类为内部威胁。然而，在这些情况下，尽管这些事件并非为了攻击公司，但通常以有风险的方式进行，因此可能会在网络引入可被威胁行动者滥用的威胁或恶意软件。该公司发现，从目标组织机构内部发动的攻击如是恶意性质，则造成的平均成本是64.8万美元，对于非恶意事件而言平均成本是48.5万美元。在2023年这些数字可能更高。  
  
除了带来经济成本外，内部威胁还可能损害品牌形象和声誉。  
  
  
**典型的内部攻击**  
  
  
  
  
  
CrowdStrike 公司解释称，利用提权漏洞获得管理员权限对于很多内部威胁而言至关重要，因为在多数情况下，恶意内鬼以网络环境的低访问权限为起始点。  
  
更高的权限可导致攻击者执行多种操作如下载和安装越权软件、擦除日志、甚至是使用要求管理员权限的工具诊断计算机上的问题。恶意内鬼最常利用的本地提权漏洞如下所示：  
  
- CVE-2017-0213：可通过 COM 基础设施利用提权的 Windows 缺陷。  
  
- CVE-2022-0847（“脏管道”）：Linux 内核管道操作管理缺陷  
  
- CVE-2021-4034 (PwnKit)：影响 Polkit 系统服务的 Linux 缺陷  
  
- CVE-2019-13272：与在内核流程中对用户权限处理不当有关的 Linux 漏洞  
  
- CVE-2015-1701：涉及内核模式驱动 “win32k.sys”进行越权代码执行的Windows 漏洞  
  
- CVE-2014-4113：针对的也是 “win32k.sys”，但涉及不同的利用方法  
  
  
  
如上漏洞均已被纳入CISA的“已知遭利用漏洞分类 (KEV)”中。即使一个系统中已修复这些漏洞，但内鬼也可通过其它方法获得提权，如在以提升后权限运行的应用中的DLL 劫持缺陷、不安全的文件系统权限或服务配置，或BYOVD 攻击等。  
  
CrowdStrike 公司在欧洲发现多起利用CVE-2017-0213的案例，一家零售店员工通过 WhatsApp 下载 exp 来安装uTorrent 并玩游戏，另外一个案例与美国某媒体公司的离职员工有关。一家澳大利亚技术公司的员工尝试获得管理员权限进行调试而造成 PwnKit 漏洞利用。CVE-2015-1701漏洞遭利用的案例是美国一家技术公司的员工尝试绕过现有控制来安装越权 Java 虚拟机。虽然上述这些案例都不可被视作恶意攻击，但它们通过修改设备应当运行的方式或在网络运行恶意或不安全程序的方式引入了风险。  
  
  
**内部错误引入风险**  
  
  
  
  
  
CrowdStrike 公司记录的近一半内部安全事件与非故意的错误有关，如利用测试不受控制、在未获得适当防护措施的情况下执行攻击性工具、下载未经审查的代码等。  
  
流入，CrowdStrike 公司表示，某些事件是由安全专业人员直接在生产工作站而非虚拟机上测试利用和利用包导致的。报告提到，多数攻击活动涉及多种工具如 Metasploit Framework 和 ElevateKit。因不谨慎而被引入的最常见漏洞如下：  
  
- CVE-2021-42013：Apache HTTP Server 2.4.49 和 2.4.50 中的路径遍历漏洞；  
  
- CVE-2021-4034 (PwnKit)：Polkit 系统服务中的界外漏洞；  
  
- CVE-2020-0601：Windows CryptoAPI 中的欺骗漏洞；  
  
- CVE-2016-3309：Windows 内核中的提权漏洞；  
  
- CVE-2022-21999：Windows Print Spooler 中的提权漏洞。  
  
  
  
在企业网络中引入这些缺陷可导致已经在网络中站稳脚跟的威胁行动者获得更多利用向量，从而增加整体安全风险。然而，更重要的是，威胁行动者创建虚假 PoC 或安全工具在设备上安装恶意软件的情况并不少见。例如，5月份，威胁行动者分发虚假的Windows PoC 利用，通过 Cobalt Strike 设备感染设备。在另外一起攻击活动中，Rapid 7 公司发现威胁行动者发布虚假的 0day 利用 PoC，安装 Windows 和 Linux 恶意软件。在这些场景中，在工作站上安装虚假利用可获得对企业网络的初始访问权限，从而导致网络间谍活动、数据被盗或勒索攻击。  
  
  
****  
代码卫士试用地址：  
https://codesafe.qianxin.com  
  
开源卫士试用地址：https://oss.qianxin.com  
  
  
  
  
  
  
  
  
  
  
  
  
**推荐阅读**  
  
[N-Able Take Control Agent 高危漏洞可用于 Windows 系统提权](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517665&idx=1&sn=81658f5393b8756f823fdc4fce193db9&chksm=ea94b48bdde33d9d1a37b9ab3441a2942eb184e06da7107fc6fd382aa2eecaf8ec0363f8e88f&scene=21#wechat_redirect)  
  
  
[Ubuntu 内核存在多个提权漏洞，近40%的用户易受攻击](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517243&idx=2&sn=329c721e1fa372f0edbd3acd7c5e9f14&chksm=ea94b551dde33c473259bf586af36e37db0dd338a1c952035c402307d640678be061c2fcc4b5&scene=21#wechat_redirect)  
  
  
[MikroTik RouterOS 存在严重的提权漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517243&idx=1&sn=a9d490b0fe3c69b9beed676a4ccc3e0e&chksm=ea94b551dde33c473e54e115f39d74066be049818db17f214e1674f3045916cd7ee85729c8b7&scene=21#wechat_redirect)  
  
  
[黑客利用WordPress 插件中的提权0day攻陷网站](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=**********&idx=1&sn=3861a45ade1fc801daa3c767fef3f318&chksm=ea94b386dde33a90582ec8599b01780524bb88c9f1a2cd4cfd9874889611594313e5443e60b1&scene=21#wechat_redirect)  
  
  
  
  
**原文链接**  
  
https://www.bleepingcomputer.com/news/security/privilege-elevation-exploits-used-in-over-50-percent-of-insider-attacks/  
  
  
题图：  
Pixabay  
 License  
  
****  
**本文由奇安信编译，不代表奇安信观点。转载请注明“转自奇安信代码卫士 https://codesafe.qianxin.com”。**  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSf7nNLWrJL6dkJp7RB8Kl4zxU9ibnQjuvo4VoZ5ic9Q91K3WshWzqEybcroVEOQpgYfx1uYgwJhlFQ/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSN5sfviaCuvYQccJZlrr64sRlvcbdWjDic9mPQ8mBBFDCKP6VibiaNE1kDVuoIOiaIVRoTjSsSftGC8gw/640?wx_fmt=jpeg "")  
  
**奇安信代码卫士 (codesafe)**  
  
国内首个专注于软件开发安全的产品线。  
  
   ![](https://mmbiz.qpic.cn/mmbiz_gif/oBANLWYScMQ5iciaeKS21icDIWSVd0M9zEhicFK0rbCJOrgpc09iaH6nvqvsIdckDfxH2K4tu9CvPJgSf7XhGHJwVyQ/640?wx_fmt=gif "")  
  
   
觉得不错，就点个 “  
在看  
” 或 "  
赞  
” 吧~  
  
