#  记一次实战逻辑漏洞挖掘-逻辑缺陷   
ZeroTrust  湘安无事   2023-11-25 15:05  
  
**点击蓝字**  
  
  
  
  
**关注我们**  
  
## 前言  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td width="557" valign="top" height="62" style="outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><section style="margin-bottom: 15px;outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 14px;color: rgb(217, 33, 66);visibility: visible;"><strong style="outline: 0px;visibility: visible;">声明：</strong></span><span style="outline: 0px;color: rgb(106, 115, 125);font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &#34;PingFang SC&#34;, Cambria, Cochin, Georgia, Times, &#34;Times New Roman&#34;, serif;font-size: 15.3px;letter-spacing: 0.544px;text-align: left;text-indent: 2em;visibility: visible;">这里是由零信任安全实验室组建的一个知识平台，平台有批量验证的脚本、工具以及一些漏洞的POC，后续还会分享网络安全资源（漏洞挖掘文章 工具 资讯<span style="outline: 0px;letter-spacing: 0.544px;text-indent: 30.6px;visibility: visible;">）</span>以及SRC漏洞挖掘案例分享等等，资源多多，干货多多！</span><span style="outline: 0px;color: rgb(106, 115, 125);font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &#34;PingFang SC&#34;, Cambria, Cochin, Georgia, Times, &#34;Times New Roman&#34;, serif;font-size: 15.3px;letter-spacing: 0.544px;text-align: left;text-indent: 30.6px;background-color: rgb(233, 237, 239);visibility: visible;"></span></section><section style="outline: 0px;color: rgb(106, 115, 125);font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &#34;PingFang SC&#34;, Cambria, Cochin, Georgia, Times, &#34;Times New Roman&#34;, serif;font-size: 15.3px;letter-spacing: 0.544px;text-align: left;text-indent: 2em;visibility: visible;">请勿利用文章内的相关技术从事非法测试，由于传播、利用此文所提供的信息或者工具而造成的任何直接或者间接的后果及损失，均由使用者本人负责，所产生的一切不良后果与文章作者和本公众号无关。工具<br style="outline: 0px;visibility: visible;"/>来自网络，安全性自测。</section></td></tr></tbody></table>  
  
  
      欢迎加入零信任攻防实验室的知识星球，星球免费！  
后续会抽奖送360定制周边礼品哈，敬请期待！平台有批量验证的脚本、工具以及一些漏洞的POC，和分享网络安全资源(漏洞挖掘文章 工具 资讯)  以及SRC漏洞挖掘的技巧分享等等，资源多多，干货多多！  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdUTIffnXkvpKYFt0CicksNOCSwhO0ia3fAMliaSPc0YTJvB9sx20MB3ZwL9zuIrmvOaohc8tUQJGibXh4w/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
## 前言  
  
本文中涉及到的漏洞已提交到专属src平台，并且在漏洞修复后得以公开。请勿在未授权状态下进行任何形式的渗透测试！！！！  
## Start  
  
一、信息收集  
  
俗话说得好，信息收集的够好，漏洞不会少。开局利用fofa，hunter，360Quqke等搜索引擎对目标进行子域、端口、网站等方面的信息进行收集，不同的搜索引擎收集到的资产也不同  
  
![](https://mmbiz.qpic.cn/mmbiz_png/txT26ZyLdURzyg1mtTJq4nNLL719Zl2DOIkT0nAUR1gdzicQNWvvj4mIPOH8lyHZQbVpk0Yqt8oV6MWfOYkL3Ug/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/txT26ZyLdURzyg1mtTJq4nNLL719Zl2D1gibKgv4pP4bJAY4a3xJVhuBiaXN2wxCTYSUYM5G2hibehd21SxcRiaWEw/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2DuQSp7dosXgAs0vPpUkgfh0hXEsx21GL5iaibAXfMn3qhnoib3lK0ltouQ/640?wx_fmt=jpeg "")  
  
渗透的本质是信息收集。而信息收集的本质并不仅仅是资产收集，而是在众多的资产里面挑选一些比较有用的资产进行测试，收集到再多的资产和信息，你不去进行测试，也没什么用。收集到相关资产之后，一个一个的去测试相关的功能，思考对应的功能可能会产生什么漏洞，并且每个都测试一遍。在子域名收集的过程中，我得到了一个类似于登录系统的网站，随后打开页面测功能点  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2D9P2OibibR2FXtrVRHqPyd9MIScrGibAzvaibibyQeGX4MDe56ckYyU8QNRg/640?wx_fmt=jpeg "")  
  
开局就一个登陆框，正常情况下，我会啪的一下输入13888888888/123456打过去。看看有没有一些开发的账号遗留下来，登录上去或许有一些收获或者信息  
  
如果提示  
账号不存在 ，或者密码错误。再拿出我的传家宝用户遍历字典及弱口令字典来进行一个定向爆破。  
  
但是这个时候提示  
账号或密码错误 ，然后就老老实实去注册，走正常流程了。  
  
这边注册完账号进行登录之后，bp抓取数据包，分析登录过程中所产生的一些数据包，并且对数据包中的一些参数做记录和分析，渗透过程中可能会用到这些参数  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2DicQGvOlYsqx4XefLlSvm3Q11jEz8iaMee2z8SuM3dU3u1v27KVsHq0fg/640?wx_fmt=jpeg "")  
  
然后发包，得到的响应如下  
  
![](https://mmbiz.qpic.cn/mmbiz_png/txT26ZyLdUTMl4GEBeWcd3UcSPdXTXG2Gw7EqA1tnrCrgNjf520GMFF2S4stcP9wsfkvtbKRoEL3yekuyKsv2A/640?wx_fmt=png "")  
  
当我发送第二次包的时候，它显示验证码获取失败，猜测这里做了校验  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2D9AeaHJ5LoQ2rcEaRM8QzicfJibqr7drj86Z30U3VOjpX4uYicfK2S9mXQ/640?wx_fmt=jpeg "")  
  
  
然后观察前面登录的时候里的数据包的参数，发现有个rediect_url参数，猜测这里是判断验证码获取的参数，然后尝试删除这个参数和后面的值，看看能不能进行绕过这个限制，如果检测机制做的不够严谨，没有检测参数的话，就可以绕过。发包，然后发现可以绕过这个短信的限制，然后再次发包，获取成功  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2D67XJusH9darBibc8T4GrBS5TJibY4mNwTg7waPYSWw3rmuic9icGwFElmg/640?wx_fmt=jpeg "")  
  
用burp并发发送数据包，就可以实现短信轰炸  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2DEaOfMCzNxibprGqEibN9ia3fUBGJx7WUPpAcKKLN4YwdMFcjWCAibweDCg/640?wx_fmt=jpeg "")  
  
饭钱到手。此时手机收到的短信：  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/txT26ZyLdURzyg1mtTJq4nNLL719Zl2DvKUsRO2fjH6ViaQpiaGht4LWmRf1a6roB3bNYahuJgNibgCouJU0OicrDA/640?wx_fmt=jpeg "")  
  
  
  
总结：  
  
在一些能接受验证码的地方，有一些参数是判定验证码有没有获取成功，或者说获取已发送短信的次数。我们测试短信轰炸漏洞的时候可以尝试删除一些参数然后观察响应结果。  
  
技术交流可加下方wx  
  
****  
****  
  
**|**  
**知识星球的介绍**  
  
不好意思，兄弟们，这里给湘安无事星球打个广告，不喜欢的可以直接滑走哦。添加下面wx加星球可享优惠  
  
1.群主为什么要建知识星球？  
```
很简单为了恰饭哈哈哈，然后也是为了建立一个圈子进行交流学习和共享资源嘛
相应的也收取费用嘛，毕竟维持星球也需要精力
```  
  
2.知识星球有哪些资源？  
```
群里面联系群主是可以要一些免费的学习资料的，因为群里面大部分是大学生嘛
大学生不就是喜欢白嫖，所以大家会共享一些资料
没有的群主wk也有,wk除了不会pc,其他都能嫖hhh
```  
  
一些实战报告，截的部分  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtODwbOOp88vXJ5mXX9NSIvA7UUtTDHJDhDCOrbSnT7UAsyyTlY1FyJhA/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtOWwAWoiaibCM3ibleLWSAKnsLREnwa09BkFZXfm5lRWtfbVwgKf0j3ISaw/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtO3qicwjmWdRCB5UxWt1jfnSfKBwD6yIyveRa3ENZ0KXVa9BtRRzD8GicA/640?wx_fmt=png "")  
  
一些1day的poc,这些也就是信息差，不想找可以让wk帮你们嫖,群主也会经常发  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtO0QGwS7xuY4vKe6X9SZCa87DMEEJJ68fnBiadQSQTDdfqgVQiaGZz9NbA/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtOdSLtwUia1EA2xEubNgTf3UYervduLFz4LWw33ic6fPw7vv1UQyLBEbrQ/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/S2ssjS1jNYtUiaMLiaTbAJqKj9icDvRsVtO4Sq6oGicP66MiaBT1o8YcVKQNLPytG0yB79mVmuXicA2foVBk3Ud5sfvw/640?wx_fmt=png "")  
  
一些共享的资源  
```
1.刀客源码的高级会员
2.FOFA在线查询与下载，key使用、360quake、shodan等
3.专属漏洞库
5.专属内部it免费课程
6.不定期直播分享（星球有录屏）
```  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/S2ssjS1jNYvS1u1PKCurEmuM61nGSElnNalHCy4YicPa9bZ23vMDPHzQPDxybG50b760tL8KcAYTGjBicGocsdXw/640?wx_fmt=jpeg "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/S2ssjS1jNYuCJm1WAIhc9XAa6OLI3ryvT32RpoHYTibSMVnsTh875E0Jk4XPduRqDicRQGMWHDD4RnueHudPHI3g/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/S2ssjS1jNYvM6WiaR5ibLImBVXffTWBPcwFRclvucl2KDBy7oCHGic78sP8CjxYf2QtRQNAxgn0BjfaLSH0ruUlCw/640?wx_fmt=jpeg "")  
  
