#  域渗透历史漏洞汇总   
点击关注→_→  黑客白帽子   2023-12-27 06:33  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0w6V8YUTyNSuV2udfyY3rWyR6V1UeHWuiab6T80I5ldZicZswCnrbicD4ibpaDMqCZ6UvFmhWLyTzptSA/640?wx_fmt=png&random=0.6636094571400317&random=0.6219011309810436&random=0.21191420540585404 "")  
  
**感谢师傅 · 关注我们**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0w6V8YUTyNSuV2udfyY3rWyR6V1UeHWuiab6T80I5ldZicZswCnrbicD4ibpaDMqCZ6UvFmhWLyTzptSA/640?wx_fmt=png&random=0.9829534454876507&random=0.2787622380037358&random=0.29583791053286834 "")  
  
  
由于，微信公众号推送机制改变，现在需要设置为星标才能收到推送消息。大家就动动发财小手设置一下呗！啾咪~~~  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0y50hQk1TiaBIAnSjzqkmZcPS4TWvohHfHPTVUBWM2mFxcqwhiaZKaQM6S7t11fuiajZ2zZqXD5hJJmA/640?wx_fmt=png "")  
  
## MS14-068(CVE-2014-6324)  
  
Kerberos 校验和漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2014-6324
```  
```
```  
  
EXP/POC:  
```
 https://github.com/abatchy17/WindowsExploits/tree/master/MS14-068
```  
```
```  
## CVE-2020-1472  
  
Netlogon特权提升漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2020-1472
```  
```
```  
  
EXP/POC:  
```
 https://github.com/blackarrowsec/redteam-research/tree/master/CVE-2020-1472
```  
```
```  
## CVE-2021-42287&42278  
  
Windows域服务权限提升漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2021-42287
 https://nvd.nist.gov/vuln/detail/CVE-2021-42278
```  
```
```  
  
EXP/POC:  
```
 https://github.com/WazeHell/sam-the-admin
 https://github.com/cube0x0/noPac
```  
```
```  
## CVE-2019-1040  
  
Microsoft Windows NTLM认证漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2019-1040
 https://paper.seebug.org/962/
```  
```
```  
  
EXP/POC:  
```
 https://github.com/Ridter/CVE-2019-1040
```  
```
```  
## CVE-2018-8581  
  
Microsoft Exchange任意用户伪造漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2018-8581
```  
```
```  
  
EXP/POC:  
```
 https://github.com/Ridter/Exchange2domain
```  
```
```  
## CVE-2020-0688  
  
Microsoft Exchange 反序列化RCE  
```
 https://nvd.nist.gov/vuln/detail/CVE-2020-0688
```  
```
```  
  
EXP/POC:  
```
 https://github.com/zcgonvh/CVE-2020-0688
```  
```
```  
## CVE-2021-1675  
  
Windows Print Spooler权限提升漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2021-1675
```  
```
```  
  
EXP/POC:  
```
 https://github.com/cube0x0/CVE-2021-1675
```  
```
```  
## CVE-2021-26855/CVE-2021-27065  
  
Exchange ProxyLogon远程代码执行漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2021-26855
 https://nvd.nist.gov/vuln/detail/CVE-2021-27065
```  
```
```  
  
EXP/POC:  
```
 https://github.com/hausec/ProxyLogon
```  
```
```  
## CVE-2020-17144  
  
Microsoft Exchange 远程代码执行漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2020-17144
```  
```
```  
  
EXP/POC:  
```
 https://github.com/Airboi/CVE-2020-17144-EXP
```  
```
```  
## CVE-2020-16875  
  
Microsoft Exchange 远程代码执行漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2020-16875
```  
```
```  
  
EXP/POC:  
```
 https://srcincite.io/pocs/cve-2020-16875.py.txt
```  
```
```  
## CVE-2021-34473  
  
Exchange ProxyShell SSRF  
```
 https://nvd.nist.gov/vuln/detail/CVE-2021-34473
```  
```
```  
  
EXP/POC:  
```
 https://github.com/dmaasland/proxyshell-poc
```  
```
```  
## CVE-2021-33766  
  
Exchange ProxyToken 信息泄露漏洞  
```
 https://nvd.nist.gov/vuln/detail/CVE-2021-33766
```  
  
  
EXP/POC:  
```
 https://github.com/bhdresh/CVE-2021-33766-ProxyToken
```  
```
```  
## 结尾  
```
原文作者：Leticia  原文链接：http://uuzdaisuki.com/2022/01/20/%E5%9F%9F%E6%B8%97%E9%80%8F%E5%8E%86%E5%8F%B2%E6%BC%8F%E6%B4%9E%E6%B1%87%E6%80%BB/#CVE-2020-16875
```  
  
**下载地址**  
  
**点击下方名片进入公众号**  
  
**回复关键字【**  
******23**  
**1227**  
******】获取**  
**下载链接**  
  
  
  

								  

									  

										  

											  
往期推荐  

										  

									  

									  

								[ 一款功能强大的通用漏洞扫描工具 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937786&idx=1&sn=51047eb39a5f499cf031858b972c6a71&chksm=8bac5545bcdbdc535a3b507cec59499bb4369cc26c80209de0304bdebf2328a4b4066cb16340&scene=21#wechat_redirect)  

							  
  

								[ 【Web渗透】Fuzz大法 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937748&idx=1&sn=b48197c008a83a42c0753a4d586b2e53&chksm=8bac556bbcdbdc7d33fe64e436eb334c3c726976a89552bebd9a9faebcaed5a664ffcee94f66&scene=21#wechat_redirect)  

							  
  

								[ 记一次对某黄色软件破解次数限制和金币视频 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937650&idx=1&sn=cfb78d0985af32baae95639187d9b04d&chksm=8bac55cdbcdbdcdb5e80b46ee9a8d0948d704506ac76b7209001f521a4c38500b62b55444946&scene=21#wechat_redirect)  

							  
  

								[ 【漏洞通告】Zabbix zbx_session cookie泄露漏洞（CVE-2023-32725） ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937587&idx=1&sn=7ba65ef1a78c0e769e7a4ce96a383ac1&chksm=8bac540cbcdbdd1a4222a386173cf9bc9d26e838fa03b8c8735a26cb82787ce26467b04c56a6&scene=21#wechat_redirect)  

							  
  

								[ 验证码渗透最全总结 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937587&idx=2&sn=9566421f87de935c45dd318b7d204a6c&chksm=8bac540cbcdbdd1a9c5d172d400f1bbb271bfb6670a7d2d17f75f94ca8852a42e9cd9a33590c&scene=21#wechat_redirect)  

							  
  

								[ LoaderGo-快速生成免杀木马GUI版本，bypass主流杀软 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937494&idx=1&sn=95e5a8d4a958600bad70b6e5e05689ba&chksm=8bac5469bcdbdd7f8185179e87515e9b193b2f6715725331e8d83eceaa261b2e424b602b1913&scene=21#wechat_redirect)  

							  
  

								[ Apache Struts2 文件上传漏洞分析（CVE-2023-50164） ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937381&idx=1&sn=8af61531b4b47ab34f7d345c27678571&chksm=8bac54dabcdbddcc58a9b967d2de5e4d203bb52939555e138696048f172fa63bb3c5600459ff&scene=21#wechat_redirect)  

							  
  

								[ 记某系统有趣的文件上传 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937279&idx=1&sn=0ec1d18c4f91e143fd8e597df7fa1172&chksm=8bac5b40bcdbd2568fa8ee35e3e25112523c5347901d6492263fa036ac8d27606ec854e398cf&scene=21#wechat_redirect)  

							  
  

								[ 记一次前端加解密到sql注入过waf的实战 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937172&idx=1&sn=a845c35036f8e31d392d193e1ccae75d&chksm=8bac5babbcdbd2bd56ac310ed85680f4b6d90752c69c0cdcac01f3b9539800b92205a08b3990&scene=21#wechat_redirect)  

							  
  

								[ Android App半自动化静态漏洞挖掘技术分析 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650937097&idx=1&sn=f4135fbf1cd9a6c2b5eaefeb99ee31e7&chksm=8bac5bf6bcdbd2e02271fdc3f15db4202562504ffe827d02b70264436e76bad050de9c4e90c3&scene=21#wechat_redirect)  

							  
  
  
  
声明：本公众号所分享内容仅用于网安爱好者之间的技术讨论，禁止用于违法途径，**所有渗透都需获取授权**  
！否则需自行承担，本公众号及原作者不承担相应的后果  
```
```  
  
  
