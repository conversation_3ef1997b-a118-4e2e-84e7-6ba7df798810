#  蓝牙曝底层安全漏洞，数十亿设备受影响   
 黑白之道   2023-12-02 08:47  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/3xxicXNlTXLicwgPqvK8QgwnCr09iaSllrsXJLMkThiaHibEntZKkJiaicEd4ibWQxyn3gtAWbyGqtHVb0qqsHFC9jW3oQ/640?wx_fmt=gif "")  
  
来自Eurecom的研究人员近期分享了六种新型攻击方式，统称为“BLUFFS”，这些攻击方式能够破坏蓝牙会话的保密性，使设备容易受到冒充和中间人攻击(MitM)。攻击发现者Daniele Antonio<PERSON>解释道，“BLUFFS”利用了蓝牙标准中两个以前未知的漏洞，这些漏洞与会话密钥的派生方式以及交换数据的解密过程有关。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39iapP8sPbMTFVTvettDGicvqL008ic3d7pe7JYwFl932UL9ZwH5E57Qiarj8md5MDpCUdsR338Rj8SOQ/640?wx_fmt=jpeg&from=appmsg&wxfrom=13&tp=wxpic "")  
  
  
这些漏洞并非受限于特定的硬件或软件配置，而是系统性的问题，也就是说它们对蓝牙技术产生了根本性的影响。该漏洞被标识为CVE-2023-24023，影响范围包括蓝牙4.2版到5.4版。  
  
  
考虑到蓝牙作为一种广泛应用的成熟无线通信标准，以及受到这些漏洞影响的版本，“BLUFFS”可能对数十亿设备构成威胁，包括笔记本电脑、智能手机和其他移动设备。  
  
  
  
**“BLUFFS”攻击原理**  
  
  
## “BLUFFS”是一系列针对蓝牙的攻击方式，旨在破坏蓝牙会话的过去和未来的保密性，对设备之间的通信造成威胁。它通过利用四个会话密钥派生过程中的漏洞（其中两个是新的）来实现，迫使派生出一个短且容易预测的会话密钥(SKC)。攻击者通过暴力破解密钥，能够解密过去的通信内容，并解密或操控将来的通信。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39iapP8sPbMTFVTvettDGicvq55kbsNz9kYQKkkG5QxoVWhN4W4Cia7LSBKR4YnbZbHvw7NdialJNlOxw/640?wx_fmt=jpeg&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
要  
执行这种攻击方式，攻击者需要在两个正在交换数据的目标设备的蓝牙范围内，并冒充其中一个设备，与另一个设备协商建立一个弱会话密钥，针对最小可能的密钥熵值并使用固定的会话密钥差分器。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39iapP8sPbMTFVTvettDGicvqQAFgibQvpbhsic3bak6WpC811Ep1QuAkazNibumnmcFOgqWEgh98zS1kA/640?wx_fmt=jpeg&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
发表的论文详细介绍了六种类型的"BLUFFS"攻击，涵盖了冒充以及中间人攻击的各种组合，无论受害者是否支持安全连接(Secure Connections, SC)或传统安全连接(Legacy Secure Connections, LSC)，这些攻击方式都能够实施。  
  
  
研究人员在GitHub上开发并分享了一个工具包，展示了"BLUFFS"攻击的有效性。该工具包包括用于测试攻击的Python脚本、ARM补丁、解析器以及在测试过程中捕获的PCAP数据样本。  
  
  
  
**影响范围巨大**  
  
  
## “BLUFFS”攻击影响多个版本的蓝牙系统，从2014年12月发布的4.2版本，一直到最新的2023年2月发布的5.4版本。  
  
  
Eurecom的论文展示了针对多种设备（包括智能手机、耳机和笔记本电脑）进行的"BLUFFS"测试结果，这些设备上运行蓝牙系统是4.1至5.2版本。最终测试结果发现，包含六种攻击方式的"BLUFFS"攻击，至少有三种攻击方式产生了有效结果，这应该引起行业的重视。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39iapP8sPbMTFVTvettDGicvqvibhO3AHmDuotQSPibsjXKK53CFjuVLobianxO0xcR1vHhic0hib1o3RpMA/640?wx_fmt=jpeg&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
论文还提出了以下修改建议，这些修改将增强会话密钥派生机制，并减轻"BLUFFS"及类似威胁的影响：  
  
- 引入一个新的密钥派生函数(KDF)用于传统安全连接(LSC)，该函数涉及互相交换和验证随机数；  
  
- 设备应使用共享配对密钥来相互验证会话密钥差分器的合法性，确保会话参与者的合法性。  
  
- 尽可能强制使用安全连接(SC)模式。  
  
- 维护会话密钥差分器的缓存，以防止重用。  
  
蓝牙SIG（特殊兴趣小组）作为一个非营利组织，负责监督蓝牙标准的发展并负责授权技术的使用，已经收到了Eurecom的报告，并在其网站上发布了一份声明。该组织建议实施各种强加密措施，例如拒绝连接强度低于七个字节的连接，并使用"安全模式4 级别4"，以确保更高的加密强度，并在配对时仅使用安全连接模式进行操作。  
  
  
> **文章来源：freebuf**  
  
  
  
黑白之道发布、转载的文章中所涉及的技术、思路和工具仅供以安全为目的的学习交流使用，任何人不得将其用于非法用途及盈利等目的，否则后果自行承担！  
  
如侵权请私聊我们删文  
  
  
**END**  
  
