#  【已复现】亿赛通电子文档安全管理系统远程代码执行漏洞   
 长亭安全应急响应中心   2023-12-13 20:11  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicR73DQHnLribiaxKN6Q51fubWyiayreJUDRibxEq7iaI63XQvic7fX3wWhd45cdxyrhQedgsrK80QibL0AzA/640?wx_fmt=png&from=appmsg "")  
  
  
亿赛通电子文档安全管理系统是一个集文档加密、权限控制和操作审计于一体的软件，用于提升企业文档的安全性和管理效率。  
  
  
2023年12月，互联网公开了一个亿赛通的远程代码执行漏洞。该漏洞利用无需前置条件，建议使用该系统的用户尽快修复。  
  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因亿赛通由于安全控制不严格，攻击者可以通过特定方式读取服务器上的某些重要文件。利用读取到的信息（如账号和密码），攻击者可以上传文件到服务器的特定位置。利用特征数据包中可能包含明显的目录遍历痕迹，如使用../等路径。攻击者通过这种方式尝试读取任意文件。漏洞影响远程代码执行风险：攻击者可能利用该漏洞在服务器上执行任意代码。数据泄露风险：敏感数据可能被未授权访问或窃取。勒索软件攻击风险：攻击者有可能上传并执行勒索软件，导致业务系统被锁定。影响版本 Affected Version 02version <= V5.***********解决方案 Solution 03临时缓解方案访问控制：加强服务器和应用的访问控制，仅允许可信IP进行访问。另外如非必要，不要将8021端口开放在互联网上。值得注意的是该漏洞位于非标准的8021服务端口（默认情况），而非常规Web端口，因此需要对这个特定端口进行专门的检查和实施相应的保护措施。监控和日志记录：加强对Web应用的监控和日志记录，特别是关注异常上传行为和路径遍历尝试。升级修复方案亿赛通官方已发布安全更新，建议访问官网（http://www.esafenet.com）联系售后升级至11月补丁版本。漏洞复现 Reproduction 04检测工具 Detection 05X-POC远程检测工具检测方法：xpoc -r 417 -t http://xpoc.org工具获取方式：https://github.com/chaitin/xpochttps://stack.chaitin.com/tool/detail/1036牧云本地检测工具检测方法：在本地主机上执行以下命令即可无害化扫描：yisaitong_rce_ct_1002008_scanner_windows_amd64.exe scan工具获取方式：https://stack.chaitin.com/tool/detail/1263产品支持 Support 06云图：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。洞鉴：以自定义POC形式支持该漏洞的原理检测。雷池：默认支持该漏洞利用行为检测。全悉：默认支持该漏洞利用行为检测。牧云：使用管理平台 23.05.001 及以上版本的用户可通过升级平台下载应急漏洞情报库升级包（EMERVULN-23.12.013）“漏洞应急”功能支持该漏洞的检测；其它管理平台版本请联系牧云技术支持团队获取该功能。时间线 Timeline 0712月12日 互联网公开漏洞情报12月13日 长亭应急响应实验室复现漏洞12月13日 长亭安全应急响应中心发布通告参考资料：[1].https://www.esafenet.com/  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
