#  分享挖洞中遇到有趣的漏洞   
原创 莫大130  安全逐梦人   2024-01-27 22:39  
  
## 分享项目中遇到的漏洞   
> 分享工作中挖掘到漏洞，相关漏洞目前均已修复，相关截图均已进行脱敏处理  
  
没啥技术含量，纯脚本小子有手就行，大佬勿喷。  
  
  
  
  
开局一个目标站点，基本上就一个登录页面，没有测试账户，还对登录账户进行了数据加密，最常见的就是对js文件内容进行了混淆。  
#### 一般这种站挖洞思路：  
  
1、扫描目录  
  
2、使用FindSomething查看js中的api接口  
  
3、使用Yakit批量对js接口发送请求，查看返回数据长度和状态码  
### ffuf fuzz目录  
  
fuff扫描速度快，配合SecLists字典，很容易扫描出来其他脆弱资产  
```
https://github.com/ffuf/ffuf

https://github.com/danielmiessler/SecLists/blob/master/Discovery/Web-Content/directory-list-lowercase-2.3-big.txt

```  
### FindSomething  
  
超级好用的api接口提取插件，平时做项目挖洞就是神器啊  
```
https://github.com/momosecurity/FindSomething

```  
##  实战   
  
通过ffuf扫描出来report目录，使用FindSomething工具提取api接口，用yakit批量对接口发送GET/POST请求，根据状态码和长度来判断  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vOGOib9z4Wz4bbe9A5BWib8R7LzjohxvP0gzBTLy7FRjV4wIMIPfiaKZQJLEgOxoXBESje52eokPI7wiaHdo9INSDQ/640?wx_fmt=png&from=appmsg "")  
  
  
在xx/config接口中返回报错  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vOGOib9z4Wz4bbe9A5BWib8R7LzjohxvP0vozlYovn0OwJnURoqeM2zL8lmu4eQwkibxqibeTiclCC8nuUGPmiaDZkyw/640?wx_fmt=png&from=appmsg "")  
  
通过a  
uth.js查询到接口位置，查看  
参数，构造请求包，  
访问  
xx/config?nmae=data-report ，会提示没有权限，在header头加上token:1 ，接口  
就会生成一  
个阿里云oss临时的key,导致接管oss存储通，泄露大量数据  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vOGOib9z4Wz4bbe9A5BWib8R7LzjohxvP0UF2iaM8N9gxrCRSWZibRfvwo0or3H1zS0GRsepK8Na7hJygyfyAUskpA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vOGOib9z4Wz4bbe9A5BWib8R7LzjohxvP0hD4QxLJ6GBtbHdWvicetWNZ0N41FiaGvibZtfj70fY7dBEXQo64QfiaicEQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
