#  已修补关键 PHP 命令执行漏洞CVE-2024-1874漏洞等：立即更新以减轻攻击   
 Ots安全   2024-04-16 18:00  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/bL2iaicTYdZn7gtxSFZlfuCW6AdQib8Q1onbR0U2h9icP1eRO6wH0AcyJmqZ7USD0uOYncCYIH7ZEE8IicAOPxyb9IA/640?wx_fmt=gif "")  
  
PHP 开发团队针对影响版本 8.1.28、8.2.18 和 8.3.6 的多个漏洞发布了紧急安全补丁。这些漏洞，从关键的命令注入缺陷到潜在的帐户泄露，需要依赖 PHP 开发网站和应用程序的用户和开发人员立即关注。  
  
**主要漏洞和潜在影响**  
  
CVE-2024-1874（严重）：Windows 系统上的命令行处理不当可能会导致攻击者注入任意命令，如果 PHP 应用程序执行批处理 ( *.bat) 或命令 ( *.cmd) 文件，则可能导致整个系统被接管。  
  
CVE-2024-2756（中）：对先前补丁的不完整修复可能会导致攻击者设置恶意 cookie，PHP 应用程序会将这些 cookie 误认为是安全的，从而为劫持用户会话或发起跨站点攻击打开了大门。  
  
CVE-2024-3096（低）：一个罕见但潜在严重的缺陷可能会导致攻击者在使用 的系统中绕过密码身份验证password_hash。这需要极不可能出现的情况，即用户的密码以空字节开头。  
  
CVE-2024-2757（高）：mb_encode_mimeheader该功能的某些输入可能会触发无限循环。此漏洞有可能通过中断电子邮件处理来导致拒绝服务攻击。  
  
  
  
  
感谢您抽出  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycNnFvFYVgXoExRy0gqCkqvrAghf8KPXnwQaYq77HMsjcVka7kPcBDQw/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycd5KMTutPwNWA97H5MPISWXLTXp0ibK5LXCBAXX388gY0ibXhWOxoEKBA/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycU99fZEhvngeeAhFOvhTibttSplYbBpeeLZGgZt41El4icmrBibojkvLNw/640?wx_fmt=gif "")  
  
来阅读本文  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWge7Mibiad1tV0iaF8zSD5gzicbxDmfZCEL7vuOevN97CwUoUM5MLeKWibWlibSMwbpJ28lVg1yj1rQflyQ/640?wx_fmt=gif "")  
  
**点它，分享点赞在看都在这里**  
  
