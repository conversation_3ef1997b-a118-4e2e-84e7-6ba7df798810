#  实战CVE漏洞分析与防范（第一季）   
看学课程  看雪学苑   2023-12-03 18:00  
  
世界上主流软件厂商开发的软件、程序，如果被发现存在漏洞，而且有一定的影响力，那么这些漏洞信息都会被保存在一个叫做CVE的数据库中进行统一的管理。  
  
  
这样一来，只要我们有某条CVE漏洞的编号，就能从CVE的官网搜索到出现漏洞的操作系统、版本号和软件等信息，从而搭建一个相同的环境，对漏洞进行研究与重现。  
  
  
想要深入了解各类  
CVE漏洞，get独立挖掘和分析漏洞的能力吗？  
  
看雪人气讲师最新课程 **实战CVE漏洞分析与防范（第一季）**  
  
  
让你如愿以偿！  
  
  
   
  
   
  
   
  
**课程简介**   
  
   
  
   
  
  
本课程为**系列课程第一季**。讲师会精心挑选各类CVE漏洞来与大家分享，并从多个维度来分析研究漏洞，以启发式的教学方法为大家呈现  
各种漏洞对抗技术，让大家有所启迪。  
  
  
通过理论+实践相结合的方式，讲师将带领大家在实战中学习如何利用一条又一条的高级特征与查杀技术，去彻底堵死这些漏洞攻击程序蔓延的势头。同时从  
安全编程技能、传统对抗方式以及启发式查杀技术等角度，让大家学习如何防范CVE漏洞。  
  
  
此外， 课程中也会穿插讲解一些关于Web安全、逆向、编程、恶意程序以及网络数据包分析等知识，让你成为更多元化、能力更全面的技术人才。  
  
  
相信大家学完之后，都能拥有和各种恶意代码对抗所必备的技术能力，并在不断的练习中，  
成为能与破坏计算机的骇客作斗争的顶级黑客。  
  
  
  
   
  
   
  
   
  
**讲师介绍**   
  
   
  
   
  
  
**姜晔**  
，看雪高人气讲师，有非常丰富的网络授课经验。曾就职于卡巴斯基实验室，任病毒分析师，负责病毒分析以及高级查杀技术的研究。目前就职于北京师范大学珠海校区，担任信息技术学院的教师，主讲计算机网络安全、C++程序设计以及物联网应用等课程。  
  
  
  
   
  
   
  
   
  
**课程大纲**   
  
   
  
  
**第00章 课程前言**  
  
0.1 课程前言  
  
0.2 写在最后的话  
  
0.3 学前须知  
  
  
**第01章 CVE-2006-3439 栈溢出漏洞**  
  
1.1 CVE-2006-3439漏洞分析总论  
  
1.2 CVE-2006-3439漏洞的静态分析  
  
1.3 PoC的编写与动态调试  
  
1.4 补丁后netapi32.dll的静态分析  
  
1.5 补丁后PoC的编写与动态调试  
  
1.6 如何进行补丁对比  
  
1.7 wcscpy()与wcscat()函数的异同  
  
  
**第02章 CVE-2008-4250 RPC漏洞**  
  
2.1 CVE-2008-4250漏洞分析总论  
  
2.2 先要弄清楚两个问题  
  
2.3 CVE-2008-4250漏洞的静态分析  
  
2.4 PoC的编写与动态调试  
  
2.5 Conficker蠕虫的提取  
  
2.6 Conficker蠕虫的对抗策略  
  
2.7 CIFS（SMB）协议分析  
  
  
**第03章 CVE-2010-0249 释放重引用漏洞**  
  
3.1 CVE-2010-0249漏洞分析总论  
  
3.2 CVE-2010-0249漏洞网络流量分析  
  
3.3 漏洞的触发与转储文件的获取  
  
3.4 崩溃回溯分析  
  
3.5 PoC代码分析  
  
3.6 释放重引用漏洞与引用计数  
  
3.7 TCP与HTTP协议简介  
  
  
**第04章 CVE-2008-2992 栈溢出漏洞**  
  
4.1 CVE-2008-2992漏洞分析总论  
  
4.2 使用PDFStreamDumper分析PDF文件  
  
4.3 Shellcode分析：获取kernel32.dll的内存地址  
  
4.4 Shellcode分析：定位API函数  
  
4.5 Shellcode分析：函数的调用与总结  
  
4.6 CVE-2008-2992漏洞成因分析  
  
4.7 采用静态启发技术对抗PDF漏洞攻击  
  
4.8 采用动态启发技术对抗PDF漏洞攻击  
  
4.9 查杀思想补充与注意事项  
  
4.10 PDF文件格式解析  
  
4.11 PE结构常用的两种地址转换  
  
4.12 两个关于文件类漏洞的实验  
  
  
**第05章 CVE-2016-7103 XSS漏洞**  
  
5.1 CVE-2016-7103漏洞分析总论  
  
5.2 CVE-2016-7103漏洞原理分析  
  
5.3 XSS攻防演练实验  
  
5.4 反射型XSS漏洞挖掘实例  
  
5.5 隐马尔可夫模型与XSS漏洞检测  
  
5.6 正则表达式入门  
  
5.7 XSS入门科普  
  
  
**第06章 CVE-2011-1249 本地提权漏洞**  
  
6.1 CVE-2011-1249漏洞分析总论  
  
6.2 CVE-2011-1249漏洞原理分析  
  
6.3 双机调试的配置方法  
  
6.4 内核漏洞分析常见概念  
  
6.5 Connect函数执行流程剖析（Ring3层）  
  
6.6 Connect函数执行流程剖析（Ring0层）  
  
![](https://mmbiz.qpic.cn/mmbiz_png/iaRRWxkaf6SHOHhXDGrmAUWeGM6TTYk4WGNF6dKlxbYUDjHibxHCYnDA2eXuibhzGL48gF3tdTjpWtqLjY39mERZA/640?wx_fmt=png "")  
  
  
  
  
   
  
   
  
   
  
**课程购买**  
  
   
  
   
  
  
1. 现在**抢先购买课程**  
，**享****8折优惠**  
！  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8Gia3zqxn9bAJxnJGxduw2k8gc2sxzfI7ajtXGOo5FliaF8BzD3pjYpS6sVrjEAeA9jEzMKnm2exiaUA/640?wx_fmt=png "")  
  
**¥1280**  
  
扫描二维码立即报名  
  
   
  
2. 报名成功后，请添加工作人员微信号：kanxuecom，进入课程群。  
  
  
还在为面试安全工程师时，面试官灵魂拷问你“是否挖掘或分析过CVE漏洞”而头秃吗？  
  
  
想要在老师的辅导下，技术水平快速出现质的飞跃吗？  
  
  
想要get安全工程师的必备技能吗？  
  
  
这门课程就是你最好的选择！  
  
  
**一步一步****积累技能后，你总会无坚不摧的。**  
  
  
**赶快加入学习吧~**  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/b96CibCt70iaa8r7PJoyAtlfHAKe8RosE3wYVKBac55p1HPBJHZS42ywnG4yYtD3jo9A9e5kawBZs4IE6R1C4wibw/640?wx_fmt=gif "")  
  
- End -  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/Uia4617poZXP96fGaMPXib13V1bJ52yHq9ycD9Zv3WhiaRb2rKV6wghrNa4VyFR2wibBVNfZt3M5IuUiauQGHvxhQrA/640?wx_fmt=jpeg "")  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz/z9433rAGTDd78cwaDvzakb7575ic82NHaKASbJ2j330Auic2Ft9xA6W1fIhzeWib47ju2MNkhofiaumYKD9YltcqTQ/640?wx_fmt=gif "")  
  
点击**阅读原文**，即可进入  
《实战CVE漏洞分析与防范（第一季）》  
！  
  
