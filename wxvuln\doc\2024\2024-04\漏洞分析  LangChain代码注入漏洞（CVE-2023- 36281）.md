#  漏洞分析 | LangChain代码注入漏洞（CVE-2023- 36281）   
 船山信安   2024-04-15 00:19  
  
## 漏洞概述  
  
LangChain是一个 LLM 编程框架，通过可组合性使用LLM构建应用程序。LangChain 0.0.171版本存在代码注入漏洞，远程攻击者可以通过load_prompt函数的JSON文件来执行任意代码。该漏洞与subclasses或template有关。  
## 受影响版本  
  
0.0.171 =< LangChain < 0.0.312  
## 漏洞分析                                                                         
  
该漏洞的根本原因是服务端在接收用户输入或用户可控参数后，未作处理或未进行严格过滤，将用户的输入直接拼接到模板中进行渲染，造成模板注入攻击。该漏洞是利用python的jinja2框架，根据官方补丁发现漏洞函数jinja2_formatter()，在该函数中增加了一段安全告警，指出jinja2模板并不具备沙盒保护机制，可能导致任意Python代码的执行。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibqwUoRjFlb4Rjy0tNwAEsBXRtD5ZqKub832icZ6DibPWkGJicqRZQEtk4w/640?wx_fmt=png&from=appmsg "")  
  
该函数使用jinja2格式化模板，调用render方法并传入关键字参数对模板进行渲染，最后返回渲染后的字符串。下一步寻找该函数的调用位置：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibb3rhW7AuKibbr7sGLbko3kOhGGnG2S5rkQicVK7oAKMEalmF9GWfLJCA/640?wx_fmt=png&from=appmsg "")  
  
DEFAULT_FORMATTER_MAPPING字典表示了不同的格式化方式对应的格式化函数，jinja2对应的格式化函数是jinja2_formatter。接着找到在哪里用到了该字典：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibz8791iah6989gQW9dOPTxjdngRYZLEGVNPzWoJk3Pwt3dbQpQOvZRJg/640?wx_fmt=png&from=appmsg "")  
  
该format函数在PromptTemplate提示模板工具类中，将输入的关键字参数传递给prompt模板以格式化字符串，使用DEFAULT_FORMATTER_MAPPING中与template_format对应的格式化函数处理模板字符串。下一步找到PromptTemplate类的利用点，根据漏洞描述，攻击者通过load_prompt函数的JSON文件来执行任意代码。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibCngLYdjwgmribWhDTpgnexfoTFPfGPtibia6bf6KzA0f7TTEwl6gcjk2Q/640?wx_fmt=png&from=appmsg "")  
  
先来分析下load_prompt加载提示信息，try_load_from_hub 是尝试从给定的路径远程加载文件，但是因为我们是加载本地文件，所以接下会跳转到 _load_prompt_from_file：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibtXjNsnRt7SBXyoHsTjnunP2Lw3lLx4UUpsfXOFt95a1RPsjicIR6lFA/640?wx_fmt=png&from=appmsg "")  
  
从文件中加载提示模板，根据文件的后缀名来确定文件类型，不同的文件类型使用不同的函数加载配置，最后将加载到的配置传递给load_prompt_from_config函数，下一步跳到load_prompt_from_config函数：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibqsLRFKEQljsdmaTNYawXeCSW8Yjcrx8FlGv4KiclLllQWbyUEFXUxHg/640?wx_fmt=png&from=appmsg "")  
  
load_prompt_from_config函数是从配置字典中加载提示，可能是包含各种数据或者文件，最后跳转到_load_prompt：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nib0q3mQClbict4R7yAjic93ZfbIW7HrsQibGm0mEH8g3xlqmLM1c0m2bmGA/640?wx_fmt=png&from=appmsg "")  
  
从配置文件中加载具体的提示模板，并返回了一个PromptTemplate对象，与前面的PromptTemplate 类关联起来。其实漏洞利用的过程可以分为三步：  
1. 加载提示模板load_prompt  
  
1. 对加载的模板格式化format  
  
1. 使用jinja2格式化模板，对模板进行渲染  
  
## 漏洞复现  
  
Langchain可以加载一个json文件：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibnaHJCEMYgMiaXyVIafXELKXxuCsQmckQBmqygRISn2wZPOhPqvJDf5Q/640?wx_fmt=png&from=appmsg "")  
  
'load_prompt'使用jinja2连接提示。由于在jinja2中是可以直接访问python的一些对象及其方法的，所以可以通过构造继承链来执行一些操作，写入一些恶意payload，加载json文件时触发模板注入attack_prompt.json。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibFquYiaPicI1icJUy4BIkGxp5VmPE0SbH0qPIvicNMaE4etahebDZWJ7sfw/640?wx_fmt=png&from=appmsg "")  
  
获取subprocess.popen在每个python环境中的不同索引。先通过  
class获取字典对象所属的类，再通过   
base（  
bases[0]）拿到基类，然后使用  
subclasses()获取子类列表，在子类列表中直接寻找可以利用的类。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibicNOJyIwdaCXqK9h8eJiaSpmic35gzTOUia7QPt4p0BZWnGsLBicnxT6MTA/640?wx_fmt=png&from=appmsg "")  
  
复现结果：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicOx6eV1px50zE7w1csGs2nibbctXHZgtcuApQ4G4WQxUjI3icVlHicywfM4LawC5kicDvdIhzcqia3vJ4Q/640?wx_fmt=png&from=appmsg "")  
## 修复方案    
  
建议您更新当前系统或软件至最新版，完成漏洞的修复。  
  
来源：https://www.freebuf.com/  
  
  
