#  一个*份证后四位的验证思路-逻辑漏洞挖掘   
原创 漏洞挖掘  渗透安全HackTwo   2023-12-27 00:00  
  
**0x01信息收集**  
  
  
通过抖*、快*等APP获得其ID为：***011201  
```
姓名：王**

通过其作品获得地址为：⼭东泰安

学校：⼭东*****
```  
  
**文章末尾可领取资料（渗透字典、app渗透教程、钓鱼例子、书籍等）**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7MxXiagzdRGojKO9rDjgHuHvsJJpPMze0bpMibzhGuBOXqfoUg3goVJfFQ/640?wx_fmt=png&from=appmsg "")  
  
****  
**0x02信息爆破**  
  
发现某校公众号，有个⽹上缴费⻚⾯，可通过*号、*机号或*份证号判断是否存在该小伙伴。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7Md6ekcOmhKDsOJsRbmicbQv4UdVdShVrtnlup7ibkia12bIkpIUueM7t2w/640?wx_fmt=png&from=appmsg "")  
  
尝试爆破，发现  
waf拦截。只能换思路。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7MDsibZNLxknFiceeicGT9XIjPj5XY1iaQSyEdKeVLpGUpPSJ9fjOxA9YLkw/640?wx_fmt=png&from=appmsg "")  
  
**0x02构造*份证**  
  
通过前⾯获取的信息，可以获得该⼈  
*份证  
前  
6位及出⽣⽇期，构造前14位：370902 地区 20011201 出*⽇期  
```
⽽后4位中，第17位为偶，及[0,2,4,6,8]；第18位为[0,1,2,3,4,5,6,7,8,9,X]
```  
  
⽣成字典  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7MPIoQc2ymwiceXibY3woNnddfg2nLNEyGER9Sb0QFxmBxxkbjx9WHQysQ/640?wx_fmt=png&from=appmsg "")  
  
**0x03⼆要素验证**  
  
使用二要素验证接⼝，写脚本进⾏爆破验证。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7MBw80uqKPHwqfNycFjFZSL3Kzejxk9qg6IttiaqSuaJQWWQXRWTZ9MRg/640?wx_fmt=png&from=appmsg "")  
  
爆破结果：  
```
{

"code": "0",

"message": "成功",

"result": {

"name": "王**",

"idcard": "37090220011201****",

"res": "1",

"description": "⼀致",

"sex": "⼥",

"birthday": "20011201",

"address": "⼭东省****市****区"

 }

}
```  
  
**0x04 More**  
  
通过*校平台获取更多信息  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq5Ocm4ZltfjvS4KUCibjvr7MgibAp2bNsJ5ficIGrnwPYubufySCTWxq2ehce5n2ckDDJpCeibSZxr0Wg/640?wx_fmt=png&from=appmsg "")  
  
通过平台漏洞获得信息  
```
{

"userInfoId": 6129326,

"userInfoName": "王**", //姓名

"merchantApprove": "1",

"siteOwnerType": 1,

"siteOwnerId": 112477,

"personnelNumber": "2030****", //学号

"phoneNo": "151********", //⼿机号

"idCardValue": "3709022001120****", //身份证

}
```  
  
**如果你喜欢这个案例可以点赞转发支持一下谢谢！**  
  
  
**0x02最后**  
  
        如果师傅们不想报昂贵的培训班又想学习  
SRC挖掘欢迎加入  
内部VIP知识星球  
，后台回复“**星球**  
”，有具体介绍，  
内部干货及思路很多  
，里面可以学习更多漏洞相关的知识和资源，包含但不限于SRC漏洞挖掘、攻防演练、外网打点、内网渗透及各种工具、学习视频、账号等等，送5张20元优惠券用完即止，早加入早享受（  
后续价格只增不降  
）。  
  
结尾  
  
# 免责声明  
  
  
# 获取方法  
  
  
**关注领取资源：**  
  
回复“app" 获取  app渗透和app抓包教程  
  
回复“渗透字典" 获取 针对一些字典重新划分处理，收集了几个密码管理字典生成器用来扩展更多字典的仓库。  
  
回复“书籍" 获取 网络安全相关经典书籍电子版pdf  
  
回复“钓鱼001” 获取钓鱼文案  
  
**压缩包解压密码：HackTwo**  
  
# 最后必看  
  
  
    本工具或文章仅面向合法授权的企业安全建设行为，如您需要测试内容的可用性，请自行搭建靶机环境，如果你学习了该文章内容需要测试请自行搭建靶机环境，勿用于非法行为。  
  
  
    为避免被恶意使用，本项目所有收录的poc均为漏洞的理论判断，不存在漏洞利用过程，不会对目标发起真实攻击和漏洞利用。  
  
  
    在使用本工具进行检测时，您应确保该行为符合当地的法律法规，并且已经取得了足够的授权。请勿对非授权目标进行扫描。  
  
  
    如您在使用本工具或阅读文章的过程中存在任何非法行为，您需自行承担相应后果，我们将不承担任何法律及连带责任。本工具或文章来源于网络，若有侵权请联系作者删除，请在24小时内删除，请勿用于商业行为，自行查验是否具有后门，切勿相信软件内的广告！  
  
  
  
# 往期推荐  
  
  
[1. 4.8-CobaltStrike4.8汉化+最新插件集成版发布](http://mp.weixin.qq.com/s?__biz=Mzg3ODE2MjkxMQ==&mid=2247483949&idx=1&sn=cae68096be06be4f0ea746ee5908dc79&chksm=cf16a49df8612d8b0b5cc2e49e6367cc91b7fd1f6d71c555d6631dbd3bd883d5242972e506b9&scene=21#wechat_redirect)  
  
  
[2. 2023HW的110+个poc发布直接下载](http://mp.weixin.qq.com/s?__biz=Mzg3ODE2MjkxMQ==&mid=2247483899&idx=1&sn=8f428144e749c1f115d39bae69072604&chksm=cf16a74bf8612e5dbc086b8af8a08b195481f367a8904f89ac44e66f06703afe54f1c6c641d6&scene=21#wechat_redirect)  
  
  
[3. 最新Nessus2023下载Windows/Linux](http://mp.weixin.qq.com/s?__biz=Mzg3ODE2MjkxMQ==&mid=2247483887&idx=1&sn=16af3498a081829d23b3dbd8037d000e&chksm=cf16a75ff8612e495b8b97e373e6bdf0297d814d48e8029a387a7c295389757fe7eccd932ba0&scene=21#wechat_redirect)  
  
  
[4. 最新xray1.9.11高级版下载Windows/Linux](http://mp.weixin.qq.com/s?__biz=Mzg3ODE2MjkxMQ==&mid=2247483882&idx=1&sn=e1bf597eb73ee7881ae132cc99ac0c8e&chksm=cf16a75af8612e4c73eda9f52218ccfc6de72725eb37aff59e181435de095b71e653b446c521&scene=21#wechat_redirect)  
  
  
[5. 最新HCL AppScan Standard 10.2.128273破解版下载](http://mp.weixin.qq.com/s?__biz=Mzg3ODE2MjkxMQ==&mid=2247483850&idx=1&sn=8fad4ed1e05443dce28f6ee6d89ab920&chksm=cf16a77af8612e6c688c55f7a899fe123b0f71735eb15988321d0bd4d14363690c96537bc1fb&scene=21#wechat_redirect)  
  
  
  
###### 渗透安全HackTwo  
  
  
微信号：  
关注公众号获取  
  
后台回复星球加入：  
知识星球  
  
扫码关注 了解更多  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/RjOvISzUFq6qFFAxdkV2tgPPqL76yNTw38UJ9vr5QJQE48ff1I4Gichw7adAcHQx8ePBPmwvouAhs4ArJFVdKkw/640?wx_fmt=png "二维码")  
  
  
  
喜  
欢的朋友可以点赞转  
发支持一下  
  
  
