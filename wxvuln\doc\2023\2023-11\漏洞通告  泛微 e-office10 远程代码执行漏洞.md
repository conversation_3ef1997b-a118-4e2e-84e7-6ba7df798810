#  漏洞通告 | 泛微 e-office10 远程代码执行漏洞   
原创 微步情报局  微步在线研究响应中心   2023-11-28 17:21  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMKNkm4Pg1Ed6nv0proxQLEKJ2CUCIficfAwKfClJ84puialc9eER0oaibMn1FDUpibeK1t1YvgZcLYl3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
01 漏洞概况****  
  
  
  
泛微e-office系统是面向中小型组织的专业协同OA软件，致力于为企业用户提供专业OA办公系统、移动OA应用等协同OA整体解决方案。微步漏洞团队通过“X漏洞奖励计划”获取到泛微e-office10远程代码执行漏洞。由于泛微e-office10前台存在SQL注入漏洞可以获取管理员账号和口令，配合后台文件包含即可导致远程代码执行。**经分析，攻击者可利用该漏洞获取服务器敏感信息、执行任意代码，建议尽快修复。**  
  
02 漏洞处置优先级（VPT）  
  
  
  
**综合处置优先级：**  
**高**  
****  
  
<table><tbody style="visibility: visible;"><tr style="height: 23.3pt;visibility: visible;"><td width="123" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞编号</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">微步编号</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">XVE-2023-6334</span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="143" valign="top" rowspan="6" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞评估</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">危害评级</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><strong style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(181, 15, 26);">高危</span></strong><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">漏洞类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">SQL注入、RCE</span><br/></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;">公开程度</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">PoC未公开</span><span style="font-size: 14px;"></span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">利用条件</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">无权限要求</span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-family: 黑体;font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">交互要求</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">0-click</span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">威胁类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">远程</span></p></td></tr><tr style="height:26.0500pt;"><td width="143" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>利用情报</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">微步已捕获攻击行为</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">是</span></p></td></tr><tr><td width="143" valign="top" rowspan="4" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>影响产品</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">产品名称</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">泛微e-office10</span></section><span style="mso-spacerun:&#39;yes&#39;;font-family:微软雅黑;mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;"><span style="font-family:微软雅黑;"></span></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">受影响版本</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">version &lt; 10.0_20231107</span><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">影响范围</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">千级</span></p></td></tr><tr style="height:26.7000pt;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有无修复补丁</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有<br/></span></p></td></tr></tbody></table>  
### 03 漏洞复现 04 修复方案 1、官方修复方案：官方已在最新版修复此问题，建议受影响的用户在服务器管理平台升级到10.0_20231107版本。2、临时修复方案：1.使用防护类设备对相关资产进行SQL注入、文件包含等攻击的防护；2.在确认不影响业务的情况下可临时关闭im服务，或使用网络ACL策略限制im服务的公网访问。05 微步在线产品侧支持情况  （1）微步在线威胁感知平台TDP已支持检测，规则ID为S3100119400、S3100119401、S3100119402、S3100119403。（2）微步在线安全情报网关OneSIG已支持防护，规则ID为3100119400、3100119401、3100119402、3100119403。（3）微步在线安全情报社区X已支持资产查询，规则为app=EOffice。06 时间线 2023.11.07 厂商修复漏洞2023.11.28 微步发布报告---End---微步漏洞情报订阅服务微步提供漏洞情报订阅服务，精准、高效助力企业漏洞运营提供高价值漏洞情报，具备及时、准确、全面和可操作性，帮助企业高效应对漏洞应急与日常运营难题；可实现对高威胁漏洞提前掌握，以最快的效率解决信息差问题，缩短漏洞运营MTTR；提供漏洞完整的技术细节，更贴近用户漏洞处置的落地；将漏洞与威胁事件库、APT组织和黑产团伙攻击大数据、网络空间测绘等结合，对漏洞的实际风险进行持续动态更新。X 漏洞奖励计划“X漏洞奖励计划”是微步X情报社区推出的一款针对未公开漏洞奖励计划，我们鼓励白帽子提交挖掘到的0day漏洞，并给予白帽子可观的奖励。我们期望通过该计划与白帽子共同努力，提升0day防御能力，守护数字世界安全。  
  
