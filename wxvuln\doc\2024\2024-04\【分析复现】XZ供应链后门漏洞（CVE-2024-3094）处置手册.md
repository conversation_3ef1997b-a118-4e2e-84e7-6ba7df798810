#  【分析复现】XZ供应链后门漏洞（CVE-2024-3094）处置手册   
原创 NS-CERT  绿盟科技CERT   2024-04-03 19:36  
  
**通告编号:NS-2024-0011-1**  
  
2024-04-03  
  
<table><tbody><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><strong><span style="font-size: 14px;">TA</span></strong><strong><span style="font-size: 14px;">G：</span></strong></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><p style="vertical-align: inherit;line-height: 1.75em;font-size: 14px;color: rgb(0, 0, 0);font-family: 微软雅黑;"><strong style="font-size: 17px;caret-color: red;font-family: 微软雅黑, sans-serif;"><span style="font-size: 14px;caret-color: red;font-family:微软雅黑, &#34;Microsoft YaHei&#34;;">liblzma/xz、后门漏洞、CVE-2024-3094</span></strong></p></td></tr><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><span style="color: rgb(0, 0, 0);"><strong><span style="font-size: 14px;">漏洞危害：</span></strong></span><span style="color: rgb(255, 0, 0);"><strong><span style="font-size: 14px;"></span></strong></span></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><p style="vertical-align:inherit;"><span style="font-family:微软雅黑, &#34;Microsoft YaHei&#34;;"><strong style="caret-color: red;"><span style="font-size: 14px;">攻击者利用此漏洞，可实现SSH未授权代码执行。</span></strong></span></p></td></tr><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><strong><span style="font-size: 14px;">版本：</span></strong></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top"><strong><span style="font-size: 14px;">1.0<br/></span></strong></td></tr></tbody></table>  
  
**1**  
  
  
**漏洞概述**  
  
  
近日，绿盟科技CERT监测到安全社区披露XZ-Utils存在供应链后门漏洞（CVE-2024-3094），CVSS评分10。由于SSH底层依赖了liblzma，当满足一定条件时，攻击者可利用此漏洞在受影响的系统上绕过SSH的认证获得未授权访问权限，从而执行任意系统命令。经排查后发现为xz的tarball上游软件包中感染后门程序，该后门在构建过程中从伪装的测试文件中提取.o文件，然后使用提取的文件修改liblzma中特定的函数，导致生成了一个被修改过的liblzma库，任何链接此库的软件都可能使用它拦截并修改与此库的数据交互。目前漏洞PoC已公开，请相关用户尽快采取措施进行排查与防护。  
  
XZ-Utils是Linux、Unix等POSIX兼容系统中广泛用于处理.xz文件的工具库套件，包含liblzma、xz等组件，集成在绝大多数Linux发行版仓库中。  
  
绿盟科技CERT已成功复现：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/VvfsuOanecr7RzCcC1RLvKBbhLGn1dI7TFT2ZLo4NknYB1DS7cjVjFq1Jk4LO8uwlzxVvEDxOW4ibVcdp6ug4CA/640?wx_fmt=png&from=appmsg "")  
  
  
参考链接：  
  
https://tukaani.org/xz-backdoor/  
  
https://www.openwall.com/lists/oss-security/2024/03/29/4  
  
**SEE MORE →******  
  
**2****事件背景**  
  
2021年10月，植入后门漏洞的开发者JiaT75开始参与XZ-Utils项目的开发，逐渐取得信任，并于2023年接管了项目维护权限，其在2024年2月向liblzma/xz提交恶意文件，引入了一个允许攻击者未授权访问SSH的隐蔽后门，同时联系Linux发行版维护者，要求将带后门的库打包并分发给最终用户，3月29日开发人员Andres Freund在分析SSH性能故障时，发现了该供应链攻击活动。目前GitHub已经关停了整个XZ-Utils项目。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/VvfsuOanecr7RzCcC1RLvKBbhLGn1dI74tb6VAKvEiaKUbRdyuVsIcuCcCmcZ3RUMa0O64QBCzIE3DTggRRDxOQ/640?wx_fmt=png&from=appmsg "")  
  
  
**3****后门分析**  
  
当链接了该恶意动态库的sshd进程启动后，liblzma.so中的恶意代码会通过Hook技术修改RSA_public_decrypt函数符号的指向地址。sshd进程在接收到新的ssh登录请求时，会触发RSA_public_decrypt函数对相关负载字段进行签名校验，校验通过后会将攻击者隐藏在证书字段中的攻击载荷提取出来，传递给system函数，从而实现任意代码执行。  
  
触发条件：  
  
- 进程路径名为/usr/sbin/sshd  
  
- 存在LANG环境变量  
  
- 不存在LD_DEBUG、LD_PROFILE环境变量  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/VvfsuOanecr7RzCcC1RLvKBbhLGn1dI7W25Z9VQzBDloT4zDooWuT1DOhEPcw7E1WzCf1fVibkbn0CEDsRHd0xA/640?wx_fmt=png&from=appmsg "")  
  
攻击者随后按照特定的报文格式构造攻击载荷，将待执行的命令（传递进system函数中）包裹在证书字段中：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/VvfsuOanecr7RzCcC1RLvKBbhLGn1dI7cCk7dZXGJekTbLfOC5gqkiayLmVk1hOEvzjrLHRJdlwLbxXZfbQCD7Q/640?wx_fmt=png&from=appmsg "")  
  
sshd进程使用RSA_public_decrypt对该段负载进行解密（校验），从而进入到由攻击者所劫持的函数中，完成对该段负载的校验并将先前填入的cmd命令传入system函数进行执行。  
  
由于这个步骤涉及到公私钥对的校验，无法通过正确解密/校验的负载将不会触发后续system的执行，因此对于原始攻击者预引入的liblzma.5.6.0和liblzma5.6.1文件，只有攻击者本身能实现后门利用。  
  
  
**4****影响范围**  
  
**受影响版本**  
  
- XZ Utils = 5.6.0 - 5.6.1  
  
  
  
注：XZ的Git发行版中未发现恶意代码，仅存在于完整的下载包中。目前已知受影响的Linux发行版：Fedora Rawhide（开发版本）Fedora 41MACOS HomeBrew x64openSUSE Tumbleweed 及 MicroOS（3月7日至3月28日期间发行）Kali Linux （3月26日至3月28日期间发行的xz-utils 5.6.0-0.2）Debian（XZ测试版本5.5.1alpha-0.1 至 5.6.1-1）**XZ供应链影响系统查询：******  
https://repology.org/project/xz/version  
  
  
**不受影响版本**  
  
- XZ Utils < 5.6.0  
  
- XZ Utils >= 5.8.0  
  
  
  
注：因植入后门的开发者于2021年开始参与维护，安全起见建议用户将XZ-Utils降级至5.4或之前版本。CentOS/Redhat/Ubuntu/Debian/Fedora等stable仓库不受影响。  
  
  
**5****漏洞检测**  
  
**5.1 版本检测**  
  
用户可以通过执行以下命令判断使用的xz是否为受影响的版本：  
<table><tbody><tr><td style="border-color: windowtext;border-width: 2px;background: none 0% 0% repeat scroll rgb(216, 216, 216);" valign="top"><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">xz --version</span></p></td></tr></tbody></table>  
  
**5.2 脚本排查**  
  
相关用户也可使用Openwall上发布的脚本检查系统是否被感染后门：  
<table><tbody><tr><td style="border-color: windowtext;" valign="top"><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">#! /bin/bash</span></p><p style="line-height: 1.75em;"><br/></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">set -eu</span></p><p style="line-height: 1.75em;"><br/></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"># find path to liblzma used by sshd</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">path=&#34;$(ldd $(which sshd) | grep liblzma | grep -o &#39;/[^ ]*&#39;)&#34;</span></p><p style="line-height: 1.75em;"><br/></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"># does it even exist?</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">if [ &#34;$path&#34; == &#34;&#34; ]</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">then</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">  echo probably not vulnerable</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">  exit</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">fi</span></p><p style="line-height: 1.75em;"><br/></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"># check for function signature</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">if hexdump -ve &#39;1/1 &#34;%.2x&#34;&#39; &#34;$path&#34; | grep -q f30f1efa554889f54c89ce5389fb81e7000000804883ec28488954241848894c2410</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">then</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">  echo probably vulnerable</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">else</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">  echo probably not vulnerable</span></p><p style="line-height: 1.75em;"><span style="line-height: 125%;font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">fi</span></p></td></tr></tbody></table>  
判断SSHD程序依赖的liblzma库文件二进制数据中是否包含后门特征码。  
  
  
**5.3 产品检测**  
  
绿盟科技远程安全评估系统（RSAS）已具备对该漏洞的扫描与检测能力，请有部署此设备的用户升级至最新版本。  
<table><tbody><tr><td style="border-color: windowtext windowtext currentcolor;border-top-style: double;border-bottom-style: none;border-left-style: double;border-bottom-width: medium;background: none 0% 0% repeat scroll rgb(217, 217, 217);" width="158" valign="center"><br/></td><td style="border-color: windowtext windowtext currentcolor currentcolor;border-top-style: double;border-bottom-style: none;border-left-style: none;border-bottom-width: medium;border-left-width: medium;background: none 0% 0% repeat scroll rgb(217, 217, 217);" width="136" valign="center"><p style="line-height: 1.75em;"><span style="font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"><strong>升级包版本号</strong></span></p></td><td style="border-color: windowtext windowtext currentcolor currentcolor;border-style: double double none none;border-bottom-width: medium;border-left-width: medium;background: none 0% 0% repeat scroll rgb(217, 217, 217);" width="287" valign="center"><p style="line-height: 1.75em;"><span style="font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"><strong>升级包下载链接</strong></span></p></td></tr><tr><td style="border-color: windowtext;border-bottom-style: double;border-left-style: double;background: none 0% 0% repeat scroll rgb(230, 230, 230);" width="158" valign="top"><p style="text-align:left;margin-bottom: 10px;line-height: 1.75em;"><span style="font-size: 14px;font-family:微软雅黑, Microsoft YaHei;"><strong><strong>RSAS V6</strong><strong>系统插件包</strong></strong></span></p></td><td style="border-color: windowtext;border-bottom-style: double;" width="136" valign="top"><p style="text-align:left;margin-bottom: 10px;line-height: 1.75em;"><span style="font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">V6.0R02F01.3501</span></p></td><td style="border-color: windowtext;border-right-style: double;border-bottom-style: double;" width="287" valign="top"><p style="text-align:left;line-height: 1.75em;"><span style="font-size: 14px;font-family:微软雅黑, Microsoft YaHei;">https://update.nsfocus.com/update/downloads/id/154193</span></p></td></tr></tbody></table>  
关于RSAS的升级配置指导，请参考如下链接：  
  
https://mp.weixin.qq.com/s/SgOaCZeKrNn-4uR8Yj_C3Q  
  
  
**6****漏洞防护**  
  
**6.1 修复措施**  
  
目前官方针对此后门漏洞发布了5.8.0的纯净版本，下载链接：https://git.tukaani.org/，相关用户也可  
将xz-utils降级至5.6之前版本或在应用中替换为7zip等组件。  
  
注：brew更新已进行了版本回退，从5.6.1降级到5.4.6；Debian发布了xz utils更新，版本号为5.6.1+really5.4.5。  
  
  
**6.2 其他建议**  
  
建立产品采购及开源软件供应链管理制度、健全应用开发生命周期安全管理、加强软件上下游威胁情报监测，及时掌握应用及产品安全风险。  
  
  
**END**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qR4ORTNELImFwJM2rh6GKbnrurdFA28jJ8chUPyC1U6aW3jhenqEiaXkmeGVmfOnvAJy8j3My901JQ7emHaicYzA/640?wx_fmt=png "")  
           
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qR4ORTNELImFwJM2rh6GKbnrurdFA28jib7icfic0lJJHh3eLRpIXiaia08KqOSEibBsz64vlOH9aqicu3lmjccEeAFWQ/640?wx_fmt=jpeg "")  
          
  
**声明**  
  
本安全公告仅用来描述可能存在的安全问题，绿盟科技不为此安全公告提供任何保证或承诺。由于传播、利用此安全公告所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，绿盟科技以及安全公告作者不为此承担任何责任。              
  
绿盟科技拥有对此安全公告的修改和解释权。如欲转载或传播此安全公告，必须保证此安全公告的完整性，包括版权声明等全部内容。未经绿盟科技允许，不得任意修改或者增减此安全公告内容，不得以任何方式将其用于商业目的。              
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qR4ORTNELImFwJM2rh6GKbnrurdFA28jib7icfic0lJJHh3eLRpIXiaia08KqOSEibBsz64vlOH9aqicu3lmjccEeAFWQ/640?wx_fmt=jpeg "")  
  
  
**绿盟科技CERT**  
****  
∣  
微信公众号  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/VvfsuOanecr7RzCcC1RLvKBbhLGn1dI7mgBdEX5S8oiaz4ibrp5hX2KH2IgcOUY7qGbbyHTyPQfITscJkF6LFEsQ/640?wx_fmt=jpeg&from=appmsg "绿盟科技CERT公众号.jpg")  
  
![](https://mmbiz.qpic.cn/mmbiz/Hu8hctxHqSW0nSJn8p8OHVEQwHicSwTibFJMBE650AxdzfISoeY8woe2QsgCINIBrccBOOUft2HuU0GsNQWibSG7g/640?wx_fmt=png "")  
  
长按识别二维码，关注网络安全威胁信息  
  
