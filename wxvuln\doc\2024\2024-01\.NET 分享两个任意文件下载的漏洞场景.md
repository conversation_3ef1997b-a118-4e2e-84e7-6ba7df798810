#  .NET 分享两个任意文件下载的漏洞场景   
专攻.NET安全的  dotNet安全矩阵   2024-01-07 08:50  
  
# 0x01 Resonse.TransmitFile  
  
.NET为Response对象提供了一个新的方法TransmitFile来解决使Response.BinaryWrite下载超过400mb的文件时导致Aspnet_wp.exe进程回收而无法成功下载的问题。Response.TransmitFile可以有效地处理大文件的下载，因为它不需要将整个文件读取到服务器内存中，而是直接从硬盘上将文件内容发送给客户端，减少了服务器的内存压力和响应时间。该方法接受一个文件路径作为参数，并将指定的文件内容发送给客户端。  
  
下  
面是  
Response.TransmitFile  
的代码示例，复现存在文件下载漏洞的场景，具体代码如下所示  
。  
  
```
string fileName = Request.QueryString["file"]; 
string filePath = Path.Combine(Server.MapPath("~/Uploads/"), fileName);
if (File.Exists(filePath))
{
     Response.Clear();
     Response.ContentType = "application/octet-stream";
     Response.AppendHeader("Content-Disposition", "attachment; filename=" + fileName);
     Response.TransmitFile(filePath);
     Response.End();
}
```  
  
提交file参数 "../web.config"，成功读取敏感文件内容。如下图  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9aLA3JzwBvbpVsWlUvU2sIl9iaHFCfHTEay4gSnF9zBQ0R19YmpibyXzLOrtQIGLhuRXCjIicXc9icQA/640?wx_fmt=png&from=appmsg "")  
# 0x02 Resonse.BinaryWrite  
  
Response.BinaryWrite  
是  
.NET  
中用于将字节数据发  
送到客户端的方法，比如直接输出图像、音频或视频等二进制数据到客户端。  
  
下面是  
Response.BinaryW  
r  
ite  
的代码示例，复现存在文件下载漏洞的场景，代码如下  
```
string fileName = Request.QueryString["file"]; 
string filePath = Path.Combine(Server.MapPath("~/Uploads/"), fileName);
FileStream fs = new FileStream(filePath, FileMode.Open);
byte[] bytes = new byte[(int)fs.Length];
fs.Read(bytes, 0, bytes.Length);
fs.Close();
Response.ContentType = "application/octet-stream";
Response.AddHeader("Content-Disposition", "attachment;  filename=" + HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8));
Response.BinaryWrite(bytes);
Response.Flush();
Response.End();
```  
  
上述代码读取文件创建一个FileStream 文件流，从文件流中读取文件内容并将存储在字节数组 bytes中。最后通过Response.BinaryWrite(bytes) 将文件的字节数组内容写入 HTTP 响应流发送到客户端。提交file参数 "../web.config"，成功读取敏感文件内容。如下图  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9mv6lbnzxs2ZUdSU7jvuHbq9zrOV4f1evqAT7In6DpicLFib3ibthVoso6J9K8EKv9jtOnEibtgYfxEQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
# 欢迎加入星球  
  
为了更好地应对基于.NET技术栈的风险识别和未知威胁，dotNet安全矩阵星球从创建以来一直  
聚焦于.NET领域的安全攻防技术，定位于  
高质量安全攻防星球社区，也  
得到了许多师傅们的支持和信任，通过星球深度连接入圈的师傅们，一起推动.NET安全高质量的向前发展  
。经过运营团队成员商议一致同意给到师傅们最大优惠力度，  
**只需199元就可以加入我们。**  
  
星球汇聚了  
各行业安全攻防技术大咖，并且每日分享.NET安全技术干货以及交流解答各类技术等问题，社区中发布**很多高质量的.NET**  
安全资源，可以说市面上很少见，都是干货。其中主题包括  
**.NET Tricks、漏洞分析、内存马、代码审计、预编译、反序列化、webshell免杀、命令执行、C#工具库**  
等等，后续还会倾力打造  
**专刊、视频**  
等配套学习资源，循序渐进的方式引导加深安全攻防技术提高以及岗位内推等等服务。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y8DlZsGiaRRGghficKFQt58Ueoynsb0my3uzMAb7VwM5bgtnb4nbl4c9xdEjGraUXic6pO0p38xmWiaRQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YibHErRN3IhgSaicia7Rl5SF0plpcuicd0KG8Cn7vGczlBRtvSJvicWejH7TOro6AGLQ627SvVzxzBnphg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y8DlZsGiaRRGghficKFQt58UeoxTMuRezdHEJu6Hp08Xgm2F49cyBI1zlcj5XqLJK8zedWlUjibYmia3g/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9y0BnibYCn1b9GMKqxd1Z5A1DLLJ9YxZeCn52XA1Kw7T5ibWCv89ZXpGjPOY7hXBDRVwNdKbMLZR3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1Y9lvf0EpBgVnMoicPtLAx2A1ls9pNaRTDZ9HLg88k7qk0Y188fdC6DHaful53ibicIFD6ib6Wl4vbaW9Q/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
