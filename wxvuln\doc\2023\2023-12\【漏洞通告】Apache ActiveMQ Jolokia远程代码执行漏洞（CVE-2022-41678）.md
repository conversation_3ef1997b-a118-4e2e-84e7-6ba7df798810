#  【漏洞通告】Apache ActiveMQ Jolokia远程代码执行漏洞（CVE-2022-41678）   
原创 NS-CERT  绿盟科技CERT   2023-11-30 15:28  
  
**通告编号:NS-2023-0046**  
  
2023-11-30  
  
<table><tbody><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="94.33333333333333"><strong><span style="font-size: 14px;">TA</span></strong><strong><span style="font-size: 14px;">G：</span></strong></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="429.3333333333333"><p style="vertical-align: inherit;line-height: 1.75em;font-size: 14px;color: rgb(0, 0, 0);font-family: 微软雅黑;"><strong style="caret-color: red;line-height: 1.57em;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">Apache ActiveMQ、Jolokia、CVE-2022-41678、远程代码执行</strong></p></td></tr><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="53"><span style="color: rgb(0, 0, 0);"><strong><span style="font-size: 14px;">漏洞危害：</span></strong></span><span style="color: rgb(255, 0, 0);"><strong><span style="font-size: 14px;"></span></strong></span></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="449.3333333333333"><p><strong style="caret-color: red;font-size: 14px;line-height: 1.57em;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">攻击者利用此漏洞，可实现远程代码执行</strong></p></td></tr><tr><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="53"><strong><span style="font-size: 14px;">版本：</span></strong></td><td style="margin: 5px 10px;border-color: rgb(216, 216, 216);word-break: break-all;" valign="top" width="449.3333333333333"><strong><span style="font-size: 14px;">1.0<br/></span></strong></td></tr></tbody></table>  
  
**1**  
  
  
**漏洞概述**  
  
  
近日，绿盟科技CERT监测发现Apache ActiveMQ Jolokia远程代码执行漏洞（CVE-2022-41678），由于在ActiveMQ的配置中，jetty允许org.jolokia.http.AgentServlet处理对/api/jolokia的请求，经过身份验证的攻击者可通过Jolokia服务发送特制的HTTP请求写入恶意文件，从而实现远程代码执行。目前漏洞PoC已公开，请受影响用户尽快采取措施进行防护。  
  
Apache ActiveMQ是美国阿帕奇（Apache）基金会的一套开源的消息中间件，它支持Java消息服务、集群、Spring Framework等。  
  
绿盟科技已成功复现此漏洞：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/VvfsuOanecoMfCjL6DWIGCvdJAia1cuq1lgDWzAABYQzOInAF7tHOgEyqHawUuFZiawS1M8xTnWmM2oLKb86F88g/640?wx_fmt=png&from=appmsg "")  
  
  
参考链接：  
  
https://activemq.apache.org/security-advisories.data/CVE-2022-41678-announcement.txt  
  
  
**SEE MORE →******  
  
**2****影响范围**  
  
**受影响版本：**  
  
- 5.16.0 <= Apache ActiveMQ < 5.16.6  
  
- 5.17.0 <= Apache ActiveMQ < 5.17.4  
  
  
  
  
**不受影响版本：**  
  
- Apache ActiveMQ >= 5.16.6  
  
- Apache ActiveMQ >= 5.17.4  
  
- Apache ActiveMQ >= 5.18.0  
  
- Apache ActiveMQ >= 6.0.0  
  
  
  
  
**3****漏洞防护**  
  
**3.1 官方升级**  
  
目前Apache ActiveMQ官方发布的新版本中已修复该漏洞，建议受影响用户升级至最新版本进行防护：  
https://github.com/apache/activemq/tags  
  
  
**3.2 临时缓解措施**  
  
1、建议开启Web 控制台认证（默认8161端口）且非必要不对公网开放，可参考官方文档https://activemq.apache.org/web-console，并设置健壮口令。  
  
2、在不影响业务的前提下，可对Jolokia接口进行访问限制，若不需Jolokia服务可直接进行禁用；限制Jolokia授权后的操作可参考：https://github.com/apache/activemq/pull/958/files  
  
  
**END**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qR4ORTNELImFwJM2rh6GKbnrurdFA28jJ8chUPyC1U6aW3jhenqEiaXkmeGVmfOnvAJy8j3My901JQ7emHaicYzA/640?wx_fmt=png "")  
           
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qR4ORTNELImFwJM2rh6GKbnrurdFA28jib7icfic0lJJHh3eLRpIXiaia08KqOSEibBsz64vlOH9aqicu3lmjccEeAFWQ/640?wx_fmt=jpeg "")  
          
  
**声明**  
  
本安全公告仅用来描述可能存在的安全问题，绿盟科技不为此安全公告提供任何保证或承诺。由于传播、利用此安全公告所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，绿盟科技以及安全公告作者不为此承担任何责任。              
  
绿盟科技拥有对此安全公告的修改和解释权。如欲转载或传播此安全公告，必须保证此安全公告的完整性，包括版权声明等全部内容。未经绿盟科技允许，不得任意修改或者增减此安全公告内容，不得以任何方式将其用于商业目的。              
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qR4ORTNELImFwJM2rh6GKbnrurdFA28jib7icfic0lJJHh3eLRpIXiaia08KqOSEibBsz64vlOH9aqicu3lmjccEeAFWQ/640?wx_fmt=jpeg "")  
  
  
**绿盟科技CERT**  
****  
∣  
微信公众号  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/VvfsuOanecoMfCjL6DWIGCvdJAia1cuq1QrP5psoR48foRG7d1oyffJ4eR466qiaibw3KicxbuS6DR3gR3Me9FX3Yg/640?wx_fmt=jpeg&from=appmsg "绿盟科技CERT公众号.jpg")  
  
![](https://mmbiz.qpic.cn/mmbiz/Hu8hctxHqSW0nSJn8p8OHVEQwHicSwTibFJMBE650AxdzfISoeY8woe2QsgCINIBrccBOOUft2HuU0GsNQWibSG7g/640?wx_fmt=png "")  
  
长按识别二维码，关注网络安全威胁信息  
  
  
  
