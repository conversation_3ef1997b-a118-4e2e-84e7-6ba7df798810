#  【python专题】poc编写过程万能正则获取字符串   
原创 南极熊  SCA御盾   2024-01-14 09:44  
  
##  星球介绍  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIhJYibTMDFIH6V3gDdk2SLStj3AmEwApApxLUqzuWYdSrMGSGJ5A3cNtmArV7tgWiaUsmoQibEZf2uIQ/640?wx_fmt=png&from=appmsg "")  
  
****  
**SCA御盾星球介绍详情请访问如下链接：**  
  
**SCA御盾**[星球介绍](http://mp.weixin.qq.com/s?__biz=MzkzNjYwODg3Ng==&mid=2247484051&idx=1&sn=16dd3413d265212ed08ce6d841718d74&chksm=c29d5790f5eade86f19f5f3a7c2e22913eef0ed21605a8f3af966da57dada759dac3357b4168&scene=21#wechat_redirect)  
  
****  
  
## 0x01 阅读须知  
  
**SCA御盾实验室的技术文章仅供参考，此文所提供的信息只为网络安全人员对自己所负责的网站、服务器等（包括但不限于）进行检测或维护参考，未经授权请勿利用文章中的技术资料对任何计算机系统进行入侵操作。利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责。本文所提供的工具仅用于学习，禁止用于其他！！！**  
  
0x02 场景描述  
  
**经过前几期的python专题文章，经师傅们反馈，已经会写一些简单的python poc脚本，但同时又遇到个新的问题，遇到多个请求传递参数时，不知道怎么从响应里面获取指定的字符，不知道python中，正则函数该怎么使用。因此，今天给师傅们分享本人常用的一些正则以及对应的python代码。**  
  
  
0x03 场景举例  
  
**1、任意文件读取漏洞读取/etc/passwd，怎么写判断语句？**  
  
这个问题有的师傅会解答，说直接使用if 'root:' in response.text:直接就可以进行判断，因为所有的/etc/passwd文件里面，都包含'root:'这个字符。当然，这么写也能作为判断依据，但是会出现较高误报。如下:  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIh3S4cNnzYfzCZf4icnP9sfxu2rp8hBTeQ8duq7tRquMs1eyy3Tj1CPp1LWn5ib61iae2mn9krQK53Fg/640?wx_fmt=png&from=appmsg "")  
**解决办法：**  
```
if re.search("root:.*:0",response.text):
```  
  
上述代码中。正则"root:.*:0"表示匹配root:、任意字符、:0这三部分，即格式为 root:xxxxx:0此类格式的字符串都能匹配；而re.search() 是 Python 的正则表达式模块 re 的一个函数，它用于在字符串中搜索与正则表达式模式匹配的子字符串。如果找到匹配，它将返回一个匹配对象；否则，它将返回 None。  
  
**2、正则获取字符串**  
```
re.findall('左边字符串(.*?)右边字符串',strs)
```  
  
re.findall() 是 Python 的正则表达式模块 re 的一个函数，用于在字符串中查找所有与正则表达式模式匹配的子字符串。上述表达式re.findall('左边字符串(.*?)右边字符串', strs) 中，这个正则表达式试图匹配以 "左边字符串" 开头，然后是任意数量的任意字符（由 (.*?) 表示，其中 .* 表示任意数量的任意字符，? 表示非贪婪匹配，即尽可能少的匹配字符），然后是 "右边字符串" 结尾的子字符串。  
  
如下：  
```
import re
strs = "左边字符串123右边字符串 左边字符串abc右边字符串"
alist=re.findall('左边字符串(.*?)右边字符串',strs)


```  
  
上述代码的strs中，123匹配中，abc也匹配中，即函数运算结果会存储在一个列表中，即alist为['123','abc']。此正则可适用于很多poc编写场景，需要师傅们灵活使用。  
  
**加入知识星球后进行在线答疑**  
  
**明日预告**  
**：金和oa-upload_json-任意文件上传漏洞复现******  
  
0x04 近期发布  
  
![](https://mmbiz.qpic.cn/mmbiz_png/RxxRc1KlrIh3S4cNnzYfzCZf4icnP9sfxwqibYjuesnvOUd0yoZ7fibORMjNTsF1m7DIZB8XLsGCyVAuHquGaAJQQ/640?wx_fmt=png&from=appmsg "")  
  
0x05 星球近况  
  
****  
**付费星球**  
  
**以下**  
**活动不支持多**  
**项叠加******  
  
**（1）近期活动：1、老带新活动（1）通过群里老人进入星球的新人，只需**  
**109**  
**元进入，老人也会得到**  
**20**  
**元现金奖励。加入方式与奖励方式皆通过私聊，不支持退款（2）星球开通分享有赏功能，通过老人分享的星球进入的新人，老人返现**  
**25**  
**元左右，新人返现**  
**15**  
**元左右2、知识星球发放**  
**20**  
**张价值**  
**30**  
**元的假日优惠券，先到先得，优惠券扫顶部二维码获得**  
  
**3、通过私聊进入星球的，只需**  
**135**  
**元，但不支持退款**  
  
**（2）星球更新：1、星球付费成员已满150人，正式与2023/12/25日起，进入费用涨价至**  
**149**  
**元。**  
  
**2、星球内即将推出积分玩法，具体加分细节查看星球获悉。每月初评选出上月积分榜前三名发放现金奖励，第一名获得**  
**150**  
**元，第二名获得**  
**100**  
**元，第三名获得**  
**50**  
**元，星主与管理员不参与评选。不得恶意刷屏刷积分，一经发现，取消其3个月的评比资格。发文每人每天不能超过3条，超过3条警告一次且不计积分，多次警告取消其3个月的评比资格。**  
  
**3、春节，微信群发放**  
**200**  
**元红包一次，按固定时间区间内，筛选出最倒霉蛋子（即红包抢的最少的人）发放鼓励奖**  
**，春节为**  
**666**  
**元，如果同时多个人抢到最低红包且数额一样，则按数量平分鼓励奖。**  
  
**（3）加入说明：**  
  
**1.加入收费说明：**  
  
**（1）现阶段扫码加入价格为**  
**¥149**  
**元**  
**（系统支持三天退款）**  
  
**（2）私聊微信加入**  
**¥135**  
**元，但不支持退款**  
  
**（3）转发任意一篇SCA御盾的公众号至5个50人以上安全群，在公众号加微信后凭截图发放8折优惠券**  
  
**（4）投稿最新漏洞poc或复现分析文章可免费加入1年(每星期限量5人)**  
  
**2. 每逢节假日会发放一定数量优惠券**  
  
**3.每天日更，工作日推送1day或0day,周末推送出货多的nday，期间不定期推送实用工具或脚本**  
  
**4. 进入星球后加群可提前一天解锁第二天的发布内容**  
  
**5.补天半自动化交洞脚本，计划于2024年农历新年后的第一个工作周于微信群推送，价格10-50元视服务而定**  
  
****  
![](https://mmbiz.qpic.cn/mmbiz_jpg/RxxRc1KlrIh1MLtGKWwgG0PsYxrNm1S79fUzHzGzsYn9Gh1aEpTHUIZOZOHZXjGMfrsXED8jKdCx0icnvtZRvGg/640?wx_fmt=jpeg&from=appmsg "")  
  
****  
