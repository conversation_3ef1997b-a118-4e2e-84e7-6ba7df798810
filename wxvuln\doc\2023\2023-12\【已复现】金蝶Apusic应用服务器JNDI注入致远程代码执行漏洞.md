#  【已复现】金蝶Apusic应用服务器JNDI注入致远程代码执行漏洞   
 长亭安全应急响应中心   2023-12-20 20:14  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicQu14BJfCt1WuQVictGhef2oAmgcu7lm0NKNFmPGZa0rqaP35ibXgTHqBMYT1l0qxHxkMia42mTgV46w/640?wx_fmt=png&from=appmsg "")  
  
金蝶Apusic应用服务器是一款企业级应用服务器，支持Java EE技术，适用于各种商业环境。  
  
  
2023年12月，互联网上披露金蝶Apusic应用服务器存在JNDI注入漏洞，攻击者可利用该漏洞获取服务器控制权限。该漏洞利用  
简单，建议受影响  
的客户尽快修复漏洞。  
  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因漏洞存在于其后台的JNDI加载功能中。问题在于服务器在处理特定POST请求时的输入验证不足，攻击者可以绕过权限校验，利用这一漏洞执行LDAP注入攻击。考虑到JNDI是Java用于访问多种目录和命名服务的API，这种漏洞可能会被用于操纵目录服务的响应，导致严重安全问题。利用特征漏洞利用特征表现为发送的异常POST请求。攻击者可能通过构造特殊的请求体，特别是操纵JNDI相关的参数，进行恶意的LDAP注入。这些请求可能包含非标准或不受信任的LDAP查询字符串。漏洞影响JNDI注入漏洞的最严重后果之一是远程代码执行（RCE）。攻击者可能不仅能够访问或修改敏感信息，而且还能够远程执行代码。这意味着攻击者可以在受影响的服务器上运行任意代码，从而完全控制受影响的系统。这种类型的攻击可能导致数据泄露、系统破坏，甚至允许攻击者利用受影响的服务器作为跳板来发起更广泛的网络攻击。因此，JNDI注入漏洞不仅威胁到应用服务器的安全性和数据完整性，还可能导致整个组织的网络安全遭受严重损害。影响版本 Affected Version 02金蝶Apusic应用服务器 <= V9.0 SP7解决方案 Solution 03临时缓解方案1. 在确认不影响业务的情况下，暂时暂停应用服务器管理控制台和移除默认首页，直到使用升级补丁。可参考官方给出的步骤进行操作（https://www.apusic.com/view-477-113.html）。2. 如非必要，不要将 受影响系统 放置在公网上。或通过网络ACL策略限制访问来源，例如只允许来自特定IP地址或地址段的访问请求。升级修复方案官方已发布新版本修复漏洞，建议尽快访问官网（https://www.apusic.com/view-477-113.html）或联系官方售后支持获取版本升级安装包或补丁。漏洞复现 Reproduction 04检测工具 Detection 05‍牧云本地检测工具检测方法：在本地主机上执行以下命令即可扫描：kingdee_aas_ldap_ct_1014499_scanner_windows_amd64.exe scan工具获取方式：https://stack.chaitin.com/tool/detail/1272产品支持 Support06‍云图：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。洞鉴：以自定义POC的形式支持检测。雷池：默认支持该漏洞利用检测。全悉：已发布规则升级包，支持检测该漏洞的利用行为。牧云：使用管理平台 23.05.001 及以上版本的用户可通过升级平台下载应急漏洞情报库升级包（EMERVULN-23.12.020）“漏洞应急”功能支持该漏洞的检测；其它管理平台版本请联系牧云技术支持团队获取该功能。  
**时间线**  
  
 Timeline   
  
  
  
**07‍**  
12月7日 官方发布补丁公告12月19日 互联网公开漏洞情报12月19日 长亭应急响应实验室漏洞分析与复现12月20日 长亭安全应急响应中心发布通告  
参考资料：  
  
[1].https://www.apusic.com/view-477-113.html  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
