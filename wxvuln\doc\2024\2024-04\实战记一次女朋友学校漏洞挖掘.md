#  实战|记一次女朋友学校漏洞挖掘   
瓜皮辰.  WIN哥学安全   2024-04-27 11:20  
  
## 免责声明：  
  
请使用者遵守《中华人民共和国网络安全法》，由于传播、利用本公众号所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，公众号及作者不为此承担任何责任。  
  
**0x00前言**  
  
  
嘿嘿，周四女朋友约我出去嗨，着实激动。趁着“带薪拉屎”期间去瞅了瞅女朋友的学校网站（女朋友还小，在上高中），其实很早以前就想搞了，只不过一直没有发现学校官网，但是在最近挖漏洞期间，在fofa中闲着没事搜索了一下这个学校，嘿，找到官网了，并成功拿到shell，记录一下。  
  
  
**0x01开搞！！！**  
  
  
首先呢，先找到官网，百度等各大搜索引擎处都没有此学校官网，猜测是没有或者是太老。于是在fofa中尝试利用title语句查找一下。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyu0DiaU9DJwiaIVzLbqhNHo31dt0Pw5oAU3A2CPT8uVXB9HHS1dva4AcuQ/640?wx_fmt=png&wxfrom=13&tp=wxpic "")  
  
  
成功找到学校网站，特别low的一个页面，看来是一个废弃的服务器了，10年的。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyu282C3ia0CN3mKckf8qibHM3sKvG7DegsicQzIdD2O3ZYljrw17Uev113Q/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
点击进入应用连接内的系统  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuztFFrU5ed2G6zgGcH8LDUCVRYeG1aObmaUAAnonnziawDRBq3EwJncg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
在输入框内，加个单引号，报错，猜测可能存在sql注入，抓包直接上sqlmap跑  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuRZicZkJJ3t1eA7cpWqD2P3Hnuqxa4XaMNeRPu3pic5VOXUnN8f4yiawTg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
sqlmap没跑出来，我这个电脑跑sqlmap有毒（每次都必须让我加–proxy，哪位表哥会解决，麻烦联系我一下，万分感谢），就随便截个图意思一下吧。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyubiaYSd5qUpDndpicPne3UfT5uKyK8ibrYibaicOicelyAanB5WamNJqhvxYw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
然后把这些系统都给sqlmap跑了一遍，均无果，我手注有点垃圾，就简单测试一遍。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuVhtSlGe4WS0vySKMKKk4DZyJzypctbuhfWOXAAAJw4Y4E1PX0kDxZg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
既然没有，那就扫一下目录吧  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuOTHPudCntJnjicjI04x7zXQ6f1bvZoWpBWicEYl35Jzhc2CibpFniacSiaw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
都试了一遍没啥东西，就不贴图了  
  
  
主站无果，就扫描一下端口，看看这个服务器上有无其他端口，推荐goby，界面简单明了，还有个漏洞检测功能。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuuiah0tvMfxccNpEtFfUrmx0U4VkwtQTFM8Xr39opicicZDjNR2VM0gicvg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
非常nice，还有另外4个web网址，其中2个还是死的，一共2个站点  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuJmzMGsAtib5PIMZqpznsDw9oibiatYsLnaZrvgX8ZJhVJNIicgFLeIqqLQ/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
由于存在验证码，就简单的手测了几个弱口令，扫了遍目录，啥也没有，那就不在这浪费时间了，转战下一个点。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyu7qPibaK7TJ7WrCfRNwgszrhgpnYd1IwMY7NQzkyK5gG15vHnpYddlCA/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
点击登录，在登陆出继续尝试弱口令  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyukeUVE5WTLo0MtEM2MbBOdK5ibcuJzaOxyicb44hMJWLdOVI3eRqECrHA/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
可以看出会自动提示是否存在该用户，经过测试用户名为admin  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuhRPweFBXYRJe6fg33KBrOqqfAQUzeuyKLOcF0fCN2BZdIZY9d4xxlw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
对不起，我失败了，我没测出弱口令。看到旁边还有个注册，我先试着注册一个账号，看看是否存在上传点或者越权啥的。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuULbRvqEcj9fF9GXibN6GHVyicRRT0v5WIHNNmZOz4qdoibLOsOiaTvGRog/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
好家伙，注册完直接是讲师账号，nb，存在上传点。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyu3N05Tk2qllTGVKV1Qh2iaByzUeiaOD6O21E0dgVowp1BAN7TH7Yuicjjw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
先随便上传一张图片，瞅一眼路径  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyub9wTQzQzR3NWutTrTZ2VhH0iaUMQ4kTIERwesAksE5XyziaAmNarYN0g/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
接下来直接上传jsp木马，fuzz下上传姿势  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuD5rVECdREL2phiakQI7JCiaseic8foWU6mibzvicKN1QRBSnm1zFGvud5dw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
前端验证，直接上传jsp木马.jpg，抓包修改为.jsp格式就行，简简单单，运气真好。  
  
  
这时候直接拼接返回的路径，用冰蝎直接连接木马  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/bMyibjv83iavyK4hEv9X1SsYNtnSsDJicyuznxT8oTWMMiczoFJenOVvgy4nQpKoEL6nIffM9A8qj9P3icSueRP4Tzw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
成功拿到shell，还是system权限，看了一下里面没啥东西，就是个老站了，无趣，打王者荣耀去。大佬勿喷哦，毕竟我是个菜鸡。  
  
  
  
Tips:  
   
  
  
文章来源：  
  
```
本文为CSDN博主「瓜皮辰.」的原创文章
https://blog.csdn.net/Guapichen/article/details/112251679
```  
  
  
  
HVV招聘：  
投递到-->  
  
```
https://send2me.cn/Ubbozd6R/S6-vFqExvjv4EA
```  
  
  
考证咨询  
：  
全网最低最优惠报考NISP/CISP/CISSP/PTE/PTS/IRE/IRS  
等证书，后台回复“好友”加V私聊。  
  
[【2024HW】持续招聘中：多个优质HW项目（有新增）](https://mp.weixin.qq.com/s?__biz=MzkwODM3NjIxOQ==&mid=2247499274&idx=1&sn=b220c249a1c38e5e2ba218bc49789de7&chksm=c0c85ffef7bfd6e8f046077774fffbd85b946a075ae4b4e0a39c1f0e50a4c29d231278f81c2c&scene=21#wechat_redirect)  
  
  
[浅谈一次省护红队的经历](https://mp.weixin.qq.com/s?__biz=MzkwODM3NjIxOQ==&mid=2247499265&idx=1&sn=ec652b37185916ca207d9c33ffaeb3a0&chksm=c0c85ff5f7bfd6e3391435346762f1fe4342989fbbc9d037877251298ca3cd58ee775909dd7a&scene=21#wechat_redirect)  
  
  
[[韭]第一次参加护网行动要注意什么？？](https://mp.weixin.qq.com/s?__biz=MzkwODM3NjIxOQ==&mid=2247499134&idx=1&sn=b31a60d9e06f07715765f6fcd870f89e&chksm=c0c85c8af7bfd59c8f348b02337df7602c80cc363faee214ec1491f396b10b386ec9847eaf33&scene=21#wechat_redirect)  
  
  
  
  
  
