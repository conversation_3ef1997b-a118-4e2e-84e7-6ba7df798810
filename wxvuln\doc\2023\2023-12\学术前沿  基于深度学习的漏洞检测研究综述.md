#  学术前沿 | 基于深度学习的漏洞检测研究综述   
 网络空间安全科学学报   2023-12-11 22:23  
  
**引用**  
  
  
邹德清 , 姬煜 , 胡雨涛 , 刘启霄 , 郑直. 基于深度学习的漏洞检测研究综述[J]. 网络空间安全科学学报, 2023, 1(1): 47-58  
  
  
ZOU Deqing , JI Yu , HU Yutao , LIU Qixiao , ZHENG Zhi. Survey on Vulnerability Detection Research Based on Deep Learning[J]. Journal of Cybersecurity, 2023, 1(1): 47-58  
  
**背  景**  
  
开源软件在现代软件中的占比高达80%~90%，开源软件在极大节省软件开发成本的同时也带来了严重的安全隐患。随着开源软件的广泛使用，开源软件漏洞也随之大量传播，因此针对海量开源代码开展高效智能漏洞检测研究具有重要意义。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/bpXMCVmmaxCGyQiakRx8icUwybUiaooyTBoemiaLm8Mf2d3NhkfeP9oCYwuIxHX6y3Wib3ic0kjzZeOyt6LozvGmyruA/640?wx_fmt=png&from=appmsg "")  
  
本文针对基于深度学习的漏洞检测方法开展研究。深度学习已在多个应用领域受到广泛关注并取得卓越成果，其强大的建模和学习能力同样适合应用于漏洞检测领域。从实现基于深度学习的漏洞检测方法的角度出发，文章以采用的技术类型为分类依据，在以下两方面对现有漏洞检测相关工作进行了总结：  
  
**01.**  
  
**文章总结了三类现有漏洞数据集**  
  
  
在漏洞检测任务中，漏洞数据集是深度神经网络训练的重要基础。一个收集全面且标注准确的漏洞数据集能够使得模型学习到精准的漏洞特征，从而达到较为理想的漏洞检测能力。现阶段的漏洞数据集可以划分为人工漏洞数据集、半人工漏洞数据集和真实漏洞数据集。  
  
其中，人工漏洞数据集根据已知漏洞模式通过人工撰写样本，具有漏洞特征明显且代码模式单一的特点。因此，基于人工漏洞数据集训练的模型往往难以适应真实环境的漏洞检测；半人工的漏洞数据集集合了人工样本和真实漏洞样本，结合了人工和真实漏洞数据集的特点；真实漏洞数据集的全部数据均为真实存在的漏洞，优势在于其训练的模型更能够应对真实漏洞检测任务，但是存在数据质量不佳的问题。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/bpXMCVmmaxCGyQiakRx8icUwybUiaooyTBoemiaLm8Mf2d3NhkfeP9oCYwuIxHX6y3Wib3ic0kjzZeOyt6LozvGmyruA/640?wx_fmt=png&from=appmsg "")  
  
**02.**  
  
**文章总结了两种现有基于深度学习的漏洞检测框架**  
  
  
对于漏洞检测方法，我们根据其采用的深度神经网络类型，将漏洞检测方法分为面向文本处理的智能漏洞检测和基于图神经网络的漏洞检测，并分别针对两种框架不同的工作流程、现有应用方法、现有的技术局限性和未来可能的发展方向进行了技术总结，这是文章的核心研究内容。  
  
简单来说，面向文本处理的智能漏洞检测方法将源代码视作普通文本进行模型训练；基于图神经网络的漏洞检测的处理对象为代码中间表示。因为图是比文本更好的代码表示方式，因此基于图神经网络的检测方法在漏洞检测领域展现了更好的检测能力。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/bpXMCVmmaxCGyQiakRx8icUwybUiaooyTBoemiaLm8Mf2d3NhkfeP9oCYwuIxHX6y3Wib3ic0kjzZeOyt6LozvGmyruA/640?wx_fmt=png&from=appmsg "")  
  
**未来研究方向**  
  
总的来说，当前基于深度学习的漏洞检测的两种主流方法都仍有其局限性：由于面向自然语言处理的深度学习模型先天性缺乏对于代码语义和语法的抽象和提炼能力，导致检测效果不够理想。  
  
因此，未来工作可以从加强样本漏洞特征刻画和细化代码处理粒度两个角度出发，提高模型的检测效果。对于基于图神经网络的检测方法，目前方法大多基于常用的代码中间表征（如，程序依赖图等）进行模型训练，并采用节点嵌入进而图嵌入的单一方式进行嵌入处理。未来工作可以对于不同类型漏洞的特征设计更加有效的中间表征方式，或者在嵌入过程加入对漏洞特征的考虑，从而提高模型训练效果。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/bpXMCVmmaxCGyQiakRx8icUwybUiaooyTBoemiaLm8Mf2d3NhkfeP9oCYwuIxHX6y3Wib3ic0kjzZeOyt6LozvGmyruA/640?wx_fmt=png&from=appmsg "")  
  
  
来源：《网络空间安全科学学报》第一期  
  
  
（点击文末左下角“**阅读原文**”可查看本篇文章）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/bpXMCVmmaxCkTcfcpQW10RBaTfPBnUVbp9Jk2IHuePBhxXQ3E40s5hUaQibnp2Qiav2IdYYTxria4cBoArYcWOspQ/640?wx_fmt=png "")  
  
  
  
《网络空间安全科学学报》由中国航天科技集团有限公司主管、 中国航天系统科学与工程研究院主办，双月刊，国内外公开发行（  
CN 10-1901/TP，ISSN 2097-3136）。办刊宗旨为“搭建网络空间安全领域学术研究交流平台，传播学术思想与理论，展示科学研究、创新技术与应用成果，助力网络空间安全学科建设，为网络强国建设提供坚实支撑与服务”。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/bpXMCVmmaxCkTcfcpQW10RBaTfPBnUVbpH7OESyiayAb2icdRqR7YHbCACAybEjpJD8NFTOxw06LzqbQNS7Uxmtw/640?wx_fmt=jpeg "")  
  
  
网站：www.journalofcybersec.com  
  
电话：010-68370159 / 68372337  
  
邮箱：<EMAIL>  
  
  
