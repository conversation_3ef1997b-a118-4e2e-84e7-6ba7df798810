#  Juniper新洞CVE-2023-36845浅析   
耳旁有首歌  黑客白帽子   2023-12-16 08:00  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0w6V8YUTyNSuV2udfyY3rWyR6V1UeHWuiab6T80I5ldZicZswCnrbicD4ibpaDMqCZ6UvFmhWLyTzptSA/640?wx_fmt=png&random=0.6636094571400317&random=0.6219011309810436&random=0.21191420540585404 "")  
  
**感谢师傅 · 关注我们**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0w6V8YUTyNSuV2udfyY3rWyR6V1UeHWuiab6T80I5ldZicZswCnrbicD4ibpaDMqCZ6UvFmhWLyTzptSA/640?wx_fmt=png&random=0.9829534454876507&random=0.2787622380037358&random=0.29583791053286834 "")  
  
  
由于，微信公众号推送机制改变，现在需要设置为星标才能收到推送消息。大家就动动发财小手设置一下呗！啾咪~~~  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PJG3jJlPv0y50hQk1TiaBIAnSjzqkmZcPS4TWvohHfHPTVUBWM2mFxcqwhiaZKaQM6S7t11fuiajZ2zZqXD5hJJmA/640?wx_fmt=png "")  
  
  
前言  
  
2023 年 8 月 17 日，Juniper 宣布了多个漏洞，包括：与防火墙 SRX 和 Switch EX 相关的 CVE-2023-36844、CVE-2023-36845、CVE-2023-36846、CVE-2023-36847、CVE-2023-36851。  
  
Juniper表示，这些CVE的CVSS评分均为5.3，如果组合在一起，该漏洞的CVSS评分可达9.8。 目前，我不知道 Juniper 是否更新了这些 CVE 的分数，因为事实上，我的以下 PoC 可以证明只有 1 个 CVE-2023-36845 可以进行 RCE，甚至接管系统的管理员权限。 因此，仅此 CVE-2023-36845 就值得 CVSS 9.8 分！  
  
CVE 和 PoC 分析谈到公开的PoC，他们使用CVE-2023-36846上传可执行文件test.php和文件php.ini。 接下来，他们利用CVE-2023-36845更改PHP的PHPRC环境变量，以通过php.ini文件执行test.php文件。 但其RCE仅止于执行phpinfo()和LFI函数读取部分内容，并不能提权或产生任何影响。  
  
于是本地找了个环境进行测试，公开的脚本如下https://github.com/kljunowsky/CVE-2023-36845https://github.com/r3dcl1ff/CVE-2023-36844_Juniper_RCE  
  
根据PoC发出以下POST请求  
```
```  
  
请求：  
```
```  
  
响应：  
```
```  
  
竟然会报错？-:**function not callable ?**使用payload <? phpinfo(); ?>测试：  
```
```  
  
过程：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnEdpoOFysrjiaHOVIXh3nPiacWkXQLoCXuXstzZtiaHsFObrAk4CATh2kA/640?wx_fmt=png&from=appmsg "")  
  
POST request:  
```
```  
  
发现响应为：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pn7ICicsWgic6V01kbZU6QUtib9W8CJ0aDowRNDpdnziafp5POia3YXrMYRibA/640?wx_fmt=png&from=appmsg "")  
  
访问界面：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnlPyChVN8F7WOJGCMia71Z9ERiaP2Bt8HRqWARjlP4uOMYgwREJIpOBIw/640?wx_fmt=png&from=appmsg "")  
  
保存到本地：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pn5ticMibg0QhvicYTJJa8hcHWGl7u8BEGNjiaYicyxcUTDfyL7Wwib4VB6PIA/640?wx_fmt=png&from=appmsg "")  
  
执行 get_current_user() 的payload  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnibUz8AaVunFC2PWxhk5uWW8ObkOvELaGbtCd4jgPWE8534IqKEh6nMw/640?wx_fmt=png&from=appmsg "")  
  
发现响应为：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnmTeGAF4NuZH0dIcxHTibtWmlq3S02pBHFnDl2JWqvx1ktMvssibaqfrQ/640?wx_fmt=png&from=appmsg "")  
  
但是用其他命令执行发现：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnGTAggn5M7yc7iaeVwHWyS5yV6WkQaEyvw5PyLkbvubp4YBXZqmicGTVA/640?wx_fmt=png&from=appmsg "")  
  
响应为：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnSuLN87zrQoPjEia4309vDzuldvofr5eLlb81homaspBthOnTAXh6E2w/640?wx_fmt=png&from=appmsg "")  
  
竟然没有这些命令，那只能祭出大杀器：反弹shell！尝试使用/bin/bash来reverse_shell但它不起作用：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pn9TMvXXwTFAYiaI6Vq5QpbKGex7ibahMkiaq5x53ReHMtC3QriaNnVdkPcg/640?wx_fmt=png&from=appmsg "")  
  
监听：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnWXN6zia7sxJN6pZFFic6wBfV32ruZUWHSfAIAzX1hFXziauDMIlGOdlqw/640?wx_fmt=png&from=appmsg "")  
  
发送POST request后，发现：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnWSvU0UlzFicqCxbzgMv48bVMr8XsXhwHsnaOwYH3iaHbic5JGMKgFz0wQ/640?wx_fmt=png&from=appmsg "")  
  
只能执行一些类似于RCE的基本命令。那么为什么 root 用户不能执行这些基本命令呢？ 因为 Juniper 从版本 7.5 开始默认安装一个工具来限制此执行权限，即使它是 root 用户，因此为 Juniper 的开发团队整了个Veriexec：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnwvkF9kUp264vicVecT0ia4QUR753OCHGd7byYgJpwI5lgPcbjCdPAU5A/640?wx_fmt=png&from=appmsg "")  
  
最后的最后，看到国外大佬研究成果：窃取登录用户的cookie登录web。在本地搜索源代码后，我发现PHPSESSION值位于PHP存储在/var/sess/中的cookie中/var/sess/：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnkSw2dlGbQw36EG0JkqS3P2yZrx5MlCObn0sAYnJZIbJ23H7lLEY4pg/640?wx_fmt=png&from=appmsg "")  
  
那就用glob() 读取/var/sess/的文件列表吧！  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnKMyMIFKtjDDib0nia6gcqNYDUmZhKB8ia8D41iaibO6SJ0CFDZibsuougD8w/640?wx_fmt=png&from=appmsg "")  
  
POST response:  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnwNe5mEBicUUAcY5R21WjibgektOdWGMbTEAo8mUIuyUQ164BswpJHTXQ/640?wx_fmt=png&from=appmsg "")  
  
https的话要使用SECUREPHPSESSID  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnLnNTTgsQ24uR4kOdLvjXud4B1icprtCqZo8jGdNvgC65ibIKO0uvOovg/640?wx_fmt=png&from=appmsg "")  
  
将文件名作为内容填充到web中：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pnad689OeYYvPlMonPRqlFGpnIq2qq5LIDL2Zz1VIntVjGxKHtZJMkOA/640?wx_fmt=png&from=appmsg "")  
  
刷新后：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnXpBTE6uzkYU5jQF928bkkyJmEYLSY7ic1ricVAWRBVQspjWx2Wsfl3sg/640?wx_fmt=png&from=appmsg "")  
  
当前是root，超级用户：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnmzTtqRjHMEtlBpBTmMBWxhv7vtbY4ibkdQgHxmFJCeoTUOveVjcwiaHQ/640?wx_fmt=png&from=appmsg "")  
  
还尝试了用来登录ssh：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pn2cBTkBF21u792K09NLjR2vD1xHWakLn546uS6btl8IYkBCzyYkv6PQ/640?wx_fmt=png&from=appmsg "")  
  
按照国外大佬的说法，此目录中会有sess-….文件，有的话可以登陆，没有就是不能登陆，因为源码中写的session超时3600秒，一小时。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2PnrYic9mbh04XWDsLIib5njjXfx9UaGBZnVOLDOF05ibfeoCgKkBIB4fycA/640?wx_fmt=png&from=appmsg "")  
  
但实际环境验证发现：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/PJG3jJlPv0xmKjBmvW44Q4Zb1hOPE2Pn9ycSGMvJzJGHOnicbZiafodBKoUSnibcs1yCspJwTwPYZoCiaAlEYV1j5w/640?wx_fmt=png&from=appmsg "")  
  
大多环境可能版本差异，并无法完美使用，可能需要更新的版本。目前次漏洞的利用算是进行了概念验证，奈何Juniper防控太强，能用的洞，也等于没用；欢迎大佬们讨论研究。  
  
  

								  

									  

										  

											  
往期推荐  

										  

									  

									  

								[ 一款用于渗透中检测网站CDN/WAF/云的工具 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936828&idx=1&sn=6da9fe4adf7733b55ff59e0b738fae79&chksm=8bac5903bcdbd015e6ca96c80d39c4e26691c75ced7fa4f3c372105bbc7b441f69bec069e4cb&scene=21#wechat_redirect)  

							  
  

								[ 【漏洞通告】微软12月多个安全漏洞 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936766&idx=1&sn=a9add1dfe230aadc04741852c27be56c&chksm=8bac5941bcdbd057c73ca279f7425b624f445f01715fea26b41c90e46450ec59e6eb00c8a49a&scene=21#wechat_redirect)  

							  
  

								[ Apache Struts2 文件上传漏洞分析（CVE-2023-50164） ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936766&idx=2&sn=56a7491a76c167b3ae464f62d101cd2b&chksm=8bac5941bcdbd057d222f33c6ef1d2b8b90868a44314b6b8dbcf46bb327cbdf2603dde196653&scene=21#wechat_redirect)  

							  
  

								[ webshell编写神器 -- 腾讯混元大模型来啦！！！ ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936670&idx=2&sn=df91a15c2917975e9a2c46564ae435b2&chksm=8bac59a1bcdbd0b738be0b9e34a49358cd356c54015b55f79a7cb16474734502e7d1b3dead81&scene=21#wechat_redirect)  

							  
  

								[ 【资讯】①Android锁屏绕过漏洞②丰田个人和财务信息被公开③用配置错误泄露368MB的数据等 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936670&idx=1&sn=896bd9bd17cb0f54b90b3858edcb2571&chksm=8bac59a1bcdbd0b75f729e23c6ff53eadf5db6f5a2954408eebaa3b5e3a04a14909dfeed475d&scene=21#wechat_redirect)  

							  
  

								[ 各大厂商常用的弱口令集合 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936580&idx=2&sn=0579c5e1b1321b4f1b127b2ea8bcfe2c&chksm=8bac59fbbcdbd0ed76dfac9ab94a94458eafcb5197f750ca61c2903bb4aac3d3ff5f0dde1606&scene=21#wechat_redirect)  

							  
  

								[ 【已复现】关于新的Struts2漏洞，你应该知道的 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936503&idx=2&sn=036d26ef8a80240cffb51d5c2cb86612&chksm=8bac5848bcdbd15e1fe81c4843c2e5829c03dcf6c06c172aeb42918819c8656e4174590032bf&scene=21#wechat_redirect)  

							  
  

								[ 一款批量Linux应急响应检查工具 ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936387&idx=2&sn=49d369d627ebb5768cf6955589667bd7&chksm=8bac58bcbcdbd1aa285197f7988d2129ace2934d3406cfcf782e7e314cf7d2626407f45ebf20&scene=21#wechat_redirect)  

							  
  

								[ 一款更易上手的GUI版xray漏扫工具（附下载） ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936302&idx=2&sn=e1cad443d34676a7e3b5ce469ef86e0f&chksm=8bac5f11bcdbd607c6171a1ed63398c71c2de3fb38ada55c9802e0bec515f4ad7687510137b9&scene=21#wechat_redirect)  

							  
  

								[ 【渗透测试】渗透测试常用方法总结，大神之笔！ ](http://mp.weixin.qq.com/s?__biz=MzA5MzYzMzkzNg==&mid=2650936215&idx=2&sn=df847db56abe56618df30876ead08199&chksm=8bac5f68bcdbd67ead17af135d86da0df66f7f944c03366da648824a66ede9f7ea502730b23e&scene=21#wechat_redirect)  

							  
  
  
  
声明：本公众号所分享内容仅用于网安爱好者之间的技术讨论，禁止用于违法途径，**所有渗透都需获取授权**  
！否则需自行承担，本公众号及原作者不承担相应的后果  
```
```  
  
  
