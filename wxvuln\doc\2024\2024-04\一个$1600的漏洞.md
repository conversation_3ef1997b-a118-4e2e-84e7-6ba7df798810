#  一个$1600的漏洞   
 迪哥讲事   2024-04-23 21:34  
  
<table><tbody><tr><td width="557" valign="top" style="word-break: break-all;"><h1 data-selectable-paragraph="" style="white-space: normal;outline: 0px;max-width: 100%;font-family: -apple-system, system-ui, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;background-color: rgb(255, 255, 255);box-sizing: border-box !important;overflow-wrap: break-word !important;"><strong style="outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;"><span style="outline: 0px;max-width: 100%;font-size: 18px;box-sizing: border-box !important;overflow-wrap: break-word !important;"><span style="color: rgb(255, 0, 0);"><strong><span style="font-size: 15px;">声明：</span></strong></span><span style="font-size: 15px;"></span></span></strong><span style="outline: 0px;max-width: 100%;font-size: 18px;box-sizing: border-box !important;overflow-wrap: break-word !important;"><span style="font-size: 15px;">文章中涉及的程序(方法)可能带有攻击性，仅供安全研究与教学之用，读者将其信息做其他用途，由用户承担全部法律及连带责任，文章作者不承担任何法律及连带责任。</span></span></h1></td></tr></tbody></table>#   
# 博客新域名：https://gugesay.com  
  
****#   
  
  
  
背景介绍  
#   
  
今天分享一位来自印度的独立安全研究员 Nauman Khan 的故事，他通过Auth0的错误配置，顺利获得1600美元赏金奖励。  
# 关于Auth0  
  
Auth0 是网站和应用程序广泛使用的身份验证平台，用于管理用户身份并确保安全访问其服务。它提供各种工作流来无缝集成登录和注册流程。  
## 错误配置  
  
在 Auth0 中创建新应用程序时，默认情况下会启用注册选项，但如果配置不当，该默认设置可能会带来安全风险，比如系统禁用了注册但实现了 Auth0 功能，则有可能绕过注册限制。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hZj512NN8jn4DxG6odibR5icPHoX9gs0WtCg8Qzy0WxibplyndHPmd2EEiaodOgmvEFj45nT75LkIHLLWd54Ijf3JQ/640?wx_fmt=png&from=appmsg "")  
# 利用过程  
  
通过搜索 Auth0 身份验证的 API 文档，成功发现了一处端点，允许用户使用对 /dbconnections/signup 的 POST 请求进行用户注册。但该端点需要特定参数：  
- client_id：请求访问 Auth0 服务的应用程序的唯一标识符  
  
- connection：指定用于身份验证的身份提供者  
  
- email：用户的电子邮件地址  
  
- password：符合配置的密码策略的所需密码  
  
## 复现步骤  
- 访问应用程序的登录页面  
  
- 在登录表单中输入构造的电子邮件和密码  
  
- 使用 Burp Suite 拦截请求  
  
- 点击提交按钮，发送登录请求  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hZj512NN8jn4DxG6odibR5icPHoX9gs0WtbZoN4z6Xvic2GdkWra0oL7TOzAt5ibRtctA3WNIw7VOl18ibv7pS7F4lA/640?wx_fmt=png&from=appmsg "")  
- 在 Burp Suite 中分析拦截的登录请求，提取以下参数：client_id、connection 以及其他所需的参数值  
  
- 将拦截的请求发送到Burp Suite的Repeater  
  
- 将请求路径从 /usernamepassword/login 更改为 /dbconnections/signup  
  
- 在请求正文中将参数名称从用户名修改为电子邮件（因为端点仅接受电子邮件参数）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hZj512NN8jn4DxG6odibR5icPHoX9gs0WtDXIXzJLwfM5ibMuz4vGpS3LdMtP7x4R7cB6fzEx9jO5zYQTCTqoBOKg/640?wx_fmt=png&from=appmsg "")  
- 发送修改后的请求  
  
- 使用新创建的凭据登录应用程序  
  
通过以上步骤，攻击者可以利用Auth0中的错误配置绕过注册机制，从而创建未经授权的帐户并获得对应用程序的访问权限。  
  
如果你是一个长期主义者，欢迎加入我的知识星球，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款前面有同学问我有没优惠券，这里发放100张100元的优惠券,用完今年不再发放  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7N5nMaJbtnMPVw96ZcVbWfp6SGDicUaGZyrWOM67xP8Ot3ftyqOybMqbj1005WvMNbDJO0hOWkCaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
  
以上内容由骨哥翻译并整理。  
  
原文：https://naumankh4n.medium.com/a-click-can-cause-1600-auth0-misconfig-9234aedad55c  
  
****  
  
  
