#  【已复现】关于新的Struts2漏洞，你应该知道的   
 长亭安全应急响应中心   2023-12-09 18:30  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicSI8cibQmkEPEOhcqLJ1iajQEWVdH1A8eicwx6V4sASEcDVFOqwcIdAKoO4PZfIcEVnByJqX6S7uvDng/640?wx_fmt=png&from=appmsg "")  
  
  
Struts2是一个流行的Java框架，用于开发企业级Web应用程序。支持动作处理、表单验证、拦截器，以及与其他Java技术的集成。  
  
  
2023年12月，Apache官方发布新版本修复了Struts2中的一个文件上传漏洞（CVE-2023-50164），并将其编号为S2-066。  
  
****  
**该漏洞的利用依赖于特定环境的条件，需要根据不同环境定制化攻击数据包。这增加了利用难度，不太可能出现自动化和大规模的利用。鉴于此，企业可以根据自身情况和风险评估来决定修复漏洞的优先级和时间安排。注意：虽然这不是一个容易被广泛利用的漏洞，但考虑到潜在的风险，建议尽早采取相应的安全措施。******  
  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因由于Struts框架在处理参数名称大小写方面的不一致性，导致攻击者能够通过修改参数名称的大小写来利用目录遍历技术（如使用../路径）上传文件到服务器的非预期位置。利用特征参数名称大小写变化：在上传请求的数据包中，可以观察到涉及文件上传功能的参数名称出现大小写变化的情况。这是由于攻击者试图利用Struts框架对参数名称大小写处理不一致的问题。目录遍历痕迹：数据包中可能包含明显的目录遍历痕迹，如使用../等路径。攻击者通过这种方式尝试上传文件到服务器的非预期目录。漏洞影响远程代码执行风险：该漏洞使得未授权的攻击者能在服务器上执行任意代码，可能导致系统被控制。数据泄露风险：攻击者可能通过这个漏洞访问和窃取服务器上的敏感数据。勒索软件攻击风险：攻击者可以上传并执行勒索软件导致业务系统被锁定。影响版本 Affected Version 02Apache Struts 2.0.0 至 2.5.32Apache Struts 6.0.0 至 6.3.0.1解决方案 Solution 03临时缓解方案增强输入验证：在Struts应用使用文件上传相关的代码处进行严格的输入验证。特别是要确保不直接将用户提供的文件名拼接为文件路径的一部分，因为这可能导致路径遍历。文件名随机化：在保存上传的文件时不直接使用用户上传的文件名，生成一个随机的文件名。这可以防止攻击者预测或操纵文件路径。访问控制：加强服务器和应用的访问控制。确保只有信任的用户和系统可以访问受影响的Struts应用。监控和日志记录：加强对Web应用的监控和日志记录，特别是关注异常上传行为和路径遍历尝试。升级修复方案Apache官方已发布安全更新，建议访问官网（https://struts.apache.org/download.cgi#struts-ga）升级至最新版本。漏洞复现 Reproduction 04检测工具 Detection 05牧云本地检测工具检测方法：在本地主机上执行以下命令即可扫描：sudo ./apache_struts2_upload_cve_2023_50164_scanner_linux_amd64 scan工具获取方式：https://stack.chaitin.com/tool/detail/1262产品支持 Support 06雷池：默认支持该漏洞利用行为检测。全悉：已发布规则升级包，支持检测该漏洞的利用行为。  
**时间线**  
  
 Timeline   
  
  
  
**07**  
12月7日 漏洞情报在互联网公开12月8日 长亭应急响应实验室复现漏洞12月9日 长亭安全应急响应中心发布通告  
参考资料：  
  
[1].https://www.openwall.com/lists/oss-security/2023/12/07/1  
  
[2].https://cwiki.apache.org/confluence/display/WW/S2-066  
[3].https://lists.apache.org/thread/yh09b3fkf6vz5d6jdgrlvmg60lfwtqhj    
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
