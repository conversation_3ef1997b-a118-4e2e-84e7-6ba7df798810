#  CatDDoS僵尸网络正在利用CVE-2023-46604进行传播   
 360威胁情报中心   2024-04-17 18:59  
  
##   
  
近期，360安全  
大脑捕获到一款僵尸网络变种正在利用Apache ActiveMQ漏洞(CVE-2023-46604)进行积极传播，样本涵盖x86、arm、mpsl等CPU架构。经过分析确认，该变种属于2023年下旬被披露的新兴DDoS僵尸网络——CatDDoS。除了使用CVE-2023-46604传播外，样本内同时还集成了CVE-2017-17215等N day漏洞payload，影响HG532路由器产品。  
  
  
**CVE-2023-46604**  
  
Apache ActiveMQ是最流行的开源、多协议、基于Java的消息代理。它支持行业标准协议，用户可以在各种语言和平台上获得客户端选择的好处，用JavaScript、C、C++、Python、.Net编写的客户端连接等，使用无处不在的AMQP协议集成多平台应用程序，通过websockets使用STOMP在web应用程序之间交换消息。  
  
2023-10-27，Apache ActiveMQ官网发布安全更新，修复了一个远程代码执行漏洞CVE-2023-46604。攻击者可构造恶意请求通过Apache ActiveMQ默认的61616端口利用该漏洞，最终在服务器上实现远程代码执行。  
  
  
影响版本      
<table><tbody><tr style="mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;"><td width="463.3333333333333" valign="top" style="border-width: 1pt;border-style: solid;border-color: windowtext;padding: 0cm 5.4pt;"><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ 5.18.0 &lt; 5.18.3</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ 5.17.0 &lt; 5.17.6</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ 5.16.0 &lt; 5.16.7</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ &lt; 5.15.16</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ Legacy OpenWire Module 5.18.0 &lt; 5.18.3</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ Legacy OpenWire Module 5.17.0 &lt; 5.17.6</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-size: 15px;"><span lang="EN-US" style="font-family: 仿宋;color: black;">Apache
  ActiveMQ Legacy OpenWire Module 5.16.0 &lt; 5.16.7</span><span lang="EN-US" style="font-family: 仿宋;"><o:p></o:p></span></span></p><p style="line-height:150%;background:#E7E6E6;"><span style="font-family: 仿宋;color: black;font-size: 15px;">Apache
  ActiveMQ Legacy OpenWire Module 5.8.0 &lt; 5.15.16</span><strong><span lang="EN-US" style="font-family:仿宋;"><o:p></o:p></span></strong></p></td></tr></tbody></table>  
由于该漏洞的利用细节与概念验证程序(PoC)已被公开，使得攻击者能够轻松发起针对该漏洞的利用活动。迄今也已经出现了多起实际的攻击案例，包括HelloKitty勒索病毒、GoTitan僵尸网络、Kinsing(又称H2Miner)挖矿木马等。  
  
**CatDDoS僵尸网络**  
##   
  
对CVE-2023-46604漏洞的利用，涉及到利用**ClassPathXmlApplicationContext**  
类来加载指定的恶意XML配置文件以实现RCE。捕获到的实际攻击如下：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZEYe9KJrv1wUibt8onC1RicKg1Js54QVEHXMJHzLSXCbBpZZjcHLNv3ibA/640?wx_fmt=png&from=appmsg "")  
  
  
mq.xml恶意文件的内容如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZocBviaCgyeicluFrWGHCby0RN80Fnvn14LKfjV7NqA9R5U0eRMgzTy7g/640?wx_fmt=png&from=appmsg "")  
  
借助sh命令，攻击者得以在受害服务器上成功下载与运行名为selfrep.x86的恶意ELF程序，带有mq.x86的运行参数以标识Apache ActiveMQ的感染来源。  
<table><tbody><tr style="mso-yfti-irow:0;mso-yfti-firstrow:yes;"><td width="93.33333333333333" valign="top" style="border-width: 1pt;border-style: solid;border-color: windowtext;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><strong><span style="font-family: 仿宋;font-size: 15px;">文件名</span><span style="font-family: 仿宋;font-size: 15px;"><o:p></o:p></span></strong></p></td><td width="261.3333333333333" valign="top" style="border-top: 1pt solid windowtext;border-right: 1pt solid windowtext;border-bottom: 1pt solid windowtext;border-left: none;padding: 0cm 5.4pt;word-break: break-all;"><p style="line-height:150%;mso-pagination:widow-orphan;"><span style="font-family: 仿宋;font-size: 15px;">selfrep.x86<o:p></o:p></span></p></td></tr><tr style="mso-yfti-irow:1;"><td width="113" valign="top" style="border-right: 1pt solid windowtext;border-bottom: 1pt solid windowtext;border-left: 1pt solid windowtext;border-top: none;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><strong><span style="font-family: 仿宋;font-size: 15px;">MD5</span><span style="font-family: 仿宋;font-size: 15px;"><o:p></o:p></span></strong></p></td><td width="261.3333333333333" valign="top" style="border-top: none;border-left: none;border-bottom: 1pt solid windowtext;border-right: 1pt solid windowtext;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><span style="font-family: 仿宋;font-size: 15px;">8BED0B9A5FCF46FDC9D31A669A3F99BE<o:p></o:p></span></p></td></tr><tr style="mso-yfti-irow:2;"><td width="113" valign="top" style="border-right: 1pt solid windowtext;border-bottom: 1pt solid windowtext;border-left: 1pt solid windowtext;border-top: none;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><strong><span style="font-family: 仿宋;font-size: 15px;">文件格式</span><span style="font-family: 仿宋;font-size: 15px;"><o:p></o:p></span></strong></p></td><td width="281.33333333333337" valign="top" style="border-top: none;border-left: none;border-bottom: 1pt solid windowtext;border-right: 1pt solid windowtext;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><span style="font-family: 仿宋;font-size: 15px;">ELF 64-bit LSB executable<o:p></o:p></span></p></td></tr><tr style="mso-yfti-irow:3;mso-yfti-lastrow:yes;"><td width="113" valign="top" style="border-right: 1pt solid windowtext;border-bottom: 1pt solid windowtext;border-left: 1pt solid windowtext;border-top: none;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><strong><span style="font-family: 仿宋;font-size: 15px;">文件大小</span><span style="font-family: 仿宋;font-size: 15px;"><o:p></o:p></span></strong></p></td><td width="281.33333333333337" valign="top" style="border-top: none;border-left: none;border-bottom: 1pt solid windowtext;border-right: 1pt solid windowtext;padding: 0cm 5.4pt;"><p style="line-height:150%;mso-pagination:widow-orphan;"><span style="font-family: 仿宋;font-size: 15px;">164.10 KB (168040 bytes)<o:p></o:p></span></p></td></tr></tbody></table>  
selfrep.x86是一个在Mirai源码基础上二次开发的新型僵尸网络，由于早期使用了catddos.pirate作为C2域名、以及攻击者留下的字符串信息中似乎对猫情有独钟，因而得名CatDDoS。CatDDoS较之Mirai的新颖之处在于引入了OpenNIC域名体系作为C2域名，同时使用了ChaCha20算法对一些关键信息进行加密，这些行为又与Fodcha僵尸网络存在一定的相似之处。  
  
selfrep.x86运行后，会修改进程名为随机字符串，并在控制台打印内容"Aren't I a menace?"。通过绑定本地127.0.0[.]1:46372端口确保单一实例。从/根目录开始遍历所有文件，尝试寻找watchdog看门狗路径，通过向watchdog发送0x80045704控制码以禁用watchdog功能，防止设备重启。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZ86j4KRZuzNcDJxzUbicmbfEkhicvKqUB1DomLSsSALQfYKtQotnW8ymw/640?wx_fmt=png&from=appmsg "")  
  
获取/proc/[pid]/fd，若其中不包含sshd等字段，则将其kill掉，同时上报给Reporter Server：  
194.127.178[.]114:7733  
。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZWOvC8UMdib5oDliap2URBc0DOPNibib1AadHm8z6ctRMznKD5TAic9IcqibA/640?wx_fmt=png&from=appmsg "")  
  
使用了ChaCha20算法对部分字符串、C2通信进行加/解密，key和nonce分别为：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZyia3dSaVb5Q73mRwEJIWLydJ8AbEfbsXdryMb3lbZdAELjic6P5qiaKhg/640?wx_fmt=png&from=appmsg "")  
  
  
解密出的字符串中包含了具体的C2：**omgnoway[.]geek**  
。该域名隶属于OpenNIC域名体系，与常见的ICANN域名不同，OpenNIC域名无法被通用DNS系统解析，需要借助某些特定的公共或私有DNS服务器进行解析。selfrep.x86样本中预先定义了用于OpenNIC域名解析的DNS服务器：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZQIdtiayibibJcx5nS3HwjznR1ejzWmTNJeMjialjIDB5V3zMPRYIIDnjAA/640?wx_fmt=png&from=appmsg "")  
  
  
CatDDoS僵尸网络目前稳定在2.0.4版本，捕获到的实际流量如下，由两部分的结构组成：修改了Mirai源码的4字节明文\x00\x00\x00\x04、交替的"长度 + 对应字段(部分字段为经过ChaCha20算法加密后的密文)"。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZibVBIkEIDBSLkLP3AFsDOtpvkgoEFULAM0xLDuSYhWVrL1eegZem8SA/640?wx_fmt=png&from=appmsg "")  
  
  
目前的CatDDoS僵尸网络支持以下攻击向量：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/6CNEHNicic4PqQQYJu4ZsaMT78STGqnSJZZKM7AEPLibm0Vx0CENYS4UUy3BicEZlITs3A5POFl0icvABIjafFrkdTA/640?wx_fmt=png&from=appmsg "")  
  
**修复建议**  
##   
  
**Tips**  
  
Apache A  
ctiveMQ官网发布的最新版本已经修复CVE-2023-46604漏洞。  
考虑到该漏洞仍在被世界各地的攻击者积极利用，建议使用Apache ActiveMQ的用户及时升级至5.15.16、5.16.7、5.17.6或5.18.3等安全版本。  
  
     
##   
  
**附录 IOC**  
  
IP  
  
185.245.83[.]56  
  
194.127.178[.]114  
  
38.6.224[.]248        
  
  
C2  
  
omgnoway[.]geek  
            
  
  
MD5  
  
8bed0b9a5fcf46fdc9d31a669a3f99be  
  
bc837807621ecef8abe394928514b154      
  
