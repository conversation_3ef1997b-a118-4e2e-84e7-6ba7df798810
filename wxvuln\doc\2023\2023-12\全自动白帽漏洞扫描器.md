#  全自动白帽漏洞扫描器   
 迪哥讲事   2023-12-12 19:19  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzxWEmicrFMxRhHeZNErZA1kJC8TZLvpNM7AsdRYaQMYmLRMQqCkBSia5Q/640?wx_fmt=png "")  
## 简介  
  
  
DarkAngel 是一款全自动白帽漏洞扫描器，从hackerone、bugcrowd资产监听到漏洞报告生成、企业微信通知。  
  
DarkAngel 下载地址：  
https://github.com/Bywalks/DarkAngel  
  
当前已支持的功能：  
- hackerone资产监听；  
  
- bugcrowd资产监听；  
  
- 自定义资产添加；  
  
- 子域名扫描；  
  
- 网站指纹识别；  
  
- 漏洞扫描；  
  
- 漏洞报告自动生成；  
  
- 企业微信通知扫描结果；  
  
- 前端显示扫描结果；  
  
## 自动生成漏洞报告  
  
自动生成漏洞报告 - MarkDown格式 - 存放地址/root/  
darkangel/vulscan/results/report  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzanD2y8oaaXEtRA05roypHWia2Ho5lNEicsiaIvRRSgJGQXAUOl8maI3qQ/640?wx_fmt=png "")  
  
支持自添加漏洞报告模板，目前已添加漏洞报告模板如下，漏洞名配置为nuclei模板文件名即可  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzbYAoEEiazjw49VMDRiciaJJPoWufR2f7gkkqibuV6Salp5vqsq8q7dP2VQ/640?wx_fmt=png "")  
  
自定义漏洞报告模板格式  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1Tz861v0h8zbOgj1aAUjkawWPTjMibPicYsCaTZCugichVFcdCcv6q91dqRw/640?wx_fmt=png "")  
## 企业微信通知  
  
可先查看如何获取配置：  
  
企业微信开发接口文档  
```
https://developer.work.weixin.qq.com/document/path/90487
```  
  
获取参数后，在/root/darkangel/vconfig/config.ini中配置参数，即可启用企业微信通知  
  
微信通知 - 漏洞结果  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1Tz0e1A6C92kjkg2rBHRQIwBb5rWiazZJwicJ9trhbL82SAAWkhLw96apuQ/640?wx_fmt=png "")  
  
微信通知 - 扫描进程  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzMsNzb4ZhtR8EYHla6uvWyoMWUoRA1iaBVI2icbM8qC4pcfxsNQq6vfzw/640?wx_fmt=png "")  
## 安装  
  
整体项目架构ES+Kibana+扫描器，所以安装需要三个部分  
  
ES镜像：  
```
```  
  
Kibana镜像：  
```
```  
  
扫描器镜像：  
```
```  
  
docker容器内挂载目录无权限运行容器时：--privileged=true  
## 用法  
```
usage:  [-h] [--scan-new-domain]
        [--add-domain-and-scan ADD_DOMAIN_AND_SCAN [ADD_DOMAIN_AND_SCAN ...]]
        [--offer-bounty {yes,no}] [--nuclei-file-scan]
        [--nuclei-file-scan-by-new-temp NUCLEI_FILE_SCAN_BY_NEW_TEMP]
        [--nuclei-file-scan-by-new-add-temp NUCLEI_FILE_SCAN_BY_NEW_ADD_TEMP]
        [--nuclei-file-scan-by-temp-name NUCLEI_FILE_SCAN_BY_TEMP_NAME]
        [--nuclei-file-polling-scan] [--delete]

DarkAngel is a white hat scanner. Every user makes the Internet more secure.

--------------------------------------------------------------------------------

optional arguments:
  -h, --help            show this help message and exit
  --scan-new-domain     scan new domain from h1 and bc
  --add-domain-and-scan ADD_DOMAIN_AND_SCAN [ADD_DOMAIN_AND_SCAN ...]
                        scan new domain from h1 and bc
  --offer-bounty {yes,no}
                        set add domain is bounty or no bounty
  --nuclei-file-scan    scan new domain from h1 and bc
  --nuclei-file-scan-by-new-temp NUCLEI_FILE_SCAN_BY_NEW_TEMP
                        use new template scan five file by nuclei
  --nuclei-file-scan-by-new-add-temp NUCLEI_FILE_SCAN_BY_NEW_ADD_TEMP
                        add new template scan five file by nuclei
  --nuclei-file-scan-by-temp-name NUCLEI_FILE_SCAN_BY_TEMP_NAME
                        use template scan five file by nuclei
  --nuclei-file-polling-scan
                        five file polling scan by nuclei
```  
### --scan-new-domain  
  
$ python3 darkangel.py --scan-new-domain  
- 监听hackerone和bugcrowd域名并进行扫描（第一次使用时会把hackerone和bugcrowd域名全部添加进去，资产过多的情况下做好准备，扫描时间很长）  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1Tzibic61EOLpj3zKicTSPIvAyQlLGk6InxyoLeUpvjvkbjRsvB4ib0xJxZ8A/640?wx_fmt=png "")  
### --add-domain-and-scan  
  
$ python3 darkangel.py --add-domain-and-scan program-file-name1 program-file-name2 --offer-bounty yes/no  
- 自定义添加扫描域名，并对这些域名进行漏洞扫描  
  
- 文件名为厂商名称，文件内存放需扫描域名  
  
- 需提供--offer-bounty参数，设置域名是否提供赏金  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzAhwtTia1BOvVRbXM2tMAZBzhqwibUQhfa0RHyDeGWKb0BnegmvZZA0pw/640?wx_fmt=png "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzTDlbibmiamgpRbfIL4ibNYicG60bltJoZESLzFSfFrR45bGiad8GibYWCaTQ/640?wx_fmt=png "")  
  
扫描结束后，会把子域名结果存在在/root/  
darkangel/vulscan/results/urls目录，按照是否提供赏金分别存放在，bounty_temp_urls_output.txt、nobounty_temp_urls_output.txt文件内  
### --nuclei-file-scan  
  
$ python3 darkangel.py --nuclei-file-scan  
- 用nuclei扫描20个url文件  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzW0vJPhkHOEJhf0PfHUicd0cTkssYtGV9SyEu1kYZFRcedjEibVw3Uscg/640?wx_fmt=png "")  
  
url列表存放位置  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzJJI1SctDibYYRFfEGGicjH3YIXJ4nT3LZu4tm4KGVOTJRnCjz5AqmOvw/640?wx_fmt=png "")  
### --nuclei-file-polling-scan  
  
$ python3 darkangel.py --nuclei-file-polling-scan  
- 轮询用nuclei扫描20个url文件，可把该进程放在后台，轮询扫描，监听是否url列表是否存在新漏洞出现  
  
### --nuclei-file-scan-by-new-temp  
  
$ python3 darkangel.py --nuclei-file-scan-by-new-temp nuclei-template-version  
- 监听nuclei-template更新，当更新时，对url列表进行扫描  
  
当前nuclei-template版本为9.3.1  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzovPHt160LtP3S6V6QzJPHEqWt10YmalACdx1WSmpN1AZ8NYzmn50Jw/640?wx_fmt=png "")  
  
执行命令，监听9.3.2版本更新  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzG1vVyRicsms7BXic5knibRbDkcMPoic04U7Ye40z4Ju9FIhJG04OgCH7TA/640?wx_fmt=png "")  
  
企业微信通知  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1Tzv2AK9dRVDND98GeKwWm3boE48uoicnTf2e7qn9dQ0Yjnib6pUTjcYzQA/640?wx_fmt=png "")  
  
url列表存放位置  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzJJI1SctDibYYRFfEGGicjH3YIXJ4nT3LZu4tm4KGVOTJRnCjz5AqmOvw/640?wx_fmt=png "")  
### --nuclei-file-scan-by-new-add-temp  
  
$ python3 darkangel.py --nuclei-file-scan-by-new-add-temp nuclei-template-id  
- 监听nuclei单template更新，当更新时，用该template对url列表进行扫描，这里是打了个时间差，某些时候先提交tempalte，验证后才会加入nuclei模板，在还未加入时，我们已经监听并进行扫描，扫描后id会自动增加，监听并进行扫描  
  
查看nuclei单template的id，这里为6296  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzeOutqvZpEdendBeMZdia55Y4bLgLdAq2jqJvEflgAJQTc0z8NRv0icoQ/640?wx_fmt=png "")  
  
执行命令，对该template进行扫描  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzicpKn0ibb12XFGNsTD4YsHmc6gGnbRCF1PiaOI8b81jGoVxGZpQaicpjBA/640?wx_fmt=png "")  
  
url列表存放位置  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzJJI1SctDibYYRFfEGGicjH3YIXJ4nT3LZu4tm4KGVOTJRnCjz5AqmOvw/640?wx_fmt=png "")  
### --nuclei-file-scan-by-temp-name  
  
$ python3 darkangel.py --nuclei-file-scan-by-temp-name nuclei-template-name  
- 用单template对url列表进行扫描  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzpicNGjp6zgpMsDCKnlNbcudLojVnib6N2QCnLz743QXuGzyPJKicPoycQ/640?wx_fmt=png "")  
## 结果显示  
  
前端 - 扫描厂商  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzvRfoBXZJsibEWtg6sDSF6jhFbKMp2aMMM0ZUicz1OiaHHe1UDubFO3l5A/640?wx_fmt=png "")  
  
前端 - 扫描域名  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzSXYqicLDJbiamLE1sju22otduU6aDxd3qLFcXmJBJJ5fGLqKBwupjOQw/640?wx_fmt=png "")  
  
前端 - 扫描结果  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzAhqtEJNP1icEEI2ibdfAnmRaJ1tdc1TqWEdjjQr1Qm8CEGCybA6eCOsw/640?wx_fmt=png "")  
  
微信通知 - 扫描进程  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1TzMsNzb4ZhtR8EYHla6uvWyoMWUoRA1iaBVI2icbM8qC4pcfxsNQq6vfzw/640?wx_fmt=png "")  
  
微信通知 - 漏洞结果  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ib3VCxzGNvRapF80qWMlbsRDuzRJED1Tz0e1A6C92kjkg2rBHRQIwBb5rWiazZJwicJ9trhbL82SAAWkhLw96apuQ/640?wx_fmt=png "")  
## 注意事项  
- 本工具仅用于合法合规用途，严禁用于违法违规用途。  
  
## Github地址  
```
https://github.com/Bywalks/DarkAngel
```  
  
如果你是一个长期主义者，欢迎加入我的知识星球([优先查看这个链接，里面可能还有优惠券](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247489122&idx=1&sn=a022eae85e06e46d769c60b2f608f2b8&chksm=e8a61c01dfd195170a090bce3e27dffdc123af1ca06d196aa1c7fe623a8957755f0cc67fe004&scene=21#wechat_redirect)  
)，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[xss研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487130&idx=1&sn=e20bb0ee083d058c74b5a806c8a581b3&chksm=e8a604f9dfd18defaeb9306b89226dd3a5b776ce4fc194a699a317b29a95efd2098f386d7adb&scene=21#wechat_redirect)  
  
  
[SSRF研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[2022年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
## 福利视频  
  
笔者自己录制的一套php视频教程(适合0基础的),感兴趣的童鞋可以看看,基础视频总共约200多集,目前已经录制完毕,后续还有更多视频出品  
  
https://space.bilibili.com/177546377/channel/seriesdetail?sid=2949374  
## 技术交流  
  
技术交流请加笔者微信:richardo1o1 (暗号:growing)  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
