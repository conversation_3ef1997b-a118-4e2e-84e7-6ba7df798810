#  JAVA代码审计-EL表达式注入漏洞(配套视频)   
原创 白给  白给信安   2024-04-20 09:26  
  
# JAVA代码审计-EL表达式注入漏洞  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/3LFWic4c794BfP9ibNtRggKic0Xtdx6eIxsBY5qyadhB7DWSLT5icLqnBW6bSz6ibBgOMg2zFicWjow0WWib33mceEDvg/640?wx_fmt=png&from=appmsg "")  
  
欢迎参加我们的Java代码审计课程！本课程旨在培养学员深入了解和实践Java应用程序安全性的技能，通过系统学习和实际案例分析，使学员能够识别并纠正潜在的安全漏洞。  
## 前期回顾  
  
之前我们已经公开了以下课程：  
1. JAVA代码审计-JAVA 开发环境搭建  
  
1. JAVA代码审计-JAVA WEB基础(一)  
  
1. JAVA代码审计-JAVA WEB基础(二)  
  
1. JAVA代码审计-JAVA WEB基础(三)  
  
1. JAVA代码审计-SQL注入漏洞代码审计(一)  
  
1. JAVA代码审计-SQL注入漏洞代码审计(二)  
  
1. JAVA代码审计-任意文件上传漏洞  
  
1. JAVA代码审计-任意文件下载漏洞  
  
1. JAVA代码审计-远程命令执行漏洞  
  
1. JAVA代码审计-XXE漏洞  
  
1. JAVA代码审计-SSRF漏洞  
  
以上课程可在公众号或 bilibili 进行查看  
## 本期内容  
### 大纲  
  
本期将继续讲解 JAVA 代码审计中 El 表达式注入漏洞相关审计，本期主要讲解内容为：**EL 表达式简介**、**EL 表达式使用**、**EL 表达式注入漏洞**、**Bean Validatio 中的表达式注入漏洞**、**CVE-2018-16621分析**、**CVE-2020-10204分析**大纲如下：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/3LFWic4c794BfP9ibNtRggKic0Xtdx6eIxsgyUaEZ8lnfLyicpZKxm8LZO7AlktDGuPq1x9ib9ZHCFoXy8cACO3Xwwg/640?wx_fmt=png&from=appmsg "")  
###   
  
