#  [在野0day预警] CVE-2024-3400 Palo Alto Networks PAN-OS 命令执行   
原创 一个不正经的黑客  一个不正经的黑客   2024-04-13 18:26  
  
## [在野0day预警] CVE-2024-3400 Palo Alto Networks PAN-OS 命令执行  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMowoBMPkIqMHyXkiazBZz7TaBzgqPKn5L5C1nLacZAQZ9z1WBCtH6FmsXHJOUSoADgNibRkezl3Zr2Q/640?wx_fmt=png&from=appmsg "")  
  
### 关于 Palo Alto Networks   
  
Palo Alto Networks 是一家全球领先的网络安全公司，总部位于美国加利福尼亚州的圣克拉拉。该公司提供综合的网络安全解决方案，旨在保护企业免受各种网络威胁和攻击。  
  
Palo Alto Networks 的核心产品是其 Next-Generation Firewall（下一代防火墙），它采用了先进的技术，包括应用程序识别、用户识别、内容过滤和威胁防御等功能，可以在网络层面提供全面的安全保护。此外，该公司还提供了其他安全产品和服务，如云安全、终端安全、威胁情报等，以满足客户在不同领域的安全需求。  
  
Palo Alto Networks 的技术创新和持续投入于研发使其成为网络安全领域的领导者之一。该公司的产品和解决方案被广泛应用于全球各种规模和类型的组织，包括企业、政府机构和服务提供商，以保护其网络免受日益复杂的网络威胁。  
### 漏洞影响版本   
  
远程主机上运行的Palo Alto Networks PAN-OS版本为10.2.x，在10.2.9-h1之前，或者11.0.x，在11.0.4-h1之前，或者11.1.x，在11.1.2-h3之前，因此受到一个漏洞的影响。  
  
Palo Alto Networks PAN-OS软件的GlobalProtect功能存在命令注入漏洞，针对特定的PAN-OS版本和不同的功能配置，可能使未经身份验证的攻击者能够在防火墙上以root权限执行任意代码。(CVE-2024-3400)  
### 官方通报   
  
https://security.paloaltonetworks.com/CVE-2024-3400  
  
满分CVSSv4.0 Base Score评级，漏洞等级为严重，务必及时修复  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMowoBMPkIqMHyXkiazBZz7TakZH11mQtOwGl5MCjqkn2UvibCb86eeWZicCCRz6hFby8CZ82sMpGo5Xw/640?wx_fmt=png&from=appmsg "")  
  
  
Palo Alto Networks PAN-OS 软件中 GlobalProtect 功能存在命令注入漏洞，针对特定 PAN-OS 版本和不同功能配置，可能使未经身份验证的攻击者能够在防火墙上以 root 权限执行任意代码。  
  
PAN-OS 10.2、PAN-OS 11.0 和 PAN-OS 11.1 的修复方案正在开发中，预计将于 2024 年 4 月 14 日前发布。Cloud NGFW、Panorama 设备和 Prisma Access 不受此漏洞影响。所有其他版本的 PAN-OS 也不受影响。  
  
受影响版本具体列表  
  
![](https://mmbiz.qpic.cn/mmbiz_png/cxf9lzscpMowoBMPkIqMHyXkiazBZz7TacvSR4HPasFb3o55L88wKib6WGNrqqzndrF6ZvvGurlhCFI8BDmVUJaw/640?wx_fmt=png&from=appmsg "")  
  
### 缓解方案   
  
建议措施：拥有威胁预防订阅的客户可以通过启用威胁 ID 95187（引入于应用程序和威胁内容版本 8833-8682）来阻止此漏洞的攻击。  
  
除了启用威胁 ID 95187 外，客户还必须确保已将漏洞保护应用到其 GlobalProtect 接口，以防止在其设备上利用此问题。有关更多信息，请参阅  
  
https://live.paloaltonetworks.com/t5/globalprotect-articles/applying-vulnerability-protection-to-globalprotect-interfaces/ta-p/340184 。  
  
如果目前无法应用基于威胁预防的缓解措施，仍然可以通过暂时禁用设备遥测来减轻此漏洞的影响，直到设备升级到修复的 PAN-OS 版本为止。升级后，应重新启用设备上的设备遥测。  
  
请参阅以下页面了解如何暂时禁用设备遥测的详细信息：  
  
https://docs.paloaltonetworks.com/pan-os/11-0/pan-os-admin/device-telemetry/device-telemetry-configure/device-telemetry-disable 。  
### 在野利用情况   
  
消息来源:  
  
Hackers Deploy Python Backdoor in Palo Alto Zero-Day Attack  
  
该安全漏洞编号为CVE-2024-3400（CVSS 评分：10.0），是一个命令注入缺陷，使未经身份验证的攻击者能够在防火墙上以 root 权限执行任意代码。  
  
值得注意的是，该漏洞仅适用于启用 GlobalProtect 网关（GlobalProtect gateway）和设备遥测（device telemetry）的 PAN-OS 10.2、PAN-OS 11.0 和 PAN-OS 11.1 防火墙配置。  
  
MidnightEclipse 操作需要利用该缺陷创建一个每分钟运行一次的 cron 作业，以获取外部服务器上托管的命令（“172.233.228[.]93/policy”或“172.233.228[.]93/patch”） ，然后使用 bash shell 执行。  
  
据称，攻击者手动管理了命令与控制（C2）服务器的访问控制列表（ACL），以确保只能从与其通信的设备进行访问。  
  
虽然该命令的确切性质尚不清楚，但怀疑该 URL 是 Volexity 防火墙上基于 Python 的后门的传送工具，Volexity 于 4 月 10 日发现了对 CVE-2024-3400 的野外利用， 2024 – 作为 UPSTYLE 进行跟踪，并托管在不同的服务器上（“144.172.79[.]92”和“nhdata.s3-us-west-2.amazonaws[.]com”）。  
  
Python 文件旨在编写和启动另一个 Python 脚本（“system.pth”），该脚本随后解码并运行嵌入式后门组件，该组件负责执行名为“sslvpn_ngx_error.log”的文件中威胁行为者的命令。操作结果写入名为“bootstrap.min.css”的单独文件。  
  
攻击链最有趣的方面是，用于提取命令和写入结果的文件都是与防火墙关联的合法文件 -  
- /var/log/pan/sslvpn_ngx_error.log  
  
- /var/appweb/sslvpndocs/global-protect/portal/css/bootstrap.min.css  
  
至于如何将命令写入 Web 服务器错误日志，威胁参与者会伪造特制的网络请求，发送到包含特定模式的不存在的网页。然后后门解析日志文件并搜索与相同正则表达式匹配的行（“img[([a-zA-Z0-9+/=]+)]”）以解码并运行其中的命令。  
  
“然后，该脚本将创建另一个线程，运行一个名为“恢复”的函数，”Unit 42 说。“恢复功能获取 bootstrap.min.css 文件的原始内容以及原始访问和修改时间，休眠 15 秒并将原始内容写回文件，并将访问和修改时间设置为其原始值”。  
### 总结   
  
作为国际上一款知名的防火墙产品，虽然根据漏洞描述来看存在一定的限制，比如要开启GlobalProtect gateway 网关，但是作为一个企业来说，这个功能往往也是开启的，建议自查全流量设备查看防火墙是否存在172.233.228.93的历史连接信息、及时升级Pan-OS版本等操作，目前漏洞利用还处于0/1Day状态，属于APT层面的攻击，小企业可能并不会被定向影响。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/2Br5HMj6hmnYcJR3oiccaz7V3tZw5Xjm3B04ojr7bHMzkMWH9ZMH8yXqmWolq9RiayQ3mStJuic1b1VWwFjH76rQw/640?wx_fmt=png "")  
  
往期推荐  
  
  
  
[逮捕！无补丁！Telegram 桌面版0Click 高危漏洞！](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503988&idx=1&sn=94e0ec0cc2cf449e5b07434cc4dd22fe&chksm=c0ce2b0df7b9a21b9088e5925c5f0deec2ee5a58ef16e33e3bc049c76850faa404c706925445&scene=21#wechat_redirect)  
  
  
[又是P0事故，腾讯云崩了，不要慌！](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503970&idx=1&sn=a6c87c87507ab0ad3fbfdac50ea3a622&chksm=c0ce2b1bf7b9a20d37d35107dfb78293b974e11ab71fa02a8096508150ca1fb431826debd584&scene=21#wechat_redirect)  
  
  
[验证码不寻常对抗系列1:   双重验证的破解之法](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503970&idx=2&sn=39da2571bd5f9ef3c2489ca9e6b15fc3&chksm=c0ce2b1bf7b9a20daf3ce683dc4130655c60c1f47a5acf95bf2a30084a69a681095cea5de90b&scene=21#wechat_redirect)  
  
  
[中介: 工资的波动性变量](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503855&idx=1&sn=42b2826344042203fd12e65415247238&chksm=c0ce3496f7b9bd80508fc41e6ef8f982d268417e03bbc3d234d4c7687a99757412f1cb276d32&scene=21#wechat_redirect)  
  
  
[软件又双叒叕更新到最新版本啦](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503837&idx=1&sn=0f70766c06a4a8ab3667117a47be9667&chksm=c0ce34a4f7b9bdb2b6ed7cc80ad439d61a4f94cd8d29e21467af0e9c717fe0a05f391cf474c8&scene=21#wechat_redirect)  
  
  
[面试: HTTP 顶级理解](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503830&idx=1&sn=e8d38eee7a00e65a012de1e1c8543d61&chksm=c0ce34aff7b9bdb95794bc302ea9045ec1494b07f64f14b342172503a55b06d15b337c3d1938&scene=21#wechat_redirect)  
  
  
[这才是真正的黑客！](https://mp.weixin.qq.com/s?__biz=MzkwODI1ODgzOA==&mid=2247503816&idx=1&sn=600be0fd0cd433adcd3b6fa44636aae0&chksm=c0ce34b1f7b9bda74312c514243f1efec477686857485720eecf3514ddd87ef1800796a198b9&scene=21#wechat_redirect)  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/Gkwlib1G5b3K9Nt5YWbRPpg7FkIfzZqQQckjUhSGibP7E0K0QlZibYwicSDiaX6HQ0eo9827ibME1NJasCXRcEpcGfOA/640?wx_fmt=png "")  
  
CAMPING  
  
点个  
蓝字关注我们  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ibP6oNQics4d2U0S7IWQ4FeO0m9WZFLHlLrBNa45CatTngnLaBfSKUZJoB0Mq0JLZicibe8pYXlmvfYDlT66R01e3w/640?wx_fmt=png "")  
  
  
