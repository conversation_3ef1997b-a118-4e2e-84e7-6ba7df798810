#  CVE-2024-21111 Oracle VirtualBox 权限提升（本地权限提升）漏洞   
 TtTeam   2024-04-24 13:30  
  
7.0.16 之前的 Oracle VirtualBox 容易受到通过符号链接跟踪的本地权限升级的影响，导致任意文件删除和任意文件移动。  
  
VirtualBox 尝试将日志文件作为 NT AUTHORITY\SYSTEM 移动到 C:\ProgramData\VirtualBox（所有用户都可以写入）中，以通过序数但最多 10 个日志来备份自己。VirtualBox 还将尝试删除第 11 个日志作为 NT AUTHORITY\SYSTEM，从而暴露出 2 个导致权限升级的错误。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/0HlywncJbB3FE91Uph4kIUicibZh74j4BEicicmGTE9WeeiaU9VzszlNwhCI9E6y6oMulgKWJcB9T8cFYRs1VicXD4jg/640?wx_fmt=png&from=appmsg "")  
  
项目地址  
  
https://github.com/mansk1es/CVE-2024-21111  
  
