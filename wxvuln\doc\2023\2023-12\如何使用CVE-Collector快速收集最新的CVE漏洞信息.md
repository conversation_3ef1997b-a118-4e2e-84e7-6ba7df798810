#  如何使用CVE-Collector快速收集最新的CVE漏洞信息   
Alpha_h4ck  FreeBuf   2023-12-30 09:01  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR38jUokdlWSNlAjmEsO1rzv3srXShFRuTKBGDwkj4gvYy34iajd6zQiaKl77Wsy9mjC0xBCRg0YgDIWg/640?wx_fmt=gif "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3ibSVSK7aRYmOiaOyBupb3NGbNDE6hyzcr2YrCMLdFFzjbxsqD4pAq8YibbkjR4CsicxicvIJaXhh87HsQ/640?wx_fmt=png&from=appmsg "")  
  
  
**关于CVE-Collector**  
  
  
  
CVE-Collector是一款功能强大且简单易用的CVE漏洞信息收集工具，该工具基于纯Python 3开发，可以帮助广大研究人员以最简单轻松的方式快速收集最新的CVE漏洞信息。  
  
  
虽然目前社区中有很多类似功能的工具可以帮助我们收集最新的CVE漏洞信息，但本工具所实现的方法相对来说更加简化了研究人员的操作过程，其中还包括针对特定网站进行资源爬取、解析HTML元素和数据检索等功能。但该工具的使用需要研究人员具备较好的Python编程基础。  
  
  
需要注意的是，该工具使用了针对https://www.cvedetails.com的查询搜索请求来收集与目标漏洞（威胁评分大于或等于6）的详细信息。  
  
  
**功能介绍**  
  
  
  
1、该工具可以创建一个简单的基于分隔符的文件，并将其用作数据库（不需要DBMS）；  
  
2、当发现了新的CVE时，该工具会自动检索目标CVE漏洞的漏洞详细信息；  
  
  
  
**如何收集CVE漏洞信息**  
  
  
  
该工具所使用的CVE漏洞信息收集方法主要分为两个阶段：  
  
  
1、在漏洞分析和威胁评估完成后收集CVE漏洞信息：该方法要在所有操作完成之后才能收集CVE信息，这样就会有大概几天的时间间隙，速度比较慢；  
  
2、在安全问题被归类为漏洞的时候收集CVE漏洞信息：这种方法会在安全问题分配了CVE ID且公开披露之后完成信息收集，但这个时候可能只能收集到漏洞的基本信息，可能也没有CVSS评分等；  
  
  
  
**工具下载**  
  
  
  
由于该工具基于Python 3开发，因此我们首先需要在本地设备上安装并配置好Python 3.x环境。接下来，广大研究人员可以直接使用下列命令将该项目源码克隆至本地：  
```
git clone https://github.com/password123456/cve-collector.git
```  
  
  
  
然后切换到项目目录中，使用pip工具和项目提供的requirements.txt文件安装该工具所需的其他依赖组件：  
```
cd cve-collector

pip3 install -r requirements.txt
```  
  
  
**工具配置**  
  
  
  
首先，我们需要搜索查询https://www.cvedetails.com/vulnerability-search.php页面，并设置工具的cvss_min_socore变量，即要检索的威胁评分最低值。  
  
  
然后添加额外的代码来接收结果，例如设置一个webhook。调用该部分代码的位置已标记为了“Send the result to webhook”。  
  
  
如果你想要让工具自动运行的话啊，你还需要在crontab或类似的任务调度工具中注册CVE-Collector代码。  
  
  
**工具使用样例**  
  
  
```
# python3 main.py



*2023-10-10 11:05:33.370262*



1.CVE-2023-44832 / CVSS: 7.5 (HIGH)

- Published: 2023-10-05 16:15:12

- Updated: 2023-10-07 03:15:47

- CWE: CWE-120 Buffer Copy without Checking Size of Input ('Classic Buffer Overflow')



D-Link DIR-823G A1V1.0.2B05 was discovered to contain a buffer overflow via the MacAddress parameter in the SetWanSettings function. Th...

>> https://www.cve.org/CVERecord?id=CVE-2023-44832



- Ref.

(1) https://www.dlink.com/en/security-bulletin/

(2) https://github.com/bugfinder0/public_bug/tree/main/dlink/dir823g/SetWanSettings_MacAddress







2.CVE-2023-44831 / CVSS: 7.5 (HIGH)

- Published: 2023-10-05 16:15:12

- Updated: 2023-10-07 03:16:56

- CWE: CWE-120 Buffer Copy without Checking Size of Input ('Classic Buffer Overflow')



D-Link DIR-823G A1V1.0.2B05 was discovered to contain a buffer overflow via the Type parameter in the SetWLanRadioSettings function. Th...

>> https://www.cve.org/CVERecord?id=CVE-2023-44831



- Ref.

(1) https://www.dlink.com/en/security-bulletin/

(2) https://github.com/bugfinder0/public_bug/tree/main/dlink/dir823g/SetWLanRadioSettings_Type
```  
  
（右滑查看更多）  
  
  
**基于分隔符的文件数据库**  
  
```
# vim feeds.db

 

1|2023-10-10 09:24:21.496744|0d239fa87be656389c035db1c3f5ec6ca3ec7448|CVE-2023-45613|2023-10-09 11:15:11|6.8|MEDIUM|CWE-295 Improper Certificate Validation

2|2023-10-10 09:24:27.073851|30ebff007cca946a16e5140adef5a9d5db11eee8|CVE-2023-45612|2023-10-09 11:15:11|8.6|HIGH|CWE-611 Improper Restriction of XML External Entity Reference

3|2023-10-10 09:24:32.650234|815b51259333ed88193fb3beb62c9176e07e4bd8|CVE-2023-45303|2023-10-06 19:15:13|8.4|HIGH|Not found CWE ids for CVE-2023-45303

4|2023-10-10 09:24:38.369632|39f98184087b8998547bba41c0ccf2f3ad61f527|CVE-2023-45248|2023-10-09 12:15:10|6.6|MEDIUM|CWE-427 Uncontrolled Search Path Element

5|2023-10-10 09:24:43.936863|60083d8626b0b1a59ef6fa16caec2b4fd1f7a6d7|CVE-2023-45247|2023-10-09 12:15:10|7.1|HIGH|CWE-862 Missing Authorization

6|2023-10-10 09:24:49.472179|82611add9de44e5807b8f8324bdfb065f6d4177a|CVE-2023-45246|2023-10-06 11:15:11|7.1|HIGH|CWE-287 Improper Authentication

7|2023-10-10 09:24:55.049191|b78014cd7ca54988265b19d51d90ef935d2362cf|CVE-2023-45244|2023-10-06 10:15:18|7.1|HIGH|CWE-862 Missing Authorization
```  
  
  
（右滑查看  
更多）  
  
  
**工具运行截图**  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3ibSVSK7aRYmOiaOyBupb3NGbA1HSD62lMopzQt3J7QPX8NX0Lzp1eQzw9x27wBQDMungrFRLmOQJoA/640?wx_fmt=png&from=appmsg "")  
  
  
  
**许可证协议**  
  
  
  
本项目的开发与发布遵循  
GPL-3.0  
开源许可证协议。  
  
  
**项目地址**  
  
  
  
**CVE-Collector：**  
  
https://github.com/password123456/cve-collector  
  
  
  
【  
FreeBuf粉丝交流群招新啦！  
  
在这里，拓宽网安边界  
  
甲方安全建设干货；  
  
乙方最新技术理念；  
  
全球最新的网络安全资讯；  
  
群内不定期开启各种抽奖活动；  
  
FreeBuf盲盒、大象公仔......  
  
扫码添加小蜜蜂微信回复“加群”，申请加入群聊  
】  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ich6ibqlfxbwaJlDyErKpzvETedBHPS9tGHfSKMCEZcuGq1U1mylY7pCEvJD9w60pWp7NzDjmM2BlQ/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/oQ6bDiaGhdyodyXHMOVT6w8DobNKYuiaE7OzFMbpar0icHmzxjMvI2ACxFql4Wbu2CfOZeadq1WicJbib6FqTyxEx6Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ61icYlLmBLDpdYEZ7nIzpGovpHjtxITB6ibiaC3R5hoibVkQsVLQfdK57w/640?wx_fmt=png "")  
  
> https://www.cvedetails.com/  
> https://www.cvedetails.com/vulnerability-search.php  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ7JfyOicficFrgrD4BHnIMtgCpBbsSUBsQ0N7pHC7YpU8BrZWWwMMghoQ/640?wx_fmt=png "")  
  
[](https://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247492186&idx=1&sn=7b90caa54b4a5e1e940295fee5b5cc50&scene=21#wechat_redirect)  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247491574&idx=1&sn=bbc0bfc9c9fb36cceb48abe51520e544&chksm=ce1ce569f96b6c7f7987a184eba004c82b37c5ba2855177ce889e8727fc63c472990b70a46fc&scene=21#wechat_redirect)  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247491557&idx=1&sn=25a978c3877189a9200d98d85f6db40a&chksm=ce1ce57af96b6c6c2e0d85fb69d61301226d2fd245d25fa61525b1180b7a4e0cf8ab2422d3b0&scene=21#wechat_redirect)  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR3icF8RMnJbsqatMibR6OicVrUDaz0fyxNtBDpPlLfibJZILzHQcwaKkb4ia57xAShIJfQ54HjOG1oPXBew/640?wx_fmt=gif "")  
  
