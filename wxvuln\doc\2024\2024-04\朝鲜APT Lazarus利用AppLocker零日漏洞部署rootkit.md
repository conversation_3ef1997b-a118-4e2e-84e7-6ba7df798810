#  朝鲜APT Lazarus利用AppLocker零日漏洞部署rootkit   
原创 紫队  紫队安全研究   2024-04-04 10:34  
  
**大家好，我是紫队安全研究。建议大家把公众号“紫队安全研究”设为星标，否则可能就无法及时看到啦！因为公众号现在只对常读和星标的公众号才能大图推送。操作方法：先点击上面的“紫队安全研究”，然后点击右上角的【...】,然后点击【设为星标】即可。**  
  
****  
新的漏洞允许 Windows 内核权限提升，表明 Lazarus APT 组织的攻击手段更加复杂。  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/sUKKZDdVP8Ssyp7AsYicCDIF5hoFJmRKrj2dkUyrKOKiaaPj9R5Pv8f8UyBHiaFAKHVS8S9pjEibRiaBQG91etiaeEeQ/640?wx_fmt=jpeg&from=appmsg "")  
  
研究人员警告称，微软在 2 月补丁星期二修复的 Windows 内核权限提升漏洞被朝鲜威胁组织 Lazarus 集团利用为零日漏洞。攻击者利用了其 FudModule rootkit 更新版本中的漏洞，该版本还增强了新功能。  
  
  
安全公司 Avast 的研究人员在一份新报告中表示：“这一原语使 Lazarus 能够在其仅包含数据的 FudModule rootkit 的更新版本中执行直接内核对象操作。” “在一个关键的改进中，rootkit 现在采用一种新的句柄表条目操作技术，试图暂停与 Microsoft Defender、CrowdStrike Falcon 和 HitmanPro 相关的 PPL（受保护进程轻量级）保护的进程。”  
  
  
AppLocker 漏洞取代自带易受攻击的驱动程序技术  
  
Lazarus 组织又名 APT38，是朝鲜政府的国营黑客团队之一，负责网络间谍和破坏活动，有时还进行网络犯罪，为政权筹集资金。该组织的活动可以追溯到很多年前，但一些研究人员认为，Lazarus 很可能是不同子组织的保护伞，这些子组织开展自己的活动并为其目标开发定制恶意软件。  
  
  
FudModule 根工具包对于 Lazarus 的工具集来说并不新鲜，2022 年其他网络安全公司也曾对其进行过分析。它是一个存在于用户空间的纯数据根工具包，通过驱动程序利用内核读/写权限来篡改 Windows 安全机制并削弱安全产品检测其他恶意组件的能力。  
  
  
在以前的版本中，攻击者通过利用他们在恶意软件系统中部署的第三方签名驱动程序中的已知漏洞来获得读/写内核权限。这种技术被称为自带易受攻击的驱动程序 (BYOVD)。要在 Windows 上安装驱动程序，您需要管理员权限，因此攻击者已经在系统上拥有提升的权限。  
  
  
但是，在 Windows 上，获得管理员访问权限和获得内核 (SYSTEM) 权限之间存在差异。这些角色在不同的完整性级别工作，并且对其施加了不同的限制，但 Microsoft 并未正式将管理员到内核视为安全边界，因为有多种方法可以从管理员帐户实现内核执行 — — 例如 BYOVD，因为不乏编写不佳的第三方驱动程序。任何管理员帐户都可以安装驱动程序，并且任何驱动程序都会加载到内核中。  
  
  
Avast 的研究人员解释道：“不过，微软并没有放弃保护管理员到内核的边界。恰恰相反。它在使这个边界更难跨越方面取得了很大进展。纵深防御保护，例如 DSE（驱动程序签名强制执行）或 HVCI（虚拟机管理程序保护的代码完整性），使攻击者越来越难以在内核中执行自定义代码，迫使大多数人诉诸纯数据攻击（他们仅通过读取和写入内核内存来实现恶意目标）。其他防御措施，例如驱动程序黑名单，正在推动攻击者转向利用鲜为人知的易受攻击的驱动程序，从而增加了攻击的复杂性。虽然这些防御措施还没有达到我们可以正式将管理员到内核称为安全边界的程度（BYOVD 攻击仍然可行，因此称其为安全边界只会误导用户产生虚假的安全感），但它们显然代表了朝着正确方向迈出的步伐。”  
  
  
Lazarus 利用的新漏洞 CVE-2024-21338位于 appid.sys 中，它是 AppLocker 背后的核心驱动程序，AppLocker 是 Windows 内置的应用程序白名单技术，这有点讽刺。微软在 CVSS 评分中给这个漏洞打了 7.8 分（满分 10 分），据 Avast 称，这可能是因为它也可以从本地服务帐户中利用，而与管理员相比，该帐户的权限更低。  
  
  
Avast 研究人员表示：“尽管该漏洞可能仅勉强满足微软的安全服务标准，但我们认为修补是正确的选择，并感谢微软最终解决了这个问题。”“修补无疑会破坏 Lazarus 的攻击行动，迫使他们要么找到新的管理员到内核零日漏洞，要么恢复使用 BYOVD 技术。”  
  
Lazarus 的改进型 rootkit 技术  
  
FudModule 根工具包利用其内核读/写访问权限来禁用安全产品用来检测可疑行为的一些重要功能：注册回调，用于检测系统注册表修改；对象回调，用于响应线程、进程和桌面句柄操作执行自定义代码；进程、线程和图像内核回调，允许端点安全产品在每次创建新进程或加载 DLL 时执行检查。  
  
  
FudModule 根工具包将删除内核中安全产品注册的所有此类回调，以削弱其恶意软件检测能力。新变种仅对其删除的回调进行了微小修改。该根工具包还会删除防病毒程序注册的文件系统微过滤器，以监控文件操作。  
  
  
该 rootkit 的一个新功能是禁用映像验证回调，该回调在将新驱动程序映像加载到内核内存时调用。一些反恶意软件程序利用此功能来检测和阻止恶意或易受攻击的驱动程序。  
  
  
虽然这似乎可以帮助攻击者使用 BYOVD 技术，但在您已经加载恶意驱动程序来部署 rootkit 后禁用这些回调就没什么意义了。除非攻击者计划在部署 rootkit 后出于某种目的加载其他恶意驱动程序，否则到那时就太晚了。众所周知，Lazarus 过去曾使用某些自定义驱动程序执行磁盘擦除攻击。此  
  
  
版本中实现的另一种新 rootkit 技术旨在直接禁用特定的安全产品，即 AhnLab V3 Endpoint Security、Windows Defender、CrowdStrike Falcon 和 HitmanPro。  
  
  
Avast 研究人员表示：“Lazarus 集团仍然是最活跃、最长期的高级持续性威胁行为者之一。尽管他们的标志性策略和技术现在已经广为人知，但他们仍然偶尔会以出乎意料的技术复杂程度让我们感到惊讶。”“FudModule rootkit 是最新的例子，代表了 Lazarus 武器库中最复杂的工具之一。”  
  
****  
**欢迎喜欢文章的朋友点赞、转发、赞赏，你的每一次鼓励，都是我继续前进的动力。**  
  
![](https://mmbiz.qpic.cn/mmbiz/sUKKZDdVP8RZenps2xiaKSRYDuicThgDkGIdllicTt2pE8c9ZlaPnY91b9niaibbI1DibuL1clJibPM66Dkz3dtNdzC6Q/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
