#  【风险通告】Apache Struts2 目录遍历漏洞（CVE-2023-50164）   
风险通告  阿里云应急响应   2023-12-11 11:14  
  
2  
0  
2  
3  
年  
1  
2  
月4  
日  
，  
Apache  
   
官  
方  
披  
露  
   
C  
V  
E  
-  
2  
0  
2  
3  
-50164  
   
Apache Struts2 目录遍历漏洞。  
  
  
0  
1  
  
漏  
洞  
描  
述  
  
  
Apache Struts 是一个开源的、用于构建企业级Java Web应用的MVC框架。2023年12月，官方披露 CVE-2023-50164 Apache Struts 目录遍历漏洞。  
  
攻击者可以通过污染相关上传参数导致目录遍历，若在具体代码环境中允许上传危险后缀文件（例如 jsp文件），则攻击者可能结合该目录遍历漏洞，可能导致上传webshell 至可解析目录，执行任意代码。  
  
  
0  
2  
  
漏  
洞  
评  
级  
  
  
CVE-2023-50164  
  
   
Apache Struts2 目录遍历漏洞 高危  
  
需要结合实际代码写法以及环境方可利用  
  
  
0  
3  
  
安  
全  
版  
本  
  
  
Apache Struts  
   
2.5.33  
  
Apache Struts  
   
6.3.0.2  
  
  
0  
4  
  
安  
全  
建  
议  
  
  
1  
、  
升  
级 Struts2   
至  
安  
全  
版  
本  
及  
其  
以  
上  
。  
  
2  
、  
阿  
里  
云  
云  
安  
全  
中  
心  
应  
用  
漏  
洞  
已于 2023.12.4   
支  
持  
该  
漏  
洞  
检  
测。  
  
  
  
0  
5  
  
相  
关  
链  
接  
  
  
https://avd.aliyun.com/detail?id=AVD-2023-50164  
  
https://cwiki.apache.org/confluence/display/WW/S2-066  
  
  
  
