#  2024-04 Oracle补丁日｜漏洞预警   
 四叶草安全   2024-04-17 16:27  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/icHYExowrVrCsQ3ZN996fgrJVmnRCYwF9pLkbia0YlpvgkqHKzibV2ia580ibpTeeMzhEMpfetK3eZCaNPFicLDDPmjg/640?wx_fmt=gif "")  
  
**01 摘要**  
  
2024年4月17日，Oracle发布了2024年4月份安全更新。  
  
本次更新共修复了441个漏洞, 其中有285个漏洞无需身份验证即可远程利用。  
  
  
**02 漏洞列表**  
  
漏洞详情如下：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/icHYExowrVrA9h5RVClvW536sJ1MbLFrf7bvBlyVUnhaJg33ibq047oPldyKXrMTXELzpw041Zq4a78gJ6qXqn6w/640?wx_fmt=png&from=appmsg "")  
  
*危害较大漏洞指的是无需身份验证且可远程利用的漏洞  
  
  
**03 漏洞详情**  
  
  
  
**CVE-2024-21015**  
  
  
受影响产品:  
  
MySQL Server 8.0.34及之前版本，8.3.0及之前版本  
  
**详情:**  
  
MySQL Server是一种关系型数据库管理系统(RDBMS)，它使用SQL语言来管理数据库。MySQL是开源并免费使用的，被广泛应用于许多网站和应用程序中，同时支持Windows、Linux和Mac等平台。MySQL Server提供了高性能、可靠性和可伸缩性，同时还支持多种存储引擎，如InnoDB和MyISAM。  
  
攻击者通过MySQL多种协议发送恶意请求，最终导致Oracle MySQL Server被拒绝服务攻击，或导致对其中数据进行未经授权的修改。  
  
  
  
  
**CVE-2024-20994**  
  
  
受影响产品:  
  
MySQL Server 8.0.36及之前版本，8.3.0及之前版本  
  
**详情:**  
  
MySQL Server是一种关系型数据库管理系统(RDBMS)，它使用SQL语言来管理数据库。MySQL是开源并免费使用的，被广泛应用于许多网站和应用程序中，同时支持Windows、Linux和Mac等平台。MySQL Server提供了高性能、可靠性和可伸缩性，同时还支持多种存储引擎，如InnoDB和MyISAM。  
  
攻击者通过MySQL 多种协议发送恶意请求，最终导致Oracle MySQL Server被拒绝服务攻击，严重情况会导致服务崩溃。  
  
  
  
  
**CVE-2024-21112**  
  
  
受影响产品:  
  
Oracle VM VirtualBox 7.0.16之前  
  
**详情:**  
  
Oracle VM VirtualBox是一种免费的虚拟机软件，由Oracle公司开发和维护。它允许用户在单个物理计算机上运行多个操作系统，并且支持Windows、Linux、macOS等平台。  
  
VirtualBox使用虚拟硬件来模拟客户机硬件，并且可以在不同的操作系统之间共享文件和网络资源。  
  
低权限攻击者利用该漏洞可以登录到 Oracle VM VirtualBox 执行的基础设施，最终接管Oracle Oracle VM VirtualBox。  
  
  
  
  
**CVE-2024-21113**  
  
  
受影响产品:  
  
Oracle VM VirtualBox 7.0.16之前  
  
**详情:**  
  
Oracle VM VirtualBox是一种免费的虚拟机软件，由Oracle公司开发和维护。它允许用户在单个物理计算机上运行多个操作系统，并且支持Windows、Linux、macOS等平台。  
  
VirtualBox使用虚拟硬件来模拟客户机硬件，并且可以在不同的操作系统之间共享文件和网络资源。  
  
低权限攻击者利用该漏洞可以登录到 Oracle VM VirtualBox 执行的基础设施，最终接管Oracle Oracle VM VirtualBox。  
  
  
  
  
******CVE-2024-21085**  
  
  
受影响产品:  
  
Oracle Java SE: 8u401及之前版本, 8u401-perf及之前版本, 11.0.22及之前版本  
  
Oracle GraalVM Enterprise Edition: 20.3.13及之前版本, 21.3.9及之前版本  
  
**详情：**  
  
Oracle Java SE (Standard Edition) 是由Oracle公司开发和发布的Java平台的标准版本。它提供了一组核心的Java类库和工具，用于开发和运行Java应用程序和applet。  
  
	  
Oracle GraalVM Enterprise Edition是一种高性能的、多语言的虚拟机，由Oracle公司开发。它支持Java、 JavaScript、 LLVM语言（如C和C++）和其他语言，可以提高程序性能和启动速度。  
  
  
未经身份验证的攻击者通过多种协议发送恶意请求，最终导致Oracle Oracle Java SE, Oracle GraalVM Enterprise Edition被拒绝服务攻击。  
  
  
  
  
**CVE-2024-21003**  
  
  
受影响产品:  
  
Oracle Java SE: 8u401及之前版本  
  
Oracle GraalVM Enterprise Edition: 20.3.13及之前版本, 21.3.9及之前版本  
  
**详情：**  
  
Oracle Java SE (Standard Edition) 是由Oracle公司开发和发布的Java平台的标准版本。它提供了一组核心的Java类库和工具，用于开发和运行Java应用程序和applet。  
  
	  
Oracle GraalVM Enterprise Edition是一种高性能的、多语言的虚拟机，由Oracle公司开发。它支持Java、 JavaScript、 LLVM语言（如C和C++）和其他语言，可以提高程序性能和启动速度。  
  
  
未经身份验证的攻击者通过多种协议发送恶意请求，最终会导致对Oracle Oracle Java SE, Oracle GraalVM Enterprise Edition中数据进行未经授权的修改。  
  
  
  
  
**CVE-2024-21006**  
  
  
受影响产品:  
  
Oracle WebLogic Server ********.0及之前版本, ********.0及之前版本  
  
**详情：**  
  
Oracle WebLogic Server是一种用于构建、部署和管理大型分布式Web应用程序、网络应用程序和数据库应用程序的中间件产品。它提供了一组强大的工具来管理应用程序的生命周期，并且支持多种编程语言和多种协议。WebLogic Server是一种商业产品，由Oracle公司开发和销售。  
  
未经身份验证的攻击者通过T3, IIOP协议发送恶意请求，最终会导致对Oracle Oracle WebLogic Server中数据进行未经授权的访问。  
  
  
  
  
**CVE-2024-21007**  
  
  
受影响产品:  
  
Oracle WebLogic Server ********.0及之前版本, ********.0及之前版本  
  
**详情：**  
  
Oracle WebLogic Server是一种用于构建、部署和管理大型分布式Web应用程序、网络应用程序和数据库应用程序的中间件产品。它提供了一组强大的工具来管理应用程序的生命周期，并且支持多种编程语言和多种协议。WebLogic Server是一种商业产品，由Oracle公司开发和销售。  
  
未经身份验证的攻击者通过T3, IIOP协议发送恶意请求，最终会导致对Oracle Oracle WebLogic Server中数据进行未经授权的访问。  
  
  
**04 修复建议**  
  
**手动安装补丁**  
  
及时安装各产品的安全补丁, 详细细节参考：  
  
https://www.oracle.com/security-alerts/cpuapr2024.html  
  
  
**05 参考资料**  
  
https://www.oracle.com/security-alerts/cpuapr2024verbose.html  
  
  
**四叶草安全******  
  
地址：西安市高新区软件新城云汇谷C2 17层  
  
电话：400-029-4789  
  
官网：www.seclover.com  
  
邮箱：  
support@se  
clover.com  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/icHYExowrVrDEPZ4ACg9Uic2ibobR1NgjibdGnu5jLanSn6fwLJFp8CkVa1gDiave3iaxYdPriafh70HhOBtichRF8eQGA/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=wxpic "")  
  
  
