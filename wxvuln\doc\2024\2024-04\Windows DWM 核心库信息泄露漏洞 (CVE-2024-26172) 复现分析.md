#  Windows DWM 核心库信息泄露漏洞 (CVE-2024-26172) 复现分析   
原创 信创实验室  山石网科安全技术研究院   2024-04-10 12:21  
  
## 漏洞描述  
  
微软四月份补丁日发布了编号为CVE-2024-26172的windows dwm核心库信息泄露漏洞，由山石网科信创安全实验室报告，目前已修复完成，漏洞详细情况详如下：  
  
<table><tbody style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__14" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">漏洞名称</span></p></td><td colspan="3" rowspan="1" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__15" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">Windows DWM 核心库信息泄露漏洞</span></p></td></tr><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__16" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">漏洞公开编号</span></p></td><td colspan="3" rowspan="1" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__17" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">CVE-2024-26172</span></p></td></tr><tr style="outline: 0px;height: 39px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__20" height="50" style="padding: 0px;outline: 0px;border-color: rgb(222, 224, 227);word-break: break-all;hyphens: auto;font-size: 10pt;"><p><span style="font-size: 17px;display: inline-block;text-indent: initial;">漏洞类型</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__21" height="50" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;outline: 0px;border-color: rgb(222, 224, 227);word-break: break-all;hyphens: auto;font-size: 10pt;"><p><span style="font-size: 17px;">信息泄露</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__22" height="50" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;outline: 0px;border-color: rgb(222, 224, 227);word-break: break-all;hyphens: auto;font-size: 10pt;"><p><span style="display: inline-block;text-indent: initial;font-size: 17px;">公开时间</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__23" height="50" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;outline: 0px;border-color: rgb(222, 224, 227);word-break: break-all;hyphens: auto;font-size: 10pt;"><p><span style="display: inline-block;text-indent: initial;font-size: 17px;">2024-04-10</span></p></td></tr><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__24" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">漏洞等级</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__25" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">重要</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__26" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">评分</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__27" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="font-size: 17px;">5.5</span></p></td></tr><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__28" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="font-size: 17px;display: inline-block;text-indent: initial;">漏洞所需权限</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__29" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">低权限</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__30" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">漏洞利用难度</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__31" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">低</span></p></td></tr><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__32" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="font-size: 17px;display: inline-block;text-indent: initial;">PoC状态</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__33" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">未知</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__34" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">EXP状态</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__35" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">未知</span></p></td></tr><tr style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__36" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="font-size: 17px;display: inline-block;text-indent: initial;">漏洞细节</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__37" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">未知</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__38" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">在野利用</span></p></td><td colspan="1" rowspan="1" width="125" data-style="border-color: rgb(222, 224, 227); font-size: 10pt; text-align: left;" class="js_darkmode__39" style="padding: 0px;font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><p style="font-family: -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;"><span style="display: inline-block;text-indent: initial;font-size: 17px;">未知</span></p></td></tr></tbody></table>  
  
![](https://mmbiz.qpic.cn/mmbiz_png/Gw8FuwXLJnQZRVCFK8yBPEcN7jhQZycUGl2tqPqibRJLdLqjkY8RbqxQaqjCJMfI19zQ9UAm4pAjibhy2NZoCZnQ/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/Gw8FuwXLJnQZRVCFK8yBPEcN7jhQZycUICs8G3Cgicx06bZdOKyLcKT8yTB7LRQNxpxK7L2NebVqUonvzXWOmvw/640?wx_fmt=png&from=appmsg "")  
  
桌面窗口管理器（DWM，以前称为桌面合成引擎或 DCE）是自 Windows Vista 以来Windows系统中的合成窗口管理器，它允许使用硬件加速来渲染 Windows的图形用户界面，该进程拥有最高的系统完整性级别和较高的权限，最近经常爆出在野利用的漏洞。DWM进程模块中存在信息泄露漏洞，攻击者可以利用该漏洞在目标进程内泄露堆地址和堆上未初始化的信息，进而进一步服务于后面的提权漏洞攻击。  
## 漏洞分析  
  
漏洞存在于  
CPropertySet::GetProperty  
函数中：  
  
```
__int64 __fastcall CPropertySet::GetProperty(__int64 this, unsigned int a2, __int64 a3)
{
  ...
  // 函数没有对属性值大小做检查
  v3 = a2;
  v4 = *(_QWORD *)(this + 88);
  v5 = *(_DWORD *)(v4 + 8 * v3);
  if ( v5 != 18 )
  {
    switch ( v5 )
    {
      case 17:
        result = 0i64;
        *(_BYTE *)a3 = *(_BYTE *)((*(_DWORD *)(v4 + 8 * v3 + 4) & 0x1FFFFFFF) + *(_QWORD *)(this + 120));
        *(_DWORD *)(a3 + 72) = 17;
        *(_BYTE *)(a3 + 76) = 1;
        return result;
      ...
    }
  }
  ...
  return 0i64;
}
```  
  
我们可以看到，当处理  
CPropertySet  
对象中的属性时，  
GetProperty  
函数并不会检查属性值是否越界，导致越界读取漏洞发生，函数的第二个参数为属性值。  
## 漏洞复现  
  
我们在分析的时候发现了一个路径可以传递任意的属性值给  
CPropertySet::GetProperty函数，同时在该函数访问完成后会将属性拷贝到第三个参数，也就是  
CExpressionValue对象中，最终返回后该值会通过  
CKeyframeAnimation对象函数拷贝到攻击者和DWM进程之间的共享内存中，可以直接读取共享内存来泄露DWM中的堆地址等信息，具体步骤如下：  
1. 创建  
CKeyframeAnimation  
对象  
  
1. 创建  
CSharedSection  
对象  
  
1. 创建  
CPropertySet  
对象  
  
1. 在这些对象上调用  
SetIntegerProperty函数设置共享内存大小，Flag等信息  
  
1. 调用  
SetReferenceProperty函数将  
CSharedSection和  
CPropertySet对象绑定到  
CKeyframeAnimation对象属性中  
  
1. 调用  
NtDCompositionCreateAndBindSharedSection  
函数建立共享内存  
  
1. 设置  
CKeyframeAnimation对象IntegerProperty ID为3的属性，该值为最终  
CPropertySet::GetProperty函数使用的第二个参数  
  
1. 调用  
NtDCompositionCommitChannel  
提交命令  
  
1. 调用Sleep函数休眠，等待DWM Render线程触发崩溃  
  
可以看到执行完poc后，进程发生了崩溃，并且是由于索引越界访问导致的：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/Gw8FuwXLJnSFHdiauicBxH7ibJeG7UicHD6Wzpj5m0s2AntumicAl8zHmuy1ib5U0P2OEQiacZgMXYIZUojqaBQfg0peQ/640?wx_fmt=png&from=appmsg "")  
## 影响版本  
  
Windows 10 Version 1809 for 32-bit Systems   
  
Windows 10 Version 1809 for ARM64-based Systems   
  
Windows 10 Version 1809 for x64-based Systems   
  
Windows 10 Version 21H2 for 32-bit Systems   
  
Windows 10 Version 21H2 for ARM64-based Systems   
  
Windows 10 Version 21H2 for x64-based Systems   
  
Windows 10 Version 22H2 for 32-bit Systems   
  
Windows 10 Version 22H2 for ARM64-based Systems   
  
Windows 10 Version 22H2 for x64-based Systems   
  
Windows 11 version 21H2 for ARM64-based Systems   
  
Windows 11 version 21H2 for x64-based Systems   
  
Windows 11 Version 22H2 for ARM64-based Systems   
  
Windows 11 Version 22H2 for x64-based Systems   
  
Windows 11 Version 23H2 for ARM64-based Systems   
  
Windows 11 Version 23H2 for x64-based Systems   
  
Windows Server 2019   
  
Windows Server 2019 (Server Core installation)  
  
Windows Server 2022   
  
Windows Server 2022 (Server Core installation)    
  
Windows Server 2022, 23H2 Edition (Server Core installation)  
## 安全建议  
  
安装相应的补丁程序，目前，官方已发布修复程序，受影响的用户可以直接升级至安全版本。  
  
  
下载地址：https://msrc.microsoft.com/update-guide/vulnerability/CVE-2024-26172  
  
## 参考信息  
  
https://msrc.microsoft.com/update-guide/vulnerability/CVE-2024-26172  
  
  
