#  【漏洞预警】WordPress LayerSlider插件SQL注入漏洞(CVE-2024-2879)   
cexlife  飓风网络安全   2024-04-03 23:05  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu027Lfnlcq6f6nn3Jkc0yzCrPqf86h0RfNe1POngHdAZiaxBt7fFhlkyYdUjRQJ7sSJnZTEIDcx5b3w/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**  
WordPress插件LayerSlider是一款可视化网页内容编辑器、图形设计软件和数字视觉效果应用程序,全球活跃安装量超过 1,000,000 次,近日监测到LayerSlider插件中存在一个SQL注入漏洞（CVE-2024-2879）,其CVSS评分为9.8,目前该漏洞的技术细节已公开。LayerSlider插件版本7.9.11 – 7.10.0中,由于对用户提供的参数转义不充分以及缺少wpdb::prepare(),可能导致通过 ls_get_popup_markup 操作受到SQL注入攻击,未经身份验证的威胁者可利用该漏洞从数据库中获取敏感信息,此外，WordPress WP-Members Membership插件中还存在一个跨站脚本漏洞（CVE-2024-1852，CVSS评分7.2）,目前该漏洞的细节及PoC已公开。WP-Members Membership插件*******及之前版本中,于输入清理和输出转义不当,未经身份验证的威胁者可利用注册请求中的X-Forwarded-For标头注入任意JavaScript,导致存储型XSS攻击,并在用户访问被注入的页面（编辑用户页面）时触发脚本执行,成功利用该漏洞可能导致创建恶意用户帐户、网站接管或将站点访问者重定向到恶意网站等。**影响范围:**CVE-2024-2879LayerSlider插件版本7.9.11-7.10.0CVE-2024-1852WP-Members Membership插件版本<= *********安全措施:**升级版本目前这些漏洞已经修复,受影响用户可升级到以下版本:LayerSlider插件版本>=7.10.1WP-Members Membership插件版本 >= *********下载链接:**https://layerslider.com/https://wordpress.org/plugins/wp-members/**参考链接:**https://www.wordfence.com/blog/2024/04/5500-bounty-awarded-for-unauthenticated-sql-injection-vulnerability-patched-in-layerslider-wordpress-plugin/https://www.wordfence.com/blog/2024/04/unauthenticated-stored-cross-site-scripting-vulnerability-patched-in-wp-members-membership-plugin-500-bounty-awarded/  
