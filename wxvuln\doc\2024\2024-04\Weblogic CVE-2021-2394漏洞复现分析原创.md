#  Weblogic CVE-2021-2394漏洞复现分析原创   
原创 huang7k  路旅安全   2024-04-01 20:28  
  
**01 影响范围**  
  
  
Oracle WebLogic Server ：  
********.0、********.0、********.0  
  
  
**02 漏洞复现**  
  
  
POC：  
```
```  
本地序列化与反序列化，dnslog收到请求  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBeOYTvHnuib5MDibemk6gsDueciaeQKERmPS2Fd1eSTjZFIf3KEb00RpDA/640?wx_fmt=png&from=appmsg "")  
  
  
**03 利用链分析**  
  
  
**利用链：**  
```
```  
  
**sink：**  
org.eclipse.persistence.internal.descriptors.MethodAttributeAccessor#getAttributeValueFromObject方法中存在反射调用，最好情况下getMethod、anObject、parameters均可控就可以造成调用任意对象的任意方法  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBic9wLficGiazx2DVmskFyfyMQ666Rgj9LUwONcRSdzp3X63EQxThPCHtg/640?wx_fmt=png&from=appmsg "")  
  
如果打了补丁的情况下，MethodAttributeAccessor已经被拉入黑名单，但是如果在其他类的反序列化过程中存在MethodAttributeAccessor的创建并调用的话则还是可以绕过的  
然后发现oracle.eclipselink.coherence.integrated.internal.cache.SerializationHelper#readAttributeAccessor中符合，会创建MethodAttributeAccessor对象并返回接着找利用链，oracle.eclipselink.coherence.integrated.internal.querying.FilterExtractor#extract中存在attributeAccessor.getAttributeValueFromObject调用  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBPboodhibKLL5f0Q23vYj5iakm7tN4RPDrvgzLRQkRtbmP4QhjI0lswNQ/640?wx_fmt=png&from=appmsg "")  
  
并且他的attributeAccessor的值正是绕过黑名单调用的方法，可以创建MethodAttributeAccessor对象  
  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBv66tgwvbsWzsPz0XuNwUOZsUMCnibA6NX8VicNPJX37mVx4vVhxT65Ew/640?wx_fmt=png&from=appmsg "")  
  
其他的都是按照调用方法直接找调用关系即可，注意子父类之间的调用关系。  
  
**source：**  
  
由于TopNAggregator$PartialResult只实现了ExternalizableLite，因此还是需要实现了Externalizable的AttributeHolder去封装PartialResult从而正常地触发反序列化  
  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBu0mdoDWE3ZMicJVMtRMmj2pAFIgf0sf3VeSC2WXnZpP8ysPaAurYWpw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/WG8t3FjTy3dDhzhriasVgT01ibzLuxmQaBGc5pHckqY4zmL1S0ObVhwwlNQv5F08bh0bGia7Jlwar8xSpbExgth2g/640?wx_fmt=png&from=appmsg "")  
  
  
  
**04 调用栈**  
  
  
```
```  
  
  
  
