#  【漏洞预警】Google Chrome Skia整数溢出漏洞CVE-2023-6345   
cexlife  飓风网络安全   2023-11-30 22:16  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00IqDebpsia8l110YkOr5vSykcaRtiagKzmcXTuRQ8l7zLfIicSSzHiab1s7eJBu1FdaBn91x1ic73r1ibA/640?wx_fmt=png&from=appmsg "")  
  
**漏洞概述:**Skia 是一个开源的2D 图形库，提供了在各种硬件和软件平台上工作的通用API，它被用作 Google Chrome 和 ChromeOS、Android、Flutter等产品的图形引擎,监测到Google发布安全公告，修复了Chrome中的一个整数溢出漏洞CVE-2023-6345,目前该漏洞已发现在野利用,Chrome版本119.0.6045.199之前在Skia中存在整数溢出漏洞，破坏渲染器进程的远程威胁者可能通过恶意文件执行沙箱逃逸，成功利用该漏洞可能导致浏览器崩溃或执行任意代码，此外，Google Chrome中还修复了以下多个高危漏洞，成功利用这些漏洞可能导致代码执行、拒绝服务等。CVE-2023-6346：WebAudio中的Use-after-free漏洞CVE-2023-6347：Mojo中的Use-after-free漏洞CVE-2023-6348：Spellcheck中的类型混淆漏洞CVE-2023-6350：libavif 中的内存访问越界漏洞CVE-2023-6351：libavif中的Use-after-free漏洞 **影响范围:**Google Chrome（Windows）版本：< 119.0.6045.199/.200Google Chrome（Mac/Linux）版本：< 119.0.6045.199  
  
**安全措施:**  
  
**升级版本**  
  
**目前这些漏洞已经修复，受影响用户可升级到以下版本：**Google Chrome（Windows）版本：>= 119.0.6045.199/.200Google Chrome（Mac/Linux）版本：>= 119.0.6045.199**下载链接：https://www.google.cn/chrome/****临时措施:手动检查更新：**可通过Chrome 菜单-【帮助】-【关于 Google Chrome】检查版本更新，并在更新完成后重新启动。此外，Microsoft 也针对这些漏洞发布了Microsoft Edge浏览器（基于Chromium）公告，Microsoft Edge用户可升级到119.0.2151.97（Stable）、118.0.2088.122（Extended Stable）或更高版本。CVE-2023-6345详情可参考:https://msrc.microsoft.com/update-guide/vulnerability/CVE-2023-6345**通用建议:**定期更新系统补丁，减少系统漏洞，提升服务器的安全性加强系统和网络的访问控制，修改防火墙策略，关闭非必要的应用端口或服务，减少将危险服务（如SSH、RDP等）暴露到公网，减少攻击面使用企业级安全产品，提升企业的网络安全性能加强系统用户和权限管理，启用多因素认证机制和最小权限原则，用户和软件权限应保持在最低限度启用强密码策略并设置为定期修改**参考链接:**https://chromereleases.googleblog.com/2023/11/stable-channel-update-for-desktop_28.html  
  
