#  通过分析JS源代码发现api漏洞   
Waleed  迪哥讲事   2023-11-08 16:00  
  
通过分析JS源代码发现api漏洞  
## 正文  
  
目标为target.com,范围为*.target.com,信息搜集之后，发现前端为react,发现了一个文件夹名字叫web-app,其中包含了前端的源代码!  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7RbiajVP3Diaia4whO5XMme0jN3s7icNbY0qQKLppjqtHfKMZicJlnWZCDkjj0zeLQw7kRo22066LXlKQ/640?wx_fmt=png "")  
  
使用了一个chrome插件来下载源代码(见文末)，然后进行代码审计  
  
首先在 JS 文件中寻找 api 接口,找到了一个名为 api 的文件夹,里面包含了大量api接口的文件  
  
将找到的所有api接口保存在一个excel表格中，并开始逐一分析/测试它们，了解它们在应用程序中的作用。  
  
经过大量的测试，发现了两个访问控制漏洞。  
  
1)在target.com上的任何视频上添加和删除按钮:  
  
target.com中有一个上传视频的功能，当我从源代码测试api接口的时候，我发现了这个接口  
```

/v1/videos/<video_id>/cta


```  
  
在源代码中，该函数如下所示：  
```


export function updateCTA(videoId: string, ctaURL: string, ctaText: string) {
    return getResults(
        appendQuery(`${TARGET_API_URL}videos/${video_id}/cta`, {
            api_key: WEB_API_KEY,
            link: ctaURL,
            text: ctaText,
        }),
        {
            method: ctaText ? 'put' : 'delete',
        }
    )
}

```  
  
除了web api密钥，这里不需要任何授权参数，因为web api密钥对所有用户都是一样的，尝试创建多个帐户，发现api密钥在所有帐户中都是一样的。  
  
在bp中发送了一个PUT请求来测试它。  
  
在 ${video_id} 中，添加一个视频 ID，该 ID 属于我上传的视频。  
  
发送请求之后如下:  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7RbiajVP3Diaia4whO5XMme0jiaFDb3hx9M7Vum6ibODyXJlskv4cO38WJmJhPW39iaeqR454hmn0raNbA/640?wx_fmt=png "")  
  
不知道这个请求在做什么，所以这里打开视频，在它下面发现了一个按钮，如果你点击它，它会将你重定向到一个网站。  
  
还可以通过发出 DELETE 请求来删除它。  
  
这意味着可以添加/删除网站上任何视频的链接，而无需任何用户交互！  
  
如果你是一个长期主义者，欢迎加入我的知识星球([优先查看这个链接，里面可能还有优惠券](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247489122&idx=1&sn=a022eae85e06e46d769c60b2f608f2b8&chksm=e8a61c01dfd195170a090bce3e27dffdc123af1ca06d196aa1c7fe623a8957755f0cc67fe004&scene=21#wechat_redirect)  
)，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
[2022年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[SSRF研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
  
[xss研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487130&idx=1&sn=e20bb0ee083d058c74b5a806c8a581b3&chksm=e8a604f9dfd18defaeb9306b89226dd3a5b776ce4fc194a699a317b29a95efd2098f386d7adb&scene=21#wechat_redirect)  
  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
## 福利视频  
  
笔者自己录制的一套php视频教程(适合0基础的),感兴趣的童鞋可以看看,基础视频总共约200多集,目前已经录制完毕,后续还有更多视频出品  
  
https://space.bilibili.com/177546377/channel/seriesdetail?sid=2949374  
## 技术交流  
  
技术交流请加笔者微信:richardo1o1 (暗号:growing)  
## 彩蛋  
  
回复extension可以获取插件链接  
  
