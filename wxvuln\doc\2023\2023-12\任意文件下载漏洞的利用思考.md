#  任意文件下载漏洞的利用思考   
 WIN哥学安全   2023-12-19 20:21  
  
**0x01 前言**  
  
  
  
任意文件下载漏洞作为最常见的WEB漏洞之一，在平常的渗透测试中经常遇到，但是很多人却并没有深入去想该如何利用这种漏洞，导致忽略了一些细节的信息。  
  
  
**0x02****传统利用**  
  
  
  
**1） 下载配置文件连数据库**  
  
****  
通过任意文件下载漏洞下载网站配置文件，利用数据库配置信息远程连接数据库。  
  
  
**php：**通过读取当前页面源码反向查找数据库配置文件  
  
**aspx:**Web.config  
  
**java:**WEB-INF/web.xml、WEB-INF/classes/applicationContext.xml 、application.yml、application.properties、conf/tomcat-users.xml  
  
**其它配置：**php.ini、my.ini、MetaBase.xml、access.log  
  
  
**2) 下载操作系统敏感文件**  
  
****  
通过下载操作系统中的文件获取敏感信息，不同操作系统中的敏感文件包括  
  
```
Windows：
  C:\Windows\win.ini
  C:\Windows\System32\drivers\etc\hosts 
  C:\ProgramData\Microsoft\Search\Data\Applications\Windows\GatherLogs\SystemIndex\SystemIndex.{%d}.gthr  其中%d替换为1-500的数字，文件中保存大量应用对应的临时文件路径，可以泄露敏感信息，有时有奇效
  C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\PowerShell\PSReadline\ConsoleHost_history.txt  其中Administrator可以替换为其它系统用户名，文件中保存Powershell历史执行命令记录
```  
```
Linux:
  /etc/passwd
  /etc/shadow
  /etc/profile
  /etc/hosts
  /etc/issue
  /etc/ssh/sshd_config
  /root/.bash_history    历史命令
  /root/.ssh/authorized_keys  ssh公钥
  /root/.ssh/id_rsa    ssh私钥
  /root/.mysql_history  
  /proc/net/arp       内网arp表信息
  /proc/net/route     内网路由表信息
  /proc/net/tcp       主机建立的tcp连接信息，类似于netstat
  /proc/[PID]/cmdline    其中pid替换为进程号，返回当进程运行时的命令
  /proc/[PID]/environ  其中pid替换为进程号，返回当进程运行时的环境变量信息
  /proc/self/loginuid  当前用户
  /proc/sched_debug  获取当前进程信息
```  
  
  
**0x03 进阶利用**  
  
  
  
**1）SpringBoot环境下的任意文件利用**  
  
****  
SpringBoot一般来说是通过jar包来启动服务,如图3.1所示，所以通过任意文件下载漏洞最有利用价值的是下载到SpringBoot对应的jar包。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJV8HCk02zotZPSvvSoR0jWOgOf95q1hnkHMjZBWngQoEoTAZiczxVk9NQ/640?wx_fmt=png&from=appmsg "")  
  
图3.1 SpringBoot启动命令  
  
  
这里可以组合linux文件下载中的多个文件，构造一条SpringBoot任意文件下载利用链，如下所示。  
  
  
**【Step1】**  
  
****  
从图3.1可以看出SpringBoot一般是通过java命令来启动的，所以可以通过java关键字来定位对应的pid进程号。  
  
  
通过任意文件读取/proc/sched_debug，获取服务器中的进程信息。通过关键字java定位SpringBoot对应的进程，获取进程号pid，如图3.2所示。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVETiakyRvmZgLRvP6ZgjNjNaRsJKOibKnJleqOWWRa7Azu7CRunibyFSvA/640?wx_fmt=png&from=appmsg "")  
  
图3.2 通过/proc/sched_debug定位pid  
  
  
**【Step2】**  
  
****  
通过/proc/[PID]/cmdline和/proc/[PID]/environ获取进程对应的信息，一般情况下通过这种方式可以拿到SpringBoot对应的jar包的绝对路径，如图3.3，图3.4所示。其中cmdline可以获取进程对应的包名，environ可以获取对应的绝对路径，组合之后可以得到jar包对应绝对路径，如图3.5所示。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVlfpNCwrGdicsUibMeOtAibOYA1TsjNia4WOfeykedJpkckl6KTbksuOu3w/640?wx_fmt=png&from=appmsg "")  
  
图3.3 通过cmdline拿到启动命令信息  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVb7zRxagvofrwibwo3tiaD5uL7Miaa1qgXk1CkhKwr60XLPVzictKGdr32A/640?wx_fmt=png&from=appmsg "")  
  
图3.4 通过environ拿到jar包对应的绝对路径  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVg5tVuszXPCB5mTwPO4jIes0Bp6SWo0pPZljmlLaemc6RgrWOtQMiagA/640?wx_fmt=png&from=appmsg "")  
  
图3.5 通过组合的路径下载jar包  
  
  
通过组合/proc/sched_debug和/proc/[PID]/environ可以满足绝大部分场景下对目标进程信息的探索。  
  
  
**2）SpringMVC环境下的任意文件利用**  
  
****  
由于java程序并不能像PHP那样通过读当前文件来一步步下载整个程序的源码做代码审计，SpringMVC环境下一般的代码处理逻辑都在controller类中，但是我们仍然可以通过任意文件下载漏洞来下载对应的源码。  
  
  
**【Step1】**  
  
****  
通过下载WEB-INF/web.xml文件，查看其中的servlet-class标签，有针对性下载标签对应的类名，如图3.6所示。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVhtiaGM1zsePjgtqwF5ylrSybJibE0vYVEOsLLaJuXqxPMyvXibr2g8wLw/640?wx_fmt=png&from=appmsg "")  
  
图3.6 web.xml中定义的Servlet类  
  
  
**【Step2】**  
  
****  
基于拿到的类的全限定类名，下载对应的class文件。如图3.6所示，拿到类的全限定类名是com.js.oa.jsflow.action.WorkFlowPdfServlet，则对应的class文件相对路径为classes/com/js/oa/jsflow/action/WorkFlowPdfServlet.class，如图3.7所示。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVvpbnOhqjibjwzbwzqgeXaicKScwzYmzcIXMdsH34EdcZjo3dibtD3PN6g/640?wx_fmt=png&from=appmsg "")  
  
图3.7 下载class文件相对于web.xml文件的相对路径  
  
  
除了web.xml中可以找到类的全限定类名，在任何一个.class文件中，均可以找到这种类似的全限定类名。通过这种方式遍历可以较完整的拿到整个SpringMVC的源码，对于其它tomcat servlet项目也是一样的思路。  
  
  
需要说明的是，并不是所有的tomcat项目的controller源码都能在WEB-INF/classes/目录下找到，还有部分系统会把逻辑代码写到jar包中并放在WEB-INF/lib/目录下，如图3.8所示，某系统在classes目录下不存在任何class文件，所有的逻辑均在WEB-INF/*.jar包中。如果代码逻辑在jar包中，就需要猜测jar包的完整名称，这种情况下通常不容易通过黑盒方式下载到源代码。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJV6blhU0eJAUpcbk72G2CPYCOBGvexA5th0iboHdzzsTlB5e1SJPv9Z1A/640?wx_fmt=png&from=appmsg "")  
  
图3.8 在classes目录下没有任何class文件  
  
  
**0x04 深度利用**  
  
  
  
在上面的方式中，还是偏向于通过任意文件下载下载网站源代码进行源码分析，但是在某些框架中，可以通过源码下载漏洞构造反序列化利用链，达到RCE的效果。  
  
  
CodeIgniter框架是一个非常流行的php框架，在中小WEB应用中具有较大的使用量。旧版本的CodeIgniter默认情况下CodeIgniter的session保存在Cookie中，并且通过反序列的方式来加载。  
  
  
我下载的CodeIgniter的源码是以前下载的，可能和最新的代码稍有出入。在system/libraries/Session.php文件中，如图4.1所示。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVjoAo9d9MmrPuKcZDWznfEvyX132iaB9p1AXnnLfdhHWx6rshkXE5ribg/640?wx_fmt=png&from=appmsg "")  
  
图4.1 CodeIgniter对session进行处理的逻辑  
  
  
其中保证Cookie不被篡改的关键是通过hash_hmac方法计算Cookie最后40位的hash值，其中$this->encryption_key是CodeIngniter框架配置文件中的值，保存在application/config/config.php文件中，这个值没有默认值，并且不能为空，如图4.2所示。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVVqyVHsHLPsicraP50DRzQIPZgCh8IcOQq8qiaLuVumcrNWIjuNq7tKxw/640?wx_fmt=png&from=appmsg "")  
  
图4.2 CodeIgniter对session进行签名校验的key  
  
  
如果存在任意文件下载漏洞，下载对应的application/config/config.php文件，则可以获取Cookie加密的key，这样就可以把任意文件下载漏洞转化为反序列化漏洞。  
  
  
在CodeIgniter框架中，如果Cookie中包含了序列化的内容，如图4.3所示，则代表可以通过任意文件读取获取加密key来构造反序列化过程，结合框架本身的反序列化利用链，则可能造成反序列化漏洞。  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/4yJaCArQwpAXukv12tMPyW0ibP0aMqDJVXl0eLN7p0XHcGrgUib5Jc4P7icZyib5C0JOwlGiaoCTUHl1YhJdfibwupcA/640?wx_fmt=png&from=appmsg "")  
  
图4.3 基于CodeIgniter框架的Cookie反序列化  
  
  
基于框架的Cookie反序列化漏洞在实际中非常普遍，其它还有很多框架也有类似的问题，Yii框架也有类似于CodeIgniter一样的逻辑，在之前一篇关于通达OA的反序列化漏洞的文章[https://mp.weixin.qq.com/s/nOQuqt_mO0glY-KALc1Xiw](https://mp.weixin.qq.com/s?__biz=MzkzNjMxNDM0Mg==&mid=2247486082&idx=1&sn=f4e73271d9c8dd97c7662c05d17101a9&scene=21#wechat_redirect)  
中，介绍过关于Yii硬编码key导致的反序列化漏洞。同样如果存在任意文件下载漏洞，则可以读取对应的key构造反序列化利用链。  
  
  
如果是aspx的目标网站，同样可以通过任意文件下载，获取Web.config中关于ViewState参数的加密密钥，ViewState参数经过解密之后会进行反序列化操作，造成.net的反序列化漏洞，详情可以参考文章  
https://paper.seebug.org/1386/。  
  
  
**0x05 总结**  
  
  
  
在渗透测试的过程中，如果遇到任意文件下载漏洞，多数场景下都可以对任意文件下载漏洞进行深入利用。本文主要结合一些特定的目标场景，总结一些关于任意文件下载漏洞的利用思路，本文仅做学习研究，请勿进行非法的网络攻击活动。  
  
  
  
  
  
  
  
  
