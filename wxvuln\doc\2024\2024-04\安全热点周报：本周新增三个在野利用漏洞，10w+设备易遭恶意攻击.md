#  安全热点周报：本周新增三个在野利用漏洞，10w+设备易遭恶意攻击   
 奇安信 CERT   2024-04-16 17:00  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr bgless="lighten" bglessp="20%" data-bglessp="40%" data-bgless="lighten" style="outline: 0px;border-bottom: 4px solid rgb(68, 117, 241);visibility: visible;"><th align="center" style="outline: 0px;word-break: break-all;hyphens: auto;border-width: 0px;border-style: none;border-color: initial;background-color: rgb(254, 254, 254);font-size: 20px;line-height: 1.2;visibility: visible;"><span style="outline: 0px;color: rgb(68, 117, 241);visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 17px;visibility: visible;">安全资讯导视 </span></strong></span></th></tr><tr data-bcless="lighten" data-bclessp="40%" style="outline: 0px;border-bottom: 1px solid rgb(180, 184, 175);visibility: visible;"><td align="center" valign="middle" style="outline: 0px;word-break: break-all;hyphens: auto;border-width: 0px;border-style: none;border-color: initial;font-size: 14px;visibility: visible;"><p style="outline: 0px;visibility: visible;">• 美国会议员公布《美国隐私权法案》，推动联邦隐私立法</p></td></tr><tr data-bglessp="40%" data-bgless="lighten" data-bcless="lighten" data-bclessp="40%" style="outline: 0px;border-bottom: 1px solid rgb(180, 184, 175);visibility: visible;"><td align="center" valign="middle" style="outline: 0px;word-break: break-all;hyphens: auto;border-width: 0px;border-style: none;border-color: initial;font-size: 14px;visibility: visible;"><p style="outline: 0px;visibility: visible;">• AT&amp;T承认超5000万用户数据泄露：已在地下论坛售卖多年</p></td></tr><tr data-bcless="lighten" data-bclessp="40%" style="outline: 0px;border-bottom: 1px solid rgb(180, 184, 175);visibility: visible;"><td align="center" valign="middle" style="outline: 0px;word-break: break-all;hyphens: auto;border-width: 0px;border-style: none;border-color: initial;font-size: 14px;visibility: visible;"><p style="outline: 0px;visibility: visible;">• 菲律宾科技部服务器遭黑客入侵：网站被篡改 25TB 数据被删除</p></td></tr></tbody></table>  
  
**PART****0****1**  
  
  
**漏洞情报**  
  
  
**1.Rust命令注入漏洞(CVE-2024-24576)安全风险通告**  
  
  
4月11日，奇安信CERT监测到Rust官方发布新版本修复Rust命令注入漏洞(CVE-2024-24576)。在Windows上使用Command API调用批处理文件（使用bat和cmd扩展名）时，Rust标准库没有正确地对参数进行转义。攻击者如果能够控制传递给生成的进程的参数，就可以通过绕过转义来执行任意的Shell命令。目前该漏洞PoC已在互联网上公开，鉴于此漏洞影响范围较大，建议客户尽快做好自查及防护。  
  
  
  
**2.Ivanti Connect Secure IPSec组件堆越界写漏洞(CVE-2024-21894)安全风险通告**  
  
  
4月9日，奇安信CERT监测到Ivanti官方发布补丁修复多个漏洞。奇安信天工实验室安全研究员协助Ivanti修复3个Ivanti Connect Secure(ICS)产品安全漏洞。其中，CVE-2024-21894 IPSEC组件堆越界写漏洞和CVE-2024-22053 IPSEC组件堆越界读漏洞两个漏洞，攻击者通过构造恶意请求，在特定条件下可触发任意代码执行；CVE-2024-22052 IPSEC组件空指针漏洞可被攻击者用于引发拒绝服务攻击。鉴于上述漏洞影响范围较大，建议客户尽快做好自查及防护。  
  
  
**PART****0****2**  
  
  
**新增在野利用**  
  
  
**1.Palo Alto Networks PAN-OS命令注入漏洞(CVE-2024-3400)**  
  
  
Palo Alto Networks PAN-OS软件的 GlobalProtect 功能中针对特定 PAN-OS 版本和不同功能配置下，未经身份验证的攻击者可能利用此漏洞在防火墙上以 root 权限执行任意代码。  
  
2024 年 4 月 10 日，Volexity 发现其一名网络安全监控 (NSM) 客户对 Palo Alto Networks PAN-OS GlobalProtect 功能中发现的漏洞进行了零日利用。Volexity 收到有关来自客户防火墙的可疑网络流量的警报。随后的调查确定该设备已被破坏。第二天，即 2024 年 4 月 11 日，Volexity 进一步观察到同一威胁行为者对其另一名 NSM 客户进行了相同的利用。  
  
Volexity 以别名 UTA0218 跟踪的威胁参与者能够远程利用防火墙设备、创建反向 shell 并将更多工具下载到设备上。攻击者专注于从设备导出配置数据，然后利用它作为在受害者组织内横向移动的入口点。  
  
在调查过程中，Volexity 观察到 UTA0218 试图在防火墙上安装自定义 Python 后门（Volexity 称之为 UPSTYLE）。UPSTYLE 后门允许攻击者通过特制的网络请求在设备上执行其他命令。  
  
成功利用设备后，UTA0218 从他们控制的远程服务器下载了额外的工具，以便于访问受害者的内部网络。他们迅速在受害者的网络中横向移动，提取敏感凭证和其他文件，以便在入侵期间和之后可能进行访问。攻击者所采用的情报技术和速度表明，威胁行为者能力很强，并且拥有清晰的剧本，知道如何访问以进一步实现其目标。防火墙设备的利用以及随后的键盘操作活动很可能是有限的和有针对性的。  
  
  
  
参考链接：  
  
https://www.volexity.com/blog/2024/04/12/zero-day-exploitation-of-unauthenticated-remote-code-execution-vulnerability-in-globalprotect-cve-2024-3400/  
  
  
  
**2.D-Link多款产品硬编码凭证漏洞(CVE-2024-3272)**  
  
  
D-Link DNS-320L、DNS-325、DNS-327L 和 DNS-340L 包含硬编码凭据，允许攻击者进行经过身份验证的命令注入，从而导致远程、未经授权的代码执行。  
  
威胁行为者正在积极扫描和利用一对安全漏洞，据说这些漏洞会影响多达 92,000 个暴露于互联网的D-Link网络连接存储（NAS）设备。  
  
这些漏洞被跟踪为 CVE-2024-3272（CVSS 评分：9.8）和 CVE-2024-3273（CVSS 评分：7.3），会影响已达到生命周期终止 （EoL） 状态的旧版 D-Link 产品。D-Link 在一份公告中表示，它不打算发布补丁，而是敦促客户更换它们。  
  
2024 年 3 月下旬，名为 netsecfish 的安全研究员表示：“该漏洞位于 nas_sharing.cgi uri 中，由于两个主要问题，该漏洞容易受到攻击：由硬编码凭据促进的后门，以及通过系统参数进行的命令注入漏洞。  
  
成功利用这些缺陷可导致在受影响的 D-Link NAS 设备上执行任意命令，从而使威胁行为者能够访问敏感信息、更改系统配置，甚至触发拒绝服务（DoS） 情况。  
  
这些问题会影响以下版本：  
  
DNS-320L、DNS-325、DNS-327L和DNS-340L  
  
D-Link 建议立即停止使用存在漏洞的 NAS，因为它们不再受支持。  
  
  
  
参考链接：  
  
https://thehackernews.com/2024/04/critical-flaws-leave-92000-d-link-nas.html  
  
  
  
**3.D-Link多款产品命令注入漏洞(CVE-2024-3273)**  
  
  
D-Link DNS-320L、DNS-325、DNS-327L 和 DNS-340L 包含命令注入漏洞。与CVE-2024-3272结合使用时，可能会导致远程、未经授权的代码执行。  
  
该攻击的固有风险来自于同时使用这两个漏洞，允许远程发送命令，同时绕过 NAS 上的任何身份验证。超过 90,000 个 NAS 容易受到此攻击。漏洞 CVE-2024-3272 依赖于在所有受影响的D-Link型号上默认使用用户帐户。“messagebus”帐户的特殊性是没有任何关联的密码，因此允许远程参与者绕过 NAS 上的身份验证。漏洞CVE-2024-3273允许在端点/cgi-bin/nas_sharing.cgi'上发送远程命令。通过结合这 2 个 CVE，可以在没有任何身份验证的情况下远程发送命令，这使得这种攻击非常危险。  
  
  
  
参考链接：  
  
https://www.stormshield.com/news/security-alert-d-link-cve-2024-3272-cve-2024-3273-stormshields-product-response/  
  
**PART****0****3**  
  
  
**安全事件**  
  
  
**1.AT&T承认超5000万用户数据泄露：已在地下论坛售卖多年**  
  
  
4月10日BleepingComputer消息，美国电信巨头AT&T向5100万名用户发出数据泄露通知，告知他们的个人信息已在一个黑客论坛上被泄露。但是，该公司尚未透露黑客如何获取了这些数据。据悉，这批数据已在黑客论坛上被反复售卖，最早在2021年，就有用户@ShinyHunters 以100万美元价格公开出售；今年3月，用户@MajorNelson 在论坛上公开了整个数据集。整个数据集有超过7100万人的信息，由于一人多帐号等原因，AT&T官方称受影响用户为约5100万人。AT&T还在通知中给出一年的身份盗窃保护服务和缓解措施。  
  
  
原文链接：  
  
https://ww  
w.bleepingcomputer.com/news/security/att-now-says-data-breach-impacted-51-million-customers/  
  
  
  
**2.英国宠物医院巨头被黑后运营受严重扰乱，猫狗和数据都面临风险**  
  
  
4月8日The Register消息，英国最大宠物医疗连锁机构CVS集团宣布遭遇了一起“网络事件”，可能有数据被盗取，部分机构的临床护理受到影响。由于此次网络事件，CVS集团被迫启动事件响应计划，下线IT系统以隔离事件影响。公司表示，临时关闭IT系统“在过去一周对运营产生重大干扰”，预计这种干扰还将持续数周。为了评估损害程度并支持采取应对措施，CVS集团已聘请外部安全专家进行调查。CVS集团表示，此次事件迫使公司加速实施云迁移策略。由于云解决方案能够增强安全性并提高运营效率，公司正在迁移机构管理系统及相关基础设施。  
  
  
原文链接：  
  
https://www.theregister.com/2024/04/08/cyber_incident_strikes_veterinary_services/  
  
  
  
**3.菲律宾科技部服务器遭黑客入侵：网站被篡改 25TB 数据被删除**  
  
  
4月3日Manila Bulletin消息，一家名为Ph1ns的黑客组织宣布对菲律宾科技部的服务器进行了毁灭性攻击。该组织声称，已经获得对虚拟机管理器、网络附加存储（NAS）和路由器等关键基础设施的访问权限，甚至获取了域管理员权限，从而能够无限制地访问员工的计算机。雪上加霜的是，他们还夸耀称加密了域控制器，有效地封锁了授权用户的访问，并在受攻击服务器上留下信息：“这个网站被菲律宾人民夺取了！”菲律宾信息和通信技术部的消息人士透露，黑客删除了25TB数据，造成一片混乱。菲律宾信息和通信技术部基础设施管理、网络安全和技能提升副秘书长Jeffrey Ian C. Dy表示：“内部报警系统在夜间10点左右检测到了对科技部的攻击，我们正在与科技部合作，尽快恢复他们的服务，并提高我们的检测和事件响应能力。”  
  
  
原文链接：  
  
https://mb.com.ph/2024/4/3/hackers-strike-department-of-science-and-technology-servers  
  
  
**PART****0****4**  
  
  
**政策法规**  
  
  
**1.国家金监总局《反保险欺诈工作办法》公开征求意见**  
  
  
4月11日，国家金融监管总局起草了《反保险欺诈工作办法（征求意见稿）》，公开征求意见。该文件共6章37条，包括总则、反欺诈监督管理、保险机构欺诈风险管理、反欺诈行业协作、反欺诈各方协同、附则。该文件提出，保险机构和行业组织应按照职责分工统筹网络安全、数据安全与创新发展，依法履行安全保护义务，完善管理制度，加强网络安全和数据安全防护，保障必要的人员和资源投入，采取网络安全、数据安全管理和技术措施，确保反欺诈信息系统安全可控运行。  
  
  
原文链接：  
  
https://www.cbirc.gov.cn/chinese/docfile/2024/93b2446612da4e008f3b348ea8a7cbea.docx  
  
  
  
**2.美国会议员公布《美国隐私权法案》，推动联邦隐私立法**  
  
  
4月7日，美国众议员Cathy Rodgers和参议员Maria Cantwell公布了《2024年美国隐私权法案》草案。该文件长达53页，内容包括数据最小化要求、消费者选择不接收定向广告的权利以及查看、更正、导出或删除其数据的权利。此外，该法案还包括数据安全条款、国家数据中介登记处。以及当存在重大隐私损害发生时，保护组织机构免于执行强制性仲裁的部分。  
  
  
原文链接：  
  
https://energycommerce.house.gov/posts/committee-chairs-rodgers-cantwell-unveil-historic-draft-comprehensive-data-privacy-legislation  
  
  
**往期精彩推荐**  
  
  
[【在野利用】Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)安全风险通告](https://mp.weixin.qq.com/s?__biz=MzU5NDgxODU1MQ==&mid=2247500842&idx=1&sn=74014f4c839835122d2fcd5c4b907597&chksm=fe79e0b2c90e69a41eda8e9264cb69bd11571200f6c94b24a7d589f1b458cb4a0d2065dc4128&token=198089224&lang=zh_CN&scene=21#wechat_redirect)  
[【已复现】Rust 命令注入漏洞(CVE-2024-24576)安全风险通告第二次更新](https://mp.weixin.qq.com/s?__biz=MzU5NDgxODU1MQ==&mid=2247500816&idx=1&sn=f52b4c881e5fb5ebb02ac720c4331f1c&chksm=fe79e088c90e699e2205cea2a513b334b7a3b5d8405b0c94a8f95324d77f6d46ae02a7e91f2b&token=198089224&lang=zh_CN&scene=21#wechat_redirect)  
  
[Rust 命令注入漏洞(CVE-2024-24576)安全风险通告](https://mp.weixin.qq.com/s?__biz=MzU5NDgxODU1MQ==&mid=2247500805&idx=1&sn=144ae8281feb31c590c9613eaa0bdbed&chksm=fe79e09dc90e698b5ee25d7a0ef967c3294aed4fca1bcc91bfa170e3010bc0c0388014aaac4f&token=198089224&lang=zh_CN&scene=21#wechat_redirect)  
  
  
  
本期周报内容由安全内参&虎符智库&奇安信CERT联合出品！  
  
  
  
  
  
  
  
  
