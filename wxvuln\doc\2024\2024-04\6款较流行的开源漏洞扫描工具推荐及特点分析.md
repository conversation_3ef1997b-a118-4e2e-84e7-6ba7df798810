#  6款较流行的开源漏洞扫描工具推荐及特点分析   
 安全牛   2024-04-09 12:40  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MM4EIk6vgGrWHn23R2KxIiadIZM72RyjzgibT7ZxEmB0f8e3EDlPibL6QbA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
未修补的漏洞是网络犯罪分子最容易攻击的目标之一。  
企业中很多的数据安全事件往往由于已知的漏洞造成的，尽管相关的安全补丁已经发布，但许多企业由于种种原因并不能及时发现并修补这些漏洞。  
  
  
当组织想要开展全面且持续的漏洞扫描工作时，通常需要得到广泛的安全社区支持。在此过程中，安全人员可以借助一些的流行开源漏洞扫描工具。由于它们具有开放源代码的特性，用户可以自由地查看、修改和定制这些工具，以满足自身的安全需求。此外，这些工具会经常更新和改进以适应不断变化的漏洞威胁。本文收集了6款目前较热门的开源漏洞扫描工具（详见下表），并从功能性、兼容性和可扩展性等方面对其应用特点进行了分析。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMTyMK7LNxKIOMsUcKFBRgwnTFVbOr5WoKZXOCOUricoSdsp2o1tDWUgA/640?wx_fmt=png&from=appmsg "")  
  
  
**1、Nmap**  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMTK9KgGkuvD8wCsDaibxO1gkOsr7InfYeplkohltMHAsz3A5IPBgyORA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
Nmap是一款非常流行的自动化安全测试工具。它可以在各种主流操作系统上运行，并快速扫描大型网络。它通常会检测以下信息：网络上有哪些主机可用，主机在运行什么服务，主机在运行哪些操作系统版本，使用哪种类型的数据包过滤器和防火墙，以及发动攻击之前需要的其他有用情报。此外，Nmap的说明文档也很全面，还有针对命令行和GUI（图形化操作界面）版本的众多教程，很容易上手。  
  
  
  
**主要特点**  
  
   
  
ㆍ快速查询开放端口，基于可用的 TCP 和 UDP 服务分析协议、应用程序和操作系统。  
  
ㆍ拥有庞大的活跃用户群，也被大多数网络和网络安全认证计划所接受。  
  
ㆍ对使用者友好，使用命令行控件自动执行漏洞扫描或将结果导出到票证系统或安全工具中。  
  
ㆍ包含一个不断增长的检测脚本库，可用于增强网络发现和漏洞评估的功能。  
  
ㆍ可基于协议请求的端口响应进行扫描，适用于所有具有开放端口的计算机、物联网设备、网站、云系统和网络设备。  
  
  
  
**不足**  
  
  
ㆍ没有正式的客户支持选项  
  
ㆍ使用时需要一定的经验或编程能力  
  
ㆍ并非所有选项在 GUI 版本中都可用  
  
   
  
**2、OpenVAS**  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMNSZXwW3359rIicicdkJ5peOiaibvSLl3mvKVLzlm9gH8MW75h0CiczfCJuQ/640?wx_fmt=jpeg&from=appmsg "")  
  
  
OpenVAS是一个较全面的开源渗透测试软件。在世界各地的渗透测试专家的帮助下，它得到了不断的支持和更新，从而使其保持最新状态。OpenVAS的其他特性还包括提供未经身份验证的测试、目标扫描和web漏洞扫描。需要说明的是，OpenVAS工具的漏洞扫描能力最初是从Nessus产品派生而来，后者现在是Tenable公司的非开源商业化产品。  
  
   
  
  
  
**主要特点**  
  
   
  
ㆍ几乎每天都会更新威胁信息源，并定期提供产品更新和功能更新。ㆍ免费版本的功能就非常全面，并在企业版本中提供更多功能和特性，同时提供客户支持。ㆍ能够对终端、服务器和云等多种系统进行常见漏洞和曝光（CVE）的扫描。ㆍ产品得到主流网络安全社区的支持，能够在许多不同的认证课程中教授。ㆍ可以为每个漏洞提供额外的上下文信息，用于漏洞修复或攻击路径解释。  
  
  
  
  
**不足**  
  
   
  
ㆍ对于初学者来说专业门槛较高ㆍ在同时进行多个扫描任务时，可能会导致程序崩溃ㆍ一些高级扫描功能需要使用付费版本  
  
  
**3、ZAP**  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMibn3adhMBSq4tZNeDiaM6xVlrgOsicdXyOHbwmqpwGO92pSbWiaiaEYWpbg/640?wx_fmt=jpeg&from=appmsg "")  
  
  
Zed Attack Proxy (ZAP)是一款用户友好的渗透测试工具，能找出网络应用中的漏洞。它不仅提供自动化扫描器，也为想要手动查找漏洞的用户提供了一套工具。ZAP通常预装在Kali Linux上，它能够将自身置于测试人员的浏览器和Web应用程序之间，拦截请求以充当"代理"。通过修改内容、转发数据包和模拟其他用户行为，ZAP也可以对应用程序进行漏洞扫描测试。  
  
   
  
  
  
**主要特点**  
  
   
  
ㆍ可执行常见的动态应用程序安全测试 （DAST），特别是针对跨站点脚本 （XSS） 漏洞，还能够执行一些新型的测试工作，例如模糊测试；  
  
ㆍ可提供 API 和 docker 集成以实现快速部署，并与 DevSecOp 工具集成，实现对开发团队的自动化工单管理；ㆍ通过Crash Override开源奖学金的支持，ZAP拥有多名全职开发人员，不再与OWASP有关联；ㆍ经常被渗透测试人员使用，可以很好地了解黑客可能发现的漏洞。  
  
   
  
  
  
**不足**  
  
  
ㆍ某些扫描功能需要额外的插件  
  
ㆍ需要一些专业知识才能使用  
  
ㆍ相比其他工具，误报率较高  
  
   
  
**4、OSV-Scanner**  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMS7TS3scP6qoynIqPfhBIdibMHBYQK5pNZT4uRyw4UN7H1zVqBIKXddQ/640?wx_fmt=jpeg&from=appmsg "")  
  
  
OSV-Scanner是一款由谷歌公司开发的开源漏洞扫描工具，提供专门的软件组成分析（SCA），可用于扫描静态软件，以确保开源软件的编程代码安全漏洞，并保护开源软件清单（SBOM）。在扫描项目时，OSV-Scanner 首先通过分析清单、软件材料清单（SBOM）和代码提交哈希值来确定正在使用的所有依赖项。这些信息用于查询 OSV 数据库，并报告与项目相关的漏洞。漏洞通过表格的形式或基于 JSON 的 OSV 格式（可选）进行报告。  
  
   
  
  
  
**主要特点**  
  
   
  
ㆍ能够定期扩展支持的编程语言列表，包括C/C++、Dart、Elixir、Go、Java、JavaScript、PHP、Python、R、Ruby和Rust。ㆍ可以从大量信息源中获取漏洞，包括Debian、Linux、Maven、npm、NuGet、OSS-Fuzz、Packagist、PyPl和RubyGems。ㆍ允许API、可脚本化和与GitHub集成的调用，以实现漏洞扫描自动化。ㆍ使用JSON存储有关受影响版本的信息，以便与开发人员工具包进行集成。ㆍ检查目录、软件清单（SBOM）、锁定文件、基于Debian的Docker镜像或在Docker容器中运行的软件。  
  
  
  
  
**不足**  
  
  
ㆍ只检查开源库中有的漏洞ㆍ产品较新，尚未被纳入到主流的认证教育中  
  
   
  
**5、CloudSploit**  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MMQnpRKfom9JTpbUjAOQVjDoB8UbRw0HudcpZgLvBByksiap3wQXwkF4A/640?wx_fmt=jpeg&from=appmsg "")  
  
  
CloudSploit是一款开源的云基础设施扫描引擎，目前被Aqua公司收购并继续对其进行维护，以使用户能够下载、修改并享受这个专业工具的好处。CloudSploit可以根据用户需求进行扫描，也可以配置为持续运行，并向安全和DevOps团队发送漏洞警报。该工具不仅检查已知的云和容器部署漏洞，还能够检查常见的配置错误问题。  
  
   
  
  
  
**主要特点**  
  
   
  
ㆍ可持续扫描AWS、Azure、Google Cloud、Oracle Cloud等环境，以便对云基础设施的更改进行警报。  
  
ㆍ通过安全人员常用的工具（如Slack、Splunk、OpsGenie、Amazon SNS等）发送实时警报和结果。  
  
ㆍ可从命令行、脚本或构建系统（Jenkins、CircleCL、AWS CodeBuild 等）调用 API。  
  
ㆍ提供了广泛的云支持，包括针对主要公共云平台（阿里云、AWS、Azure、Google Cloud 等）的插件严重程度。  
  
   
  
  
  
**不足**  
  
   
  
ㆍ某些功能需要付费使用ㆍ必须与其他安全工具一起使用ㆍ专注于公有云基础设施安全性  
  
  
**6、sqlmap**  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/kuIKKC9tNkByZDoAqrUdxxqQUxkiae9MM8jCYnujwX3xfQjPlwpZFYlsEQqOickl6fOM43HMNwylK219BdGKVQQg/640?wx_fmt=jpeg&from=appmsg "")  
  
  
sqlmap是一款专注但功能强大的免费数据库漏洞扫描工具。尽管其适用范围有限，但在一些需要进行严格合规和安全测试的数字化业务场景中，数据库漏洞测试往往是至关重要的组成部分。SQLmap能够自动化查找与SQL注入相关的威胁和攻击的过程。相比其他的web应用程序渗透测试工具，SQLmap具有较强大的测试引擎和多种注入攻击识别能力，并支持多种数据库服务器，如MySQL、Microsoft Access、IBM DB2和SQLite。  
  
   
  
  
  
**主要特点**  
  
   
  
ㆍ可通过DBMS凭据、IP地址、端口和数据库名称直接连接到数据库进行漏洞扫描测试。  
  
ㆍ支持可调用的（代码或GitHub）集成，可执行任意命令，检索标准输出并生成报告。  
  
ㆍ可扫描多种类型的SQL注入，包括：基于布尔的盲注、基于时间的盲注、基于错误的注入、基于UNION查询的注入、堆叠查询和带外注入等。  
  
ㆍ自动识别和使用密码哈希进行具有许可访问权限的测试，还可以进行密码破解。  
  
ㆍ支持超过30个数据库管理系统。  
  
   
  
  
  
**不足**  
  
   
  
ㆍ没有图形用户界面，需要通过命令行ㆍ只针对数据库中的漏洞ㆍ需要一定的数据库专业知识才能有效使用  
  
   
  
参考链接：  
  
https://www.esecurityplanet.com/applications/open-source-vulnerability-scanners/  
  
  
   
  
相关阅读  
  
[2023年最严重的10起0Day漏洞攻击事件](http://mp.weixin.qq.com/s?__biz=MjM5Njc3NjM4MA==&mid=2651127224&idx=1&sn=df326396a4a9f474a83ec80d10124a38&chksm=bd144d6b8a63c47d233825a9444ea928a363282805844fb094d43da9656838544bab29dc706a&scene=21#wechat_redirect)  
  
  
[蛰伏近三年！复盘XZ开源项目后门投毒事件完整时间线](http://mp.weixin.qq.com/s?__biz=MjM5Njc3NjM4MA==&mid=2651128919&idx=1&sn=3a2ec2ba155f01ffaac24beebd76ecf6&chksm=bd15b4848a623d926471fc4e0116b051e0f2a396c6dcc0c6660110364a8a398d81e86d2f5851&scene=21#wechat_redirect)  
  
  
[实战化攻防演练必备——6款适合蓝队的开源防御工具及特点分析](http://mp.weixin.qq.com/s?__biz=MjM5Njc3NjM4MA==&mid=2651128271&idx=1&sn=1fcdba25658ce77f4f70ae3f26613065&chksm=bd15b11c8a62380a0bb17a890935c25c064335c9ea34cd4aeb26f2715ee024d8b0c73663da05&scene=21#wechat_redirect)  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/kuIKKC9tNkAZYNibk7aDDd0hAkQGzOfLPfjXUPaypbuDrr5exabqWXmSOeZVUZtP6zqw9YGWib9xNQdvx1iaCicTUA/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=webp "")  
  
  
  
