#  【漏洞复现】Confluence 远程代码执行漏洞(CVE-2023-22527)   
原创 xia0chen  安全攻防屋   2024-01-23 23:10  
  
**免责声明：文章来源互联网收集整理，请勿利用文章内的相关技术从事非法测试，由于传播、利用此文所提供的信息或者工具而造成的任何直接或者间接的后果及损失，均由使用者本人负责，所产生的一切不良后果与文章作者无关。该文章仅供学习用途使用。**  
  
**0X00 漏洞描述**  
  
Atlassian Confluence 远程代码执行漏洞(CVE-2023-22527)，该漏洞是由模板注入引起的，未经身份验证的远程攻击者可利用该漏洞构造恶意请求导致远程代码执行  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/b6UzoibqnYFGx4QhuGb3A1DJxEgg4bUG0eVHzN1Kw5wKog1djX9g0UxQmuw46L5eRRn4CCuoNicH4OPygCTbtNJg/640?wx_fmt=png&from=appmsg "")  
  
**0X01 漏洞影响**  
  
  
系统版本（8.0至8.5.3）上均存在该漏洞  
  
**0X02 漏洞利用**  
```
```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/b6UzoibqnYFGx4QhuGb3A1DJxEgg4bUG0pwTwpwtJHYnETmzdeS5beIwHhm0zA7ZYNTHPThlibsCIrjSWnt9VZfw/640?wx_fmt=png&from=appmsg "")  
  
0X03 POC及Nuclei检测  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/b6UzoibqnYFGx4QhuGb3A1DJxEgg4bUG0VcsAsmkAguDykPcswdw6S6pU98rKuia7A8Anv4Evib2PltS0le8Ye7Tw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/b6UzoibqnYFGx4QhuGb3A1DJxEgg4bUG0O8p78WDdGFiab8Euh7RkwD6rJvK9N8akNU4JgNHlye9KicTDJfzd11ibw/640?wx_fmt=png&from=appmsg "")  
**注:关注公众号安全攻防屋回复 "CVE-2023-22527" 获取漏洞EXP**  
  
  
