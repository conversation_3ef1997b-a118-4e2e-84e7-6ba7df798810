#  【漏洞预警】MySQL2远程代码执行漏洞（CVE-2024-21508）   
cexlife  飓风网络安全   2024-04-15 20:11  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg9rCb5CEiakdmPAd6uMdo671UvpSNeW9ujC5u5rPtGJPC13eiadz0U08kw/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**mysql2是适用于Node.js的MySQL客户端库,该库的每周下载量超过200万次,近日监测到mysql2存在一个远程代码执行漏洞（CVE-2024-21508）,其CVSS评分为9.8,目前该漏洞的细节及PoC已公开,mysql2版本3.9.4之前,由于readCodeFor函数中未正确验证supportBigNumbers和bigNumberStrings值,威胁者可通过构造恶意对象并将其作为参数传递给 connection.query函数来执行恶意JavaScript 代码,从而导致远程代码执行。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg95rSsNORpph9zHrzDsVic55ltdq9t0M4yrxHxG1HaX335oZExgklHK5Q/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg9YhJPe6W1xcyprGVib3FmujWSvKSUMcwftFn826U3VEVFb3aSQsQOxaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg93DtlYuqSzJbVOTLticI791nrl3J1lqYL2h4LXB6ENruS0kbwicq0EbHQ/640?wx_fmt=png&from=appmsg "")  
  
**poc:**  
  
 {sql:`SELECT INDEX_LENGTH FROM information_schema.tables LIMIT 1`, supportBigNumbers:"console.log(1337)"}****  
```
```  
  
mysql2 (npm) < 3.9.4**安全措施:**升级版本目前该漏洞已经修复,受影响用户可更新到mysql2 3.9.4或更高版本**下载链接:**https://github.com/sidorares/node-mysql2/releases**临时措施:**  
  
暂无  
  
**参考链接:**https://github.com/sidorares/node-mysql2/pull/2572  
  
