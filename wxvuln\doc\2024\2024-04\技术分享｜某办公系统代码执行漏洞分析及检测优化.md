#  技术分享｜某办公系统代码执行漏洞分析及检测优化   
Town  GobySec   2024-04-08 17:37  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWX8AQHicUdJZjezQhHxqaz3aWopwFBC7lmCqzRefNIPwprQYTfH0eX8g/640?wx_fmt=png&from=appmsg "")  
  
G  
o  
b  
y  
社  
区  
第  
   
37  
   
篇  
技  
术  
分  
享  
文  
章  
  
全  
文  
共  
：  
5012  
   
字  
   
   
   
预  
计  
阅  
读  
时  
间  
：  
13  
   
分  
钟  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**01 概述**  
  
最近国内某知名 OA 厂商出了一个远程代码执行漏洞，漏洞的产生原因是系统在处理上传的 Phar 文件的时候存在缺陷，导致攻击者能够上传经过伪装之后的 Phar 文件到服务器，从而触发远程代码执行。此类漏洞如果要达到执行自定义命令的效果，一般是借助 PHP 环境生成特定的 Phar 文件来实现，但是这就意味着漏洞利用这一过程无法脱离 PHP 环境，可移植性非常差，所以跨语言实现 Phar 文件的构建成为了一个需要完成的必选项。  
  
本文主要以**泛微E-Office /eoffice10/server/public/api/attachment/atuh-file 代码执行漏洞**为例讲述我们是如何在 Go 中实现 Phar 文件的生成、验证功能，以及是如何利用 FOFA 强大指纹规则体系，实现远超同类检测工具30%的漏洞检出率这一效果的。  
  
最终我们成功地在 Goby 中使用 Go 语言实现了漏洞的检测与利用，并加入了命令执行回显、一键反弹 Shell 等便捷的利用方式，先看看我们的最终效果吧（视频展示为靶场环境）：  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**02 漏洞成因**  
  
Phar 是 PHP 的一种归档文件格式，它可以将一组 PHP 脚本文件、资源文件以及元数据打包成一个单独的文件。Phar 文件可以在运行时被加载和执行，就像一个独立的应用程序。Phar 文件通常用于分发 PHP 应用程序或将多个 PHP 脚本文件组织成一个易于分发和部署的单个文件。  
  
当应用程序加载 Phar 文件的时候，其中的序列化数据将被反序列化为 PHP 对象。在这个过程中，PHP 解释器会尝试将序列化的字符串转换回原始对象。在这个过程通常通过调用   
unserialize()  
 函数来完成。当我们看到   
unserialize()  
 函数，基本上就是条件反射的想到了反序列化问题了，如果我们能够找到一个 POP 链，然后再让应用程序加载一个经过恶意修改之后的 Phar 文件的时候，我们就能利用   
unserialize()  
 函数达到命令任意命令的效果了。  
  
本例在网上已经有不少的公开文章给出了分析和验证方式了，这里我们就不花费大篇幅来进行讲述了。  
  
### 2.1 漏洞成因  
  
1. 该 O  
A 系统可以在上传认证文件的接口未授权上传以   
inc  
   
、   
evf  
   
、   
license  
   
结尾的文件，且以   
inc  
   
结尾的文件名必须为   
register.inc  
   
，  
  
2. 在认证文件读取的时候是靠传入的   
fil  
e  
   
参数获取数据库中的对应信息，并将对应的文件路径交由   
file_get_contents  
   
进行文件内容的读取，这里能发现，如果   
file_get_contents  
   
读取的文件路径是以   
phar://xx   
的形式就可以触发反序列化了，  
  
3.恰巧第二点所需的文件路径我们  
是可以在   
/eoffice10/server/public/api/attachment/path/migrate  
   
这个路由下控制   
descPa  
th   
参数来提供的。  
  
至此我们可以通过上传恶意的 Phar 文件，然后修改文件路径为   
phar://xx   
的形式，最后在认证文件读取的时候通过   
file_get_co  
ntents   
触发恶意 Phar 文件的反序列化。  
###   
### 2.2 验证方式  
  
1. 通过 phpggc 中的链(经过测试 Laravel/RCE4 和 RCE9 可用)生成 Phar 文件  
```
   ./phpggc -p phar Laravel/RCE4 system whoami > RCE4.phar
```  
  
2. 通过注册授权的位置上传 Phar 的序列化数据  
  
3. 通过 phar:// 目录穿越到上传的 Phar 文件  
  
4. 通过导入上传的 Phar 文件造成反序列化代码执行  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWiahPCrn7DibkfleMVLrEymf3fmHBib1JbrOLaXyhBGyPKnT6RpbRLSWFQ/640?wx_fmt=png&from=appmsg "")  
  
这也算得上是比较经典的 Phar 反序列化漏洞了。  
  
但是这样操作的话也是存在一些问题的：  
  
1. 要验证该漏洞的话首先的得有 PHP 环境  
  
2. 每次想要测试不同的命令的时候都需要重新生成 Phar 文件  
  
3. 对文件进行修改的时候需要重新进行签名  
  
这些问题就使得验证方案可移植性差、操作麻烦  
  
面对这些问题最好的解决办法其实就是通过跨语言实现 Phar 文件的生成，虽然说现在跨语言移植也一直都是一个大的问题，但是有困难还是需要想办法去解决的。当然了现在由结果看来现在这个问题是解决了的。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**03 Phar But Golang**  
  
由于 Go 语言目前没有现成的 Phar 文件生成机制，因此我们需要使用 Go 语言根据 Phar 文件的文件结构来生成 Phar 文件以作为最终的通用解决方案。  
  
Phar 文件结构大致可以分为 Stub、manifest、Contents、Signature 四个部分。  
### 3.1 Stub  
  
Stub其实也是一个简单的PHP文件，它的格式形如  
xxx<?php xxx __HALT_COMPILER();?>  
   
，在 Stub 中前面的内容是不限制的，但是在 Stub 中必须有   
__  
HALT_COMPILER()  
   
，没有这个，PHP 就无法识别出它是 Phar 文件。  
### 3.2 Manifest  
  
Manifest 是 Phar 文件反序列化漏洞的关键部分，其可以分为以下九个部分  
  
1. 1～4字节：确定了 manifest 的长度  
  
2. 5～8字节：这个 Phar 文件中包含了几个文件  
  
3. 9～10字节：API 的版本  
  
4. 11～14：全局 Phar 位图标志（与是否包含签名和文件压缩格式相关）  
  
5. 15～18：别名的长度  
  
6. 别名：如果别名的长度为0，那就是没有别名，否则这个的长度等于前面四个字节所限定的长度  
  
7. 别名之后的四个字节：这四个字节限定了后面的元数据的长度  
  
8. 元数据：这里面的数据就是用户自己添加的类，然后会以序列化的方式进行存储。这里也是这次漏洞触发的关键，当 PHP 处理 Phar 文件的时候就会自动将这部分的数据进行反序列化操作，这就会导致触发 Phar 反序列化漏洞。  
  
9. 再往后走就是所包含文件每个文件的条目了  
  
因为内容比较多，所以我们就根据的 Phar 文件示例挑重点来看看  
  
首先是第一部分 manifest 的长度：  
  
   
5A 00 00 00  
   
(90)是 manifest 的1～4字节，也就是表示的 manifest 的长度为90个字节。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWmkCh3pBpVgkZk8YQRiaFcWR8JicNJGu5C9XUztUVNKprTB7zPVVqnSYw/640?wx_fmt=png&from=appmsg "")  
  
接下来的是第七部分元数据的长度：  
  
   
24 00 00 00  
   
(36)是 manifest 的19～22字节，表示的是后面元数据的长度，直接从19字节开始是因为前面的   
0  
0 00 00 00  
   
是别名长度，因为别名长度为0，所以没有别名。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWS0gVK3ica1move1ic22icssB3m22EGNVyyDm09qGdmdl9fVXI23Ciaht2A/640?wx_fmt=png&from=appmsg "")  
  
然后接下来的就是此次漏洞最关键的第八部分元数据：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWEU5DCt6lhn8PBDBMAic4Ob1gtI7z0nKouWCwicqJXvaVfXHQjRRAIlqA/640?wx_fmt=png&from=appmsg "")  
  
  
根据图上的信  
息也能看到所选区域就是元数据部分，箭头指的地方就是元数据的长度，这个是和前面第七部分元数据长度对应上的。元数据的内容是 PHP 用   
serialize()  
   
进行序列化得到的结果，虽说这里是经过序列化  
的内容，但是还是能比较明显的看出这段数据的整体结构的，基本上可以看作是   
数据类型:数据集合中的个数/数据长度:数据内容  
   
，然后根据这样的原理可以对序列化数据进行修改。  
  
然后， **Contents**里面就是存放的一些文件数据了，这个在此漏洞中没有什么体现，暂且略过。  
### 3.3 Signature  
  
 **Signature**是一个可选项，使用情况和 manifest 中的全局 Phar 位图标志有所关联。允许使用的签名方式有 MD5, SHA1, SHA256, SHA512和 OPENSSL。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWqQ1gjegxgBHrguWj7F6XeWIrWn0ic6YBoKvqELdjNp0Vuv8INkGzaxw/640?wx_fmt=png&from=appmsg "")  
  
在 Signature 这个部分也是分为三个部分组成，一个变长(根据签名类型变化)的签名内容，签名类型(签证尾部的   
01  
代表md5加密，   
02  
   
代表sha1加密，   
03  
   
代表sha256加密，   
04  
   
代表sha512加密)，还有最终的标识。  
  
以上就是我们自定义生成 Phar 文件所需要的关键的结构内容了，其实当 Phar 文件的结构被解构出来之后任意的一门程序语言想要生成一个自定义的 Phar 文件就已经不算是什么难事了。  
### 3.4 Phar Golang  
  
根据上述的结构就可以写一个脚本来生成 Phar 文件  
  
整理一下我们需要完成的步骤：  
  
1. 自定义的文件头  
  
2. 生成反序列化命令执行的序列化数据  
  
3. 可控化执行的命令的位置的长度和数据  
  
4. 输入想要执行的命令，并得出命令长度  
  
5. 得出元数据和 manifest 的长度  
  
6. 生成其他的数据  
  
7. 选择签名方式，生成签名  
  
8. 按照顺序组合起来  
  
然后按照上述步骤编写了一个测试函数，这个函数需要传入三个参数：一个自定义的 Stub，一个元数据的序列化流，一个签名的模式。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRW2iaZ63ecqH1mEVETelC4XwIKCMGPtY4qibbHQMJrIlMGAmibpQD0icXm6Q/640?wx_fmt=png&from=appmsg "")  
  
以下是测试函数输出的一些解析信息  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRW0CzvnzflibHmOvs1jZ1iaxuwWaPIF9v6K5DyHjWwdft3OCupexeW8jrg/640?wx_fmt=png&from=appmsg "")  
  
最后是生成的文件  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWmDooCHHUl8ghPCxuyib0Zx1GelYuvffwvd85pho4LYibR18429dxNZZg/640?wx_fmt=png&from=appmsg "")  
  
综上，我们成功完成了 Phar 文件的自定义生成。  
  
现在我们的自定义 Phar 文件是生成成功了，但是咱们的最终目的还是需要集成到 POC 中能够进行使用。编写完 POC，并使用这个函数生成的 Phar 文件进行测试，测试结果输出了我们想要执行的命令也就证明我们生成的 Phar 文件没有问题。  
  
测试结果证明我们是成功的。  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**04 插曲**  
  
后续在看网上的一些文章的时候发现了一个有意思的现象，这里又涉及到我们公司的另一款产品：FOFA。  
  
在  
 FOFA  
 中自然是有着  
   
泛微E-Office10  
   
的相关指纹语法的，我们可以通过   
FOFA:app="泛微-EOffice10"  
   
来对 eoffice10 的资产进行准确检索。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWcwpRzxXrXjib2jMuJqbwueuXDUfZqFG98zq9vkPOeyFDrCvpOHCqapw/640?wx_fmt=png&from=appmsg "")  
  
然后在网上发现有的师傅给的查询条件还加上了   
body="eoffice10"  
   
，诶？难道有的资产的 body 中不存在 eoffice10 吗？  
  
经过测试发现有一部分已经打上了指纹的资产，确实不存在该接口：   
/eoffice10/server/public/api/attachment/atuh-file  
   
，难道这个资产并不是   
泛微-EOffice10   
吗？FOFA 的指纹出问题了？  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWo6WhCUia8P42gKXzfpqib5MPuV1vTQXlsPicj8a9VuuDDSGic676ubm6Sw/640?wx_fmt=png&from=appmsg "")  
  
于是我们抱着给 FOFA 找 Bug 的想法，开始检查这部分异常指纹的资产，才发现这类资产对前置的路由地址进行了修改，把   
eoffice10  
   
修改成了   
eoffice  
   
，但其他指纹细节与   
eoffice10  
   
一致，所以响应的接口地址也需要做同样的调整，这恰恰说明，FOFA 是正确的！！!  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWEHdoh2ia1pArw2bTmfcAgSJxRnQ0LWwdFWl4kYsKjQ9lU2w5WC3JOYg/640?wx_fmt=png&from=appmsg "")  
  
  
这就意味着一个问题，如果企业在对这类资产进行安全自查时，所使用的自查工具，如果不具备动态调整路径的功能，则有可能得到完全错误的结果，甚至存在漏洞也无法发现。而经过我们全网分析，变更路径的资产占比颇重，在全球范围内一共有着9659条   
泛微-EOffice10  
   
资产，其中对路由进行了调整的资产数量达到：3587条，足足占了总量的37%。  
  
我们可以使用   
FOFA:app="泛微-EOffice10" && body!="/eoffice10/"  
   
很轻易的找到这部分资产。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWNULb3iaNUibUCyXkVulPBL2VpvLKrhJPs8WAVjb0NSgEZWJm4sccXwNA/640?wx_fmt=png&from=appmsg "")  
  
然后使用我们 Goby 的检测结果：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKkGvhZwWPoWcEuSxkUgeRWtFgNXw7IrRLQQnQoUib9htCPOIYQyGVGf4NfGrV4KvHj7ib7YUlhsuDg/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**05 总结**  
  
在本文中主要是以**泛微E-Office /eoffice10/server/public/api/attachment/auh-file 代码执行漏洞**这个漏洞为契机，为解决通常需要 PHP 环境才能生成 Phar 文件的情况，在 Goby 中通过跨语言的方式实现了输入指定数据就能生成恶意的 Phar 文件，以完成 Phar 反序列化漏洞的测试和利用。以此为例在 Goby 上实现了漏洞攻击效果，并且实现了远超同类检测工具30%的漏洞检出率，还加入了命令执行回显、一键反弹 Shell 的利用方式。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWElvrR0XQbqBDCHC68DicU6TwYLR54jEJE3rqy2icwicrV85dICfKrJsOQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
**06 参考**  
  
https://www.php.net/manual/en/phar.fileformat.php  
https://peiqif4ck.github.io/penetrationtest/2024/03/articles/b8f9d080e95e9930/  
https://blog.csdn.net/qq_53003652/article/details/133656975  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWfiaAtUngV8rgLh0bIibv9SumD1Y9ZmphGxK9lKiakkOWDp2gRsLjZInPg/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjKzq4TFicia2yUjianoH80KtrWfiaAtUngV8rgLh0bIibv9SumD1Y9ZmphGxK9lKiakkOWDp2gRsLjZInPg/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
**最新****Goby 使用技巧分享**  
**：**  
  
• M1sery | [Adobe ColdFusion 序列化漏洞（CVE-2023-29300）](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247529590&idx=1&sn=1c31354c67dd41b2a44e0b3205c01ab5&chksm=eb849fd6dcf316c01d09f9ba703dafa48132b0205be239fb78c38a9a76ca801f2a5b2a3738a6&scene=21#wechat_redirect)  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247529590&idx=1&sn=1c31354c67dd41b2a44e0b3205c01ab5&chksm=eb849fd6dcf316c01d09f9ba703dafa48132b0205be239fb78c38a9a76ca801f2a5b2a3738a6&scene=21#wechat_redirect)  
  
• M1sery | [Adobe ColdFusion WDDX 序列化漏洞利用](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247529637&idx=1&sn=95d4f341231d495b34dfdb67d9a2bd6d&chksm=eb849f05dcf31613fa264f7e40a36b69e35099672f0cdaf0308236e3823e29d4f55733eb083c&scene=21#wechat_redirect)  
  
  
• TonyD0g | [跨越语言的艺术：Flask Session 伪造](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247534582&idx=1&sn=730e5f7f47faf0d441a8bd50a4a63242&chksm=eb84ac56dcf325401063008fcef6a43f6212ac53ad8fd1a3ea6984eb81481a7064fef5c1644b&scene=21#wechat_redirect)  
  
  
• kv2 |[ 针对蜜罐反制Goby背后的故事](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247534992&idx=1&sn=dddda69700c9f3931f02a941132cf7bb&chksm=eb84a230dcf32b26a29bf6fef77936f24060aeaaaa50367fff64a53ce9ecc1ae23635ad769cd&scene=21#wechat_redirect)  
  
  
• Gryffinbit | [某下一代防火墙远程命令执行漏洞分析及防护绕过](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247535253&idx=1&sn=a63da635da47e36d7a285a69fff6f365&chksm=eb84a135dcf32823c3baa9ffe8764369b8ed1dbda68729cb451512fb86f09740320c78689cd8&scene=21#wechat_redirect)  
  
  
[• 路人乙 | U8 Cloud ServiceDispatcher反序列化漏洞及补丁分析](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247535559&idx=1&sn=fe740dada545636afdef120589048d4b&chksm=eb84a067dcf32971d9188072cf349eeb104785c6e787455338ffc6b3bd22637fe88afe0ba2c7&scene=21#wechat_redirect)  
  
  
[• 路人丁 |](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247535559&idx=1&sn=fe740dada545636afdef120589048d4b&chksm=eb84a067dcf32971d9188072cf349eeb104785c6e787455338ffc6b3bd22637fe88afe0ba2c7&scene=21#wechat_redirect)  
  
 [漏洞调试的捷径：精简代码加速分析与利用](http://mp.weixin.qq.com/s?__biz=MzI4MzcwNTAzOQ==&mid=2247536004&idx=1&sn=596409bf8b0963e06b45070ac97bc686&chksm=eb84a624dcf32f32bba6823b7400f88f593cf7c9a6aaca1c455ece8af984cbefa7069a74f149&scene=21#wechat_redirect)  
  
  
  
  
更  
多  
   
>  
>  
   
   
技  
术  
分  
享  
  
  
  
G  
o  
b  
y  
   
欢  
迎  
表  
哥  
/  
表  
姐  
们  
加  
入  
我  
们  
的  
社  
区  
大  
家  
庭  
，  
一  
起  
交  
流  
技  
术  
、  
生  
活  
趣  
事  
、  
奇  
闻  
八  
卦  
，  
结  
交  
无  
数  
白  
帽  
好  
友  
。  
  
也  
欢  
迎  
投  
稿  
到  
   
G  
o  
b  
y  
（  
G  
o  
b  
y  
   
介  
绍  
/  
扫  
描  
/  
口  
令  
爆  
破  
/  
漏  
洞  
利  
用  
/  
插  
件  
开  
发  
/  
   
P  
o  
C  
   
编  
写  
/  
   
I  
P  
   
库  
使  
用  
场  
景  
/  
   
W  
e  
b  
s  
h  
e  
l  
l  
   
/  
漏  
洞  
分  
析  
   
等  
文  
章  
均  
可  
）  
，  
审  
核  
通  
过  
后  
可  
奖  
励  
   
G  
o  
b  
y  
   
红  
队  
版  
，  
快  
来  
加  
入  
微  
信  
群  
体  
验  
吧  
~  
~  
~  
- 微  
信  
群  
：  
公  
众  
号  
发  
暗  
号  
“  
加  
群  
”  
，  
  
加  
入  
我  
们  
的  
社  
区  
大  
家  
庭  
  
- 获  
取  
版  
本  
：  
h  
t  
t  
p  
s  
:  
/  
/  
g  
o  
b  
y  
s  
e  
c  
.  
n  
e  
t  
/  
s  
a  
l  
e  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GGOWG0fficjLVsnYdd0PiaCzAr1V8cElzgJlhod7oXgjMibCeSME8sw0ia2zSeZ7UugvjmyUr4wh9V9DJWoBZTjRYg/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
