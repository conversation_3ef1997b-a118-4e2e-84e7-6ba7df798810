#  【复现】JumpServer 后台模板注入漏洞（CVE-2024-29202）的风险通告   
原创 赛博昆仑CERT  赛博昆仑CERT   2024-04-01 10:56  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/iaZ7t7b9Dodvib7ddpGMC6vx4COAy4sBoGbGCkwVUIJSHBPI0z1Utrp1h5ys6ygT3albl3PgjejJcRRRiaDFFbMBA/640?wx_fmt=gif "")  
  
  
-  
赛博昆仑漏洞安全通告-  
  
JumpServer 后台模板注入漏洞（CVE-2024-29202）的风险通告   
  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/7j1UQofaR9fsNXgsOXHVKZMJ1PCicm8s4RHQVjCJEjX63AsNibMx3So4wSMAvubEOoU2vLqYY7hIibIJbkEaPIDs5A4ianh5jibxw/640?wx_fmt=svg "")  
  
  
  
****  
**漏洞描述**  
  
JumpServer 是广受欢迎的开源堡垒机，是符合 4A 规范的专业运维安全审计系统。JumpServer 帮助企业更安全的方式管控和登录所有类型的资产，实现事前授权、事中监察、事后审计，满足等保合规要求。  
  
近日，赛博昆仑CERT监测到JumpServer后台模板注入漏洞（CVE-2024-29202）的漏洞情报。经过授权的攻击者可以利用Jinja2模板注入漏洞在JumpServer的Celery容器中执行任意代码。由于Celery容器以root权限运行并具有数据库访问权限，攻击者可以从所有主机窃取敏感信息或操纵数据库。  
  
<table><colgroup><col width="182"/><col width="182"/><col width="182"/><col width="182"/></colgroup><tbody><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞名称</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;"><p><span style="color: rgb(0, 122, 170);">JumpServer 后台模板注入漏洞</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞公开编号</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;"><p><span style="color: rgb(0, 122, 170);">CVE-2024-29202</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>昆仑漏洞库编号</strong></span></p></td><td colspan="3" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;"><p><span style="color: rgb(0, 122, 170);">CYKL-2024-002720</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞类型</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">模板注入</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>公开时间</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">2024-03-29</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞等级</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">高危</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>评分</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">10</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞所需权限</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">普通用户权限</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞利用难度</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">低</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>PoC</strong><strong>状态</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">已公开</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>EXP</strong><strong>状态</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td></tr><tr style="height:39px;"><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>漏洞细节</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">已公开</span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);"><strong>在野利用</strong></span></p></td><td colspan="1" rowspan="1" style="border-color: rgb(221, 221, 221);font-size: 10pt;padding: 8px;vertical-align: top;" width="127"><p><span style="color: rgb(0, 122, 170);">未知</span></p></td></tr></tbody></table>  
  
  
**影响版本**  
  
v3.0.0 <= JumpServer <= v3.10.6  
  
**利用条件**  
  
普通用户权限  
  
**漏洞复现**  
  
目前赛博昆仑CERT已确认漏洞原理，复现截图如下：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/iaZ7t7b9DodvNicdoVZaSib5b5sSibSExTkPpzI9eb1kDibnG53mHWya1GQwjQpzRrEq8g7ukN7YzJWheYOQmH4IIfQ/640?wx_fmt=png&from=appmsg "")  
  
**防护措施**  
  
  
  
目前，官方已发布修复建议，建议受影响的用户尽快升级至安全版本。  
  
 下载地址：https://github.com/jumpserver/jumpserver/releases  
  
**技术咨询**  
  
赛博昆仑支持对用户提供轻量级的检测规则或热补方式，可提供定制化服务适  
配多种产品及规则，帮助用户进行漏洞检测和修复。  
  
赛博昆仑CERT已开启年订阅服务，付费客户(可申请试用)将获取更多技术详情，并支持适配客户的需求。  
  
联系邮箱：<EMAIL>  
  
公众号：赛博昆仑CERT  
  
**参考链接**  
  
https://github.com/jumpserver/jumpserver  
  
  
  
  
**时间线**  
  
 2024年03月29日，官方发布通告  
  
2024年04  
月01  
日，赛博昆仑CERT公众号发布漏洞风险通告  
  
  
  
