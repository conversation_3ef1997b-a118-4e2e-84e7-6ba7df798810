#  【漏洞通告】Apache ActiveMQ Jolokia 代码执行漏洞(CVE-2022-41678)   
深瞳漏洞实验室  深信服千里目安全技术中心   2023-11-30 21:23  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DsL5aXjzm8Ugvvqn8QaLDpVnibVYH3BB7ibjmBbD5SInMd3ib4RyOnsJNQ/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
Apache ActiveMQ Jolokia 代码执行漏洞(CVE-2022-41678)  
  
**组件名称：**  
  
Apache ActiveMQ  
  
**影响范围：**  
  
Apache ActiveMQ < 5.16.6  
  
5.17.0< Apache ActiveMQ < 5.17.4  
  
**漏洞类型：**  
  
远程代码执行  
  
**利用条件：**  
  
1、用户认证：需要用户认证  
  
2、前置条件：默认配置  
  
3、触发方式：远程  
  
**综合评价：**  
  
<综合评定利用难度>：困难，需要用户认证。  
  
<综合评定威胁等级>：高危，能造成远程代码执行。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DnKADwG0hM3FMia5FKaoEU67pxtHVoDVfc1O7WtUWcF6SVcsazGBYvTA/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
Apache ActiveMQ 是最流行的开源、多协议、基于 Java 的消息代理。它支持行业标准协议，因此用户可以从多种语言和平台的客户端选择中受益。从使用 JavaScript、C、C++、Python、.Net 等编写的客户端进行连接。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DnKADwG0hM3FMia5FKaoEU67pxtHVoDVfc1O7WtUWcF6SVcsazGBYvTA/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2023年11月30日，深瞳漏洞实验室监测到一则Apache ActiveMQ组件存在远程命令执行漏洞的信息，漏洞威胁等级：高危。  
  
ActiveMQ中，经过身份验证的用户默认情况下可以通过 /api/jolokia/接口操作MBean执行任意代码，导致服务器失陷。  
  
  
**影响范围**  
  
Apache ActiveMQ < 5.16.6  
  
5.17.0 < Apache ActiveMQ < 5.17.4  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DnKADwG0hM3FMia5FKaoEU67pxtHVoDVfc1O7WtUWcF6SVcsazGBYvTA/640?wx_fmt=gif&from=appmsg "")  
  
**如何检测组件版本**  
  
  
通常ActiveMQ文件夹名称中包含版本信息。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DnKADwG0hM3FMia5FKaoEU67pxtHVoDVfc1O7WtUWcF6SVcsazGBYvTA/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
官方已发布新版本修复该漏洞，受影响用户建议尽快更新Apache ActiveMQ到最新版本。  
  
链接如下：https://github.com/apache/activemq/tags  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3DnKADwG0hM3FMia5FKaoEU67pxtHVoDVfc1O7WtUWcF6SVcsazGBYvTA/640?wx_fmt=gif&from=appmsg "")  
  
**深信服解决方案**  
  
  
**1.风险资产发现**  
  
支持对 Apache ActiveMQ 的主动检测，可**批量检出**业务场景中该事件的受影响资产情况，相关产品如下：  
  
**【深信服主机安全检测响应平台CWPP】**已发布资产检测方案。  
  
**【深信服云镜YJ】**已发布资产检测方案。  
  
  
**2.漏洞主动扫描**  
  
支持对   
Apache ActiveMQ Jolokia代码执行漏洞(CVE-2022-41678)   
的主动扫描，可**批量快速检出**  
业务场景中是否存在**漏洞风险**  
，相关产品如下：  
  
**【深信服云镜YJ】**预计2023年12月03日发布扫描方案。  
  
**【深信服漏洞评估工具TSS】**预计2023年12月04日发布扫描方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月04日发布扫描方案。  
  
（需要具备TSS组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月03日发布扫描方案，（需要具备云镜组件能力）。  
  
  
**3.漏洞安全监测**  
  
支持对  
Apache ActiveMQ Jolokia代码执行漏洞(CVE-2022-41678)  
的监测，可依据流量收集**实时监控**  
业务场景中的**受影响资产情况，快速检查受影响范围**  
，相关产品及服务如下：  
  
**【深信服安全感知管理平台SIP】**预计2023年12月02日发布检测方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月02日发布检测方案，（需要具备SIP组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月02日发布检测方案。  
  
  
**4.漏洞安全防护**  
  
支持对Apache ActiveMQ Jolokia代码执行漏洞(CVE-2022-41678)的防御，可**阻断攻击者针对该事件的入侵行为**，相关产品及服务如下：  
  
**【深信服下一代防火墙AF】**预计2023年12月02日发布防护方案。  
  
**【深信服Web应用防火墙WAF】**预计2023年12月02日发布防护方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月02日发布防护方案，（需要具备AF组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月02日发布防护方案，（需要具备AF组件能力）。  
  
  
**参考链接**  
  
  
https://www.openwall.com/lists/oss-security/2023/11/28/1  
  
  
**时间轴**  
  
  
  
**2023/11/30**  
  
深瞳漏洞实验室监测到CVE-2022-41678详细PoC已在互联网公开。  
  
  
**2023/11/30**  
  
深瞳漏洞实验室发布漏洞通告。  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5yg8iaCotRYdChLSkTYpQo3Dy5ZialKMZQXCZJskNpZkUJd0mfuaY6BySuYNLzpGvm1Kh0NQIHzco8Q/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
