#  【漏洞预警】OpenSSH ProxyCommand命令注入漏洞CVE-2023-51385   
cexlife  飓风网络安全   2023-12-26 19:12  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu002oVKq350yMQhr8Ano5dLFJbHLvcEOVXnPnkjjxFIXu1cL6hcJz6PPuI1f0kvnOTlWb4VNQn9m3A/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**OpenSSH 是 SSH （Secure SHell） 协议的免费开源实现。SSH协议族可以用来进行远程控制，或在计算机之间传送文件,近日监测到OpenSSH发布安全更新，其中修复了一个命令注入漏洞，在9.6版本之前的OpenSSH中，用户名和主机名中可以通过注入恶意Shell字符导致命令注入,该漏洞是由于OpenSSH中的ProxyCommand命令中未对%h，%p表示的用户名和主机名输入进行安全过滤，导致当攻击者可以控制%h,%p值时可以注入恶意Shell字符进行命令注入攻击，常见攻击场景如一个不受信任的 Git 仓库可能存在含有 shell 元字符的用户名或主机名子模块，当用户递归更新该仓库时则会触发漏洞执行。  
  
**影响版本:**OpenSSH<9.6  
  
Host *.example.com  
  
ProxyCommand /usr/bin/nc -X connect -x *********:8080 %h %p  
  
git clone https://github.com/vin01/poc-proxycommand-vulnerable --recurse-submodules  
  
PoC:git clone https://github.com/vin01/poc-proxycommand-vulnerable-v2 --recurse-submodules**修复建议:****正式防护方案:**官方已经发布了安全更新，建议受影响的用户立即更新到安全版本**安全版本:**OpenSSH >= 9.6**官方最新版下载安装地址：**  
  
https://www.openssh.com/openbsd.html  
  
