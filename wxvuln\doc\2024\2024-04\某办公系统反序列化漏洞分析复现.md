#  某办公系统反序列化漏洞分析复现   
 白帽子   2024-04-05 00:02  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/ofBa42GG7Shb736Qree8xzh3FalD631YYibvXXL5XzxAqPibEviaiaT2t9PxYxUvXbXfo1ibrCLEsawHjHxbvvr8AUg/640?wx_fmt=gif "")  
  
            
  
最近看到某办公系统  
出了远程代码执行漏洞，公开的资料中路径或payload都打了厚码，所以就搭了一个环境康一下。漏洞版本介于20180516和20240222之间，于是在52上了找了20200421版本进行搭建。  
## 0x01 初步分析    
  
通过使用公开的闭源POC工具进行验证，再查看中间件Apache的access日志,发现了以下3条路径。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRJZ7A0Siaq10Hkc0j5N2Xrb9Ys1bNTib4nJ3judgFXzuiaa0jsxSaopDzg/640?wx_fmt=png "")  
  
  
通过测试发现漏洞的功能点在注册授权处,此处做了白名单，只可以上传.inc的注册文件，即使注册文件不正确，但是文件仍会落地到根目录上一层attachment\2024\M\D中。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRIl2yDCMNR6bnYtVqSStcC02QQWzrEgrsWwtbgIicQIMMS6tuco7JLCQ/640?wx_fmt=png "")  
  
      
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRdo8mSPMJDRdQQ3hJCibtdCJiansXGMHhgrNWYgyz4vDM2xdbJDb6gaLQ/640?wx_fmt=png "")  
  
## 0x02 POC     
  
使用pr+burp抓POC工具验证的数据包,找到了3条路径的详细数据包。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRRib0srMBUicECrtFbmW45GFKGBwhqNBByIfrHKSK5wdkJWiaicvMciawxzA/640?wx_fmt=png "")  
  
  
             
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRyTeylzicd9GtWDXbETB2xqGV282ibFdwdXwTZnf6SAAbo65sCZU9VhNw/640?wx_fmt=png "")  
  
  
                 
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRjkaDd6xsZo3cANO6ctEMw2r3Y3Uict5tUXYbNqWgmkwTF8FaT3MSsdQ/640?wx_fmt=png "")  
  
  
             
  
第一个包data中Illuminate\Broadcasting\PendingBroadcast、Illuminate\Bus\Dispatcher 和 Illuminate\Broadcasting\BroadcastEvent，这三个均属于Laravel PHP 框架中的类名，其中PendingBroadcast类是很经典的入口类，在Laravel 5的反序列化链中长用的就是这个类中的__destruct方法。  
  
第二个包使用了伪协议phar://，用于访问存储在临时文件夹attachment下文件  
  
通过查看配置信息，该OA也确实使用了Laravel框架，版本为5.6-dev,刚好是存在漏洞的版本。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRd6Qze8vxJ3GnSQTiaYVgBJl68qic9vrYXBjM2fNUw4W6eMnfffrGictxw/640?wx_fmt=png "")  
  
  
漏洞利用过程大概是：注册授权处上传phar序列化数据>>phar://目录穿越指定文件位置>>import方法导入造成反序列化，  
  
是比较经典的文件上传+目录穿越+phar反序列化漏洞，   
     
  
## 0x03 EXP     
  
方法一：  
既然是Laravel框架的Getgad Chains，那直接用phpggc中的链生成phar文件就可以了，经测试Laravel/RCE4和RCE9这2条链可以使用。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRlIUW3BdzcphGfPDhvtKYgfXro5g0ibiaDer8yx8ia4fXKfS7FSlRNe3GA/640?wx_fmt=png "")  
  
  
php -d phar.readonly=0 ./phpggc -p phar Laravel/RCE4 system whoami >register.inc  
  
             
  
方法二：直接写一个POP链  
  
fs师傅写的链，真香！！！！！！解决了echo的写不进去内容的问题。  
  
流程：  
PendingBroadcast类中的__construct作为入口，控制参数$this->event让其等于new BroadcastEvent(),控制参数$this->events让其等于new Dispatcher() >> 进入BroadcastEvent类控制$this->event为我们要执行的命令 >> 进入Dispatcher类控制$this->events为我执行命令需要的函数system >> 序列化数据和签名。  
  
在stub中可以添加图片头一类的东西进行简单混淆，运行生成payload。     
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDR4OibGI7FLwCztsryn3S35ov0AQo4QupoFImeAgLibh46JzMcGc301vbA/640?wx_fmt=png "")  
  
  
下来编写py脚本实现自动化，把上面三个数据包写进去，第一个包data部分pyload需要把phar文件内容base64编码，防止复制破坏原始文件。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRrAxK9ib10ZadoSDkNjug4juyXqay7LxibRY2ZzCzaKfZOhRhpdLibzNyA/640?wx_fmt=png "")  
  
  
其次phar文件存在签名，命令变量在每次修改后，需要使用hashlib库对修改数据加签。  
  
完了后发现执行whoami和执行其他命令的返回包结构不一样，正则匹配有问题，还得写个判断，并且像nue user这样带有空格的系统命令出问题。fs师傅说这搞的花里胡哨的，直接写shell他不香嘛？想想确实是这样[捂脸]。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRgM8orZqr7xPhHbC06YpnEcoZZ68icB64xYokybPF3MLkWqDlKYxJXdQ/640?wx_fmt=png "")  
  
  
直接改脚本传shell，屮！屮！这里还要考虑的问题就是免杀。这里写文件直接写是写到了php文件夹下，需要跳目录写到网站根目录下。     
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRDnClXAibrNrzp7YVvF7wnZljDFcYB8UbXROtx4Z8MibG5f8ZSVDK9yZQ/640?wx_fmt=png "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRvDoKZniaqibqXsuUHW7AF6RPrG502X5NDgicodVSQxiaIZsqqWnAO2lHAQ/640?wx_fmt=png "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/ofBa42GG7Sh8PkMLhgCZBQ2V1fOxxwDRKrCNNIcY5zY5HNzWTt4pIVNK4Szx9go4vLkyMRqtNbQGFUhLMoHHSA/640?wx_fmt=png "")  
  
## 0x04小结     
  
此办公系统的反序列化漏洞主要原因在其使用了laravel的php框架，而laravel 5和8都存在漏洞，其次上传注册文件的接口未鉴权和未对文件内容做过滤导致可上传有恶意程序的文件，再者未对desc_path的参数值做过滤导致目录跳跃。  
  
建议使用该系统的用户立即使用官方提供的补丁进行升级维护，避免进一步损失。      
  
声明：本文的内容仅用于学习交流，禁止用于非法测试     
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/ofBa42GG7Shb736Qree8xzh3FalD631YQ8lcv4ZictUgmkAsBng285CXdibI5oJSYGQnZFhxrqeiakKhMTedWMhwA/640?wx_fmt=jpeg "")  
  
  
