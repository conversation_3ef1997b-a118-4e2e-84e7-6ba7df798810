#  【漏洞预警】Perl for Windows 任意代码执行漏洞 CVE-2023-47039   
cexlife  飓风网络安全   2024-01-03 18:16  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00aK6hLus26I25zKPRiamq2QCP27ZRtmeia3Rhv5pYXj1yzBsgufqjj2TymQjEpFTM2wTpyNyE8fC6A/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述：**  
  
Perl 是具有通用、解释型、跨平台性的编程语言，Perl 受影响的Windows版本中，当运行一个使用 Windows Perl 解释器的可执行文件时，Perl 会在系统环境变量中查找和执行 cmd.exe,由于路径搜索顺序最初会在当前工作目录中查找 cmd.exe,攻击者可利用这个缺陷将恶意的 cmd.exe 放置在权限较弱的位置，例如C:\ProgramData，并诱使受害者（如管理员）在当前目录执行Perl解释器，进而加载攻击者可控的恶意的 cmd.exe，导致任意代码执行。  
**影响范围：**perl(-∞, 5.32.1)**修复方案：**升级perl到 5.32.1 或更高版本**下载链接：**https://www.perl.org/get.html**参考链接：**https://access.redhat.com/security/cve/CVE-2023-47039https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=1056746https://bugzilla.redhat.com/show_bug.cgi?id=2249525  
