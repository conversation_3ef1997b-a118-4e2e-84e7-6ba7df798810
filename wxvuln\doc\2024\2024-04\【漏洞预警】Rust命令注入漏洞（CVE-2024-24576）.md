#  【漏洞预警】Rust命令注入漏洞（CVE-2024-24576）   
cexlife  飓风网络安全   2024-04-10 23:11  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDLGDb0HUTQH4ibwfFyJxY1NTgxz1lZ7ib6FgDbyD73S4jNSbksEFHH6icg/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**  
  
Rust是一种通用、编译型编程语言,强调性能、类型安全和并发性,支持函数式、并发式、过程式以及面向对象的编程风格,近日监测到Rust标准库中存在命令注入漏洞（CVE-2024-24576，被称为BatBadBut）,该漏洞的CVSS评分为10.0，可能在Windows系统上导致命令注入攻击,目前该漏洞的细节已公开,Rust标准库1.77.2 版本之前,在 Windows上使用Command API 调用批处理文件（带有bat和cmd扩展名）时,Rust标准库没有正确转义批处理文件的参数,能够控制传递给生成进程的参数的攻击者可绕过转义执行任意shell命令。**影响范围:**Rust < 1.77.2（Windows平台）注:如果代码或依赖项之一使用不受信任的参数执行批处理文件,则 Windows上1.77.2之前的所有Rust版本都会受到影响,其他平台或使用不易受到影响。BatBadBut漏洞允许攻击者在满足特定条件时对间接依赖CreateProcess函数的Windows应用程序执行命令注入,CreateProcess函数在执行批处理文件时会隐式生成cmd.exe。BatBadBut漏洞可能影响Erlang、Go、Haskell、Java、Node.js、PHP、Python、Ruby、Rust等多种编程语言，当编程语言封装 CreateProcess 函数并为命令参数添加转义机制时存在安全问题，导致恶意命令行参数可能能够执行命令注入。**安全措施:**升级版本目前官方已在Rust 版本1.77.2中缓解修复了该漏洞，改进了转义代码的稳健性，并更改了CommandAPI,使其在无法安全地转义参数时返回InvalidInput错误。受影响用户可更新到Rust 1.77.2或更高版本。**下载链接:**https://blog.rust-lang.org/2024/04/09/Rust-1.77.2.html**临时措施:**避免在Windows上使用不受信任的参数调用批处理文件  
  
