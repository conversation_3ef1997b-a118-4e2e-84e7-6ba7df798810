#  【长亭科技技术支持】关于防范利用xz-utils恶意代码植入漏洞实施网络攻击的风险提示   
 长亭安全应急响应中心   2024-04-16 14:22  
  
供应链安全是当前网络攻击的重灾区，**长亭科技技术团队近日发现Linux压缩工具xz-utils存在恶意代码植入漏洞**  
，及时做出应急响应，对该漏洞进行事件溯源和技术分析，给出了针对该漏洞的风险消控方案，并在第一时间上报给工业和信息化部网络安全威胁和漏洞信息共享平台（NVDB），帮助国内企事业单位有效防范可能利用此漏洞的网络攻击行为。  
  
  
**以下内容转自“网络安全威胁和漏洞信息共享平台”公众号原文：**  
  
近日，工业和信息化部网络安全威胁和漏洞信息共享平台（NVDB）监测发现，Linux压缩工具xz-utils存在恶意代码植入漏洞，可被恶意利用实施网络攻击。  
  
  
xz-utils是Linux系统下一款用于压缩和解压.xz格式文件的开源软件，由于攻击者以开源项目维护者身份在项目文件中植入恶意代码，并通过开源社区分发，导致用户在使用带有恶意代码的xz-utils软件时，可被攻击者利用恶意代码执行任意命令，获取系统权限。经初步排查，该漏洞影响xz-utils 5.6.0和5.6.1版本。  
  
  
目前官方尚未发布针对此漏洞的修复方案，建议相关单位和用户立即通过“xz --version”命令排查系统本地是否安装受影响的xz-utils版本，尽快回退xz-utils至5.4.x版本，并持续关注所用Linux发行版的官方安全通知，获取安全补丁和更新的最新信息，防范网络攻击风险。  
  
  
感谢奇安信网神信息技术（北京）股份有限公司、北京微步在线科技有限公司、**北京长亭科技有限公司**、深信服科技股份有限公司、北京天融信网络安全技术有限公司提供技术支持。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NCzpaOPov3MYiaCey79kEuaHS1SZSGXvAj0RicS8LpU1u9XibhbJibtcD0CjQWTOVU6GWXCsFY1zLNnfYHSg5g0QZQ/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
