#  【漏洞预警】Zabbix代码执行漏洞CVE-2023-32727   
cexlife  飓风网络安全   2023-12-19 21:42  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03iaabjuhH11GPKS3D21IGUibibcbUlUFUpOcDicqj727CbbQVNevZKjpwTc80micqeXZmw7ibr30E0JibYA/640?wx_fmt=png&from=appmsg "")  
  
**漏洞简述:**Zabbix是一个基于WEB界面的提供分布式系统监视以及网络监视功能的企业级开源监控解决方案，可以用来监控服务器、硬件、网络等,近日监测到Zabbix中修复了一个icmpping()代码执行漏洞（CVE-2023-32727），其CVSS评分为6.8。有权配置Zabbix项目的威胁者可以使用函数 icmpping() 以及其中的附加恶意命令在当前 Zabbix 服务器上执行任意代码。此外，Zabbix中还修复了zabbix_agent2 smartctl 插件中的代码注入漏洞（CVE-2023-32728，中危），由于Zabbix Agent 2 item（监控项）key smart.disk.get在将其参数传递给 shell 命令之前不会对其进行清理，可能导致远程代码执行，威胁者可在任何具有 Zabbix Agent2 侦听并安装了 smartctl 的设备上执行任意代码。**影响范围:**CVE-2023-32727Zabbix 版本4.0.0 - 4.0.49Zabbix 版本5.0.0 - 5.0.38Zabbix 版本6.0.0 - 6.0.22Zabbix 版本6.4.0 - 6.4.7Zabbix 版本7.0.0alpha0 - 7.0.0alpha6CVE-2023-32728Zabbix 版本5.0.0 - 5.0.38Zabbix 版本6.0.0 - 6.0.23Zabbix 版本6.4.0 - 6.4.8Zabbix 版本7.0.0alpha1 - 7.0.0alpha7 **安全措施:升级版本**目前这些漏洞已经修复，受影响用户可升级到以下版本:CVE-2023-32727：Zabbix 版本4.0.0 - 4.0.49：升级到4.0.50或更高版本Zabbix 版本5.0.0 - 5.0.38：升级到5.0.39或更高版本Zabbix 版本6.0.0 - 6.0.22：升级到6.0.23rc1或更高版本Zabbix 版本6.4.0 - 6.4.7：升级到6.4.8rc1或更高版本Zabbix 版本7.0.0alpha0 - 7.0.0alpha6：升级到7.0.0alpha7或更高版本CVE-2023-32728：Zabbix 版本5.0.0 - 5.0.38：升级到5.0.39rc1或更高版本Zabbix 版本6.0.0 - 6.0.23：升级到6.0.24rc1或更高版本Zabbix 版本6.4.0 - 6.4.8：升级到6.4.9rc1或更高版本Zabbix 版本7.0.0alpha1 - 7.0.0alpha7：升级到7.0.0alpha8或更高版本**下载链接：**https://github.com/zabbix/zabbix/tags**通用建议:**1.定期更新系统补丁，减少系统漏洞，提升服务器的安全性。2.加强系统和网络的访问控制，修改防火墙策略，关闭非必要的应用端口或服务，减少将危险服务（如SSH、RDP等）暴露到公网，减少攻击面。3.使用企业级安全产品，提升企业的网络安全性能。4.加强系统用户和权限管理，启用多因素认证机制和最小权限原则，用户和软件权限应保持在最低限度。5.启用强密码策略并设置为定期修改**参考链接:**https://support.zabbix.com/browse/ZBX-23857https://support.zabbix.com/browse/ZBX-23858  
  
