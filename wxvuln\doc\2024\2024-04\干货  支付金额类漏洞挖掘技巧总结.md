#  干货 | 支付金额类漏洞挖掘技巧总结   
中铁13层打工人  渗透安全团队   2024-04-04 22:27  
  
前言  
  
支付类逻辑漏洞在漏洞挖掘中是常常出现的问题之一。此类漏洞挖掘思路多，奖励高，是炙手可热的漏洞。此篇文章主要围绕挖掘支付逻辑漏洞时的一些思路分享。  
  
前言：  
  
支付类逻辑漏洞在漏洞挖掘中是常常出现的问题之一。此类漏洞挖掘思路多，奖励高，是炙手可热的漏洞。此篇文章主要围绕挖掘支付逻辑漏洞时的一些思路分享。  
## 支付逻辑漏洞成因：  
  
支付漏洞可能由以下原因造成：  
1. 前端验证不充分：在前端页面上，没有进行足够的验证和限制，使得用户可以通过修改页面元素或发送自定义请求来篡改支付金额、支付类型、支付状态等。  
  
1. 客户端数据不可信：客户端（如移动应用）在进行支付时，没有对传输的数据进行完整性验证和加密，导致恶意用户可以直接修改数据包中的支付金额、订单号等与订单有关的参数。  
  
1. 服务器端验证不严格：支付请求在到达服务器端时，没有进行足够的验证和校验，使得攻击者能够更改支付相关参数并绕过服务器端的验证机制。  
  
1. 不安全的存储和传输：支付金额数据在存储或传输过程中未经适当的加密保护，导致黑客可以窃取或篡改数据。  
  
## 支付逻辑漏洞挖掘技巧：  
  
在实际漏洞挖掘中，一般最先尝试的就是更改数据包发包内容，可以直接修改支付金额、更改支付状态、更改支付类型、更改提交订单支付的时候其中的订单信息等等，当然也会有一些新奇的功能点可以测试。这些在测试中会遇到的操作可以分为以下几类：  
#### 一、更改支付金额  
  
在支付流程中，可以修改支付价格的步骤有很多，包括订购、确认信息、付款等。在涉及到价格的步骤中都可以尝试修改，如果网站在某一环节存在逻辑上的漏洞，就可以利用该漏洞对支付价格进行修改。可以直接修改提交订单中的价格字段，一般可尝试0.01，1.00，1等  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhukuRrcsYuw5SLTXyh0E3F9zPvcvIMA8xIVYVhUOBL9xE2Mw0y9zYKg/640?wx_fmt=png&from=appmsg&random=0.20687621466584183 "")  
#### 二、更改支付状态  
  
在测试中有的时候订单得支付状态是由用户提交订单时的某个数据包参数决定的，服务端通过支付状态判断订单支付与否，这时我们可以尝试找到这个参数（可以通过正常支付订单的数据包进行对比），对支付状态进行修改。或者还有一种情况是通过检查订单是否支付，这个时候可以通过抓取已支付的订单数据包将其中的订单编号改为未支付的编号，实现绕过。  
  
1、直接修改为已支付状态  
  
2、修改未支付的订单号为已支付订单号  
#### 三、修改支付类型  
  
通常在提交订单付款时，这里的type一般是对支付方式的判断，可能会存在开发人员测试的时候遗留的无需支付的type值，根据支付方式判断支付与否。可以通过fuzz特定值去实现绕过。比如比较常见的值0（这里需要结合实际进行测试不同的处理方式type值不同），可以实现不需要付款订单就会自动生成。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhCmlPic1ogpwujJODQRRvfG2wZZmNa3VwhkN34picrxfQtuPbJRnWmFUg/640?wx_fmt=png&from=appmsg&random=0.9497328129825924 "")  
#### 四、更改订单信息  
  
服务端只检查支付是否完成，并没有确认订单金额与银行支付金额是否相同，过分信任客户端提交的数据。此时可以通过替换支付订单号、更换商品id的方式，来完成花少钱买更贵的东西。同时生成两个订单号，一个贵的一个便宜，首先支付便宜的，银行往回返回的时候，替换订单号，然后就可以完成两个订单的同时支付。  
  
常见位置在生成订单、生成支付链接等。  
  
1、修改商品编号  
  
直接在生成的订单中替换商品编号。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhP8c0BP7vnibEGqzdb41kvwmpGR2tDoHvbmSGl7s9ANyCibETV7icbZ5lQ/640?wx_fmt=png&from=appmsg&random=0.8636785870440888 "")  
  
2、修改订单号  
  
将金额不同的订单进行替换，可以支付一个金额较少的订单，然后将订单号修改为金额较大的订单，少付实际金额。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhIHk7pibF2LUQNqMoCZBJcTAXWibhlSNYVtMnEHzIfetSpIJJo9OxedOw/640?wx_fmt=png&from=appmsg&random=0.36808996647694014 "")  
  
3、越权使用他人优惠券、越权使用他人积分等  
#### 五、更改数量实现优惠支付  
  
支付金额是由购买数量乘以商品单价决定的，这时我们在数据包中修改购买数量，将其修改为负数或者小数，如果站点后台对此没有进行过滤，就有可能存在支付漏洞。  
  
1、将正常的数量值修改至最小值0.01，可以实现低价购买。比如：原价300修改修量为0.01后实付金额变为3。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRh0ceicWUpPic6JBiaLciaiaNsDmd0jEiay6RrAHYu40sPbzsM0cUA9CFA3cHg/640?wx_fmt=png&from=appmsg&random=0.27716979656172813 "")  
  
2、未对负数做检验的还可以将数量改为负数。（这里需要注意，因为后端大部分会校验不允许实付金额小于0或者0.01等，所以有的时候要想实现订单成功生成需要结合实际修改价格）  
  
生成订单时有参数表示商品数量，修改为-1  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhJibF0Skf4XeW1QVR6EQHMR1F3icpINcZUJdd8e7ibw7F4pp777VmzaScQ/640?wx_fmt=png&from=appmsg&random=0.3813530772166296 "")  
  
修改数量为-1后会发现，此时金额为负数。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRh8DCuu6PN7sRpUSIF84Db5BEfzJ5rNp1HTgIk362l2u3ng0rrSkszTQ/640?wx_fmt=png&from=appmsg&random=0.09481361464967142 "")  
  
在提交订单支付的时候，为保证支付成功需要修改金额。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhCbFKejuoXQ5b8GLzlGE5ySLccTfPUytBvfVZfCp44oW1rLmzGDpiaQg/640?wx_fmt=png&from=appmsg&random=0.6857865855170631 "")  
  
3、对数量没有做负数校验的时候也可以巧用负数抵消实现0元购  
  
在计算价格时，没有对负数进行验证，通过修改某个商品数量为-1实现与1的抵消实现0元购。  
  
同时购买两件商品，修改两件商品其中价格低的商品的金额为负数，实现价格的抵消，低价购买商品。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhWZDdAUF7CdoeGeGsR8oC4icD7ptoW0iaWQPxZm5keuN9mOve4yMpJ2gQ/640?wx_fmt=png&from=appmsg&random=0.8311219676656012 "")  
  
4、手动增加订单中商品相关的多个参数以达到少付多买的目的。  
  
有的时候在提交订单时抓取数据包可以看到只有一套商品的信息，尝试多添加几套同样的参数订单是否会有变化。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhWkGkKCpZRjbKQDjzZPSUC3C50HrO2xFMem9qmUlClx1g5EicdibTQEJg/640?wx_fmt=png&from=appmsg&random=0.29855003540824443 "")  
  
尝试在提交订单的时候多添加几个此类参数  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhC91sTyTgD9mFhDa0Kl4zUy8P0q6ZWJ5C72xdTnrfGdSD61gKDwuJaA/640?wx_fmt=png&from=appmsg&random=0.3511575168460539 "")  
  
提交订单实际支付金额未变仍是一个商品的价格，但是实际套餐已经变成了四个。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRhibQibehXlbIfh2MeG7v2d65sVEJ6lJCGDFvm6FticKmaerwOaV8Fm7PtA/640?wx_fmt=png&from=appmsg&random=0.939425098692602 "")  
#### 六、重复支付，突破限购  
  
在支付系统中，服务端没有做好相关验证，比如订单状态被错误更新或者未更新，未对订单多重提交进行校验。那么就可以并发订单实现优惠订单多次提交。需要注意的是这里有的时候会根据实际支付订单判断，并发了多个订单也可能只有一个优惠订单可以正常支付。  
  
并发订单，多台设备同时提交优惠订单。  
  
常见于限购，一个账号仅许购买一次等  
  
1、限制一个优惠订单时直接并发生成多个优惠订单  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRh89sDQMS4QQKbX2SoYIRW486Mb5ialsCSehrxHyZ6GBY8iazlUTAJ56XA/640?wx_fmt=png&from=appmsg&random=0.7215330680727206 "")  
  
2、使用多台设备、多个浏览器、多种支付方式（wx、支付宝等）购买优惠订单  
  
常见于购买会员，会员第一个月往往会有优惠价。生成一个优惠订单后不支付，打开多个设备或者虚拟器设备，同时提交生成优惠订单，再分别支付，有的时候会发现会员截至日期顺延，突破限制以优惠价格购买会员。  
  
3、退款处并发。退款的时候可以发起同一订单多次退款，达到多退款的目的。  
#### 七、优惠券多次使用  
  
常见于涉及优惠券的订单中。可以在提交订单的时候修改发包中优惠券的值尝试使用大额优惠券，或者按照原数据包中优惠券的构造参数手工添加几张优惠券，达到优惠券叠用的目的。有优惠券面值参数的也可以直接修改数据包中优惠券的面值。  
  
1、在一个订单中叠加使用优惠券  
  
2、修改优惠券标识，尝试使用其他商品中的大额优惠券  
  
3、直接修改优惠券的面值。实际金额计算会扣除优惠的部分，此时修改优惠券面值可以实现低价购买。  
#### 八、遍历隐藏或者下架优惠id获取优惠链接  
  
漏洞常见位置：会员处、商品处（隐藏商品，已下架商品，开发测试低价商品等）  
  
1、遍历隐藏优惠券  
  
一般会有一些开发时测试的大额优惠券，或者已经过期下架的优惠券，通过遍历可以被使用。  
  
2、遍历商品id从而fuzz到已下架的商品  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/CBJYPapLzSFe2nXJv4xgPGVypibLggwRh4VyJWFicqO2ZDBPhD36xcsxBcKLoaTprQhXrscAg3ePJwl4xjibfdspg/640?wx_fmt=png&from=appmsg&random=0.5985251450630926 "")  
#### 九、利用小数点精度四舍五入  
  
0.019=0.02（比如充值0.019元，第三方支付截取到分也就是0.01元，但是系统四舍五入为0.02）。  
```
文章来源: https://forum.butian.net/share/2778
文章作者：中铁13层打工人
如有侵权请联系我们，我们会进行删除并致歉
```  
  
  
**★**  
  
**付费圈子**  
  
  
**欢 迎 加 入 星 球 ！**  
  
**代码审计+免杀+渗透学习资源+各种资料文档+各种工具+付费会员**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.4447566002908574&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**进成员内部群**  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/pPVXCo8Wd8AQHAyOTgM5sLrvP6qiboXljGWG0uOdvcNR8Qw5QJLxSVrbFds2j7MxExOz1ozb9ZoYwR68leoLdAg/640?wx_fmt=jpeg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.09738205945672873&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**星球的最近主题和星球内部工具一些展示******  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/pPVXCo8Wd8Doq0iczyRiaBfhTQyfzqSGuia4lfHfazabEKr2EDe7sGVoxUhLrNRA4FbI1yef6IkWdmzxvZrTiaJncg/640?wx_fmt=jpeg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8BmE6FAA8Bq7H9GZIRt1xYZpmYNWxrrzolt71FtX5HyM03H0cxkiaYelv7ZSajLtibEdBXUpCibdItXw/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8ADSxxicsBmvhX9yBIPibyJTWnDpqropKaIKtZQE3B9ZpgttJuibibCht1jXkNY7tUhLxJRdU6gibnrn0w/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8DKZcqe8mOKY1OQN5yfOaD5MpGk0JkyWcDKZvqqTWL0YKO6fmC56kSpcKicxEjK0cCu8fG3mLFLeEg/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8DAc8LkYEjnluf7oQaBR9CR7oAqnjIIbLZqCxwQtBk833sLbiagicscEic0LSVfOnbianSv11PxzJdcicQ/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8B96heXWOIseicx7lYZcN8KRN8xTiaOibRiaHVP4weL4mxd0gyaWSuTIVJhBRdBmWXjibmcfes6qR1w49w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8DAc8LkYEjnluf7oQaBR9CRBgpPoexbIY7eBAnR7sWS1BlBAQX51QhcOOOz06Ct2x1cMD25nA6mJQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/pPVXCo8Wd8AqNwoQuOBy9yePOpO5Kr6aHIxj7d0ibfAuPx9fAempAoH9JfIgX4nKzCwDyhQzPrRIx4upyw5yT4Q/640?wx_fmt=png&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
****  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/pLGTianTzSu7XRhTMZOBAqXehvREhD5ThABGJdRialUx3dQWwO7fclsicyiajicKfvXV4kHs38nkwFxUSckVF2nYlibA/640?wx_fmt=gif&random=0.4447566002908574&tp=wxpic&wxfrom=5&wx_lazy=1 "")  
  
  
**加入安全交流群**  
  
  

								[                ](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489372&idx=1&sn=5e14ba5fa59059fb1ee405e56ef90d40&chksm=c175eaf3f60263e5ef5415a8a9fc134f0890fdb9c25ab956116d17109baf98b3bd6bed572a2d&scene=21#wechat_redirect)  

			                  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ndicuTO22p6ibN1yF91ZicoggaJJZX3vQ77Vhx81O5GRyfuQoBRjpaUyLOErsSo8PwNYlT1XzZ6fbwQuXBRKf4j3Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1&tp=wxpic "")  
  
  
**推荐阅读**  
  
  
  
[干货｜史上最全一句话木马](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489259&idx=1&sn=b268701409ad4e8785cd5ebc23176fc8&chksm=c175eb44f60262527120100bd353b3316948928bd7f44cf9b6a49f89d5ffafad88c6f1522226&scene=21#wechat_redirect)  
  
  
  
[干货 | CS绕过vultr特征检测修改算法](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247486980&idx=1&sn=6d65ae57f03bd32fddb37d7055e5ac8e&chksm=c175f3abf6027abdad06009b2fe964e79f2ca60701ae806b451c18845c656c12b9948670dcbc&scene=21#wechat_redirect)  
  
  
  
[实战 | 用中国人写的红队服务器搞一次内网穿透练习](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247488628&idx=1&sn=ff2c617cccc00fe262ed9610c790fe0e&chksm=c175e9dbf60260cd0e67439304c822d28d510f1e332867e78a07d631ab27143309d14e27e53f&scene=21#wechat_redirect)  
  
  
  
[实战 | 渗透某培训平台经历](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247488613&idx=1&sn=12884f3d196ac4f5c262a587590d516d&chksm=c175e9caf60260dcc0d5d81a560025d548c61fda975d02237d344fd79adc77ac592e7e562939&scene=21#wechat_redirect)  
  
  
  
[实战 | 一次曲折的钓鱼溯源反制](http://mp.weixin.qq.com/s?__biz=MzkxNDAyNTY2NA==&mid=2247489278&idx=1&sn=5347fdbf7bbeb3fd37865e191163763f&chksm=c175eb51f602624777fb84e7928bb4fa45c30f35e27f3d66fc563ed97fa3c16ff06d172b868c&scene=21#wechat_redirect)  
  
  
  
**免责声明**  
  
由于传播、利用本公众号渗透安全团队所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，公众号渗透安全团队及作者不为**此**  
承担任何责任，一旦造成后果请自行承担！如有侵权烦请告知，我们会立即删除并致歉。谢谢！  
  
好文分享收藏赞一下最美点在看哦  
  
