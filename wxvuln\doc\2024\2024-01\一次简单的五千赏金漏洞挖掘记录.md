#  一次简单的五千赏金漏洞挖掘记录   
 迪哥讲事   2023-12-31 23:14  
  
## 0x01 前言  
#   
  
最近一直忙于学习，公众号一直未更新，大家见谅。  
  
## 0x02 漏洞背景  
  
一次众测项目，授权对目标子域进行渗透，我们称目标为target.com。  
## 0x03 漏洞挖掘过程  
  
漏洞一：  
  
存在一个子域名称为a.target.com。目标系统为一个企业客户管理的系统，可以自己注册企业账户进行登录。里面存在角色管理的功能，可以自己创建角色并添加用户。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/pOOKGW9VicErTFkrFSaJ2PlOmaw8D1icyrz3O79eCMsNNwcxsrC55p8FhPAFLia5ZUoBecIHo07XBfOsricIlgia6Vg/640?wx_fmt=png "")  
  
如上图所示，我们添加一个只有日志查询功能的角色，并添加一个用户赋予其刚创建的角色。使用刚加入的低权限账号登录，发现企业管理员的大多功能都可使用。如查看管理员：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/pOOKGW9VicErTFkrFSaJ2PlOmaw8D1icyrzzqfagibQwicUexozOt0IDiaVxN4d10b8Ajs895lQWqH4AuSJaaia6Obwg/640?wx_fmt=png "")  
  
至此一个越权漏洞已经可以提交了。  
  
漏洞二：  
  
使用企业超级管理员创建一个角色，拥有与企业管理员相同的功能，并添加一个用户称为test赋予其刚创建的角色，使用test正常登录。使用另一个浏览器登录企业超级管理员的账号，修改test账号的角色的权限，修改为最小权限，如第一张图所示，仅可查看日志信息。  
  
此时发现test用户仍然可以使用之前赋予的管理员的所有功能，正常情况修改角色，角色里面登录中的用户会被强制踢下线。至此，逻辑设计缺陷漏洞已经可以提交。  
## 0x04 厂商反馈  
  
俩个漏洞合并提交获得了五千赏金。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/pOOKGW9VicErTFkrFSaJ2PlOmaw8D1icyrkvia2xjGIsw0yrnuHOdqP3YNXhSaIMx3eP47n2tK2iaNzYSR84VtasmQ/640?wx_fmt=png "")  
## 0x05 总结  
  
关于企业管理或者团队管理之类的系统，大家可以都试试此类漏洞。  
  
如果你是一个长期主义者，欢迎加入我的知识星球([优先查看这个链接，里面可能还有优惠券](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247489122&idx=1&sn=a022eae85e06e46d769c60b2f608f2b8&chksm=e8a61c01dfd195170a090bce3e27dffdc123af1ca06d196aa1c7fe623a8957755f0cc67fe004&scene=21#wechat_redirect)  
)，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[xss研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487130&idx=1&sn=e20bb0ee083d058c74b5a806c8a581b3&chksm=e8a604f9dfd18defaeb9306b89226dd3a5b776ce4fc194a699a317b29a95efd2098f386d7adb&scene=21#wechat_redirect)  
  
  
[SSRF研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[2022年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
## 福利视频  
  
笔者自己录制的一套php视频教程(适合0基础的),感兴趣的童鞋可以看看,基础视频总共约200多集,目前已经录制完毕,后续还有更多视频出品  
  
https://space.bilibili.com/177546377/channel/seriesdetail?sid=2949374  
## 技术交流  
  
技术交流请加笔者微信:richardo1o1 (暗号:growing)  
  
  
