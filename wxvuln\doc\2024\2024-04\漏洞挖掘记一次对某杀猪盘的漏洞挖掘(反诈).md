#  漏洞挖掘|记一次对某杀猪盘的漏洞挖掘(反诈)   
原创 爱州  州弟学安全   2024-04-15 19:00  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/icdGEWOnYLpNJUTyXhK4Iic6TJFLAAboGBK3V3tSviaWr4PZG8a6IYoiaMTg23QFLvasNxpQL1Ed9qLsPUmGPH1mPw/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=webp "")  
  
**0x01 前言**  
  
    这天无聊闲逛，学习技术，无意间在某公众号下看到有赚钱的信息  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpPq425oRB6zdfjZicDhiaDzYweyOEBDtoCJbibKunXQyM6cGbtTWMGCGhFrjibtvDPoYSv2fTIdibHklnA/640?wx_fmt=png&from=appmsg "")  
  
    但是，当我打开后，文章被作者删了，好巧不巧的是，有位师傅保存下来了广告图，图就不发了，别再因为这个文章被和谐  
```
什么是杀猪盘:
  我们近几年经常听到的反诈，骗子无时无刻不在
  骗子首先会开发虚假的"投zi"、"理cai"网站
  接着会找准定位群体，以多种千奇百怪的方式让你变成那头"猪"
  前期让你尝到甜头，后期等你上头后拉黑删除
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOia7r6eweHx8VhBvBDZibBA138XkVTFv9pjWq371mSibfHibSKLLvQwxS2fasdKcf8nsh30fbRCtnq5Q/640?wx_fmt=png&from=appmsg "")  
      
以下是这个杀猪盘的网络拓扑，结尾会有相关的反诈及架构拓扑  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlob6TFVDsOcwtgDfBj0cxHy3ozkBia30lY1OhdC8HuskLv9siarQSPhcg/640?wx_fmt=png&from=appmsg "")  
  
*** 文章仅供参考，勿违规对正常网站进行非法测试，后果自负**  
  
**0x02 情节发展**  
  
    首先，拿到的是所谓网站的下载链接 a.targetxzapp.com，下载后可以申请福利，申请福利需要再次下载那个客服APP，其实都是网页类型，有个APP显得高端，后来通过抓包看到WEB地址，直接访问首页是这个  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlWiaV0IdP9NNU8sZLIG536zLXzROFgYXZZc1evgtHXicok2w6Ezo9NibTw/640?wx_fmt=png&from=appmsg "")  
  
    后面url加上page/ 后看到了所谓的首页，首页非常简单，就是PHP，除了支付接口调用以外，其它非常简单  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKloUZ1uYTe8ek4QabxzI24b6fMiaD7vYlxP4GPkfkxaHW6f4RedoNAbtg/640?wx_fmt=png&from=appmsg "")  
  
    一个所谓的弘马产业页面，冒用名义(后经过拉数据后，能看到一些日志，发现源码就是套壳，就是改个名)  
  
    香港亚马逊云(无安全设备)、无CDN、TP框架、VUE  
  
    既然是TP框架，我们先让它报错，可以看到我们能看到报错payload  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlNnwQ3AtGOlvxAiaAhPZuwWXhvqFVuKksBW2xK0nBzo3Yznz6j18FZFA/640?wx_fmt=png&from=appmsg "")  
  
    注册了个账号，没有验证模块，但是发现，不管干啥都得实名  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlRIwDmS1RrjhMm4CuX1k6h3TguOpgowQOjvqqAGkKfyjDwWyYtIauOw/640?wx_fmt=png&from=appmsg "")  
  
    想着乱写个信息，结果后端竟然可以校验  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlj82bq3TwmLjr5CRYZvBquERg4Iasl9gqkNY8sF2SSUXiapgnTJAdia5w/640?wx_fmt=png&from=appmsg "")  
  
    算了，先不管了，没有上传点，因为VUE特性容易未授权，想着看看能不能测出来点敏感信息，结果都是关于用户的，陷入僵局  
  
    然后就看注入点，因为TP框架当请求有问题的时候会报错，我们在登录的时候，输入万能密码，直接保存，看到了执行的SQL语句  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlLvftB1vTnxwMLl9fqU33Fb9SuuXmJsgc9gVXk6QPFaW4lVMicttlNPg/640?wx_fmt=png&from=appmsg "")  
```
他这里用到了括号，我们可以用括号进行闭合
select * from user where (phone=1) order by id asc limit 1;
select * from user where (phone=') OR NOT 1063=1063# 被注释) order by id asc limit 1;
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlg21G1UyLUSG546z1APHjw5SYmT9DzCibW6uuHc4h0UUxGXRPCtoVSEw/640?wx_fmt=png&from=appmsg "")  
  
    
  用sqlmap直接跑即可，为了更清晰的验证，使用时间盲注的payload确实可以  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlV277G9JThwLQsQNZ62xqvBHic1UaxmlSSBqYfCVDldyZZpBSR34NxFw/640?wx_fmt=png&from=appmsg "")  
  
    库里面的admin表中是弱口令，奈何扫了一夜都没找到后台  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOia7r6eweHx8VhBvBDZibBA1y17SpOGTOgAh3qtbCFibgddSbnzXTaG2RN7nBTwATxwhSwpwGwBRasg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKl2lGwicibOKBK2aEotiawwqEMhqKKyS5sX2a6DYrOcVLN8zJ2XhFXOFnHQ/640?wx_fmt=png&from=appmsg "")  
  
    因为他们把很多操作日志和前端的一些编码都存到数据库里面了，所以我想看看有没有所谓的管理员操作日志，干脆把数据能梭的都梭出来  
  
    因为前端没有上传入口和尽我所能找到的RCE入口(太菜)，在想，能不能通过SQLmap进行getshell  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlzvKPiavB2uicSHysRficG8sibWsVf9Neb8D5PR2vADrG8d7dq7W4uzUsNA/640?wx_fmt=png&from=appmsg "")  
  
    真的笑了，os-shell竟然没有权限写入，看一下基本信息，还不是DBA权限，这开发上线前做等保了吧![](https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/newemoji/Yellowdog.png "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlc4lKN2oXQPE4RzRsRQ2LIvXEteYToMbJ6T6FvA4t0D5Iiblm3jR7b3A/640?wx_fmt=png&from=appmsg "")  
  
   
   本想用sql-shell看一下能不能通过udf拿shell，结果有很多命令语句不能执行，因为他的版本是5.6，好像在mysql的5.5.3后限制了很多，当前版本默认禁止导入导出文件，udf提权也不行![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKl6ZXibyb5t9ecDxIhgoiamKC6ibX3pXWVKznwptamY1IZWq9sQzKbAEdZg/640?wx_fmt=png&from=appmsg "")  
  
  
  
    在扫到test.txt文件后，看到是充值账单，看到  
seller_email字段中收款邮箱，查了一下是企业实名的，不知道这个企业是被冒用了还是什么  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlMoFugdUvJuutHAlgQWN8IIxKv9OZZQ7DIqicfeqYXRLib79S71QicsnjQ/640?wx_fmt=png&from=appmsg "")  
  
    然后，在库中看到用户的提现和操作日志，看日期这一个库应该套壳了很多站，看提现记录都是失败，你说你充进来的钱还能提出来吗，根本不可能，提现去阿富汗啊铁铁  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOia7r6eweHx8VhBvBDZibBA1SOYGhibUpnrPS64JOw114CtUTvhh0v5FXjfvplYAMxJIz0KMmnPb8cQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlaAZYSfwYmpqq5Do2KKiaSDTCwlficauicfgBTjiaHU8fZficHkZHDE5lLkw/640?wx_fmt=png&from=appmsg "")  
  
    在user表中看到，userid为1的用户，注册地址是在越南，IP地址是27.77.xx.x，越南胡志明，但是不知道他是挂的IP还是什么，因为是动态IP，不固定，没看出来什么。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlPzqeNFVk9NqHcAvqhotXDibeosY23PJL3fxl79VXfS8dpPPDEo08P5A/640?wx_fmt=png&from=appmsg "")  
  
    
  通过user表我们还能看到他人的绑卡信息，以及余额信息，最高有充了8000，看数据库日志，好像是刚搭建的，  
有很多都像是虚假的信息，机器人用户  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlxJribEs5mukibpSteFrTJY0YKRyNcOdu8aIQUD9KX8Z8wXcgOqGE0EWA/640?wx_fmt=png&from=appmsg "")  
  
    还有两个ZFB公钥和私钥，这玩意没用过，不知道咋利用，其它的没有什么能研究的了，无非就是历史操作记录和用户一些个人信息了，没有找到admin或者有关后台的信息了，然后我就想能不能通过userid为1的用户信息，对所有框插入xss呢，想法天真了，并不执行  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKl3998IYqzj9cU66Yt06tUJAOw3tjRjtvXzpQ4sngEPgRd7Snyzy2q6A/640?wx_fmt=png&from=appmsg "")  
  
    客服端口无法发送特殊文件，因为用的某恰，而且也没法XSS，，当然了，相关获取的数据已提交给相关人员、ZFB客服，对相关账号查封等，这次唯一的遗憾没有拿到shell  
  
**0x03 总结**  
  
    总结一下：网站为境外搭建，根据域名特征，域名不固定，没有特征无法枚举，有客服域名，有前台域名，有后台管理域名，用的是同一个数据库，但是没有找到管理员后台，网站是套的模板，用的是ThinkPHP框架，模板很简单，简单到没有getshell的点(菜是原罪)  
  
    接着分析当前境外份子的常用充钱套路，避免上当  
  
![](https://mmbiz.qpic.cn/mmbiz_png/icdGEWOnYLpOibI2JOIRZ62J42Fia8xibLKlLkVHWashSWcYDmQicG5o2vrlYWw91zVY43NHkkkYMolSb973dKW50ibQ/640?wx_fmt=png&from=appmsg "")  
```
天上不会掉馅饼，掉的只会是陷阱，望守好钱袋子，不违法不犯法
```  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/icdGEWOnYLpNFmaibjiblRPm0aA0rnNUZqJvJrp9GeQ5c8bRZRxdeXJnIFRic8RGuTKycd8meXcoRibTpzMmaGrvjiag/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=webp "")  
  
