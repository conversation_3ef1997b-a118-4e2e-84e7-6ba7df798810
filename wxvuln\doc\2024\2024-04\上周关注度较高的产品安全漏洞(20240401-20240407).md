#  上周关注度较高的产品安全漏洞(20240401-20240407)   
原创 CNVD  CNVD漏洞平台   2024-04-08 18:55  
  
**一、境外厂商产品**  
**漏洞**  
  
**1、Apache Fineract SQL注入漏洞（CNVD-2024-16106）**  
  
Apache Fineract是美国阿帕奇（Apache）基金会的一套开源数字金融服务平台。该平台能够为用户提供数据管理、贷款和储蓄投资组合管理以及实时财务数据等功能。Apache Fineract 1.8.5之前版本存在SQL注入漏洞，攻击者可利用该漏洞使用sqlSearch参数发送特制的SQL语句，查看、添加、修改或删除后端数据库中的信息。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16106  
  
**2、Dell PowerScale OneFS信息泄露漏洞（CNVD-2024-16220）**  
  
Dell PowerScale OneFS是美国戴尔（Dell）公司的一个操作系统。提供横向扩展NAS的PowerScale OneFS操作系统。Dell PowerScale OneFS存在信息泄露漏洞，该漏洞源于包含使用损坏或有风险的加密算法。攻击者可利用该漏洞危及敏感信息的机密性和完整性。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16220  
  
**3、Apache InLong代码问题漏洞（CNVD-2024-16113）**  
  
Apache InLong是美国阿帕奇（Apache）基金会的一站式的海量数据集成框架。提供自动化、安全、可靠的数据传输能力。Apache InLong 1.8.0版本到1.10.0版本存在代码问题漏洞，攻击者可利用该漏洞通过发送特制的有效负载，读取系统上的任意文件。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16113  
  
**4、IBM Cognos Analytics表单跨站请求伪造漏洞**  
  
IBM Cognos Analytics是美国国际商业机器（IBM）公司的一套商业智能软件。IBM Cognos Analytics表单处理存在跨站请求伪造漏洞，远程攻击者可以利用该漏洞构建恶意URI，诱使请求，可以目标用户上下文执行恶意操作。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-15733  
  
**5、Apache Fineract权限提升漏洞**  
  
Apache Fineract是美国阿帕奇（Apache）基金会的一套开源数字金融服务平台。该平台能够为用户提供数据管理、贷款和储蓄投资组合管理以及实时财务数据等功能。Apache Fineract存在权限提升漏洞，攻击者可利用该漏洞获取任何角色的提升权限。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-16108  
  
  
**二、境内厂商产品漏洞**  
  
**1、北京亿赛通科技发展有限责任公司电子文档安全管理系统存在命令执行漏洞（CNVD-2024-14992）**  
  
北京亿赛通科技发展有限责任公司是国内数据安全、网络安全及安全服务三大业务提供商。北京亿赛通科技发展有限责任公司电子文档安全管理系统存在命令执行漏洞，攻击者可利用该漏洞获取服务器控制权。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14992  
  
**2、浙江大华技术股份有限公司智慧园区综合管理平台存在未授权访问漏洞（CNVD-2024-14380）**  
  
浙江大华技术股份有限公司是领先的监控产品供应商和解决方案服务商。浙江大华技术股份有限公司智慧园区综合管理平台存在未授权访问漏洞，攻击者可利用该漏洞未授权添加用户。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14380  
  
**3、浙江大华技术股份有限公司智慧园区综合管理平台存在信息泄露漏洞（CNVD-2024-14798）**  
  
浙江大华技术股份有限公司是领先的监控产品供应商和解决方案服务商。浙江大华技术股份有限公司智慧园区综合管理平台存在信息泄露漏洞，攻击者可利用该漏洞获取敏感信息。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14798  
  
**4、北京亚控科技发展有限公司组态王（KingView）存在拒绝服务漏洞**  
  
组态王（KingView）是北京亚控科技发展有限公司生产的一款工业自动化组态软件。北京亚控科技发展有限公司组态王（KingView）存在拒绝服务漏洞，攻击者可利用该漏洞导致软件崩溃。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-14200  
  
**5、Tenda AC10操作系统命令注入漏洞（CNVD-2024-15743）**  
  
Tenda AC10是中国腾达（Tenda）公司的一款无线路由器。Tenda AC10U 15.03.06.49版本存在操作系统命令注入漏洞，该漏洞源于/goform/WriteFacMac文件的formWriteFacMac函数的mac参数未能正确过滤构造命令特殊字符、命令等。攻击者可利用该漏洞导致任意命令执行。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2024-15743  
  
  
说明：关注度分析由CNVD根据互联网用户对CNVD漏洞信息查阅情况以及产品应用广泛情况综合评定。  
  
