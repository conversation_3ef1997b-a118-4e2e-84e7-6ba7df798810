#  CVE-2023-36025 Windows SmartScreen安全功能绕过漏洞分析   
原创 天元实验室  M01N Team   2023-12-20 18:00  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GT2ialeUNK2KkLOObHcibuv7j4SZV1sqw6ceiceushfMv1ffXjap87mL8Og/640?wx_fmt=gif&from=appmsg "")  
  
**摘要**  
  
  
CVE-2023-36025是微软于11月补丁日发布的安全更新中修复Windows SmartScreen安全功能绕过漏洞。攻击者可以通过诱导用户单击特制的URL来利用该漏洞，对目标系统进行攻击。成功利用该漏洞的攻击者能够绕过Windows Defender SmartScreen检查及其相关提示。该漏洞的攻击复杂性较低，可创建并诱导用户点击恶意设计的 Internet 快捷方式文件 (.URL) 或指向此类文件的超链接来利用该漏洞，无需特殊权限即可通过互联网进行利用。  
  
  
  
**01 漏洞复现**  
  
  
根据网上公开EXP,构造一个恶意的smb服务器，构造恶意的zip文件如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTQU2sv1SjNJzaLUm2UDSbgiaRsDpnicuGRw1luacILZVNPcGUV3MPZV7Q/640?wx_fmt=png&from=appmsg "")  
  
  
构造恶意的url文件，其中URL填入我们指定的恶意zip文件下的vbs文件。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTYojDibWiacdxVlsInKVOXQlALSibQo2iaGtgbztREiapkssZxrBTXYyyQVw/640?wx_fmt=png&from=appmsg "")  
  
  
正常情况下用户双击来自网络的可执行文件的时候，会触发Windows Defender SmartScreen的检查。然而，当点击恶意url文件，windows会首先解压恶意zip文件并放入临时目录，然后调用Wscript执行vbs文件。按常理说，虽然解压了vbs文件到本地目录，但vbs本质上还是来自于网络中的可执行文件，应该触发Windows Defender SmartScreen的检查，但事实却是没触发，这也就是漏洞所在。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTNnG23ezGozjTX2XObLWkZ9taTL6ozeBh6muPWrxtWlXzXI8wgvldOA/640?wx_fmt=png&from=appmsg "")  
  
  
**02 漏洞定位**  
  
  
在复现了该漏洞后，由于没有详细的漏洞细节披露，只能推测微软可能patch的位置，问题可能有两处：  
  
    
1. explorer在解压zip文件后没有调用SmartScreen进行检查，这种情况问题微软就可能patch在explorer解压文件，调用程序执行文件这块的执行逻辑。    
  
  
1. explorer正确解压了文件，并且在执行恶意vbs文件前正确调用了SmartScreen进行检查，只是绕过了SmartScreen执行逻辑，导致返回了安全的结果，如果是这样，patch就可能出现在smartScreen中。  
  
  
  
  
由于diff explorer与SmartScreen并不现实，这里直接使用process monitor查看diff前后的执行逻辑差别。    
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTYd6ALG9UWxAxwS64mbPTrc4Hs7fxSIZVbkLuyiadSuaibhtD4hcGjiaOQ/640?wx_fmt=png&from=appmsg "")  
  
  
可以看出问题出现在explorer,在未修复的版本中没有调用SmartScreen进行检查而是直接启动了wscript去执行我们的恶意vbs文件。    
  
接下来逻辑就比较清晰，往上追溯堆栈，寻找可能为执行逻辑相关的dll文件，然后根据堆栈调用与diff来寻找功能代码，定位漏洞点。      
  
经过枯燥的审计过程，通过对其中的堆栈进行审查，发现了几个强相关dll文件，分别为  
windows.storage.dll与zipfldr.dll。    
  
  
其中前者更多是关于explorer的各种操作的dll依赖库，而后者更专注于对压缩文件夹的处理，通过zipfldr.dll,explorer能够像处理普通文件夹一样处理ZIP文件。用户可以在其中查看、打开、复制和移动文件，就像它们是常规文件夹一样。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTMict8KVia80mcjZfe5jAQfpEPbPmPxV9T1FZYmiaGMLkzTC9HBjlT6nmA/640?wx_fmt=png&from=appmsg "")  
  
  
通过图上windows.storage.dll的调用堆栈，配合diff可以发现，漏洞出现在windows.storage.dll中的CInvokeCreateProcessVerb::ProcessCommandTemplate函数中。  
  
  
  
**03 漏洞分析与修复**  
  
  
通过对CInvokeCreateProcessVerb::ProcessCommandTemplate函数的功能审计，可以确定该函数是用来处理和执行一个命令模板。  我们关心的逻辑主要在关于CheckSmartScreenWithAltFile的验证部分。在未安装补丁版本中可以看到部分执行逻辑如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTn6LSPBvM7kZXYZf48vvmLYHk7T7eTuGX4XeSRuY6BiaTESc1CTejLTQ/640?wx_fmt=png&from=appmsg "")  
  
explorer首先调用ParamIsApp判断将要执行或者下载的文件是否带有参数，如果没有参数则调用ShouldDownloadItem判断该文件是否需要下载，如果需要下载则调用DownloadItem函数。    
  
该函数通过调用SHELL32!CLocalCopyHelper::Download实现下载后返回Shell Item对象。    
  
下载后调用CInvokeCreateProcessVerb::InitSelectedItem函数对shell item对象进行了一系列的初始化，并且检查文件路径是否恶意。    
  
最后调用CheckSmartScreenWithAltFile进行smartscreen验证。    
  
CheckSmartScreenWithAltFile中通过IsSmartScreenEnabled实现com调用到smartscreen.exe进行合法验证，如果验证失败，则调用SafeOpenPromptForShellExec弹框警告。    
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GT6np2AIp6MDpmVZ0w4vU2V0bjrFSx1J3OWl59S7Ycf9Q4bnV9VHsofA/640?wx_fmt=png&from=appmsg "")  
  
  
以上的思路似乎都没有问题，但是紧随其后的第二个逻辑就有了一些问题。也就是对ParamIsApp不满足条件的处理，由上图我们可以看出在执行的文件没有参数的情况下，我们是没办法绕过smartscreen的，可是我们通过url又没法给file路径带参数。  
  
但是当我们调试exp的时候发现，exp调用的是Wscript执行，这种就不满足ParamIsApp的处理。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTLUxmXLwmiaEAhnAaqrfKUS59eUKVsGUjreBcXJlGVNDky21cic3Oibib8Q/640?wx_fmt=png&from=appmsg "")  
  
  
如下图所示，如果执行带有参数，windows认为这个就是安全的(奇怪的回路),在几乎一套流程下调用DownloadItem函数后没有进行smartscreen验证。      
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTWXSKU1ibn9CQ2r9lanhT5vKhu6APMqB5rU1WHXgVfgfmUevqlWp1PaQ/640?wx_fmt=png&from=appmsg "")  
  
  
这种行为也许是可以理解的，因为如果我们通过`file:`去下载执行远程文件的时候，确实是无法带参数执行。但是微软确实没有考虑到如果解压到本地路径后再调用系统应用启动的情况。    
  
在补丁中，微软也是不出所料的在第二个逻辑下添加了CheckSmartScreenWithAltFile验证。  
  
  
**04 漏洞影响**  
  
  
该漏洞影响了Windows 10和Windows 11的绝大多数发行版。并且由于逻辑漏洞的特殊性，该漏洞配合社会工程学将带来一些比较大的危害。值得一提的是，TA544组织曾利用该漏洞作为攻击链的一环。   
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GT9ftOvdtCQTEgaJTXuN5Uoq4ybKeCtdp2K7rlKiaib1HAomk915DfFNaA/640?wx_fmt=png&from=appmsg "")  
  
  
可以看出该组织利用该漏洞使用file://.zip/*.vhd的方法令目标挂载了一个vhd文件，该磁盘中可能是一个白加黑文件，配合其他工具执行调用。  为了防范该漏洞，建议用户及时更新系统补丁，并且不要轻信来自陌生人的邮件或链接。同时，也要注意检查系统中是否存在异常的磁盘或文件。如果发现有可疑的活动，应立即报告给安全专家或相关部门。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTzytA8lKG7yfUFjHPWu4YCbN5BZ4F1soof523cwWkc4wA4HxVhCvhKg/640?wx_fmt=png&from=appmsg "")  
  
  
  
**绿盟科技天元实验室**专注于新型实战化攻防对抗技术研究。  
  
研究目标包括：漏洞利用技术、防御绕过技术、攻击隐匿技术、攻击持久化技术等蓝军技术，以及攻击技战术、攻击框架的研究。涵盖Web安全、终端安全、AD安全、云安全等多个技术领域的攻击技术研究，以及工业互联网、车联网等业务场景的攻击技术研究。通过研究攻击对抗技术，从攻击视角提供识别风险的方法和手段，为威胁对抗提供决策支撑。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTRhVHUDeJsVmGYyflecm3NnibiaODDTtQibueZV6TRGnbVdnkJ3ibTTWCnw/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
  
**M01N Team公众号**  
  
聚焦高级攻防对抗热点技术  
  
绿盟科技蓝军技术研究战队  
  
![](https://mmbiz.qpic.cn/mmbiz_png/TPGibEO8KBwYFZatgrlbWesvX7Sooib3GTzoNXFFicnxTrcVWWkyYiamibdzQWCRYSU2icQYcr9ncDd74HGjym7KbvBg/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
**官方攻防交流群**  
  
网络安全一手资讯  
  
攻防技术答疑解惑  
  
扫码加好友即可拉群  
  
