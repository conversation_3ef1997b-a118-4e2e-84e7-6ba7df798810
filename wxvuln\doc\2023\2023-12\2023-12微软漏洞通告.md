#  2023-12微软漏洞通告   
火绒安全  火绒安全   2023-12-13 17:36  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/0icdicRft8tz4TwribzNDjQvqsWEWszn7jyHd8ZE3L5iboJOQdYnJ2a3FSm6gZjCTOBXHbiaj743fRoviaVbdUU1ibbzw/640?wx_fmt=gif "")  
  
  
  
微软官方发布了2023年12月的安全更新。本月更新公布了42个漏洞，包含11个特权提升漏洞、8个远程执行代码漏洞、8个信息泄露漏洞、5个身份假冒漏洞、5个拒绝服务漏洞，其中4个漏洞级别为“Critical”（高危），30个为“Important”（严重）。**建议用户及时使用火绒安全软件（个人/企业）【漏洞修复】功能更新补丁。**  
  
  
**涉及组件**  
  
  
  
  
- Azure Connected Machine Agent  
  
- Azure Machine Learning Xi  
  
- Microsoft Bluetooth driver  
  
- Microsoft Dynamics  
  
- Microsoft Edge (based on Chromium)  
  
- Microsoft Office Outlook  
  
- Microsoft Office Word  
  
- Microsoft Power Platform connector  
  
- Microsoft WDAC OLE DB provider for SQL  
  
- Microsoft Windows DNS  
  
- Windows Cloud Files Mini Filter Driver  
  
- Windows Defender  
  
- Windows DHCP Server  
  
- Windows DPAPI (Data Protection Application Programming Interface)  
  
- Windows Internet Connection Sharing (ICS)  
  
- Windows Kernel  
  
- Windows kernel-mode driver  
  
- Windows Local Security Authentication Subsystem Service (LSASS)  
  
- Windows Media  
  
- Windows MSHTML platform  
  
- Windows ODBC driver  
  
- Windows Telephony Server  
  
- Windows USB mass storage-level driver  
  
- Windows Win32K  
  
- XAML Diagnostics  
  
  
  
  
(向下滑动可查看)  
  
  
**以下漏洞需特别注意**  
  
  
  
  
**Microsoft Power Platform 连接器欺骗漏洞**  
  
**CVE-2023-36019**  
  
严重级别：高危  CVSS：9.6  
  
被利用级别：有可能被利用  
  
该漏洞需要用户交互，用户必须点击将被攻击者入侵的特制 URL 才能触发此漏洞。成功利用此漏洞的攻击者可以操纵恶意链接、应用程序或文件，将其伪装成合法链接或文件以欺骗受害者。  
  
  
**Internet 连接共享 (ICS) 远程执行代码漏洞**  
  
**CVE-2023-35630/CVE-2023-35641**  
  
严重级别：高危 CVSS：8.8  
  
被利用级别：有可能被利用/很有可能被利用  
  
此攻击仅限于与攻击者所在的相同网段相连接的系统。此攻击不能跨多个网络（例如广域网）执行，只能局限于同一网络交换机或虚拟网络上的系统。在 CVE-2023-35630 中，攻击者需要修改 DHCPv6 消息中的长度字段来利用此漏洞。在 CVE-2023-35641 中，攻击者需要向运行 Internet 连接共享服务的服务器发送恶意定制的 DHCP 消息来实现。成功利用漏洞后可能导致攻击者在目标机器上远程执行任意代码。  
  
  
**Windows MSHTML 平台远程代码执行漏洞**  
  
**CVE-2023-35628**  
  
严重级别：高危 CVSS：8.1  
  
被利用级别：很有可能被利用  
  
该漏洞不需要用户交互，攻击者可以通过发送经特殊设计的电子邮件而不要求受害者打开、阅读或点击链接来利用此漏洞。成功利用漏洞后可能导致攻击者在目标计算机上远程执行任意代码。  
  
  
**Microsoft ODBC 驱动程序远程执行代码漏洞**  
  
**CVE-2023-35639**  
  
严重级别：严重 CVSS：8.8  
  
被利用级别：有可能被利用  
  
攻击者可以试图诱导经过身份验证的用户通过 OLEDB 连接到恶意 SQL 服务器，建立连接后，服务器可以向 SQL 客户端发送专门恶意创建的回复。这可能导致用户服务器接收到恶意网络数据包并允许攻击者在目标 SQL 客户端应用程序的上下文中远程执行任意代码。  
  
  
**Win32k 特权提升漏洞**  
  
**CVE-2023-35631/CVE-2023-36011**  
  
严重级别：严重 CVSS：7.8  
  
被利用级别：很有可能被利用  
  
该漏洞不需要用户交互，成功利用此漏洞的攻击者可提升受攻击系统账户的权限至 SYSTEM 权限执行任意代码。  
  
  
**修复建议**  
  
  
  
  
1、通过火绒个人版/企业版【漏洞修复】功能修复漏洞。![](https://mmbiz.qpic.cn/sz_mmbiz_gif/0icdicRft8tz6JM0bptCnP8Sz8m18FwTnjxUJY6JTobUFiaCTgVZUm6zWLqATm8sWX3XFjh7vcmFh0k0M6RQpHNXw/640?wx_fmt=png "")  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/0icdicRft8tz6gB6VJib4MjxQXeibichSibdp1amnG0W1p5XECjhLibNBvbH253HHwNDa15HMac0UQv3oEjGiaPa7ORSpQ/640?wx_fmt=png&from=appmsg "企业微信截图_17000193007166.png")  
  
  
2、下载微软官方提供的补丁  
  
https://msrc.microsoft.com/update-guide  
  
  
完整微软通告：  
  
https://msrc.microsoft.com/update-guide/releaseNote/2023-Dec  
  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/0icdicRft8tz6JM0bptCnP8Sz8m18FwTnjZpNfzpuBuP9vsELIIuagZVLlKrRGvIhbOXNdf22pUC76WE0yxGUAmQ/640?wx_fmt=jpeg "公众号二维码.jpg")  
  
关注公众号  
  
了解更多安全干货、资讯、以及火绒安全大事记  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/0icdicRft8tz6JM0bptCnP8Sz8m18FwTnjOprnsQkCP3xLsgP9HxZFzn0NWTV2ibIGOv63o5WibmhqUgYF46mJgAzA/640?wx_fmt=gif "")  
  
转发，点赞，在看，安排一下？  
  
  
