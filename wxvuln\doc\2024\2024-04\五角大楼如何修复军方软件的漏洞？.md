#  五角大楼如何修复军方软件的漏洞？   
 网络安全应急技术国家工程中心   2024-04-09 14:59  
  
美国军方使用的太多应用程序都是基于充满漏洞的代码构建，并且由五角大楼本身分发。可以肯定的是，软件增强军事行动能力的宣传听越来很诱人，但它也隐藏了太多危险的安全问题。    
  
如果软件要更多地带来好处而不是带来负担，那么就必须发现并修复其不可避免的缺陷，然后才能被俄罗斯和其他外国对手利用。不幸的是，在对五角大楼提供给其单位和承包商使用的流行开源软件进行的分析中，有强有力的证据表明，美国军方正在交付的软件不安全，并且包含许多已知的软件漏洞-CVE。  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/0KRmt3K30icW4oQS3icvuhJrqV0CG0k4GC7PY3yVLAjuMCTySQRRMTtsp6zL63NOMibBw4M2ic3JPOtnXxFP24Y28w/640?wx_fmt=jpeg&from=appmsg&wxfrom=13&tp=wxpic "")  
  
幸运的是，美国军方、民选领导人和公众不需要正常接受这种情况。有一些技术和组织解决方案可以让军队安全地使用软件。创建安全、免劳的软件至少需要重新考虑军队软件供应链中的各个环节，并优先选择快速更新的软件。它还需要重新考虑应该有一个单一的、免费的军事运行的安全软件存储库的想法。软件行业喜欢“单一事实来源”的想法，但这种极权主义思维（军事官僚有时也喜欢）在快速发展的软件世界中是灾难的根源。  
  
未被承认的软肋  
  
与美国企业界一样，军队也热情拥抱数字化转型。现在有数十家军事“软件工厂”。军事承包商希望成为“软件巨头”。大西洋理事会最近甚至成立了软件定义战争委员会。  
  
乍一看，这种“软件定义一切”的心态是合理且健康的。计算机和软件在现代军事组织中的普遍存在是不可否认的。五角大楼曾经是世界上最大的办公楼，如今已成为数字化的沃土，电脑和电缆无处不在。指挥中心主要是屏幕和数据库。此外，对生成式人工智能的强烈兴趣只会进一步将军事行动与软件结合在一起。确实，美国军方怎么可能不拥抱软件和软件开发呢？  
  
![](https://mmbiz.qpic.cn/mmbiz_png/0KRmt3K30icW4oQS3icvuhJrqV0CG0k4GCSbGseyL36Sc0rV5TlGvJ3xoIdIlzy4X7pq4ibSJRPQQKDA4Syj0fkqw/640?wx_fmt=png&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
但军队普遍使用软件也有其不利的一面。不幸的事实是，大多数软件都充满安全缺陷——不仅仅是“零日”或未被发现的安全缺陷，还包括已知的安全漏洞。每年发现的已知漏洞数量正在迅速增长：从2015年的约6,000个增加到2023年的近29,000个。重要的是，其中许多缺陷都存在于软件组件中：通常广泛使用和重用的代码块，以构建整个系统和应用程序。  
  
使用具有已知漏洞的软件至少存在两个危险。  
  
首先是敌人可能利用它们来收集情报并阻碍美国的军事行动。事实上，最近的软件安全研究表明，敌对军队和情报机构的大多数活动都是利用已知缺陷，而不是零日漏洞。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/0KRmt3K30icW4oQS3icvuhJrqV0CG0k4GCcFoCiccFl4ia8q8bdu6kh3BFDZgVnygDIeFkh06iaicACSj6VFxdFDI99Q/640?wx_fmt=png&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
第二个是让军事软件组织陷入与识别、分类和修复已知漏洞以满足合规性和安全性要求相关的繁重工作中。作者和一位同事采访十个组织的软件专业人员时，发现许多现代软件组织每年花费数千个小时进行漏洞管理是很常见的。他们采访过的一个美国军事单位可能每年花费15,000小时的人员时间进行漏洞管理。这是所谓数字化转型的一个未被承认的弱点。  
  
军方软件漏洞知多少  
  
那么，军事软件中已知的软件漏洞有多常见？   
  
为了回答这个问题，分析师最好随机抽取美国军方使用或开发的软件样本并进行分析。不幸的是，撇开保密问题不谈，没有这样的已知数据集。  
  
然而，有一个了解美国军事软件的好窗口：美国空军通过其Iron Bank（铁库）网站提供软件组件。这些组件是“乐高积木”，借用空军前首席软件官的术语，军事软件团队和军事承包商可以使用它们来构建和部署软件应用程序。用技术术语来说，它们是软件“容器”，是将开源软件组件和自定义代码捆绑到更大的软件应用程序中的有效方法。为了方便研究人员，铁库中的容器可供任何人免费检查和分析。   
  
![](https://mmbiz.qpic.cn/mmbiz_png/0KRmt3K30icW4oQS3icvuhJrqV0CG0k4GCOLjXkKgHD8sZqsh0CtficBicmTYus2Z5srYM2HYqb9uPBupA3AzUk6dw/640?wx_fmt=png&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
Iron Bank中一套特别受欢迎且重要的“乐高积木”是构成军方Big Bang平台的容器，即“盒子里的软件工厂”。对构成该平台大部分的容器的分析发现，它们平均包含57个已知漏洞。每个漏洞至少会给负责评估软件的软件安全专家带来麻烦，更令人不安的是，还带来被恶意利用的风险。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/0KRmt3K30icW4oQS3icvuhJrqV0CG0k4GC6HzRakHYICbd9duGrAMNEBgZykO3OWiaIlN0q9tRQJ6YIhib487t5BJQ/640?wx_fmt=png&from=appmsg&tp=wxpic&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
软件安全专家会指出，并非所有软件漏洞都是一样的。有些确实比其他更严重，给用户带来了更高的风险。因此，该分析还考虑了这些Iron Bank图像是否也包含严重或高严重性漏洞。平均而言，每个容器至少有一个。  
  
漏洞可以修复吗？  
  
理想情况下，美国军方不会向自己的部队和承包商提供充满已知漏洞的软件构建块。  
  
解决这个问题的第一种方法是技术性的。军方应优先采购可快速更新的软件，以便在不可避免地出现新漏洞时能够快速修复。传统的开源软件供应商，包括Red Hat Enterprise Linux等Linux发行版和Debian等社区运行的发行版，并不以提供稳定软件的名义优先考虑快速更新。Alpine和Wolfi等相对较新的Linux发行版提供了这种快速的软件更新，同时尝试提供稳定性。Iron Bank容器镜像最近才开始“基于”Alpine和Wolfi。这种趋势应该会持续下去。  
  
第二种方法是组织方法。例如，Iron Bank目前只向其用户提供一种价格：免费。虽然这看起来“成本低廉”，但软件公司没有动力向Iron Bank提供安全和高质量的组件，因为它们无法收费。如果Iron Bank创建了“私有”存储库，允许公司对高质量软件构建块的访问进行收费，那么Iron Bank映像的已知漏洞可能会大大减少。   
  
但也许铁库本身就是一个问题。目前尚不清楚美国军方在维护强化容器镜像存储库方面是否具有相对优势。现在有很多公司竞相这样做。军方并不生产自己的笔记本电脑；他们自己生产笔记本电脑。也许它不应该构建自己的强化容器镜像存储库。  
  
从这个分析来看，军队和软件行业太相似了。两者都太喜欢缩写词，并且都发布了带有太多已知漏洞的软件。希望利用数字化转型的国防领导人不应该等到他们的软件变得FUBAR了。  
  
作者简介 ：  
  
John Speed Meyers是Chainguard实验室的负责人。他领导着一个专注于开源软件安全的研究团队。他此前曾在In-Q-Tel和兰德公司工作。  
  
**参考资源：**  
  
1.https://www.defenseone.com/ideas/2024/04/how-fix-militarys-software-snafu/395489/  
  
2.https://s3.amazonaws.com/media.hudson.org/Software+Defines+Tactics.pdf  
  
3.https://p1.dso.mil/who-we-are  
  
原文来源  
：网空闲话plus  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
