#  【风险通告】XZ-Utils 5.6.0/5.6.1版本后门风险（CVE-2024-3094）   
风险通告  阿里云应急响应   2024-04-01 13:58  
  
2024年3月29日，安全社区披露 CVE-2024-3094 XZ-Utils 5.6.0/5.6.1版本后门风险。  
  
01  
  
风险描述  
  
  
XZ-Utils是Unix等操作系统中广泛用于处理.xz文件的套件，包含liblzma、xz等组件，已集成在Debian、Ubuntu、Centos等发行版仓库中。  
  
2024年3月29日，安全社区披露其存在 CVE-2024-3094 XZ-Utils 5.6.0-5.6.1版本后门风险。该后门存在于XZ Utils的5.6.0和5.6.1版本中，由于SSH底层依赖了liblzma等，攻击者可能利用这一后门在受影响的系统上绕过SSH的认证获得未授权访问权限，执行任意代码。   
  
经排查，企业主流使用的Linux发行版（例如Red Hat、CentOS、Debian、Ubuntu）的Stable稳定版未合并存在后门的版本，因此不受影响。建议客户根据实际情况进行排查。  
  
  
02  
  
风险评级  
  
  
CVE-2024-3094 XZ-Utils 5.6.0/5.6.1版本后门风险 严重  
  
  
03  
  
后门版本  
  
  
XZ Utils 5.6.0  
  
XZ Utils 5.6.1   
  
liblzma 5.6.0   
  
liblzma 5.6.1  
  
  
04  
  
安全建议  
  
  
1、可使用云安全中心-主机资产-中间件功能查询进程所使用依赖的liblzma版本 或者 手动运行 xz -V 或 xz -version 获取系统安装的xz/liblzma版本。   
  
2、若使用了受影响版本xz/  
liblzma的操作系统（Fedora 41、Debian sid 测试版等）建议更新至安全版本。具体详情可在查询 https://repology.org/project/xz/versions  
  
  
05  
  
时间线  
  
  
3月29日 安全社区披露   
CVE-2024-3094 XZ-Utils 5.6.0/5.6.1版本后门风险  
  
3月30日 阿里云云安全中心支持   
liblzma资产检索、漏洞检测、后门查杀与告警  
  
  
06  
  
相关链接  
  
  
https://www.openwall.com/lists/oss-security/2024/03/29/4  
  
https://avd.aliyun.com/detail?id=AVD-2024-3094  
  
  
