#  漏洞通告 | 金蝶Apusic应用服务器远程代码执行漏洞   
原创 微步情报局  微步在线研究响应中心   2023-12-19 16:59  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMKNkm4Pg1Ed6nv0proxQLEKJ2CUCIficfAwKfClJ84puialc9eER0oaibMn1FDUpibeK1t1YvgZcLYl3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
01 漏洞概况****  
  
  
  
金蝶Apusic应用服务  
器（Kingdee Apusic Application Server）是金蝶软件推出的一款企业级Java应用服务器。它提供了一个稳定、高效、可扩展的运行环境，用于部署和管理企业级Java应用程序。  
  
  
微步漏洞团队监测到金蝶官方12月7日发布的漏洞修复处理说明修复了“X 漏洞奖励计划”收录的一处金蝶Apusic应用服务器远程代码执行漏洞。金蝶Apusic应用服务器的管控台存在访问路径权限控制失效、对参数校验不严格的问题。攻击者可以构建绕过权限控制的请求，恶意访问和操作管控台，导致安全风险，配合其他安全缺陷即可导致远程代码执行。  
经分析与研判，该漏洞利用难度低，攻击者可利用该漏洞控制服务器，可能会造成敏感数据泄露、被勒索等业务风险，建议尽快修复。  
  
02 漏洞处置优先级（VPT）  
  
  
  
**综合处置优先级：**  
**高**  
****  
  
<table><tbody style="visibility: visible;"><tr style="height: 23.3pt;visibility: visible;"><td width="123" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);"><strong style="visibility: visible;">漏洞编号</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">微步编号</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="color: rgb(84, 84, 84);"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">XVE-2023-</span><span style="font-family: 微软雅黑;font-size: 10.5pt;letter-spacing: 0pt;text-indent: 21pt;">7578</span></span></p><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);color: rgb(84, 84, 84);"></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="143" valign="top" rowspan="6" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);"><strong style="visibility: visible;">漏洞评估</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">危害评级</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="color: rgb(84, 84, 84);"><strong style="visibility: visible;"><span style="color: rgb(84, 84, 84);font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;">高危</span></strong></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">漏洞类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;visibility: visible;"><span style="color: rgb(84, 84, 84);font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;">RCE<br style="visibility: visible;"/></span></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">公开程度</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(84, 84, 84);">PoC未公开</span></p></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">利用条件</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;visibility: visible;"><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(84, 84, 84);">无权限要求</span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><br/></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><br/></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">交互要求</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;color: rgb(84, 84, 84);">0-click</span></p></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;color: rgb(84, 84, 84);">威胁类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;color: rgb(84, 84, 84);">远程</span></p></td></tr><tr style="height:26.0500pt;"><td width="143" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;color: rgb(84, 84, 84);"><strong>利用情报</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;color: rgb(84, 84, 84);">微步已捕获攻击行为</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;color: rgb(84, 84, 84);">是</span></p></td></tr></tbody></table>  
  
### 03 漏洞影响范围 产品名称金蝶Apusic应用服务器受影响版本V9.0 SP7及以下版本影响范围千级有无修复补丁有前往X情报社区资产测绘查看影响资产详情：https://x.threatbook.com/v5/survey?q=app%3DApusic  
  
### 04 漏洞复现 05 修复方案 1. 官方修复方案：官方已发布修复方案，已经在最新的V9.0 SP8版本解决：https://www.apusic.com/view-477-113.html2. 临时修复方案：1） 使用防护类设备，对url中存在“//”特征的恶意请求进行重点监控2） 如非必要，避免将资产暴露在互联网或使用网络ACL限制访问来源3）不影响业务的情况下，暂停应用服务器管理控制台和移除默认首页，操作步骤如下：停止应用服务器后，移除如下文件(webtool.war或admin.war、index.jsp)普通的管理控制台的安装文件: <安装目录>\lib\webtool.war安全管理控制台的安全文件: <安装目录>\lib\admin.war首页在<应用服务器安装目录>\domains\mydomain\applications\ default\public_html\index.jsp06 微步产品侧支持情况  微步威胁感知平台TDP已支持检测，规则ID为 S3100128814、S310012881707 时间线 2023.04 微步“X漏洞奖励计划”获取该漏洞相关情报 2023.04 微步威胁感知平台TDP支持检测2023.12 厂商发布漏洞修复处理说明2023.12 微步发布报告- End -微步漏洞情报订阅服务微步提供漏洞情报订阅服务，精准、高效助力企业漏洞运营提供高价值漏洞情报，具备及时、准确、全面和可操作性，帮助企业高效应对漏洞应急与日常运营难题；可实现对高威胁漏洞提前掌握，以最快的效率解决信息差问题，缩短漏洞运营MTTR；提供漏洞完整的技术细节，更贴近用户漏洞处置的落地；将漏洞与威胁事件库、APT组织和黑产团伙攻击大数据、网络空间测绘等结合，对漏洞的实际风险进行持续动态更新。扫码在线沟通↓↓↓点此电话咨询X 漏洞奖励计划“X漏洞奖励计划”是微步X情报社区推出的一款针对未公开漏洞的奖励计划，我们鼓励白帽子提交挖掘到的0day漏洞，并给予白帽子可观的奖励。我们期望通过该计划与白帽子共同努力，提升0day防御能力，守护数字世界安全。活动详情：https://x.threatbook.com/v5/vulReward  
  
