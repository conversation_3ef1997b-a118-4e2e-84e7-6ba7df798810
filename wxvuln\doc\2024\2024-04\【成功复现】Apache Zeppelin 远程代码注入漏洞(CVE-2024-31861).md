#  【成功复现】Apache Zeppelin 远程代码注入漏洞(CVE-2024-31861)   
弥天安全实验室  弥天安全实验室   2024-04-16 20:39  
  
网安引领时代，弥天点亮未来     
  
  
  
  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDCVZx96ZMibcJI8GEwNnAyx4yiavy2qelCaTeSAibEeFrVtpyibBCicjbzwDkmBJDj9xBWJ6ff10OTQ2w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
**0x00写在前面**  
  
  
**本次测试仅供学习使用，如若非法他用，与平台和本文作者无关，需自行负责！**  
0x01漏洞介绍Apache Zeppelin是美国阿帕奇（Apache）基金会的一款基于Web的开源笔记本应用程序。该程序支持交互式数据分析和协作文档。Apache Zeppelin 0.10.1版本至0.11.1之前版本存在代码注入漏洞，该漏洞源于代码生成控制不当。0x02影响版本0.10.1 <= Apache Zeppelin < 0.11.10x03漏洞复现  
1.访问漏洞环境   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDYHCreF3Nwt86GzOhkt3RIsneMkWkXdgPxoo0w1Jq9vc1FB1eK1xSjscpwicVA1QWRWPC11cQJ9oQ/640?wx_fmt=png&from=appmsg "")  
  
2.对漏洞进行复现创建文件，默认解释器选择：sh成功后，进行id命令执行操作0x04修复建议目前厂商已发布升级补丁以修复漏洞，补丁获取链接：https://github.com/apache/zeppelin/releaseshttps://zone.huoxian.cn/d/2902-apache-zeppelin-shell-cve-2024-31861弥天简介学海浩茫，予以风动，必降弥天之润！弥天安全实验室成立于2019年2月19日，主要研究安全防守溯源、威胁狩猎、漏洞复现、工具分享等不同领域。目前主要力量为民间白帽子，也是民间组织。主要以技术共享、交流等不断赋能自己，赋能安全圈，为网络安全发展贡献自己的微薄之力。口号 网安引领时代，弥天点亮未来 知识分享完了喜欢别忘了关注我们哦~学海浩茫，予以风动，必降弥天之润！   弥  天安全实验室  
  
