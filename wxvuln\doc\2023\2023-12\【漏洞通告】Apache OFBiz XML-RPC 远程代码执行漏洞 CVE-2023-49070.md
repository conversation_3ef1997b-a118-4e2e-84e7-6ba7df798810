#  【漏洞通告】Apache OFBiz XML-RPC 远程代码执行漏洞 CVE-2023-49070   
深瞳漏洞实验室  深信服千里目安全技术中心   2023-12-06 17:21  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOgI6fYClPespQAF24uUJJDyEn4va6PCC6ffUZAialhH9XrEVj0enQibzA/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
Apache OFBiz XML-RPC 远程代码执行漏洞（CVE-2023-49070）  
  
**组件名称：**  
  
Apache OFBiz  
  
**影响范围：**  
  
Apache OFBiz < 18.12.10  
  
**漏洞类型：**  
  
远程代码执行  
  
**利用条件：**  
  
1、用户认证：否  
  
2、前置条件：默认配置  
  
3、触发方式：远程  
  
**综合评价：**  
  
<综合评定利用难度>：容易，无需授权即可远程代码执行。  
  
<综合评定威胁等级>：高危，能造成远程代码执行。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOB1iarAlNBqhCYHStYRTDXxWjzMzUGwx8PoSTIJegj7ktXH2nIzMcEYw/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
Apache OFBiz是一套足够灵活的业务应用程序，可以在任何行业中使用。通用架构允许开发人员轻松扩展或增强它以创建自定义功能。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOB1iarAlNBqhCYHStYRTDXxWjzMzUGwx8PoSTIJegj7ktXH2nIzMcEYw/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2023年12月6日，深瞳漏洞实验室监测到一则Apache OFBiz组件存在远程代码执行漏洞的信息，漏洞编号：CVE-2023-49070，漏洞威胁等级：高危。  
  
该漏洞是由于在小于18.12.10版本的Apache OFBiz中对于之前的XML-RPC代码执行漏洞修复不当导致，**攻击者可利用该漏洞在未授权的情况下，构造恶意数据执行代码执行攻击，最终获取服务器权限从而执行任意命令。**  
  
  
**影响范围**  
  
目前受影响的Apache OFBiz版本：  
  
Apache OFBiz < 18.12.10  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOB1iarAlNBqhCYHStYRTDXxWjzMzUGwx8PoSTIJegj7ktXH2nIzMcEYw/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
当前官方已发布最新版本，建议受影响的用户及时更新升级到最新版本。链接如下：  
  
https://ofbiz.apache.org/download  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOB1iarAlNBqhCYHStYRTDXxWjzMzUGwx8PoSTIJegj7ktXH2nIzMcEYw/640?wx_fmt=gif&from=appmsg "")  
  
**深信服解决方案**  
  
  
**1.风险资产发现**  
  
支持对 Apache OFBiz 的主动检测，可**批量检出**业务场景中该事件的受影响资产情况，相关产品如下：  
  
**【深信服主机安全检测响应平台CWPP】**已发布资产检测方案。  
  
**【深信服云镜YJ】**已发布资产检测方案。  
  
  
**2.漏洞主动扫描**  
  
支持对 Apache OFBiz XML-RPC 远程代码执行漏洞（CVE-2023-49070）  
的主动扫描，可**批量快速检出**  
业务场景中是否存在**漏洞风险**  
，相关产品如下：  
  
**【深信服云镜YJ】**预计2023年12月10日发布扫描方案。  
  
**【深信服漏洞评估工具TSS】**预计2023年12月11日发布扫描方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月11日发布扫描方案，（需要具备TSS或CWPP组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月10日发布扫描方案，（需要具备云镜或CWPP组件能力）。  
  
  
**3.漏洞安全监测**  
  
支持对 Apache OFBiz XML-RPC 远程代码执行漏洞（CVE-2023-49070）  
的监测，可依据流量收集**实时监控**  
业务场景中的**受影响资产情况，快速检查受影响范围**  
，相关产品及服务如下：  
  
**【深信服安全感知管理平台SIP】**预计2023年12月11日发布检测方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月11日发布检测方案，（需要具备SIP组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月11日发布检测方案。  
  
  
**4.漏洞安全防护**  
  
支持对 Apache OFBiz XML-RPC 远程代码执行漏洞（CVE-2023-49070）的防御，可**阻断攻击者针对该事件的入侵行为**，相关产品及服务如下：  
  
**【深信服下一代防火墙AF】**预计2023年12月11日发布防护方案。  
  
**【深信服Web应用防火墙WAF】**预计2023年12月11日发布防护方案。  
  
**【深信服安全托管服务MSS】**预计2023年12月11日发布防护方案，（需要具备AF组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2023年12月11日发布防护方案，（需要具备AF组件能力）。  
  
  
**参考链接**  
  
  
https://www.openwall.com/lists/oss-security/2023/12/04/2  
  
  
**时间轴**  
  
  
  
**2023/12/6**  
  
深瞳漏洞实验室监测到Apache OFBiz官方发布新版本。  
  
  
**2023/12/6**  
  
深瞳漏洞实验室发布漏洞通告。  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMONanOu3eotlzUKI59Wj4ynCjmXJyz1t8koU0pw5DibXOZ7P51xeyUxUQ/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5w9fwiaWZZCum6xqQfB15BMOODicpkS6XCeYNmNxDibicmykOgPBxjia4AgBsBiaHJ5WrkkuyCYc7zdfOlw/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
