#  Burpsuite 与 Chatgpt 联动，AI自动化漏洞分析   
原创 人遁安全  宇宙最强黑客八嘎酱   2024-01-21 08:48  
  
# 也不知斗转星移,日夜几何。往日之人已不记得往日之事，曾经种种也只余雪泥鸿爪，暮雪残阳。兜兜转转几世轮回，恍惚之间却又回到那宏伟的殿堂，背持双戟的少年讷。。。。。===摘自小说评论  
# 前言  
  
📌 随着人工智能和自然语言处理技术的不断发展，AI & GPT（生成式预训练模型）在安全测试领域的应用越来越受关注。这些工具利用AI的强大能力，能够自动生成测试用例、识别安全漏洞和评估系统的安全性。本文将介绍基于AI & GPT的安全测试工具的概念和应用，以及它们在软件开发和网络安全中的重要性。  
  
基于 AI & GPT 的安全测试工具在软件开发和网络安全中具有广泛的应用。它们可以帮助开发团队识别潜在的安全漏洞、评估系统的安全性，并提供改进建议。以下是一些常见的应用场景：自动化测试，安全漏洞识别，安全补丁自动化，异常检测，AI 模型部署安全。  
# BurpGPT  
  
🔫 Burp Suite 扩展集成了 OpenAI 的 GPT，可以执行额外的被动扫描以发现高度定制的漏洞，并支持运行任何类型的基于流量的分析。  
  
项目地址：https://github.com/aress31/burpgpt  
> ❝  
> burpgpt 利用 AI 的强大功能来检测传统扫描程序可能遗漏的安全漏洞。它将网络流量发送到用户指定的 OpenAI model ，从而在被动扫描器中实现复杂的分析。此扩展提供可自定义的 prompts ，可以进行定制的网络流量分析，以满足每个用户的特定需求。查看示例用例部分以获得灵感。  
> ❞  
  
  
该扩展程序会生成一份自动安全报告，该报告根据用户的 prompt 和来自 Burp 发出的请求的实时数据总结潜在的安全问题。通过利用 AI 和自然语言处理，该扩展简化了安全评估流程，并为安全专业人员提供了扫描的应用程序或端点的更高级别的概述。这使他们能够更轻松地识别潜在的安全问题并确定分析的优先级，同时覆盖更大的潜在攻击面。  
> ❝  
> **「安装」**  
> ❞  
  
- git clone https://github.com/aress31/burpgpt 克隆项目到本地  
  
- gradle shadowJar 编译生成 jar 文件，文件在目录：lib/build/libs/  
  
- 随后打开burpsuite，使用Extensions add 选择目录地址添加插件  
  
- ok  
  
> ❝  
> **「使用」**  
> ❞  
  
- 在 burpsuite 工具栏里面会出现一个新的 Burpgpt 栏目，点击设置 API，模型，设置字段长度，设置自定义提示  
  
- 在 proxy 历史里面或者选择一个请求右键，点击 Do passive scan ，会自动使用 Burpgpt 检测扫描请求的安全漏洞，会自动生成报告。  
  
- 随后会自动调用 gpt 中的分析模型，对请求以及响应进行漏洞自动化分析  
  
> ❝  
> **「测试」**  
> ❞  
  
- 本地搭建 Dvwa 靶场进行测试。  
  
- 对靶场中各个请求使用 Burpgpt 调用 gpt 进行自动化被动扫描漏洞分析，会自动生成报告。  
  
> ❝  
> **「总结」**  
> ❞  
  
- 该程序有社区版，以及专业版，和 burpsuite 一样，专业版需要付费，大家自行测试后，交流讨论。  
  
> ❝  
> **「参考地址」**  
> ❞  
  
- https://github.com/aress31/burpgpt  
  
- https://burpgpt.app/  
  
  
  
