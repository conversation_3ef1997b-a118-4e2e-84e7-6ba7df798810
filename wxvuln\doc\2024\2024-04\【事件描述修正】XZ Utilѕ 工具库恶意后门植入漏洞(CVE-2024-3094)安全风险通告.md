#  【事件描述修正】XZ Utilѕ 工具库恶意后门植入漏洞(CVE-2024-3094)安全风险通告   
 奇安信 CERT   2024-04-01 00:01  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="center" rowspan="1" colspan="4" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1.5em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 255, 255);letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">漏洞概述</span></strong><br style="outline: 0px;visibility: visible;"/></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;caret-color: red;letter-spacing: 0px;visibility: visible;">XZ Utilѕ 工具库恶意后门植入漏洞</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" rowspan="1" colspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">漏洞编号</strong></span></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-family: &#34;Helvetica Neue&#34;, Helvetica, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei&#34;, 微软雅黑, Arial, sans-serif;letter-spacing: 0.578px;text-decoration-style: solid;text-decoration-color: rgb(0, 0, 0);visibility: visible;">QVD-2024-11691</span>,CVE-2024-3094</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">2024-03-29</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">影响量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">万级</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);font-size: 17px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">奇安信评级</span></strong></span></p></td><td valign="middle" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高危</span></strong></span></p></td><td valign="middle" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">CVSS 3.1分数</span></strong></p></td><td valign="middle" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">10.0</strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">威胁类型</strong></span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">供应链攻击、后门</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">利用可能性</span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;caret-color: rgb(255, 0, 0);color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;">高</strong></span></strong></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已公开</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已发现</span></strong><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"></span></span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已公开</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"><strong style="outline: 0px;letter-spacing: 0.544px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">部分公开</span></strong></span></span><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);letter-spacing: 0.544px;visibility: visible;"></span></span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="4" rowspan="1" align="left" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">危害描述：</span></strong><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;text-align: justify;visibility: visible;">3月29日有开发人员在安全邮件列表上发帖称，他在调查SSH性能问题时发现了涉及XZ包中的供应链攻击，进一步溯源发现SSH使用的上游liblzma库被植入了后门代码，当满足一定条件时，将会解密流量里的C2命令并执行。</span></p><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;text-align: justify;visibility: visible;">存在后门的liblzma会主动劫持sshd got表内的RSA_public_decrypt函数到Llzma_index_prealloc (systemd为sshd添加依赖liblzma)，之后在传递给RSA_public_decrypt的N值（公钥）中提取有效载荷，并对指纹进行校验，在Ed448签名验证之前，使用固定的ChaCha20密钥进行解密，满足条件后将执行C2命令。如果有效载荷格式错误或来自攻击者密钥的签名无法验证，后门将恢复到常规操作。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响组件**  
  
XZ是一种高压缩比的数据压缩格式，由Tukaani项目开发，几乎存在于每个Linux发行版中，无论是社区项目还是商业产品发行版。它帮助将大文件格式压缩（然后解压缩）为更小、更易管理的大小，以便通过文件传输进行共享。liblzma是一个用于处理XZ压缩格式的开源软件库。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**漏洞描述**  
  
近日，奇安信CERT监测到**XZ Utilѕ工具库恶意后门植入漏洞(CVE-2024-3094)**，3月29日有开发人员在安全邮件列表上发帖称，他在调查SSH性能问题时发现了涉及XZ包中的**供应链攻击**，进一步溯源发现SSH使用的上游liblzma库被植入了后门代码，当满足一定条件时，将会解密流量里的C2命令并执行。**目前，企业使用的主流Linux发行版（Red Hat/CentOS/Debian/Ubuntu）的Stable稳定版仓库中尚未合并该存在后门的软件版本**，**鉴于此漏洞影响范围较大，建议客户尽快做好自查及防护。**  
  
  
**>**  
**>**  
**>**  
**>**  
  
**事件经过**  
  
2021年，Jia Tan 创建GitHub账户。之后积极参与 XZ Utils 项目的维护，并逐渐获取信任，获得了直接 commit 代码的权利。2024年2月，Jia tan向 liblzma / xz项目中提交恶意文件。2024年3月28日，Ubuntu注意到一个上游的漏洞影响了xz-utils源代码包。2024年3月29日，微软PostgreSQL开发人员Andres Freund在调试SSH性能问题时，在开源安全邮件列表中称，他在xz软件包中发现了一个涉及混淆恶意代码的供应链攻击。目前 GitHub 已经关停了整个xz项目。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4ic0kgNMKzX0picAD7QIvicSX32rPia5eI82QuuqxWxicjUa9ib177D8DxSte9oQW9ibW5k8ZyEnmiboexh7Q/640?wx_fmt=png&from=appmsg "")  
  
  
  
**本次更新内容：**  
  
**事件描述修正。**  
  
  
  
**02**  
  
**影响范围**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响版本**  
  
xz == 5.6.0   
  
xz == 5.6.1  
  
liblzma== 5.6.0   
  
liblzma== 5.6.1  
  
  
**>**  
**>**  
**>**  
**>**  
  
**其他受影响组件**  
  
使用了受影响版本XZ的操作系统或软件如openSUSE、Fedora 41、Liblzma、Debian非稳定的测试版 5.5.1alpha-0.1 到 5.6.1-1  
  
详情可在此查询：  
  
https://repology.org/project/xz/versions  
  
  
**03**  
  
**验证及处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**处置建议**  
  
目前 GitHub 已经关停了整个xz项目。  
  
<table><colgroup style="outline: 0px;"><col width="147" style="outline: 0px;width: 110.25pt;"/><col width="140" style="outline: 0px;width: 105pt;"/><col width="485" style="outline: 0px;width: 363.75pt;"/><col width="284" style="outline: 0px;width: 213pt;"/></colgroup><tbody style="outline: 0px;"><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" width="70" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">操作系统</td><td width="98" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">是否受影响</td><td width="202" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">影响版本</td><td width="213" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">官方公告</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Red Hat</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Fedora</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">Fedora 41 and Fedora Rawhide</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Debian<span style="outline: 0px;"> </span>所有稳定版</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://security-tracker.debian.org/tracker/CVE-2024-3094</td></tr><tr height="64" style="outline: 0px;height: 48pt;"><td height="48" width="113" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">Debian testing,<br style="outline: 0px;"/>unstable and<br style="outline: 0px;"/>experimental<br style="outline: 0px;"/>distributions</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">5.5.1alpha-0.1（于 2024 年 2 月 1 日上传）到 5.6.1-1</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://lists.debian.org/debian-security-announce/2024/msg00057.html</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Kali Linux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">在3月26日至3月29日期间更新过的任何Kali安装</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.kali.org/blog/about-the-xz-backdoor/</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">OpenSUSE</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">Tumbleweed snapshot &lt;= 20240328</td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://news.opensuse.org/2024/03/29/xz-backdoor/</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Amazon Linux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="108" style="outline: 0px;height: 81pt;"><td height="81" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Alpine</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">5.6.0<br style="outline: 0px;"/>5.6.0-r0<br style="outline: 0px;"/>5.6.0-r1<br style="outline: 0px;"/>5.6.1<br style="outline: 0px;"/>5.6.1-r0<br style="outline: 0px;"/>5.6.1-r1</td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="32" style="outline: 0px;height: 24pt;"><td height="24" width="113" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">MACOS<br style="outline: 0px;"/>HomeBrew x64</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">MicroOS</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td x:str="" width="202" style="outline: 0px;word-break: break-all;hyphens: auto;">3月7日至3月28日期间发行</td><td style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">SUSE<span style="outline: 0px;"> <br style="outline: 0px;"/></span>全部版本</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;">否</td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://www.suse.com/security/cve/CVE-2024-3094.html</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">archlinux</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://security.archlinux.org/CVE-2024-3094</td></tr><tr height="19" style="outline: 0px;height: 14.25pt;"><td height="14" x:str="" align="center" valign="top" width="3" style="outline: 0px;word-break: break-all;hyphens: auto;">Alpine edge</td><td x:str="" width="190" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;"><span style="outline: 0px;color: rgb(255, 0, 0);">是</span></strong></td><td width="202" style="outline: 0px;word-break: break-all;hyphens: auto;"><br/></td><td x:str="" style="outline: 0px;word-break: break-all;hyphens: auto;">https://pkgs.alpinelinux.org/package/edge/main/x86/xz</td></tr></tbody></table>  
可据此查看受影响的开源操作系统 https://repology.org/project/xz/versions  
自查脚本：  
```
#! /bin/bash
set -eu
# find path to liblzma used by sshd
path="$(ldd $(which sshd) | grep liblzma | grep -o '/[^ ]*')"


# does it even exist?
if [ "$path" == "" ]
then
  echo probably not vulnerable
  exit
fi


# check for function signature
if hexdump -ve '1/1 "%.2x"' "$path" | grep -q f30f1efa554889f54c89ce5389fb81e7000000804883ec28488954241848894c2410
then
  echo probably vulnerable
else
  echo probably not vulnerable
fi
```  
  
  
也可通过如下命令查看系统本地是否安装了受影响的XZ：  
  
$ xz --version  
  
xz (XZ Utils) 5.6.1  
  
iblzma 5.6.1  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icsuABj6MaiaW02vfdUO92UudCICqvNuRDPFl8tLVZdCqHt66OhIBk1lTNqxbxxPmne2UtAicYWJQqA/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
目前官方尚无最新版本，需对软件版本进行降级5.4.X，请关注官方新版本发布并及时更新。  
  
Fedora Linux 40 用户需 xz 回退到 5.4.x 版本可参考：  
  
https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users  
  
https://bodhi.fedoraproject.org/updates/FEDORA-2024-d02c7bb266  
  
  
**>**  
**>**  
**>**  
**>**  
  
**测绘分析**  
  
**奇安信天问xz/liblzma后门影响全网软件测绘分析**  
  
奇安信技术研究院[“天问”软件供应链安全监测平台]  
  
(https://tianwen.qianxin.com/)利用积累的海量软件空间测绘数据，发现开源生态中的若干软件存在使用后门组件的情况，其他系统、软件和固件上暂未发现直接使用后门组件的情况。  
  
具体地，我们的测绘分析发现crates.io中的liblzma-sys包使用了受后门影响的版本。对于其他软件以及固件，由于含有后门的两个版本的发布时间在2024年2月24日之后，目前尚未被多数软件使用，因此对现有软件的影响有限。但是，由于实际使用中历史版本的xz组件有可能升级到后门版本从而产生影响，因此，我们对xz组件的历史版本影响情况进行了全面测绘分析，以便相关人员和组织排查可能的风险，尽管这些版本没有受后门影响。  
  
详情请见：https://mp.weixin.qq.com/s/RqK0Ps-AZeCzy_uCXzRQ1Q  
  
  
**04**  
  
**部分IOC**  
  
目前，奇安信CERT已发现部分IOC，如下所示：  
<table><colgroup style="outline: 0px;"><col width="295" style="outline: 0px;width: 221.25pt;"/><col width="333" style="outline: 0px;width: 249.75pt;"/></colgroup><tbody style="outline: 0px;"><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" width="296" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;">MD5</strong></td><td width="241" x:str="" align="center" valign="top" style="outline: 0px;word-break: break-all;hyphens: auto;"><strong style="outline: 0px;">文件名</strong></td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">4f0cf1d2a2d44b75079b3ea5ed28fe54</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">x86_64-linux-gnu-liblzma.so.5.6.0</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">d26cefd934b33b174a795760fc79e6b5</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">liblzma_la-crc64-fast-5.6.1.o</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">d302c6cb2fa1c03c710fa5285651530f</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">usr/lib/liblzma.so.5</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">212ffa0b24bb7d749532425a46764433</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">00000001.liblzma_la-crc64-fast.o</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">53d82bb511b71a5d4794cf2d8a2072c1</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">liblzma.so.5.6.1</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">35028f4b5c6673d6f2e1a80f02944fb2</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">bad-3-corrupt_lzma2.xz</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">9b6c6e37b84614179a56d03da9585872</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">bad-3-corrupt_lzma2.xz</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">540c665dfcd4e5cfba5b72b4787fec4f</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">good-large_compressed.lzma</td></tr><tr height="18" style="outline: 0px;height: 13.5pt;"><td height="13" x:str="" width="68" style="outline: 0px;word-break: break-all;hyphens: auto;">89e11f41c5afcf6c641b19230dc5cdea</td><td x:str="" width="241" style="outline: 0px;word-break: break-all;hyphens: auto;">good-large_compressed.lzma</td></tr></tbody></table>  
  
  
**05**  
  
**参考资料**  
  
[1]https://www.redhat.com/en/blog/urgent-security-alert-fedora-41-and-rawhide-users  
  
[2]https://www.openwall.com/lists/oss-security/2024/03/29/10   
  
[3]https://repology.org/project/xz/versions  
  
[4]https://www.cisa.gov/news-events/alerts/2024/03/29/reported-supply-chain-compromise-affecting-xz-utils-data-compression-library-cve-2024-3094   
  
[5]https://sysdig.com/blog/cve-2024-3094-detecting-the-sshd-backdoor-in-xz-utils/  
  
[6]https://github.com/byinarie/CVE-2024-3094-info  
  
  
  
**06**  
  
**时间线**  
  
2024年3月31日，奇安信 CERT发布安全风险通告。  
  
  
  
**07**  
  
**漏洞情报服务**  
  
奇安信ALPHA威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4ibwhxJOZciaboNrAwAtzFHJ2sbuFLCpwkxvLjWomfStKrFF76vNt2IINrKUvnYqUOxcmRwpabIxDoA/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs49Igdxlxc1q7BeE5iboX6paDVVsREZWW2IdEchn6jxzl58jEzK9ac28icrV76htzjib85icjXstPS5VmQ/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到  
**ALPHA威胁分析平台**  
订阅更多漏洞信息。  
  
  
