#  主流大语言模型集体曝出训练数据泄露漏洞   
 关键基础设施安全应急响应中心   2023-12-06 15:09  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/iaz5iaQYxGogu9mmlWW761t8mEeSeNmE1vuE1U06DA4sb4iaPwfZkDicjdS7WnmAnsdaL9Uiaibib09PR56eicZ4ejfR4Q/640?wx_fmt=png&from=appmsg "")  
  
近日，安全研究人员发布的一篇论文给“百模大战”的生成式人工智能开发热潮浇了一盆冷水。研究发现，黑客可利用新的数据提取攻击方法从当今主流的大语言模型（包括开源和封闭，对齐和未对齐模型）中大规模提取训练数据。  
  
论文指出，当前绝大多数大语言模型的记忆（训练数据）可被恢复，无论该模型是否进行了所谓的“对齐”。黑客可以通过查询模型来有效提取训练数据，甚至无需事先了解训练数据集。  
  
研究者展示了如何从Pythia或GPT-Neo等开源语言模型、LLaMA或Falcon等主流半开放模型以及ChatGPT等封闭模型中提取数以GB计的训练数据。  
  
研究者指出，已有技术足以攻击未对齐的模型，对于已经对齐的ChatGPT，研究者开发了一种新的发散数据提取攻击，该攻击会导致大语言模型改变聊天机器人的内容生成方式，以比正常行为高150倍的速率疯狂输出训练数据（下图）：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvZNSOwzR1QmR9a1Qpd0BABzL0ibhYCdasrAIYiacrU3a1us712iacRtPTdlqa94KbGysDUmWBIicSvGnQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图1:发散攻击导致对齐后的chatGPT以150倍的速度输出训练数据  
  
研究者表示：发散数据提取攻击方法在实际攻击中可恢复的训练数据大大超出了事前的预期，同时也证明当前的大语言模型对齐技术并不能真正消除记忆。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvZNSOwzR1QmR9a1Qpd0BABzSOLY0TdsWQ50dYvuDbntujiaeD6A48AmlNkZLn65e5I0lzMYBIEia2yw/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
研究者利用偏差攻击提取训练数据中的隐私信息  
  
据研究者介绍，大型语言模型（LLMs）会从其训练数据集中记忆样本，可被攻击者利用提取隐私信息（上图）。先前的安全研究工作已经对开源模型记忆的训练数据总量进行了大规模研究，并且通过手动标注示记忆和非记忆样本，开发并验证了针对（相对）小型模型如GPT-2的训练数据提取攻击。  
  
在最新发布的论文中，研究者将“成员推断攻击”（用于确定数据样本是否训练数据）和数据提取攻击两种方法统一起来，对语言模型中的“可提取记忆”进行了大规模研究。  
  
研究者开发了一种可扩展方法，通过与TB级数据集比对，检测模型输出的数万亿个token的记忆内容，并对流行的开源模型（例如Pythia，GPT-Neo）和半开源模型（例如LLaMA，Falcon）进行了分析。研究者发现，无论开源还是闭源的大语言模型都无法避免新的数据提取攻击，而且参数和Tokens规模更大、性能更强劲的模型更容易受到数据提取攻击：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvZNSOwzR1QmR9a1Qpd0BABzxhBayjAGzqiczUcicj4ia2265rFKEfL2oaHibqhERV5CjlJgn87t0AfUDg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
九个开源大语言模型测试结果  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvZNSOwzR1QmR9a1Qpd0BABzmwZCzPwiamVxI2I6yrITbKH6DgP6RswlbhWpwkBsDz5JNia3utrhQq7g/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
九个半开源（训练算法和训练数据不公开）大语言模型的测试结果  
  
研究者发现，“对齐模型”也不能避免新的数据提取攻击。例如，gpt-3.5-turbo对常规数据提取攻击免疫，看上去似乎成功“忘记了”训练数据。研究者推测是因为ChatGPT已经通过RLHF进行了对齐，目的是使其成为“安全高效”的，可推向市场（生产环境）的个人聊天助手。  
  
但研究者开发了新的提示策略（仅适用于GPT3.5turbo），成功绕过了gpt-3.5-turbo的对齐技术，使其“偏离”预设的聊天机器人风格，表现得像一个基础语言模型，以典型的web文本格式大量输出文本。  
  
为了检查这些输出的文本是否是此前从互联网上采集的训练数据，研究者将几个公开可用的大型网络训练数据集合并成一个9TB大小的数据集。通过与这个数据集匹配，研究者以200美元的查询成本从ChatGPT对话中恢复了一万多个训练数据集样本。研究者粗略估计，通过更多的查询可以提取超过10倍的（训练）数据。  
  
研究者在论文中透露，在7月11日发现该漏洞后，通知了包括OPT、Falcon、Mistral和LLaMA等模型开发者，并在8月30日向OpenAI披露了其漏洞，并根据90天漏洞披露规则，于11月30日发布论文，希望能唤起业界对大语言模型数据安全和对齐挑战的关注。  
  
最后，研究者警告大语言模型应用开发者，渗透测试结果表明现有的大语言模型安全措施（模型对齐和内容记忆测试）难以发现大语言模型的隐私漏洞，更不用说那些隐藏在模型算法代码中的“休眠漏洞”。如果没有极端的安全措施，现阶段不应训练和部署涉及隐私和敏感信息的大模型应用（编者：例如医疗、法律、工程）。  
  
**论文链接：**  
  
https://arxiv.org/abs/2311.17035  
  
  
  
原文来源：GoUpSec  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/iaz5iaQYxGogvC8qicuLNlkT5ibJnwu1leQiabRVqFk4Sb3q1fqrDhicLBNAqVY4REuTetY1zBYuUdic0nVhZR4FHpAfg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
