#  【已复现】IP-guard WebServer 权限绕过漏洞   
长亭应急  黑伞安全   2024-04-16 18:02  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicT5NBr7Cw7O8585FvJdKKPoyLslX8wWzu6ERXwzh9lBwP9HsYjwso4wht2rn27GGYOHzFTNo59kgA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
IP-guard是溢信科技开发的终端安全管理软件，专注于数据保护、员工行为监控和系统管理简化。2024年4月，互联网上披露IP-guard WebServer权限绕过漏洞情报，攻击者可利用该漏洞读取配置文件，获取数据库权限。该漏洞利用简单，建议受影响的客户尽快修复漏洞。  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因由于权限验证机制中存在设计缺陷，攻击者能够规避安全验证，通过后端接口执行文件的任意读取和删除操作。漏洞影响该漏洞的成功利用允许攻击者规避安全验证，通过后端接口执行文件的任意读取和删除操作。利用这一漏洞，攻击者有可能获取数据库的配置详情，并控制整个数据库系统。影响版本 Affects 02IP-guard < 4.82.0609.0解决方案 Solution 03临时缓解方案1.  配置入侵检测系统（IDS）和入侵防御系统（IPS）以及Web应用防火墙（WAF），以识别和阻止包含这类特征的请求，从而保护系统免受此类远程命令执行攻击的影响；同时为了避免攻击者绕过安全设备实施攻击，建议尽快修复漏洞。2.  如非必要，不要将 IP-guard 放置在公网上。或通过网络ACL策略限制访问来源，例如只允许来自特定IP地址或地址段的访问请求。升级修复方案官方已发布新版本修复漏洞，建议尽快访问官网（https://www.ip-guard.net/）或联系官方售后支持获取版本升级安装包或补丁，升级至4.82.0609.0版本及以上。漏洞复现 Reproduction 04  
**产品支持**  
  
   
Support   
  
  
  
**05**  
  
  
**云图**  
：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。  
  
**洞鉴**  
：  
在引擎版本大于6.15.3_r16的产品中，默认支持该漏洞的原理检测。  
  
**雷池**  
：默认支持检测该漏洞的利用行为。  
  
**全悉**  
：默认支持检测该漏洞的利用行为。  
  
  
  
**时间线**  
  
   
Timeline   
  
  
  
**06**  
2023年11月 长亭科技收到漏洞情报2024年4月 漏洞情报公开2024年4月 长亭安全应急响应中心发布通告  
参考资料：  
  
[1].https://www.ip-guard.net/  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
  
  
  
  
  
