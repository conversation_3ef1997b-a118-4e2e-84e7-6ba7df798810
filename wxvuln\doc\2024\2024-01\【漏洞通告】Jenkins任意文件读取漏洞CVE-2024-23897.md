#  【漏洞通告】Jenkins任意文件读取漏洞CVE-2024-23897   
深瞳漏洞实验室  深信服千里目安全技术中心   2024-01-27 10:01  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUpJMZ7uw9UhnzVnFbLFick9acx8TUVZlKpiajJhay6f6LEd6ZoECwj5Ow/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
Jenkins任意文件读取漏洞（CVE-2024-23897）  
  
**组件名称：**  
  
Jenkins  
  
**影响范围：**  
  
Jenkins ≤ 2.441  
  
Jenkins ≤ LTS 2.426.2  
  
**漏洞类型：**  
  
任意文件读取  
  
**利用条件：**  
  
1、用户认证：否  
  
2、前置条件：默认配置  
  
3、触发方式：远程  
  
**综合评价：**  
  
<综合评定利用难度>：容易，无需授权即可读取任意文件。  
  
<综合评定威胁等级>：严重，能造成任意文件读取。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUY4JfSwvd1dI9lDHkCCvBicjtM0z7gwMrXkk1wpxjF3Kozs8wujQS0zA/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
Jenkins（Hudson Labs）是美国CloudBees公司的一套基于Java开发的持续集成工具。该产品主要用于监控持续的软件版本发布/测试项目和一些定时执行的任务。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUY4JfSwvd1dI9lDHkCCvBicjtM0z7gwMrXkk1wpxjF3Kozs8wujQS0zA/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2024年1月26日，深瞳漏洞实验室监测到一则Jenkins存在任意文件读取漏洞的信息，漏洞编号：CVE-2024-23897，漏洞威胁等级：严重。  
  
该漏洞是由于Jenkins受影响版本中的命令解析器的一个功能错误，**攻击者可利用该漏洞在未授权的情况下，构造恶意数据执行任意文件读取攻击，最终造成服务器敏感性信息泄露。**  
  
  
**影响范围**  
  
目前受影响的Jenkins版本：  
  
Jenkins ≤ 2.441  
  
Jenkins ≤ LTS 2.426.2  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUY4JfSwvd1dI9lDHkCCvBicjtM0z7gwMrXkk1wpxjF3Kozs8wujQS0zA/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
当前官方已发布最新版本，建议受影响的用户及时更新升级到最新版本。链接如下：  
  
https://www.jenkins.io/security/advisory/2024-01-24/  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUY4JfSwvd1dI9lDHkCCvBicjtM0z7gwMrXkk1wpxjF3Kozs8wujQS0zA/640?wx_fmt=gif&from=appmsg "")  
  
**深信服解决方案**  
  
  
**1.风险资产发现**  
  
支持对Jenkins的主动检测，可**批量检出**业务场景中该事件的受影响资产情况，相关产品如下：  
  
**【****深信服主机安全检测响应平台CWPP****】**已发布资产检测方案。  
  
**【深信服云镜YJ】**预计 2024 年 1月 27 日发布资产检测方案。  
  
**【深信服漏洞评估工具TSS】**预计 2024 年 1月 29 日发布资产检测方案。  
  
****  
**2.漏洞主动扫描**  
  
支持对Jenkins任意文件读取漏洞(CVE-2024-23897)的主动扫描，可**批量快速检出**业务场景中是否存在漏洞风险，相关产品如下：  
  
**【深信服云镜YJ】**预计 2024 年 1月 27 日发布扫描方案。  
  
**【深信服漏洞评估工具TSS】**预计 2024 年 1月 29日发布扫描方案。  
  
**【深信服安全托管服务MSS】**预计 2024 年 1月 29日发布扫描方案,（需要具备TSS组件能力）。  
  
**【深信服安全检测与响应平台XDR】**已发布扫描方案,（需要具备云镜组件能力）。  
  
****  
**3.漏洞安全监测**  
  
支持对Jenkins任意文件读取漏洞(CVE-2024-23897)的监测，可依据流量收集实时监控业务场景中的**受影响资产情况，快速检查受影响范围**，相关产品及服务如下：  
  
**【深信服安全感知管理平台SIP】**预计2024年1月31日发布监测方案。  
  
**【深信服安全托管服务MSS】**预计2024年1月31日发布监测方案（需要具备SIP组件能力）。  
  
**【深信服安全检测与响应平台XDR】**预计2024年1月31日发布监测方案。  
  
****  
**4.漏洞安全防护**  
  
支持对Jenkins任意文件读取漏洞(CVE-2024-23897)的防御，**可阻断攻击者针对该事件的入侵行为**，相关产品及服务如下：  
  
**【深信服下一代防火墙AF】**预计2024年1月31日发布防护方案。  
  
**【深信服Web应用防火墙WAF】**预计2024年1月31日发布防护方案。  
  
**【深信服安全托管服务MSS】**预计2024年1月31日发布防护方案（需要具备AF组件能力）。  
  
**【深信服安全检测与响应平台XDR】**已发布防护方案（需要具备AF组件能力）。  
  
  
**参考链接**  
  
  
https://www.jenkins.io/security/advisory/2024-01-24/  
  
  
**时间轴**  
  
  
  
**2024/1/26**  
  
深瞳漏洞实验室监测到Jenkins任意文件读取漏洞(CVE-2024-23897)攻击信息。  
  
  
**2024/1/27**  
  
深瞳漏洞实验室发布漏洞通告。  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUpPEtk8tCicuOG2ZibV0GbXeKEyKn2Xs6sXFj1d4AHvMqnkX82ROxuic6A/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5zvN6n2ziaDfy82IM1SU8WTUuZXnpKHyAia8Vg0euKTEE5MHYuoP0KmadQvesDupvqfc1Mqg9sMEZ9w/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
