#  上周关注度较高的产品安全漏洞(20231218-20231224)   
 国家互联网应急中心CNCERT   2023-12-26 14:46  
  
**一、境外厂商产品漏洞**  
  
**1、Fortinet FortiADC缓冲区溢出漏洞**  
  
Fortinet FortiADC是美国飞塔（Fortinet）公司的一款应用交付控制器。Fortinet FortiADC存在缓冲区溢出漏洞，该漏洞源于应用在处理不受信任的输入时出现边界错误。攻击者可利用该漏洞通过特制的CLI请求执行任意代码或命令。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98180  
  
**2、Adobe Dimension越界读取漏洞（CNVD-2023-98217）**  
  
Adobe Dimension是美国奥多比（Adobe）公司的是一套2D和3D合成设计工具。Adobe Dimension 3.4.10及之前版本存在越界读取漏洞，攻击者可利用该漏洞导致敏感内存泄露。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98217  
  
**3、Dell NetWorker加密问题漏洞**  
  
Dell NetWorker是美国戴尔（Dell）公司的一个应用程序。提供戴尔公司的论坛讨论功能。Dell NetWorker Virtual Edition 19.8及之前版本存在加密问题漏洞，该漏洞源于SSH组件使用了不推荐的加密算法，攻击者可利用该漏洞导致信息泄露。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-99335  
  
**4、Adobe Experience Manager跨站脚本漏洞（CNVD-2023-99431）**  
  
Adobe Experience Manager（AEM）是美国奥多比（Adobe）公司的一套可用于构建网站、移动应用程序和表单的内容管理解决方案。该方案支持移动内容管理、营销销售活动管理和多站点管理等。Adobe Experience Manager 6.5.18版本及之前版本存在安全漏洞，攻击者可利用该漏洞通过注入精心设计的有效载荷执行任意Web脚本或HTML。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-99431  
  
**5、NETGEAR WNR2000命令执行漏洞**  
  
NETGEAR WNR2000是美国网件（NETGEAR）公司的一款无线路由器。NETGEAR WNR2000 v4 1.0.0.70版本存在命令执行漏洞，该漏洞源于应用未能正确过滤构造命令特殊字符、命令等。攻击者可利用该漏洞导致任意命令执行。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-99028  
  
  
**二、境内厂商产品漏洞**  
  
**1、Huawei HarmonyOS DFR模块授权问题漏洞**  
  
Huawei HarmonyOS是中国华为（Huawei）公司的一个操作系统。提供一个基于微内核的全场景分布式操作系统。Huawei HarmonyOS存在授权问题漏洞，该漏洞源于DFR模块存在接口无权限校验。攻击者可利用该漏洞导致机密性受影响。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98179  
  
**2、Tenda W30E formResetMeshNode函数缓冲区溢出漏洞**  
  
Tenda W30E是中国腾达（Tenda）公司的一款路由器。Tenda W30E V16.01.0.12(4843)版本存在缓冲区溢出漏洞，该漏洞源于函数formResetMeshNode未能正确验证输入数据的长度大小，远程攻击者可利用该漏洞在系统上执行任意代码或者导致拒绝服务攻击。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98195  
  
**3、LMXCMS SQL注入漏洞（CNVD-2023-98192）**  
  
lmxcms（梦想cms）是中国梦想cms（lmxcms）公司的一个建站系统。LMXCMS v1.4版本存在SQL注入漏洞，该漏洞源于应用缺少对外部输入SQL语句的验证。攻击者可利用该漏洞通过TagsAction.class执行任意代码。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98192  
  
**4、Tenda AC10 compare_parentcontrol_time函数缓冲区溢出漏洞**  
  
Tenda AC10是中国腾达（Tenda）公司的一款无线路由器。Tenda AC10 US_AC10V4.0si_V16.03.10.13_cn版本存在缓冲区溢出漏洞，该漏洞源于compare_parentcontrol_time函数的time参数未能正确验证输入数据的长度大小，远程攻击者可利用该漏洞在系统上执行任意代码或者导致拒绝服务攻击。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-98204  
  
**5、ZTE MF286R命令注入漏洞（CNVD-2023-99925）**  
  
ZTE MF286R是中国中兴通讯（ZTE）公司的一款无线路由器。ZTE MF286R CR_LVWRGBMF286RV1.0.0B04版本存在命令注入漏洞，该漏洞源于应用未能正确过滤构造命令特殊字符、命令等。攻击者可利用该漏洞导致任意命令执行。  
  
参考链接：  
  
https://www.cnvd.org.cn/flaw/show/CNVD-2023-99925  
  
说明：关注度分析由CNVD根据互联网用户对CNVD漏洞信息查阅情况以及产品应用广泛情况综合评定。  
  
