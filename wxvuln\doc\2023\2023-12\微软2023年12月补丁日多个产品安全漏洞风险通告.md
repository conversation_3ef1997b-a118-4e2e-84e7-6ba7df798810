#  微软2023年12月补丁日多个产品安全漏洞风险通告   
 奇安信 CERT   2023-12-13 09:56  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
  
<table><tbody><tr><td valign="middle" align="center" rowspan="1" colspan="4" style="background-color: #4676d9;border-color: #4676d9;"><p style="line-height: 1.5em;"><span style="color: #ffffff;letter-spacing: 0px;"><strong><span style="color: #ffffff;font-size: 13px;letter-spacing: 0px;">漏洞概述</span></strong><br/></span></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="135"><p style="line-height: 1em;"><span style="font-size: 13px;letter-spacing: 0px;"><strong><span style="font-size: 13px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: #4676d9;"><p style="line-height: 1em;"><span style="font-size: 13px;caret-color: red;letter-spacing: 0px;">微软2023年12月补丁日多个产品安全漏洞</span></p></td></tr><tr><td valign="middle" align="left" rowspan="1" colspan="1" style="border-color: #4676d9;" width="135"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;"><strong>影响产品</strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: #4676d9;"><p style="line-height:1em;"><span style="color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;display: inline !important;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">Windows Server、Microsoft Office等</span></p></td></tr><tr><td valign="middle" align="left" colspan="1" rowspan="1" style="border-color: #4676d9;" width="135"><p style="line-height:1em;"><strong><span style="font-size:13px;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" style="border-color: #4676d9;" width="151"><p style="line-height:1em;"><span style="color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;display: inline !important;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">2023-12-13</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" style="border-color: #4676d9;" width="177"><p style="line-height:1em;"><strong><span style="font-size:13px;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">影响对象数量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" style="border-color: #4676d9;" width="94"><p style="line-height:1em;"><span style="color: #000000;font-size: 13px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;display: inline !important;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">千万级</span></p></td></tr><tr><td valign="middle" align="left" style="border-color: #4676d9;" width="135"><p style="line-height: 1em;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 17px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;">奇安信评级</span></strong></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="151"><p style="line-height: 1em;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #ff0000;font-size: 17px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;">高危</span></strong></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="177"><p style="line-height: 1em;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #000000;font-size: 17px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;">利用可能性</span></strong></p></td><td valign="middle" align="left" style="border-color: #4676d9;" width="94"><p style="line-height: 1em;"><strong style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;color: #ff0000;font-size: 17px;text-align: -webkit-left;caret-color: #ff0000;text-decoration-thickness: initial;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;"><span style="max-inline-size: 100%;margin: 0px;padding: 0px;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: none 0px !important;cursor: text;font-size: 13px;">高</span></strong></p></td></tr><tr><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="135"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="151"><p style="line-height: 1em;"><span style="font-size: 13px;color: #000000;"><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">未公开</span></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="177"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="94"><p style="line-height: 1em;"><span style="font-size: 13px;color: #000000;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">未发现</span></p></td></tr><tr><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="135"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="151"><p style="line-height: 1em;"><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="177"><p style="line-height: 1em;"><span style="font-size: 13px;"><strong><span style="font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" style="border-color: #4676d9;" width="94"><p style="line-height: 1em;"><span style="font-size: 13px;color: #000000;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;">未公开</span></p></td></tr><tr><td valign="middle" colspan="4" rowspan="1" align="left" style="border-color: #4676d9;"><p style="line-height:1em;"><strong><span style="font-size:13px;">危害描述：</span></strong><span style="color: rgba(0, 0, 0, 0.9);font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-decoration-thickness: initial;display: inline !important;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;">攻击者利用这些漏洞，可造成权限提升、远程代码执行等。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
  
本月，微软共发布了35个漏洞的补丁程序，修复了Windows Server、Microsoft Office等产品中的漏洞。**值得注意的是，微软在23年10月10日停止了Windows 11 Version 21H2 的安全更新和技术支持，建议您尽快升级系统。**  
经研判，以下13个重要漏洞值得关注（包括4个紧急漏洞、9个重要漏洞），如下表所示：  
  
<table><tbody><tr><td style="border-color: #4676d9;background-color: #4676d9;" align="center" valign="middle" width="102"><p style="line-height:1em;"><strong><span style="font-size: 13px;letter-spacing: 0px;color: #ffffff;">编号</span></strong></p></td><td style="border-color: #4676d9;background-color: #4676d9;" align="center" valign="middle" width="266"><p style="line-height:1em;"><strong><span style="font-size: 13px;letter-spacing: 0px;color: #ffffff;">漏洞名称</span></strong></p></td><td style="border-color: #4676d9;background-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><strong><span style="font-size: 13px;letter-spacing: 0px;color: #ffffff;">风险等级</span></strong></p></td><td style="border-color: #4676d9;background-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><strong><span style="font-size: 13px;letter-spacing: 0px;color: #ffffff;">公开状态</span></strong></p></td><td style="border-color: #4676d9;background-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><strong><span style="font-size: 13px;letter-spacing: 0px;color: #ffffff;">利用可能</span></strong></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35628</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Windows MSHTML 平台远程代码执行漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">紧急</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35641</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Internet 连接共享(ICS) 远程代码执行漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">紧急</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35630</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Internet 连接共享(ICS) 远程代码执行漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">紧急</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较少</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36019</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Microsoft Power Platform 连接器欺骗漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">紧急</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较少</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36005</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Windows Telephony Server 权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35633</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Windows 内核权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35632</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">WinSock 的 Windows 辅助功能驱动程序权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35631</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Win32k 权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36696</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Windows Cloud Files Mini   Filter Driver 权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36011</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Win32k 权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-35644</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Windows Sysmain 服务权限提升</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36010</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">Microsoft Defender 拒绝服务漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr><tr><td style="border-color: #4676d9;" align="left" valign="middle" width="102"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">CVE-2023-36391</span></p></td><td style="border-color: #4676d9;" align="left" valign="middle" width="266"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">本地安全认证子系统服务权限提升漏洞</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">重要</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;">未公开</span></p></td><td style="border-color: #4676d9;" align="center" valign="middle" width="63"><p style="line-height:1em;"><span style="font-size: 13px;letter-spacing: 0px;color: #ff0000;">较大</span></p></td></tr></tbody></table>  
  
  
**02**  
  
**重点关注漏洞**  
  
**>**  
**>**  
**>**  
**>**  
  
**更容易被利用漏洞**  
  
以下11个漏洞被微软标记为 “Exploitation More Likely”，这代表这些漏洞更容易被利用：  
  
- CVE-2023-35641 Internet 连接共享 (ICS) 远程代码执行漏洞  
  
- CVE-2023-36010 Microsoft Defender 拒绝服务漏洞  
  
- CVE-2023-35631 Win32k 权限提升漏洞  
  
- CVE-2023-36011 Win32k 权限提升漏洞  
  
- CVE-2023-35632 WinSock 的 Windows 辅助功能驱动程序权限提升漏洞  
  
- CVE-2023-36696 Windows Cloud Files Mini Filter Driver 权限提升漏洞  
  
- CVE-2023-35628 Windows MSHTML 平台远程代码执行漏洞  
  
- CVE-2023-35644 Windows Sysmain 服务权限提升  
  
- CVE-2023-36005 Windows Telephony Server 权限提升漏洞  
  
- CVE-2023-35633 Windows 内核权限提升漏洞  
  
- CVE-2023-36391 本地安全认证子系统服务权限提升漏洞  
  
  
  
  
以下漏洞由奇安信代码安全实验室研究员发现并提交，CVE-2023-36011 Win32k 权限提升漏洞。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs48x7kq9kjfBbxpkqs87OTbVTrsFjcCibLI3ytXkSnKjDFzZrZKaicGsnYdTtmea1AghRQObAyVEawLg/640 "")  
  
  
**鉴于这些漏洞危害较大，建议客户尽快安装更新补丁。**  
  
  
**>**  
**>**  
**>**  
**>**  
  
**重点关注漏洞详情**  
  
经研判，以下13个漏洞值得关注，详细信息如下：  
  
  
**1****、****CVE-2023-35628 Windows MSHTML****平台远程代码执行漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows MSHTML 平台远程代码执行漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35628</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">远程未经身份验证的攻击者可以通过发送经特殊设计的电子邮件来利用此漏洞，该电子邮件在被 Outlook 客户端检索和处理时会自动触发。这可能导致在预览窗格中查看电子邮件之前就被利用。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35628</span></p></td></tr></tbody></table>  
  
**2****、****CVE-2023-35641 Internet****连接共享****(ICS)****远程代码执行漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Internet 连接共享 (ICS) 远程代码执行漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35641</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">网络相邻且未经身份验证的远程攻击者可以向运行 Internet 连接共享服务的服务器发送恶意定制的 DHCP 消息来利用此漏洞，成功利用该漏洞可在目标服务器上执行任意代码。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35641</span></p></td></tr></tbody></table>  
  
**3****、****CVE-2023-35630 Internet****连接共享****(ICS)****远程代码执行漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Internet 连接共享 (ICS) 远程代码执行漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">远程代码执行</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35630</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">网络相邻且未经身份验证的远程攻击者可以向运行 Internet 连接共享服务的服务器发送恶意定制的 DHCP 消息来利用此漏洞，成功利用该漏洞可在目标服务器上执行任意代码。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35630</span></p></td></tr></tbody></table>  
  
**4****、****CVE-2023-36019 Microsoft Power Platform****连接器欺骗漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Microsoft Power   Platform 连接器欺骗漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">安全特性绕过</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">紧急</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36019</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未经身份验证的远程攻击者可向受害者发送特制的 URL 来利用此漏洞。受害者点击特制的URL，会在客户端的浏览器上执行恶意脚本。</span></p><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">自 2023 年 11 月 17 日起，新创建的使用   OAuth 2.0 进行身份验证的自定义连接器将自动具有每个连接器重定向 URI。现有   OAuth 2.0 连接器必须在 2024 年 2 月 17 日之前更新为使用每个连接器重定向 URI。有关详细信息，请参阅   https://learn.microsoft.com/en-us/connectors/custom-connectors/#21-oauth- 20.</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36019</span></p></td></tr></tbody></table>  
  
**5****、****CVE-2023-36005 Windows Telephony Server****权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows Telephony   Server 权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36005</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows Telephony Server 存在条件竞争漏洞，经过身份验证的远程攻击者可以利用该漏洞可在&#34;NT AUTHORITY\Network Service&#34;帐户的上下文中执行任意代码。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36005</span></p></td></tr></tbody></table>  
  
**6****、****CVE-2023-35633 Windows****内核权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows 内核权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35633</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞允许本地用户在系统中提升特权。此漏洞存在是因为应用程序在Windows内核中未正确施加安全限制，从而导致安全限制绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35633</span></p></td></tr></tbody></table>  
  
**7****、****CVE-2023-35632 WinSock****的****Windows****辅助功能驱动程序权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">WinSock 的 Windows 辅助功能驱动程序权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35632</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞允许本地用户在系统中提升特权。此漏洞存在是因为应用程序在Windows辅助功能驱动程序（Ancillary   Function Driver） for WinSock中未正确施加安全限制，从而导致安全限制绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35632</span></p></td></tr></tbody></table>  
  
**8****、****CVE-2023-35631 Win32k****权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Win32k 权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35631</span></p><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36011</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞允许本地用户在系统中提升特权。此漏洞存在是因为应用程序在  Win32k中未正确施加安全限制，从而导致安全限制绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35631</span></p><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36011</span></p></td></tr></tbody></table>  
  
**9****、****CVE-2023-36696 Windows Cloud Files Mini Filter Driver****权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows Cloud Files   Mini Filter Driver 权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36696</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞允许本地用户在系统中提升特权。此漏洞存在是因为应用程序在Windows Cloud Files Mini Filter Driver中未正确施加安全限制，从而导致安全限制绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36696</span></p></td></tr></tbody></table>  
  
**10****、****CVE-2023-35644 Windows Sysmain****服务权限提升**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Windows Sysmain 服务权限提升</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-35644</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞使得本地用户能够在系统中提升特权。这一漏洞的存在是因为应用程序在Windows Sysmain服务中未正确施加安全限制，导致了安全限制的绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-35644</span></p></td></tr></tbody></table>  
  
**11****、****CVE-2023-36010 Microsoft Defender****拒绝服务漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">Microsoft Defender 拒绝服务漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">拒绝服务</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36010</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞使得远程攻击者能够发起拒绝服务（DoS）攻击。这一漏洞的存在是因为在Microsoft Defender中未充分验证用户提供的输入。远程攻击者可以向应用程序输入特制的数据，进而实施拒绝服务（DoS）攻击。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36010</span></p></td></tr></tbody></table>  
  
**12****、****CVE-2023-36391****本地安全认证子系统服务权限提升漏洞**  
<table><tbody><tr style="height:6px;"><td valign="top" style="border-width: 1px;border-style: solid;border-color: windowtext;padding: 5px 10px;" width="84" height="6"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞名称</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: 1px solid windowtext;border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: none;padding: 5px 10px;" width="469" height="6"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">本地安全认证子系统服务权限提升漏洞</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞类型</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">权限提升</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">风险等级</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="66"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">重要</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="70"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞</span></strong><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">ID</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">CVE-2023-36391</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">公开状态</span></strong></span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="124"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未公开</span></p></td><td valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="85"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">在野利用</span></strong></span></p></td><td colspan="3" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="260"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">未发现</span></p></td></tr><tr><td valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="84"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">漏洞描述</span></strong></span></p></td><td colspan="5" valign="top" style="border-top: none;border-left: none;border-bottom: 1px solid windowtext;border-right: 1px solid windowtext;padding:5px 10px;" width="469"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">该漏洞允许本地用户在系统中提升特权。此漏洞的存在是因为应用程序在本地安全性机构子系统服务中未正确施加安全限制，导致了安全限制的绕过和特权提升。</span></p></td></tr><tr><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553"><p style="text-align:left;"><span style="font-size: 14px;letter-spacing: 0px;"><strong><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">参考链接</span></strong></span></p></td></tr><tr style="height:29px;"><td colspan="6" valign="top" style="border-right: 1px solid windowtext;border-bottom: 1px solid windowtext;border-left: 1px solid windowtext;border-top: none;padding: 5px 10px;" width="553" height="29"><p style="text-align:left;"><span style="line-height: 150%;font-size: 14px;letter-spacing: 0px;">https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2023-36391</span></p></td></tr></tbody></table>  
  
  
  
**03**  
  
**处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**安全更新**  
  
**使用奇安信天擎的客户可以通过奇安信天擎控制台一键更新修补相关漏洞，也可以通过奇安信天擎客户端一键更新修补相关漏洞。**  
  
  
也可以采用以下官方解决方案及缓解方案来防护此漏洞：  
  
**Windows自动更新**  
  
Windows系统默认启用 Microsoft Update，当检测到可用更新时，将会自动下载更新并在下一次启动时安装。还可通过以下步骤快速安装更新：  
  
1、点击“开始菜单”或按Windows快捷键，点击进入“设置”  
  
2、选择“更新和安全”，进入“Windows更新”（Windows Server 2012以及Windows Server 2012 R2可通过控制面板进入“Windows更新”，步骤为“控制面板”-> “系统和安全”->“Windows更新”）  
  
3、选择“检查更新”，等待系统将自动检查并下载可用更新  
  
4、重启计算机，安装更新  
  
系统重新启动后，可通过进入“Windows更新”->“查看更新历史记录”查看是否成功安装了更新。对于没有成功安装的更新，可以点击该更新名称进入微软官方更新描述链接，点击最新的SSU名称并在新链接中点击“Microsoft 更新目录”，然后在新链接中选择适用于目标系统的补丁进行下载并安装。  
  
  
**手动安装补丁**  
  
另外，对于不能自动更新的系统版本，可参考以下链接下载适用于该系统的12月补丁并安装：  
  
https://msrc.microsoft.com/update-guide/releaseNote/2023-Dec  
  
  
**>**  
**>**  
**>**  
**>**  
  
**产品解决方案**  
  
**奇安信天擎终端安全管理系统解决方案**  
  
奇安信天擎终端安全管理系统并且有漏洞修复相关模块的用户，可以将补丁库版本更新到：  
2023.12.13.1及以上版本，对内网终端进行补丁更新。  
  
推荐采用自动化运维方案，如果控制中心可以连接互联网的用户场景，建议设置为自动从奇安信云端更新补丁库至  
2023.12.13.1版本。  
  
控制中心补丁库更新方式：每天04:00-06:00自动升级，升级源为从互联网升级。  
  
纯隔离网内控制中心不能访问互联网，不能下载补丁库和补丁文件，需使用离线升级工具定期导入补丁库和文件到控制中心。  
  
  
  
**04**  
  
**参考资料**  
  
[1]https://msrc.microsoft.com/update-guide/releaseNote/2023-Dec  
  
  
  
**05**  
  
**时间线**  
  
2023年12月13日，奇安信 CERT发布安全风险通告。  
  
  
  
**06**  
  
**漏洞情报服务**  
  
奇安信ALPHA威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4ibeoC2F6g0jNoibcwyhKK0FGDVva9QSXmhVXRHY4rKhKrI12UTvAAicLIbmNQDCozek3CajwfQyLvQQ/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640 "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4ic3cLXU5jOgKEhcSRVsxJLgxhTvWFXwWKJoBKVJGTXufCB4ibhdb7YIJyVL2LiaKUiaourVoMXgaUoXA/640 "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到**ALPHA威胁分析平台**  
订阅更多漏洞信息。  
  
