#  【工具发布】2023年100个勒索风险漏洞检测工具   
 长亭安全应急响应中心   2023-12-26 19:22  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicS03Ik1BgaaXdKAh7LprPYYnVdFnHT8Sw0WJRV7aH4PK8H3M93GhgsNNZicYpjtyUHwibKXrGEj6bTA/640?wx_fmt=png&from=appmsg "")  
  
  
近期，长亭安全应急响应中心发布了453个勒索风险漏洞列表。为了帮助大家更好的排查勒索风险漏洞，牧云产线现推出本地检测工具，支持一键检测以下100个高危漏洞（详见附录）。  
  
  
  
  
  
**检测方法**  
  
 Delection Methods   
  
  
  
**01****‍**  
  
  
在本地主机上执行以下命令即可扫描：  
  
```
./chaitin_ransomware_vulnerability_scanner_linux_amd64 scan --help
INFO[0000] log level is set to info
NAME:
   长亭牧云勒索风险漏洞检测工具 scan
USAGE:
   长亭牧云勒索风险漏洞检测工具 scan [command options] [arguments...]

OPTIONS:
   --output value                       输入 json 结果到文件中
   --vuln_id value [ --vuln_id value ]  漏洞编号
   --all                                扫描所有漏洞 (default: false)
   --json                               json 格式输出到控制台 (default: false)
   --json-stream-output value           json 格式输出到文件
   --extra value                        额外信息
   --help, -h                           show help

# example: 
# 扫描所有漏洞
sudo ./chaitin_ransomware_vulnerability_scanner_linux_amd64 scan --all

# 扫描指定漏洞
sudo ./chaitin_ransomware_vulnerability_scanner_linux_amd64 scan --vuln_id **********

# 扫描漏洞并将结果输出到文件
sudo ./chaitin_ransomware_vulnerability_scanner_linux_amd64 scan --vuln_id ********** --output result.json
```  
  
  
  
**工具获取方式：**  
  
https://stack  
.chaitin.c  
om/tool/detail/1274  
  
  
**100个高危漏洞**  
  
 100 High-Risk    
Vulnerabilities   
  
  
  
**02‍**  
  
● 均可在长亭漏洞库(https://stack.chaitin.com/vuldb/)通过搜索查看漏洞描述、漏洞危害、修复方法● 可使用牧云本地检测工具一键检测漏洞存在情况● 牧云具有更为完整的勒索风险解决方案，包括但不限于支持更多的漏洞检测、高危漏洞24小时应急支持等漏洞编号漏洞名称CT-926575金山终端安全系统V9 SQL注入漏洞CT-930605用友 U8 Cloud LoginVideoServlet 反序列化远程代码执行漏洞CT-930586用友 U8 Cloud FileManageServlet 反序列化远程代码执行漏洞CT-846016用友NC/NC Cloud IMetaWebService4BqCloud SQL注入漏洞CT-882436用友U8 Cloud /ServiceDispatcherServlet 反序列化漏洞CT-532073用友NC NCMessageServlet反序列化漏洞CT-727299用友NC/NCC /uapjs/jsinvoke 任意文件上传漏洞CT-926653用友NC FileParserServlet 远程代码执行漏洞CT-833273用友NC Cloud nccloudfiles 任意文件上传漏洞CT-838653用友时空KSOA软件 /servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet SQL注入漏洞CT-838588用友时空KSOA软件 /servlet/imagefield SQL注入漏洞CT-802304用友时空KSOA downnewsatt 任意文件读取漏洞CT-893849用友GRP-U8 /u8qx/bx_historyDataCheck.jsp SQL注入漏洞CT-845889亿赛通电子文档安全管理系统 importFileType.do 任意文件上传漏洞CT-824696亿赛通电子文档安全管理系统 /UploadFileFromClientServiceForClient 任意文件上传漏洞CT-996910亿赛通电子文档安全管理系统 远程代码执行漏洞CT-1002008亿赛通电子文档安全管理系统 远程代码执行漏洞CT-949255XXL-JOB 调度中心默认 accessToken 存在鉴权绕过CT-846001企业微信 私有化版本 点击文件静默下载执行逻辑漏洞CT-846321WPS Office 客户端 <=12.1.0.15355 远程代码执行漏洞CT-828096WPS Office 客户端远程代码执行漏洞CT-846047Windows Server NPS 服务远程代码执行漏洞CT-835697企业微信 /cgi-bin/gateway/agentinfo 未授权访问漏洞CT-928242WebLogic 存在远程命令执行CT-790203WebLogic LinkRef 反序列化远程代码执行CT-790182WebLogic T3/IIOP 反序列化漏洞CT-261524Oracle WebLogic Server 输入验证错误漏洞CT-741567泛微OA E-Cology XXE漏洞CT-839098泛微OA ShowDocsImage SQL注入漏洞CT-828039泛微OA ifNewsCheckOutByCurrentUser SQL注入漏洞CT-787974泛微OA FileDownloadForOutDoc 前台SQL注入漏洞CT-697307泛微OA E-Cology9 /mobile/plugin/CheckServer.jsp SQL注入漏洞CT-469681泛微OA XmlRpcServlet 任意文件读取漏洞CT-812507泛微OA clusterupgrade 前台文件上传漏洞CT-531322泛微OA E-Cology9 browser.jsp SQL注入漏洞CT-727311泛微 ofslogin.jsp 任意用户登陆漏洞CT-839143东方通TongWeb应用服务器 mbean 远程代码执行漏洞CT-839164东方通TongWeb应用服务器 /console/service 远程代码执行漏洞CT-790976ThinkPHP lang参数 远程命令执行漏洞CT-404883Spring Cloud Function functionRouter SPEL代码执行漏洞CT-405042Spring Framework JDK >= 9 远程代码执行漏洞CT-831610Smartbi Token 回调地址鉴权绕过CT-778385Smartbi windowUnloading 未授权远程命令执行CT-765607Smartbi 内置用户登陆绕过漏洞CT-846214Smartbi windowUnloading 鉴权绕过CT-817703Smartbi setEngineAddress 权限绕过漏洞CT-926601致远OA /getAjaxDataServlet 路径存在XML实体注入CT-646979致远OA /seeyon/ajax.do formulaManager 任意文件上传漏洞CT-631513致远OA /yyoa/HJ/iSignatureHtmlServer.jsp 路径存在SQL注入漏洞CT-868971致远OA 前台任意用户密码重置漏洞CT-831552致远OA syncConfigManager 方法存在远程代码执行漏洞CT-797515Apache RocketMQ NameServer 远程命令执行漏洞CT-745346Apache RocketMQ Broker 未授权访问漏洞CT-683765瑞友天翼应用虚拟化系统 SQL 注入漏洞CT-845953腾讯QQ 点击文件静默下载执行逻辑漏洞CT-790635Openfire Administration Console 权限绕过漏洞CT-756407OfficeWeb365 /PW/SaveDraw 任意文件上传漏洞CT-728853NginxWebUI runCmd 远程命令执行漏洞CT-750794Alibaba Nacos Jraft 存在反序列化-版本检测CT-636997Alibaba Nacos 默认密钥身份伪造漏洞CT-662291MinIO 安全漏洞CT-790524MinIO 信息泄露漏洞CT-809826Metabase 远程代码执行漏洞CT-206619Apache Log4j2 远程代码执行漏洞CT-845296LiveBOS /ScriptVariable.jsp 远程代码执行漏洞CT-846066蓝凌OA jg_service.jsp 金格任意文件写入漏洞CT-845931蓝凌OA sysUIComponent.do 任意文件上传漏洞CT-915928金蝶 EAS 及 EAS Cloud 远程代码执行漏洞CT-949212金蝶云星空 ScpSupRegHandler 任意文件上传漏洞CT-763698金蝶云星空 远程代码执行漏洞CT-838935金蝶云星空 /CommonFileServer 任意文件读取漏洞**********金蝶Apusic应用服务器 /admin//protect/jndi/loadTree LDAP注入漏洞CT-883884JumpServer堡垒机 会话回放未授权访问漏洞CT-898129JumpServer 重置密码验证码被预测致账号劫持漏洞CT-839424JeecgBoot 积木报表 testConnection JDBC 远程代码执行CT-838503Jeecg-Boot Freemarker /jmreport/queryFieldBySql 模版注入漏洞CT-959313IP-guard WebServer 远程命令执行漏洞CT-827723iDocView /html/2word 远程代码执行漏洞CT-833226华天动力OA /OAapp/bfapp/buffalo/workFlowService SQL注入漏洞CT-531477宏景人力资源管理系统 OfficeServer.jsp 任意文件上传漏洞CT-712121宏景HR系统 /servlet/codesettree SQL注入漏洞CT-17520Apache Tomcat AJP 文件读取与包含漏洞CT-790352GeoServer ows SQL注入漏洞CT-828112帆软报表 /channel 反序列化绕过漏洞CT-827944Microsoft Exchange Server 安全漏洞CT-983967泛微 EOffice 10 SQL注入漏洞CT-898014泛微 E-Office 10 远程代码执行漏洞CT-828055泛微E-Office v9 /E-mobile/App/Init.php 文件包含漏洞CT-670759Apache Druid 远程代码执行漏洞CT-845980Coremail 邮件处理模块 远程代码执行漏洞CT-753949畅捷通T+ Ufida.T.SM.UIP.MultiCompanyController,Ufida.T.SM.UIP.ashx 存在SQL注入漏洞CT-947127Atlassian Confluence Data Center & Server 权限绕过漏洞CT-909378Atlassian Confluence Data Center & Server 权限提升漏洞CT-805348Atlassian Confluence Data Center & Server 登录后远程代码执行漏洞CT-433592Atlassian Confluence 远程代码执行漏洞CT-790473Apache Superset 默认SECRET_KEY 漏洞CT-996923Apache Struts2 目录遍历漏洞CT-790303Apache Dubbo 反序列化远程代码执行漏洞CT-984682Apache ActiveMQ Jolokia 远程代码执行漏洞CT-855664Apache ActiveMQ 服务端口 远程代码执行漏洞  
  
长亭科技的漏洞应急响应服务因其深度和准确性而著称。我们的专家团队不断追踪最新的安全威胁，结合多方情报资源，为客户提供全面的漏洞分析和应急响应指导。如需了解更多详情，请联系您所在区域/行业的销售代表，我们的专业售前团队将为您提供服务。我们提供的服务包括但不限于：  
  
  
- **深度分析报告：**提供全面深入的重大安全漏洞分析，为企业揭示潜在风险和影响，增强对安全威胁的理解和应对能力，从而促进更明智的决策制定和策略部署。  
  
  
- **独家检测工具：**通过使用定制化的检测解决方案，企业能够快速、准确地识别系统中的安全漏洞，大幅提升漏洞管理效率和系统安全性。  
  
  
- **攻击痕迹发现：**助力企业迅速识别并分析安全入侵事件，从而有效预防数据泄露和系统损害，加强企业的安全防护能力。  
  
  
- **经过验证的缓解措施**  
**：**提供经过实战检验的漏洞应对策略，帮助企业迅速降低安全风险，增强系统的抵御能力和业务连续性。  
  
  
- **补丁有效性分析：**  
确保企业能够选择最合适和有效的漏洞修复方案，减少安全更新的不确定性，提高系统稳定性和安全性。  
  
  
- **热点漏洞合集排查：**  
通过持续监测和分析最紧迫的安全威胁，企业能够及时了解和应对最新的安全挑战，确保业务的持续运营和数据安全。  
  
  
  
