#  实战| 任意用户密码重置漏洞   
原创 zkaq-zbs  掌控安全EDU   2023-12-16 12:03  
  
****  
  
扫码领资料  
  
获网安教程  
  
免费&进群  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vco1FVfwFUib59ibtxfT0E9p3LCqbNibakO6q1NfX2eEib89fQmJkDc6V8dc2BcvicgIiaQZp4oSHGXgefpQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/b96CibCt70iaaJcib7FH02wTKvoHALAMw4fchVnBLMw4kTQ7B9oUy0RGfiacu34QEZgDpfia0sVmWrHcDZCV1Na5wDQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
#   
  
****  
本文由掌  
控  
安  
全  
学  
院  
   
-  
 zbs   
   
   
投  
稿  
## 一、漏洞说明：  
  
xxxx学院身份认证系统有严重的逻辑设计缺陷：账户登录、手机登录、密码找回三个接口找到n个逻辑漏洞包括任意账号密码修改、信息泄露（应该还有更多，但是有很多重复的漏洞，没必要再找了）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YR4d8OM8KzgqRNicfuELjicmOd9t3QC2dicmQuYbtcnD3rO19rxc9OyVIA/640?wx_fmt=png&from=appmsg "")  
edusrc高危漏洞审核通过（还没修复就先打满码码）![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YWaF4vXOR4aEaRpDgGynyj6iathZMCK437Q7pZStc4sudibC6ttNtL0zg/640?wx_fmt=jpeg&from=appmsg "")  
  
## 二、漏洞复现：  
  
做个简简单单的账户信息收集【edusrc账户搜集还挺重要的，因为基本都不能注册，只能用学校学生的账号】  
  
这里直接百度 site:学院url (学号|电话) ，就有挺多的了（学校一般会公示什么什么学生信息）![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YWbL8pLx4AuO9E3FKocK57xt6fU0AqTOmj1beRlIAnKrqOMJNxVez9w/640?wx_fmt=png&from=appmsg "")  
得到学号格式 —> 年份 1500 xxxx 和若干学生信息  
  
学号登录接口：这里拿个收集的学号试试：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Yfm3PfZW2tGW6ianbKnWOe2trzIOkVctAjia9nSfHvTicTGBiavJ0ov6ia2A/640?wx_fmt=png&from=appmsg "")  
【Base64加密了数据包传参信息，并没有什么用。】漏洞一：这里首先有一个验证码不更换长期有效漏洞：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Y9e0fDeIxGqBw8RtDARvr2q9iaPlSSpYS22dgTWvsql63m1WRsXgKJCQ/640?wx_fmt=png&from=appmsg "")  
  
验证码错误回显应该是这样的：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Y7Ih0XzG7r6q1j6qzSZIicoSrJD5UD18TDuUia7KC9PTiaLYMkg5xMgibHA/640?wx_fmt=png&from=appmsg "")  
  
存在危害：1.可以对一个弱密码使用收集的用户名进行爆破2.对一个用户名进行弱密码爆破  
  
bp抓包发现数据包残留信息：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YVuMotZapfoB8aQVpbxGjaJyLZ5CSycC5HCeMOibb19X9OPIpJTSjucA/640?wx_fmt=png&from=appmsg "")  
  
发现发送包有验证码md5加密：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YXicvIOibD3FP7RYDX4WN8T4XroPd0sl9yJNia3vdGfJIy0v1mptSSiappQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YsVOKmyQJ7xEIBxkMsQFErzowAicA5y8x2icqnr5kUPelruicE0ZQficRlg/640?wx_fmt=png&from=appmsg "")  
  
漏洞二：【不仅验证码多次使用不更换，还有数据包遗留（信息）验证码漏洞】！！！！  
存在危害：只要logincode带着一个验证码md5加密，后面所有的登陆接口所有操作都可以无视验证码了—>这里的验证码功能等于没有，而且也没有ip访问限制，撞库就得死。  
  
电话登录接口：  
  
没注册的手机号会提示，抓包看看：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YicT5F0kpGiciapob3XCTYB4xLWoJk1IzLFoeFzylHvIWDkHxTp1BDOOHQ/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Y19WP1S6VicFr2q7oPiaNCSeswHfiahJ3IiatFqxor1SB4RPlscqUZ8YYKQ/640?wx_fmt=jpeg&from=appmsg "")  
漏洞三：可对该点进行爆破：得到数据库存储的账户手机号。  
存在危害：可以给他们都发送短信验证码，钱要亏死啊小爆破演示验证一下：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YIMGo6KL5Y7yf9lkQmSudDwJFE1teb7zWJuANjEFgneVFQTrHbGVhxQ/640?wx_fmt=jpeg&from=appmsg "")  
  
存在危害：拿大字典爆破，学生电话都被揭露啦，危险危险  
  
漏洞四：返回的数据包里有信息残留：手机号会附带相应学号符合信息收集学号格式年份+1500+xxxx  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YGn2OrNV9Wickc4icVuNLOLjw5XxXlKrSvswBLaIiaW9M5ThpR1qwFnBrA/640?wx_fmt=png&from=appmsg "")  
【有童鞋的信息啦，只能打码打的这么丑了】  
存在危害：1.【很多人会拿手机号当密码，这样子也算一个数据包残留敏感信息的漏洞】2.【后面测试的找回密码第一个接口是验证手机号和学号！！！，你这简直太搞了】  
  
抓短信验证码响应包：![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YMTfIxXNlRH2QMhn1wOQArWdDic3ibU1icphvS8JXMAXtBczlibDnoHJ14Q/640?wx_fmt=png&from=appmsg "")  
Smslogincode似短信验证码MD5加密【后面验证了】  
  
漏洞五：短信验证码可爆破，可无限尝试  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YzMFd9ydUsU5FZHKMwwbXoibEMcxbAKK2uBSxyzIVl4DWFMicPhxObTxw/640?wx_fmt=png&from=appmsg "")  
  
这里只测了四位数，还是在验证没失效，其实从000000-999999 爆过去肯定就能过了【或者找个验证码字典，不过我觉得它肯定是纯数字】这里肯定是有逻辑漏洞危害的，因为密码找回我就爆破成功了。这个学院做的网站真的好不安全啊！！  
存在危害：学生数据信息会被泄露，登录页面可爆破  
  
密码找回接口：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Yic998cjibutNDw6Bic8ia2hOfdg1yKCYaFh4bNWSrJuIs5icrwyqx0o2IzA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YIjX2hnsxzTPiaFaSkq7nZibMGGwTic25P9XfKeHZ3aibSNqZpJ15TicSbYw/640?wx_fmt=png&from=appmsg "")  
上文拿到的手机号和对应的学号直接过了第一个验证。。。。【无力吐槽了】  
  
抓发送验证码响应包：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Y4bDePKW7m80XYqFiaDK63zIsEXcXjL0kj0vjJG7MRNxNq8JE82LX39g/640?wx_fmt=png&from=appmsg "")  
漏洞七：数据包（信息）短信验证码残留：findpwcode。。。。。明显的验证码就在这里Md5解密到了，但是改人家密码是不对的  
  
但是迫于审核员需要我成功的截图，我只能勉为其难更改了这位童鞋的密码【提交漏洞时交代清楚了，请求联系学院改回】  
  
爆破验证码（四位数）：![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5Y1uaApa7YPasRFLleSIYiatpDMM3h3wQvTJ6YJeIrSjAf3lDelG7vWMA/640?wx_fmt=jpeg&from=appmsg "")  
爆破成功，重置密码：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YKKZYicbgrUUwka6L0lolmEINhwkicMfs4Am8p8ovXesHfPzcN1PWGhqg/640?wx_fmt=jpeg&from=appmsg "")  
  
登录后访问：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcpOed6VibTa0VQQMbMiavkc5YEXTT9kCSCDxKfrRvFSImNCoMhyDQnX0FicVvY5FrO379SYTWLojnKSA/640?wx_fmt=jpeg&from=appmsg "")  
里面有很多隐私信息泄露，而且还涉及消费功能  
  
存在危害：学生账户数据信息泄露  
## 三、漏洞修复建议：  
  
1、验证码单次有效，校验一次就作废【这里的动态验证码跟没有一样，防不了一点爆破】升级一下把，换个滑块2、对ip进行限制3、改代码，数据包不要残留呢么多信息，太夸张了4、手机登录也需要动态验证码，手机号不存在和发送短信验证码也需要动态验证码验证后才可以执行5、找回密码接口多一点验证，第一个验证接口信息收集都能过6、换一个开发  
  
  
申明：本公众号所分享内容仅用于网络安全技术讨论，切勿用于违法途径，  
  
所有渗透都需获取授权，违者后果自行承担，与本号及作者无关，请谨记守法.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/BwqHlJ29vcqJvF3Qicdr3GR5xnNYic4wHWaCD3pqD9SSJ3YMhuahjm3anU6mlEJaepA8qOwm3C4GVIETQZT6uHGQ/640?wx_fmt=gif&wxfrom=5&wx_lazy=1 "")  
  
**没看够~？欢迎关注！**  
  
  
  
  
**分享本文到朋友圈，可以凭截图找老师领取**  
  
上千**教程+工具+交流群+靶场账号**哦  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/BwqHlJ29vco1FVfwFUib59ibtxfT0E9p3LGPhDofes96gFP0UgSX1TibYEBb9MTFRCEQyEbuPFD9LwwnRZA0ibkwZg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
******分享后扫码加我！**  
  
**回顾往期内容**  
  
[Xray挂机刷漏洞](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247504665&idx=1&sn=eb88ca9711e95ee8851eb47959ff8a61&chksm=fa6baa68cd1c237e755037f35c6f74b3c09c92fd2373d9c07f98697ea723797b73009e872014&scene=21#wechat_redirect)  
  
  
[零基础学黑客，该怎么学？](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247487576&idx=1&sn=3852f2221f6d1a492b94939f5f398034&chksm=fa686929cd1fe03fcb6d14a5a9d86c2ed750b3617bd55ad73134bd6d1397cc3ccf4a1b822bd4&scene=21#wechat_redirect)  
  
  
[网络安全人员必考的几本证书！](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247520349&idx=1&sn=41b1bcd357e4178ba478e164ae531626&chksm=fa6be92ccd1c603af2d9100348600db5ed5a2284e82fd2b370e00b1138731b3cac5f83a3a542&scene=21#wechat_redirect)  
  
  
[文库｜内网神器cs4.0使用说明书](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247519540&idx=1&sn=e8246a12895a32b4fc2909a0874faac2&chksm=fa6bf445cd1c7d53a207200289fe15a8518cd1eb0cc18535222ea01ac51c3e22706f63f20251&scene=21#wechat_redirect)  
  
  
[代码审计 | 这个CNVD证书拿的有点轻松](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247503150&idx=1&sn=189d061e1f7c14812e491b6b7c49b202&chksm=fa6bb45fcd1c3d490cdfa59326801ecb383b1bf9586f51305ad5add9dec163e78af58a9874d2&scene=21#wechat_redirect)  
  
  
[【精选】SRC快速入门+上分小秘籍+实战指南](http://mp.weixin.qq.com/s?__biz=MzUyODkwNDIyMg==&mid=2247512593&idx=1&sn=24c8e51745added4f81aa1e337fc8a1a&chksm=fa6bcb60cd1c4276d9d21ebaa7cb4c0c8c562e54fe8742c87e62343c00a1283c9eb3ea1c67dc&scene=21#wechat_redirect)  
  
##     代理池工具撰写 | 只有无尽的跳转，没有封禁的IP！  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/BwqHlJ29vcqJvF3Qicdr3GR5xnNYic4wHWaCD3pqD9SSJ3YMhuahjm3anU6mlEJaepA8qOwm3C4GVIETQZT6uHGQ/640?wx_fmt=gif&wxfrom=5&wx_lazy=1 "")  
  
点赞+在看支持一下吧~感谢看官老爷~   
  
你的点赞是我更新的动力  
  
  
  
