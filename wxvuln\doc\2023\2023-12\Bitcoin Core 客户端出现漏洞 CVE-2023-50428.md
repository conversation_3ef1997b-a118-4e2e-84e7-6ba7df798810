#  Bitcoin Core 客户端出现漏洞 CVE-2023-50428   
DO SON  代码卫士   2023-12-12 17:24  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Az5ZsrEic9ot90z9etZLlU7OTaPOdibteeibJMMmbwc29aJlDOmUicibIRoLdcuEQjtHQ2qjVtZBt0M5eVbYoQzlHiaw/640?wx_fmt=gif "")  
  
   
聚焦源代码安全，网罗国内外最新资讯！****  
  
**编译：代码卫士**  
  
**美国国家漏洞库 (NVD) 提到，最近引发大量关注的比特币铭文被报送为漏洞，编号为CVE-2023-50428，且现在正处于分析状态。被纳入 NVD 列表说明漏洞已被发现、分类并值得公众注意。**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/oBANLWYScMTwbIAbnwWkA8HdzmyVrgduibibwu6jl2a4H4ibrC67ic5dibAY8JKO82Vmomhiaxkg1iapfDNOKvsT1X3vw/640?wx_fmt=png&from=appmsg "")  
  
  
NVD 对CVE-2023-50428的说明是，“在 Bitcoin Core 26.0 版本及 Bitcoin Knots 25.1.knots20231115 版本中，可通过将数据混淆为代码的方式（如通过 OP_FALSE OP_IF），绕过 datacarrier 的大小限制。该漏洞已被铭文在2022年和2023年利用。”  
  
Bitcoin Core 客户端的开发人员 Luke Dashjr 近期在社交媒体的发言被NVD引用为外部来源。他发布帖子表示，“‘铭文’正在利用 Bitcoin Core 中的一个漏洞对区块链发动垃圾攻击。自2013年起，Bitcoin Core 就允许用户对他们所中继或挖掘的交易中的额外数据进行大小限制（`-datacarriersize`）。通过将数据混淆为程序代码，铭文绕过了这个限制。该漏洞在 Bitcoin Knots v25.1 中修复，所花费时间要比平时多一些，因为去年年末我的工作流被严重扰乱（v24完全被跳过）。Bitcoin Core 在即将发布的 v26版本中仍然易受攻击。我只能希望会在明年发布的v27版本中得到修复。”  
  
  
**漏洞与 Ordinal 协议的相关性**  
  
  
  
  
铭文涉及将其它数据嵌入到特定的“聪”（比特币的最小单位satoshi）中。该数据可以任何数字化格式呈现如图像、文本或其它媒体格式。每当数据被添加到“聪”时，它就变成了比特币区块链的永久性组成部分。  
  
尽管数据嵌入成为比特币协议的特性已有一段时间，但它在2022年年末随着 Ordinal 协议的出现才开始流行。该协议允许将唯一的数字化艺术直接嵌入到比特币交易中，就像以太网上的NFTs 功能一样。  
  
如果这个所谓的“漏洞”得到修正，那么可能会对网络上的铭文产生限制。当问到如漏洞被修复后，Ordianls 和 BRC-20 令牌是否会不复存在时，Luke Dashjr 给出了肯定的回复，“理解正确”。然而，鉴于网络的不可变性质，已有的铭文将继续存在但将会变成不可交易状态。  
  
  
  
代码卫士试用地址：  
https://codesafe.qianxin.com  
  
开源卫士试用地址：https://oss.qianxin.com  
  
  
  
  
  
  
  
  
  
  
  
  
**推荐阅读**  
  
[Bitcoin Core 软件修复严重的 DDoS 漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247488136&idx=2&sn=5462299ba116d631cff868b64ff0e623&chksm=ea9723e2dde0aaf476ca2d944fd3d820015487886784c01e22399a9819acb337ce672340f742&scene=21#wechat_redirect)  
  
  
[黑客利用0day，从General Bytes比特币ATM盗走150万美元](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247515986&idx=1&sn=5eff29117a43de02c6e24051651a6f86&chksm=ea948e38dde3072e5c313bd7e56c2044587c7b7b42072206200ca8c56d01ef4e3a2136e2b227&scene=21#wechat_redirect)  
  
  
[为避免攻击，研究员把严重的比特币漏洞详情焐了两年](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247495037&idx=2&sn=8ad91cfcc0f096e65069927ae64d022e&chksm=ea94dc17dde35501ed12e9d497d69e3a652b8ccea34ed405bd3bb702cd49dc02671086300e99&scene=21#wechat_redirect)  
  
  
[币安被黑客盗走逾7000个比特币，价值超4100万美元](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247489900&idx=2&sn=b10cb5c19d77fe414284057f750efe90&chksm=ea972806dde0a1103abe20c0fb21802083386b70457b6a0e3c42111c3b76b3b52258aa0f10de&scene=21#wechat_redirect)  
  
  
[黑客“双花攻击”热门比特币交易所 数天牟利1800万美元](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=**********&idx=5&sn=d05ee1f57cb8d24a8d30a6f7c3589dff&chksm=ea973f93dde0b685373d9512d0c7df249fd13aa48d1f627657ee012c5c94096ea8e61351a7e2&scene=21#wechat_redirect)  
  
  
  
  
**原文链接**  
  
https://securityonline.info/cve-2023-50428-bitcoin-core-client-vulnerability/  
  
  
题图：  
Pixabay  
 License  
  
****  
**本文由奇安信编译，不代表奇安信观点。转载请注明“转自奇安信代码卫士 https://codesafe.qianxin.com”。**  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSf7nNLWrJL6dkJp7RB8Kl4zxU9ibnQjuvo4VoZ5ic9Q91K3WshWzqEybcroVEOQpgYfx1uYgwJhlFQ/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSN5sfviaCuvYQccJZlrr64sRlvcbdWjDic9mPQ8mBBFDCKP6VibiaNE1kDVuoIOiaIVRoTjSsSftGC8gw/640?wx_fmt=jpeg "")  
  
**奇安信代码卫士 (codesafe)**  
  
国内首个专注于软件开发安全的产品线。  
  
   ![](https://mmbiz.qpic.cn/mmbiz_gif/oBANLWYScMQ5iciaeKS21icDIWSVd0M9zEhicFK0rbCJOrgpc09iaH6nvqvsIdckDfxH2K4tu9CvPJgSf7XhGHJwVyQ/640?wx_fmt=gif "")  
  
   
觉得不错，就点个 “  
在看  
” 或 "  
赞  
” 吧~  
  
