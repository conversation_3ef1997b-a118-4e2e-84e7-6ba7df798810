#  经验分享 | 记一次通过子域模糊测试识别漏洞并获取高额赏金的经历   
FreddyLu666  FreeBuf   2024-04-26 19:01  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR38jUokdlWSNlAjmEsO1rzv3srXShFRuTKBGDwkj4gvYy34iajd6zQiaKl77Wsy9mjC0xBCRg0YgDIWg/640?wx_fmt=gif "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNWWhq6G1iaxCaQ1LStngPeMAibAFscFQZxdkITJgFPhrTquyh04E8QFOg/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNhaPlmVRCSaeZPEqjraDEOxPPXQ001qFzRhMpLdFoicqyrejZyLKyLVA/640?wx_fmt=jpeg&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/0pygn8iaZdEfON2XFbCe9JcKhRtZ3JppibU2EckBNcenLicicGcz5QxYwaWIYzM5gaFD9lClJeKtmedAXjW4Fia4JUA6m807EicgWP/640?wx_fmt=svg&from=appmsg "")  
  
左右滑动查看更多  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/0pygn8iaZdEfON2XFbCe9JcKhRtZ3JppibU2EckBNcenLicicGcz5QxYwaWIYzM5gaFD9lClJeKtmedAXjW4Fia4JUA6m807EicgWP/640?wx_fmt=svg&from=appmsg "")  
  
  
  
本文由漏洞猎人Abdullah Nawaf于2024年3月18日发表在Medium网站，本文记录了Abdullah Nawaf的一次漏洞挖掘过程，而此次漏洞挖掘也成功让他获取到了三万五千美元的漏洞奖金。本文旨在跟大家分享一名专业漏洞猎人的漏洞挖掘心路历程，仅出于经验分享和教育目的撰写。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNvJb5lSe3HS2YHQ2b1bQibJdA5UVEoQEbKHwOIsKOrrlj2SKNn8PTjCQ/640?wx_fmt=jpeg&from=appmsg "")  
  
  
Abdullah Nawaf是一名专业的全职漏洞奖金猎人，而他也一直活跃在BugCrowd排名前五十的榜单上，其P1级别漏洞排名为11，主要挖掘的是P1和P2级别的漏洞。  
  
  
接下来，我们一起看看Abdullah Nawaf和Orwa Atyat（另一名安全专家）是如何利用子域名模糊测试以及其他一系列多个安全漏洞实现了远程代码执行，并将漏洞上报后，拿到了三万五千美元的漏洞奖金。  
  
  
**故事开始**  
  
  
##   
  
虽然现在是2024年，但这个故事要从2022年说起。当时，Abdullah Nawaf在BugCrowd上报告了一个能够导致SQLI&RCE的身份认证绕过漏洞，当时这个漏洞在上报后的第二天就被修复了。  
  
  
然后时间来到了2024年3月份，Abdullah Nawaf和orwa决定重新去测试一下这个漏洞，看看现在这个漏洞是否还存在。当时他们的测试目标是admin.Target.com，并使用了下列命令来进行子域名模糊测试任务：  
```
ffuf -w /subdomain_megalist.txt -u 'https://adminFUZZ.Target.com' -c  -t 350 -mc all  -fs 0
```  
  
注意，上述命令中使用到的subdomain_megalist.txt链接可以在文末的参考资料中找到。  
  
  
在该命令的帮助下，他们成功找到了一个地址为「admintest.Target.com」的子域名：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNRLlgQv4r8H8jAR9WQ14mypTKtoh6n9e0NgiazicwZQCbH2OibJpdK1g7A/640?wx_fmt=jpeg&from=appmsg "")  
  
  
大家可能发现了，上图中包含了很多错误，但其实没关系，因为你在进行的是子域名模糊测试，而这些错误表明目标子域名是无效的。  
  
  
「admintest.Target.com」是存在安全漏洞的，因为它所使用的后端跟源子域名「admin.Target.com」所使用的后端是相同的。  
  
  
接下来，我们一起看看他们所发现的每一个安全漏洞。  
  
  
**身份认证绕过和BAC**  
  
  
  
当你访问「https://admintest.Target.com」时，会被重定向到「https://admintest.Target.com/admin/login.aspx」。通过读取和分析节点相关的JavaScript文件后，研究人员发现了一个路径为「https://admintest.Target.com/admin/main.aspx」的节点。在浏览器中打开并访问该节点后，将会再次将他们重定向到登录页面，不过这一次他们在Burp中发现了一些不一样的东西：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNk1rZEUKRP1ZFunjhH0UibK4RQqlntd3whDAVm8xWkcW4Vk9NArqjq8Q/640?wx_fmt=jpeg&from=appmsg "")  
  
  
这一次，请求中的「Content-Length」内容非常多，对于重定向响应来说是真的非常多。  
  
  
在这里他们发现，即使被重定向到了登录页面，最终仍然是可以访问目标地址的，而且还会具备完整的访问权限。将图中标记的三个Header删除之后，他们便能够访问目标管理面板了：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNOhLFFNAhd3t6RdUysfrC0icYrIibFibvpsAVOOhHMDuHaaVWgDVwWZdWA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
通过使用Burp的匹配和替换功能，或使用Burp拦截响应：  
```
change 302 Moved Temporarily to 200 OK

remove Location: /admin/Login.aspx?logout=y

remove html redirect code
```  
  
他们便能够实现完全的身份认证绕过，而且这种级别的身份认证绕过非常的强大，不仅能够绕过前段，而且深入分析后，他们还发现了一个名为「adduser.aspx」的节点，这个节点当时同样可以将他们重定向到登录页面，因为adduser.aspx也使用了跟main.aspx一样的重定向策略。既然使用了一样的策略机制，那也就意味着，他们同样能够访问该节点并通过身份认证绕过来添加一个管理员账号。除此之外，他们甚至还发现了另一个能够在不需要任何身份认证的情况下显示管理员用户名和密码的节点：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNSkyzz7QsYhalJcz99Us4lrcdMTOcf9c0j5usKNpMIvNkTQs437FI7Q/640?wx_fmt=jpeg&from=appmsg "")  
  
### SQLI  
  
  
成功添加了管理员账号之后，他们便能够使用该账号登录目标站点的管理员面板了。  
  
  
随后，他们还发现了一个名为「SQLQuery.aspx」的节点，通过该节点的名称，大家估计也能够猜到它是干嘛的了吧？  
  
  
他们当时的第一反应就是尝试下列查询语句：  
```
Select * from users
```  
  
果不其然，他们获取到了所有的用户信息，其中包括用户密码、电子邮件和用户名等数据：  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNAyQeuFC71PlhA5AibShtgEO9ibVvawpIBWl7xcfLsvZU22mu7Flhf89Q/640?wx_fmt=jpeg&from=appmsg "")  
  
### 远程代码执行RCE  
  
  
由于目标站点所使用的数据库是mssql，因此他们便打算利用xp_cmdshell来尝试提权至RCE。关于xp_cmdshell的内容可以参考【https://www.mssqltips.com/sqlservertip/1020/enabling-xpcmdshell-in-sql-server】。  
  
  
简单来说，关于xp_cmdshell可以帮助用户通过mssql在目标系统中执行命令。默认配置下，这个功能是被禁用的，但是开启它其实也不难，可以直接使用sqlmap的「--os-shell」命令选项来启用该功能。  
  
  
但是在他们当时的测试场景下，他们根本不需要使用sqlmap，因为他们可以直接向数据库发送并执行查询请求，比如说：  
```
Select * from users
```  
  
或  
```
SELECT @@version
```  
  
因此，为了让xp_cmdshell能够正常工作，他们要做的第一件事情就是通过执行下列查询来启用该功能（通过mssql在目标系统中执行命令）：  
```
SP_CONFIGURE "show advanced options", 1

RECONFIGURE

SP_CONFIGURE "xp_cmdshell", 1

RECONFIGURE
```  
  
接下来，下列命令执行后，RCE便「映入眼帘」：  
```
xp_cmdshell ‘whoami’
```  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR39YKrISbxRdA8XI8oOqjpJNyJH93PEy0aecUb702hCfIgLx22d7faN9TAR5Xnw3UHibGPv669Vtc2A/640?wx_fmt=jpeg&from=appmsg "")  
  
  
在发现了上述安全问题后，他们便立刻将相关信息上报给了厂商，并成功拿到了三万五千美元的漏洞奖金。  
  
  
**总结**  
  
  
##   
  
通过跟大家分享这一次的漏洞奖励计划经历，希望大家能够更加了解漏洞猎人的思路，总结如下：  
  
  
**1、一定要在Burp中检查重定向响应;**  
  
****  
实际上，很多节点都会存在本文所述的这种身份绕过漏洞，在Burp中捕捉并检查重定向响应后，我们就能够清楚地了解前后端的交互和反应情况了；  
  
  
**2、如果你在一个子域名中找到了可疑的安全问题，可以通过尝试子域名模糊测试方法来进行分析和测试;**  
```
admin-FUZZ.target.com 例如: admin-stg.target.com

FUZZ-admin.target.com 例如: cert-admin.target.com

adminFUZZ.target.com  例如: admintest.target.com

FUZZadmin.target.com  例如: testadmin.target.com

admin.FUZZ.target.com 例如: admin.dev.target.com
```  
  
相关命令如下：  
```
ffuf -w /subdomain_megalist.txt -u 'https://adminFUZZ.Target.com' -c  -t 350 -mc all  -fs 0

-t means threads , dont make it so high u could miss alot of working subs , aslo its dpends in your network speed

,sinc im using vps 350 find for me

-mc all means macth all respone codes like 200,302,403 and this importent
```  
  
**3、在报告漏洞信息之前一定要尝试提升漏洞（影响）;**  
  
****  
**4、漏洞的质量永远比数量更重要！**  
  
****  
【  
FreeBuf粉丝交流群招新啦！  
  
在这里，拓宽网安边界  
  
甲方安全建设干货；  
  
乙方最新技术理念；  
  
全球最新的网络安全资讯；  
  
群内不定期开启各种抽奖活动；  
  
FreeBuf盲盒、大象公仔......  
  
扫码添加小蜜蜂微信回复“加群”，申请加入群聊  
】  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3ich6ibqlfxbwaJlDyErKpzvETedBHPS9tGHfSKMCEZcuGq1U1mylY7pCEvJD9w60pWp7NzDjmM2BlQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/oQ6bDiaGhdyodyXHMOVT6w8DobNKYuiaE7OzFMbpar0icHmzxjMvI2ACxFql4Wbu2CfOZeadq1WicJbib6FqTyxEx6Q/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ61icYlLmBLDpdYEZ7nIzpGovpHjtxITB6ibiaC3R5hoibVkQsVLQfdK57w/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
> https://bugcrowd.com/HackerX007  
> https://github.com/netsecurity-as/subfuz/blob/master/subdomain_megalist.txt  
> https://github.com/danielmiessler/SecLists/tree/master/Discovery/DNS  
> https://www.mssqltips.com/sqlservertip/1020/enabling-xpcmdshell-in-sql-server  
> https://medium.com/@s12deff/microsoft-sql-server-to-rce-984016b4aaf8  
> https://medium.com/@HX007/subdomain-fuzzing-worth-35k-bounty-daebcb56d9bc  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/qq5rfBadR3icEEJemUSFlfufMicpZeRJZJ7JfyOicficFrgrD4BHnIMtgCpBbsSUBsQ0N7pHC7YpU8BrZWWwMMghoQ/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247493362&idx=1&sn=39c9b1c4d709e5ad0babb44995b0e412&chksm=ce1f1c6df968957be704d2843b3f448b252d2a2e1b5271efa486c3e57819849e0e287b04568b&scene=21#wechat_redirect)  
  
[](http://mp.weixin.qq.com/s?__biz=Mzg2MTAwNzg1Ng==&mid=2247493318&idx=1&sn=02dc5120e00a3d6759be8fcf1b49ec0a&chksm=ce1f1c59f968954fd868b2f8cefa0e8bc5dd703c36dd6db4fc03923be36783a7d4cc791c18b6&scene=21#wechat_redirect)  
  
[](https://mp.weixin.qq.com/s?__biz=MjM5NjA0NjgyMA==&mid=2651253272&idx=1&sn=82468d927062b7427e3ca8a912cb2dc7&scene=21#wechat_redirect)  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/qq5rfBadR3icF8RMnJbsqatMibR6OicVrUDaz0fyxNtBDpPlLfibJZILzHQcwaKkb4ia57xAShIJfQ54HjOG1oPXBew/640?wx_fmt=gif&wxfrom=5&wx_lazy=1&tp=webp "")  
  
