#  八大主流中文手机输入法曝出泄密漏洞，仅华为幸免   
 安全内参   2024-04-25 19:39  
  
**关注我们**  
  
  
**带你读懂网络安全**  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvY3YGeGS99tJpzJhCYLpA2pr2nJo2E227LEjmnve31dcvXhPUklygGj9CLgKreSJtCO8JR3Fn0c4w/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
近日，隐私研究机构citizenlab发布安全报告显示，除华为以外的八家厂商（百度、荣耀、讯飞、OPPO、三星、腾讯、vivo、小米）的手机云输入法应用均存在严重漏洞，黑客可利用这些漏洞完全窃取用户输入内容，或实施网络窃听。  
  
  
  
**超10亿用户面临泄密风险**  
  
  
  
报告指出，仅搜狗、百度和讯飞这三家的输入法市场份额就占中国第三方输入法市场的95%以上，用户数量约为10亿。  
  
  
除第三方输入法程序用户之外，研究者还发现，来自荣耀、OPPO和小米这三家厂商的设备预装键盘输入法也存在漏洞。2023年，仅荣耀、OPPO和小米三家就占据了中国智能手机市场份额的近50%。此外，三星和vivo的设备也捆绑了存在漏洞的键盘输入法，但并不是默认选项。  
  
  
Citizenlab表示已经向上述八家存在漏洞的手机输入法厂商报告了漏洞，大多数厂商做出了积极回应，严肃对待问题并修复了报告的漏洞，但仍有一些键盘应用程序存在漏洞。  
  
  
Citizenlab在报告中警告说，五眼联盟情报机构此前曾利用类似的中文输入法安全漏洞实施大规模监控。  
  
  
  
**云输入法安全危机**  
  
  
  
随着用户规模的不断增长，云输入法应用的后端技术正变得越来越复杂，人们对此类应用的潜在安全风险也越来越重视。安全研究人员对云输入法应用安全最关注的问题主要有两点：  
  
- 用户数据在云服务器上是否安全  
  
- 信息从用户设备传输到云服务器的过程中是否安全  
  
研究人员测试了腾讯、百度、讯飞、三星、华为、小米、OPPO、vivo和荣耀输入法的多个平台版本（安卓、iOS和Windows版本）。其中腾讯、百度和科大讯飞——是键盘输入法应用的开发者，其余六家——三星、华为、小米、OPPO、vivo和荣耀是手机制造商，它们要么自己开发了键盘输入法，要么预装了上述三个输入法产品。  
  
  
为了更好地了解这些厂商的键盘应用是否安全地实现了其云推荐功能，研究者对这些输入法进行了安全分析以确定它们是否充分加密了用户的输入按键记录。  
  
  
研究者使用了静态和动态分析方法：使用jadx反编译并静态分析Dalvik字节码，并使用IDAPro反编译并静态分析本机机器码；使用frida动态分析Android和iOS版本，并使用IDAPro动态分析Windows版本。最后，研究者使用Wireshark和mitmproxy执行网络流量捕获和分析。  
  
  
对九家厂商的输入法进行分析后，研究者发现只有一家厂商（华为）的输入法应用在传输用户按键记录时未发现任何安全问题。其余八家厂商的每一家至少有一款应用发现了漏洞，黑客可以利用该漏洞完全窃取用户输入的内容（下图）：  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/INYsicz2qhvY3YGeGS99tJpzJhCYLpA2p2SDvvnDZxHac1AhcwKpc8XtFyCR6U9Nhicj2jywlxFYOltluwaoQ6tw/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
在大多数情况下，攻击者只需要是网络上的被动窃听者即可利用这些漏洞。但是，在某种情况下，针对使用腾讯搜狗API的应用，攻击者还需要能够向云服务器发送网络流量，但他们不必一定是中间人(MitM)或在网络第3层欺骗来自用户的流量。在所有情况下，攻击者都必须能够访问客户端软件的副本。  
  
  
由于苹果和谷歌的键盘输入法应用都没有将按键记录传输到云服务器以进行云推荐，因此没有（也无法）分析这些键盘的安全功能。  
  
  
研究者表示，虽然业界一直在推动开发能够保密用户数据的隐私感知云输入法，但目前并未得到广泛使用。  
  
  
  
**隐私专家的建议**  
  
  
  
输入法安全漏洞可导致个人财务信息、登录账号和隐私泄露。隐私专家建议手机用户应保持应用程序和操作系统更新到最新版本。如果用户担心云输入法的隐私问题，建议考虑切换到完全在设备上运行的本地输入法应用（模式）。  
  
  
参考链接：  
  
https://github.com/citizenlab/reports/blob/main/2024-04-not-so-silent-type.pdf  
  
  
  
**推荐阅读**  
- [网安智库平台长期招聘兼职研究员](http://mp.weixin.qq.com/s?__biz=MzI4NDY2MDMwMw==&mid=2247499450&idx=2&sn=2da3ca2e0b4d4f9f56ea7f7579afc378&chksm=ebfab99adc8d308c3ba6e7a74bd41beadf39f1b0e38a39f7235db4c305c06caa49ff63a0cc1d&scene=21#wechat_redirect)  
  
  
- [欢迎加入“安全内参热点讨论群”](https://mp.weixin.qq.com/s?__biz=MzI4NDY2MDMwMw==&mid=2247501251&idx=1&sn=8b6ebecbe80c1c72317948494f87b489&chksm=ebfa82e3dc8d0bf595d039e75b446e14ab96bf63cf8ffc5d553b58248dde3424fb18e6947440&token=525430415&lang=zh_CN&scene=21#wechat_redirect)  
  
  
  
  
  
  
文章来源：GoUpSec  
  
  
点击下方卡片关注我们，  
  
带你一起读懂网络安全 ↓  
  
  
  
  
