#  Oracle使用2024年4月CPU修补了230个漏洞   
何威风  河南等级保护测评   2024-04-19 00:01  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rTibWNx9ARWlh1TrpvO3lw5r1aGYan6OQs6nm63hg1YdDiceTvCg0ChN0OdNLY05oibsdZUdwyOlOzgDCMrMu1icaA/640?wx_fmt=png&from=appmsg "")  
  
**Oracle 周二宣布了 441 个新安全补丁，作为 2024 年 4 月重要补丁更新的一部分。其中 200 多个解决了可能被远程、未经身份验证的攻击者利用的缺陷。**  
  
Oracle   
  
2024 年 4 月的 CPU  
中发现了大约 230 个独特的 CVE   
。超过 30 个安全补丁可解决严重级别的漏洞。  
  
Oracle Communications 本月收到的安全补丁数量最多，达到 93 个。其中，71 个解决了无需身份验证即可远程利用的错误。  
  
接下来是融合中间件（51 个安全补丁 - 35 个解决可远程利用、未经身份验证的问题）、金融服务应用程序 (49 - 30) 和电子商务套件 (47 - 43)。  
  
还发布了针对 MySQL 的大量补丁（36 个补丁 – 9 个针对可远程利用的漏洞，无需身份验证）、系统 (22 – 16)、通信应用程序 (14 – 10)、Java SE (13 – 10)、虚拟化 (13 – 1) 、分析 (12 – 5)、企业管理器 (11 – 7)、PeopleSoft (10 – 5) 和零售应用程序 (10 – 9)。  
  
本月收到安全补丁的其他 Oracle 产品包括数据库服务器、商业、建筑和工程、保险应用程序、供应链、支持工具、食品和饮料应用程序、医疗保健应用程序、公用事业应用程序、Hyperion、酒店应用程序、健康科学应用程序、自主健康框架、大数据空间和图形、全局生命周期管理和 GoldenGate。  
  
其中一些应用程序的补丁还解决了其他安全缺陷，其中一些解决了多个不可利用的缺陷。Oracle 针对影响多个应用程序的漏洞发布了单独的修复程序。  
  
建议Oracle客户尽快应用补丁。  
  
“据报道，攻击者之所以成功，是因为目标客户未能应用可用的 Oracle 补丁。因此，甲骨文强烈建议客户继续使用积极支持的版本，并立即应用关键补丁更新安全补丁。”这家科技巨头指出。  
  
周二，Oracle 还宣布发布针对  
Solaris 操作系统中第三方组件的  
13 个新安全补丁、针对 Oracle Linux 的  
  
71 个新补丁  
以及  
针对 Oracle VM Server for x86 的   
3 个新安全补丁。  
  
