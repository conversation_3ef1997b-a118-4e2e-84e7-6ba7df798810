#  微软12月补丁星期二值得关注的漏洞   
综合编译  代码卫士   2023-12-13 17:58  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Az5ZsrEic9ot90z9etZLlU7OTaPOdibteeibJMMmbwc29aJlDOmUicibIRoLdcuEQjtHQ2qjVtZBt0M5eVbYoQzlHiaw/640?wx_fmt=gif "")  
  
   
聚焦源代码安全，网罗国内外最新资讯！****  
  
**编译：代码卫士**  
  
**本月，微软修复了33个漏洞，加上前几天修复的9个Chromium 漏洞，则共计修复42个漏洞。**  
  
  
在所发布的补丁中，其中4个是 “严重”级别，29个是“重要”级别。一般来说12月的漏洞发布量较小，本月也不例外。实际上本月是自2017年起，漏洞数量最少的12月。不过今年一年微软共计修复900多个CVE漏洞，是微软补丁史上最为忙碌的年份之一。  
  
  
**已遭公开披露的 0day 漏洞**  
  
  
  
  
本月修复了一个 AMD 0day 漏洞。该漏洞在8月份发布，当时仍是未修复状态。  
  
CVE-2023-20588 是位于AMD 特定处理器中的一个除零漏洞，可能导致返回敏感数据。该漏洞在2023年8月披露，不过AMD 公司除了提供缓解措施外，并未提供任何修复方案。该公司提出的缓解措施是，“开发人员可在更改权限边界前，确保除法运算中未使用权限数据。由于该漏洞需要本地访问权限，因此AMD 认为潜在影响较低。”微软在本月补丁星期二中发布了安全更新，修复了受影响AMD 处理器中的该漏洞。  
  
  
**其它值得关注的漏洞**  
  
  
  
****  
**CVE-2023-35628**是 Windows MSHTML 平台上的RCE 漏洞。该漏洞可导致远程未认证攻击者仅通过向目标发送特殊构造邮件的方式，在受影响系统上执行任意代码。这通常说明 Preview Pane 是一个攻击向量，但在本案例中并非如此。Outlook 检索并处理邮件时，代码执行就会发生，而这发生在 Preview Pane 之前。毫无疑问，勒索团伙将尝试为该漏洞创造可靠 exploit，而由于利用无需memory-shaping 技术，因此可能会遇到一些问题。  
  
**CVE-2023-36019**：微软 Power 平台上的连接器欺骗漏洞。这是本月CVSS评分最高 (9.6) 的漏洞，位于 web 服务器上。然而，如果受影响系统遵循特殊构造的链接，则恶意脚本将会在客户端浏览器上执行。微软通过 Microsoft 365 Admin Center 将该漏洞告知受影响用户。Admin Center 用户应当认真阅读该公告，获悉全部漏洞详情。  
  
**CVE-2023-35636**是微软 Outlook 信息披露漏洞。该Outlook 漏洞并不具备 Preview Pane 攻击向量。然而，如遭利用，则可导致 NTLM 哈希遭泄露。这些哈希可用于欺骗其它用户并获得企业内更多访问权限。今年年初，微软将类似漏洞称为提权漏洞而非信息泄露漏洞。不管如何划分，威胁行动者非常青睐于这类漏洞并经常利用。  
  
  
  
代码卫士试用地址：  
https://codesafe.qianxin.com  
  
开源卫士试用地址：https://oss.qianxin.com  
  
  
  
  
  
  
  
  
  
  
  
  
**推荐阅读**  
  
[微软11月补丁星期二需要关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247518139&idx=1&sn=941a9da7093fb53ada0f874db9d3ef39&chksm=ea94b6d1dde33fc740734b8dc6fbbcd0d0ea7446e8a8bfa3c8bdb1423e2e2c01536e8adf2960&scene=21#wechat_redirect)  
  
  
[十月补丁星期二值得关注的漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517841&idx=1&sn=996f3399f60a91c25395c5c879a049e1&chksm=ea94b7fbdde33eedc9257fadac74eda5188d3e34694eb6e3eb872b3d35e3d410c2e0cf04f03a&scene=21#wechat_redirect)  
  
  
[补丁星期二：微软、Adobe和Firefox纷纷修复已遭利用的 0day 漏洞](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=2247517643&idx=1&sn=83e85b6b9bf3a9f0cf0c1843c9589950&chksm=ea94b4a1dde33db74b2b9c5ff5da439c9a2169fcab51d215bdc495affe02787d31ab6bcf7b98&scene=21#wechat_redirect)  
  
  
[微软7月补丁星期二修复132个漏洞：5个已遭利用0day且1个无补丁](http://mp.weixin.qq.com/s?__biz=MzI2NTg4OTc5Nw==&mid=**********&idx=1&sn=5074282ae6c24bac3355b40d1cabb8fa&chksm=ea94b232dde33b24e9adff41dd364497012cd4fe06f43ac2e6579c5f297ce1443c3c745b757b&scene=21#wechat_redirect)  
  
  
  
  
**原文链接**  
  
https://www.zerodayinitiative.com/blog/2023/12/12/the-december-2023-security-update-review  
  
https://www.bl  
eepingcomputer.com/news/microsoft/microsoft-december-2023-patch-tuesday-fixes-34-flaws-1-zero-day/  
  
  
题图：  
Pixabay  
 License  
  
****  
**本文由奇安信编译，不代表奇安信观点。转载请注明“转自奇安信代码卫士 https://codesafe.qianxin.com”。**  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSf7nNLWrJL6dkJp7RB8Kl4zxU9ibnQjuvo4VoZ5ic9Q91K3WshWzqEybcroVEOQpgYfx1uYgwJhlFQ/640?wx_fmt=jpeg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/oBANLWYScMSN5sfviaCuvYQccJZlrr64sRlvcbdWjDic9mPQ8mBBFDCKP6VibiaNE1kDVuoIOiaIVRoTjSsSftGC8gw/640?wx_fmt=jpeg "")  
  
**奇安信代码卫士 (codesafe)**  
  
国内首个专注于软件开发安全的产品线。  
  
   ![](https://mmbiz.qpic.cn/mmbiz_gif/oBANLWYScMQ5iciaeKS21icDIWSVd0M9zEhicFK0rbCJOrgpc09iaH6nvqvsIdckDfxH2K4tu9CvPJgSf7XhGHJwVyQ/640?wx_fmt=gif "")  
  
   
觉得不错，就点个 “  
在看  
” 或 "  
赞  
” 吧~  
  
