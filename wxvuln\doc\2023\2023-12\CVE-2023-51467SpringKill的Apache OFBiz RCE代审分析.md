#  CVE-2023-51467|SpringKill的Apache OFBiz RCE代审分析   
原创 春纱  卫界安全-阿呆攻防   2023-12-30 12:15  
  
   
  
此文章由SpringKiller安全研究师傅产出，这位佬是一个能独立开发一款企业级IAST，伸手0day伸脚1day，能手搓操作系统用脚逆向的师傅，还是OWASP的代码贡献者之一。哦，差点忘了，这个师傅能书会画，上厅堂下厨房无所不能，呆哥建议爱学习的可以找他击剑一下。  
  
  
SpringKill师傅的Github地址：https://github.com/springkill  
  
sdfd  

				
				  
  
   
  
01  
  
 漏洞描述  
  

				
				  
  
因为前面爆出的OFBiz漏洞中的权限绕过并没有被修复，所以导致了这次的漏洞，通过外部调用groovy并且绕过黑名单达到RCE。  
   
  
02  
  
 漏洞分析  
  

				
				  
  
   
漏洞的位置很简单也很明了：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hFPkDXcMlMuadOiauDUzNOPa4GyPtCUZVFyzMGRexuIR46TtxgHALUMrMLt4ln1ianVcGrGyvibLTheT9MCmZRsIg/640?wx_fmt=png&from=appmsg "")  
  
  
关键就是我们如何找到一个入口，传入这个expression呢，那么恰好后台有这么个功能可以进行表达式的传入：  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hFPkDXcMlMuadOiauDUzNOPa4GyPtCUZVsJr68RicXLE53HCzww116LStkdvvQk8jMZFXYicxAeJJibS7Hnx1sVtKg/640?wx_fmt=png&from=appmsg "")  
  
  
查看controller.xml可以知道，这里的类型是一个视图，所以我们直接去看视图的配置：  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hFPkDXcMlMuadOiauDUzNOPa4GyPtCUZVHVb17wTkwU5MyBra1rMWCRHJGpb59WHwQWr3ZCmDGZUQNr8XyhWUSA/640?wx_fmt=png&from=appmsg "")  
  
  
定位到这么一个文件，去看一下里面的详细信息，接受值的内容如下，后面就是调用shell执行groovy了，就不截图了：  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hFPkDXcMlMuadOiauDUzNOPa4GyPtCUZVYBlOPkbypVk7xm0LF8DzwE0TX0LKB5w2nCOzpAxAU1ouUASHBB7dmA/640?wx_fmt=png&from=appmsg "")  
  
  
最后的执行效果如下：  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/hFPkDXcMlMuadOiauDUzNOPa4GyPtCUZVj7kx7lOrticDibIdPYtzYDG33SMbbYZEQuUibNxdFh6m27JqIForEnUsQ/640?wx_fmt=png&from=appmsg "")  
  
  
其实发现这个漏洞最开始并不是配置文件来的，web界面中有个功能就叫  
可编程导出  
，就是这个url，同时参数也是这个参数，通过没有修复的鉴权来绕过登录直接去执行命令。  
  
  
传入的值为：  
groovyProgram=println+%22calc%22.execute%28%29.text  
，并没有什么特殊的技术含量，只是执行了execute而已。  
  
