#  【已复现】金蝶Apusic应用服务器文件上传致远程代码执行漏洞   
 长亭安全应急响应中心   2023-12-20 20:14  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicQu14BJfCt1WuQVictGhef2owc015YbsxweLQ9NaSZ3Pm7BfZ9kBwv9L0WX8pjGzRrIibT6p81yh3ZA/640?wx_fmt=png&from=appmsg "")  
  
  
金蝶Apusic应用服务器是一款企业级应用服务器，支持Java EE技术，适用于各种商业环境。  
  
  
2023年12月，互联网上披露金蝶Apusic应用服务器存在文件上传漏洞，攻击者可利用该漏洞获取服务器控制权限。该漏洞利用简单，建议受影响的客户尽快修复漏洞。  
  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因漏洞主要源于其后台文件上传功能的安全缺陷。虽然该功能位于后台，但由于在处理访问路径上存在不足，攻击者得以绕过正常的权限检查而在前台使用后台功能。而且，服务器未能正确验证和过滤上传文件的路径，允许攻击者通过构造特殊的文件名和路径（例如使用"../../../../"进行路径穿越）将恶意脚本上传至服务器的敏感或非预期目录。利用特征从流量层面来看，该漏洞的利用特征体现在异常的HTTP POST请求中。在正常情况下，文件上传功能的使用应仅限于预设路径。然而，在攻击流量中，可以观察到文件名包含了旨在越过目录限制的payload。这些请求通常以多个连续的"../"序列出现，这是路径穿越攻击的典型标志。漏洞影响这一漏洞的成功利用可能导致多种安全风险。首先，攻击者能够在服务器上的任意位置上传文件，这可能用于植入恶意软件或篡改现有文件。此外，通过上传特定的脚本或执行文件，攻击者可能获得服务器的进一步控制权。最严重的情况下，这可能导致服务器的完全接管，敏感数据泄露，甚至将服务器转化为发起其他攻击的跳板。影响版本 Affected Version 02金蝶Apusic应用服务器 <= V9.0 SP7解决方案 Solution 03临时缓解方案1. 在确认不影响业务的情况下，暂时暂停应用服务器管理控制台和移除默认首页，直到使用升级补丁。可参考官方给出的步骤进行操作（https://www.apusic.com/view-477-113.html）。2. 如非必要，不要将 受影响系统 放置在公网上。或通过网络ACL策略限制访问来源，例如只允许来自特定IP地址或地址段的访问请求。升级修复方案官方已发布新版本修复漏洞，建议尽快访问官网（https://www.apusic.com/view-477-113.html）或联系官方售后支持获取版本升级安装包或补丁。漏洞复现 Reproduction 04产品支持 Support05云图：默认支持该产品的指纹识别，同时支持该漏洞的PoC原理检测。全悉：已发布规则升级包，支持检测该漏洞的利用行为。雷池：已发布规则升级包，支持检测该漏洞的利用行为。  
**时间线**  
  
 Timeline   
  
  
  
**06**  
12月7日 官方发布补丁公告12月19日 长亭应急响应实验室漏洞分析与复现12月20日 长亭安全应急响应中心发布通告  
参考资料：  
  
[1].https://www.apusic.com/view-477-113.html  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
