#  还是狠炸裂的！Chromium libxslt XXE漏洞（CVE-2023-435）   
长亭应急响应  黑伞安全   2023-11-19 18:40  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/FOh11C4BDicSiaXPIUHVsq2337Iacam7EGYp4QC3DVlzLRBmLMRSWfet3j9vfylsPvJ44b2OUpKEmVhtjSukNCBQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
修订：编号为CVE-2023-4357。Chromium 是一个开源的网络浏览器项目，由 Google 主导开发。Chromium 常被用作其他浏览器项目的基础，例如Chrome、 Opera、Brave 和 Microsoft Edge。2023年6月，一位国外安全研究员Igor Sak-Sakovskii向Chromium项目报告了一个XXE漏洞，攻击者能够利用该漏洞绕过浏览器的安全限制，访问读取本地文件。  
  
  
**漏洞描述**  
  
   
Description   
  
  
  
**0****1**  
  
漏洞成因这一安全漏洞的根源在于Chromium项目集成了libxslt，libxslt被用于XSL处理，但它允许在XSL document()方法加载的文档内包含外部实体。攻击者可以利用这一行为从http(s)://URLs 访问file://URLs，进而绕过安全限制，获取文件访问权限。此外，当使用-no-sandbox属性时，攻击者能够在任意操作系统上读取任何文件。这一漏洞主要由于Chromium在实施安全策略和沙盒机制时未能充分考虑与libxslt的交互方式。利用特征这个漏洞的利用相对隐蔽，不需要复杂的交互或留下明显的痕迹。攻击者可以通过发送一个包含恶意XSL样式表和SVG图像的网站链接给受害者。一旦受害者点击这个链接，浏览器便会加载并解析这些恶意内容。攻击者能够利用这个漏洞来读取本地文件。这种攻击通常是不容易感知的，受害者可能完全不知道他们已经受到攻击。因此，检测这类攻击非常困难，这也使得这类漏洞更加危险。漏洞影响这个漏洞对基于Chromium的浏览器安全构成了严重威胁。攻击者可以利用这个漏洞来实现对本地文件的未授权访问。这可能导致敏感信息泄露，特别是在使用禁用沙盒参数（-no-sandbox）的环境下。此外，由于Chromium被广泛用于应用内自集成浏览器，这种漏洞的存在可能被用于更复杂的攻击链中，进一步威胁到用户的安全和隐私。影响版本 Affects 02Chrome < 116.0.5845.96以及其他使用Chromium内核的浏览器和应用程序  
**解决方案**  
  
   
Solution   
  
  
  
**03******  
临时缓解方案对于使用Chromium内核的浏览器和应用程序，请定期检查并关注来自官方渠道的安全更新和补丁。一旦发布了针对此漏洞的修复更新，应尽快进行升级。在等待官方发布安全更新期间，建议用户不要点击或打开来自不可信或未知来源的URL。这种预防措施可以减少受到潜在攻击的风险。升级修复方案Google官方已发布新版本Chrome修复漏洞，建议尽快在Chrome内点击“关于Google Chrome”升级到最新版本。漏洞复现 Reproduction 04时间线 Timeline 056月29日 安全研究员报告漏洞8月31日 Chromium官方修复漏洞10月25日 漏洞情报在互联网公开11月17日 长亭应急响应实验室复现确认漏洞11月17日 长亭安全应急响应中心发布通告参考资料：[1].https://chromereleases.googleblog.com/2023/08/stable-channel-update-for-desktop_15.html[2].https://bugs.chromium.org/p/chromium/issues/detail?id=1458911  
  
  
**长亭应急响应服务**  
  
  
  
  
全力进行产品升级  
  
及时将风险提示预案发送给客户  
  
检测业务是否收到此次漏洞影响  
  
请联系长亭应急团队  
  
7*24小时，守护您的安全  
  
  
第一时间找到我们：  
  
邮箱：<EMAIL>  
  
应急响应热线：4000-327-707  
  
  
收录于合集   
#  
2023漏洞风险提示  
  
 29  
个  
  
上一篇  
【已复现】金蝶云星空ScpSupRegHandler任意文件上传漏洞  
  
  
