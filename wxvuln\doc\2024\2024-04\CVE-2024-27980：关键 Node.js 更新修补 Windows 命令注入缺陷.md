#  CVE-2024-27980：关键 Node.js 更新修补 Windows 命令注入缺陷   
 Ots安全   2024-04-11 17:41  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/bL2iaicTYdZn7gtxSFZlfuCW6AdQib8Q1onbR0U2h9icP1eRO6wH0AcyJmqZ7USD0uOYncCYIH7ZEE8IicAOPxyb9IA/640?wx_fmt=gif "")  
  
在 Windows 上未启用 shell 选项的情况下通过 child_process.spawn 的 args 参数进行命令注入 (CVE-2024-27980) - (HIGH)  
  
由于child_process.spawn / child_process.spawnSync中的批处理文件处理不当，即使未启用shell选项，恶意命令行参数也可以注入任意命令并实现代码执行。  
  
**影响：**  
  
此漏洞影响活动版本线中的所有用户：18.x、20.x、21.x  
  
感谢 ryotak 报告此漏洞，并感谢 Ben Noordhuis 修复该漏洞。  
  
**概括**  
  
Node.js 项目将于 2024 年 4 月 9 日星期二或之后不久发布 18.x、20.x、21.x 版本的新版本，以便解决：  
- 1. 严重性高的问题。  
  
**影响**  
  
Node.js 的 18.x 发行版容易受到 1 个高严重性问题的影响。Node.js 20.x 版本系列容易受到 1 个高严重性问题的影响。Node.js 的 21.x 版本系列容易受到 1 个高严重性问题的影响。  
  
  
  
  
感谢您抽出  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycNnFvFYVgXoExRy0gqCkqvrAghf8KPXnwQaYq77HMsjcVka7kPcBDQw/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycd5KMTutPwNWA97H5MPISWXLTXp0ibK5LXCBAXX388gY0ibXhWOxoEKBA/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycU99fZEhvngeeAhFOvhTibttSplYbBpeeLZGgZt41El4icmrBibojkvLNw/640?wx_fmt=gif "")  
  
来阅读本文  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWge7Mibiad1tV0iaF8zSD5gzicbxDmfZCEL7vuOevN97CwUoUM5MLeKWibWlibSMwbpJ28lVg1yj1rQflyQ/640?wx_fmt=gif "")  
  
**点它，分享点赞在看都在这里**  
  
