#  【漏洞预警】PuTTY密钥恢复漏洞（CVE-2024-31497）   
cexlife  飓风网络安全   2024-04-17 23:06  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu01azmpPC83Vze7znjViaRCk31xWibI2RryVCAXZUlwD0SU9TZRjlQZz6nx5EVib06rMbZLJ36kNQia9xQ/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**PuTTY是一种流行的开源终端仿真器、串行控制台和网络文件传输应用程序,支持 SSH、Telnet、SCP和SFTP等协议,可使用该软件通过SSH远程访问和管理服务器和其他网络设备,近日监测到PuTTY中存在一个密钥恢复漏洞（CVE-2024-31497）,PuTTY 版本0.68-0.80中使用NIST P521曲线的ECDSA私钥生成签名的代码中存在漏洞（当使用PuTTY或Pageant对SSH服务器进行身份验证时,它会根据密钥生成签名）,获得约60条签名消息和公钥的威胁者可能恢复用户的NIST P-521私钥,然后伪造签名,登录使用该密钥的任何服务器,导致信息泄露和未授权访问SSH服务器等。**影响范围:**PuTTY版本 0.68 - 0.80注:该漏洞除了影响PuTTY 之外,以下（不一定完整）产品列表捆绑了受影响的PuTTY 版本,因此也容易受到攻击:FileZilla 3.24.1 - 3.66.5WinSCP 5.9.5 - 6.3.2TortoiseGit 2.4.0.2 - 2.15.0TortoiseSVN 1.10.0 - 1.14.6**安全措施:**升级版本目前该漏洞已经修复，受影响用户可升级到以下版本:PuTTY 0.81FileZilla 3.67.0WinSCP 6.3.3TortoiseGit 2.15.0.1建议TortoiseSVN用户在通过SSH 访问 SVN 版本库时,配置 TortoiseSVN使用最新PuTTY 0.81版本的Plink,直到补丁可用。**下载链接:**https://www.chiark.greenend.org.uk/~sgtatham/putty/latest.html**临时措施:**唯一受影响的密钥类型是521位ECDSA(ecdsa-sha2-nistp521),也就是说,在Windows PuTTYgen中,在“Key fingerprint”框的开头出现一个带有ecdsa-sha2-nistp521的密钥,或者在加载到Windows Pageant时被描述为“NIST p521”,或在SSH协议或密钥文件中的ID开头为 ecdsa-sha2-nistp521,其他大小的ECDSA和其他关键算法不受影响,如果拥有这种类型的密钥,建议立即撤销它:从所有 OpenSSH authorized_keys 文件和其他SSH服务器中的相应文件中删除旧的公钥，这样被破坏的密钥的签名就没有任何价值了。然后生成一个新的密钥对来替换它。参考链接:https://www.openwall.com/lists/oss-security/2024/04/15/6  
  
