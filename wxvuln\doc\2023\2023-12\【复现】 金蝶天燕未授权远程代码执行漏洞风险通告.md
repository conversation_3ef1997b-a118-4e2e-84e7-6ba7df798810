#  【复现】 金蝶天燕未授权远程代码执行漏洞风险通告   
原创 赛博昆仑CERT  赛博昆仑CERT   2023-12-19 20:25  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/iaZ7t7b9Dodvib7ddpGMC6vx4COAy4sBoGbGCkwVUIJSHBPI0z1Utrp1h5ys6ygT3albl3PgjejJcRRRiaDFFbMBA/640?wx_fmt=gif "")  
  
  
-  
赛博昆仑漏洞安全通告-  
  
金蝶天燕未授权远程代码执行漏洞风险通告  
  
  
![](https://mmbiz.qpic.cn/mmbiz_svg/7j1UQofaR9fsNXgsOXHVKZMJ1PCicm8s4RHQVjCJEjX63AsNibMx3So4wSMAvubEOoU2vLqYY7hIibIJbkEaPIDs5A4ianh5jibxw/640?wx_fmt=svg "")  
  
  
  
****  
**漏洞描述**  
  
  
金蝶Apusic应用服务器（Apusic Application Server，AAS）是一款标准、安全、高效、集成并具丰富功能的企业级应用服务器软件，全面支持JakartaEE8/9的技术规范，提供满足该规范的Web容器、EJB容器以及WebService容器等，支持Websocket1.1、Servlet4.0、HTTP2.0等最新的技术规范，为企业级应用的便捷开发、灵活部署、可靠运行、高效管控以及快速集成等提供关键支撑。  
  
近日，赛博昆仑CERT监测到  
金蝶天燕未授权远程代码执行漏洞  
的情报。  
Apusic应用服务器的管控台存在访问路径权限控制失效、对参数校验不严格的问题，攻击者可以构建绕过权限控制的请求，恶意访问和操作管控台，导致安全风险。  
<table><tbody><tr><td valign="top" style="border-width: 1pt;border-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞名称</span></strong><o:p></o:p></span></p></td><td colspan="3" valign="top" style="border-top-width: 1pt;border-color: rgb(221, 221, 221);border-right-width: 1pt;border-bottom-width: 1pt;border-left-width: initial;border-left-style: none;padding: 3pt 6pt 1.5pt;"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="letter-spacing: 1.8px;text-align: left;text-indent: 28px;text-wrap: wrap;background-color: rgb(255, 255, 255);font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">金蝶天燕未授权远程代码执行漏洞</span><span style="color: rgb(0, 122, 170);"><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞公开编号</span></strong><o:p></o:p></span></p></td><td colspan="3" valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;">暂无</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">昆仑漏洞库编号</span></strong><o:p></o:p></span></p></td><td colspan="3" valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span lang="EN-US" style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;font-family: Arial, sans-serif;">CYKL-2023-023027</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞类型</span></strong><o:p></o:p></span></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="127"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">代码执行</span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="148"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">公开时间</span></strong></span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="133"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span lang="EN-US" style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;font-family: Arial, sans-serif;">2023-12-07</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞等级</span></strong><o:p></o:p></span></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="127"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">高危</span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="148"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">评分</span></strong></span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="133"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;">暂无</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞所需权限</span></strong><o:p></o:p></span></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="127"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">无权限要求</span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="148"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞利用难度</span></strong></span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="133"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;">低</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span lang="EN-US" style="font-size: 11pt;line-height: 17.6px;font-family: Arial, sans-serif;">PoC</span></strong><strong><span style="font-size: 11pt;line-height: 17.6px;">状态</span></strong><o:p></o:p></span></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="127"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">未知</span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="148"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span lang="EN-US" style="font-size: 11pt;line-height: 17.6px;font-family: Arial, sans-serif;">EXP</span></strong><strong><span style="font-size: 11pt;line-height: 17.6px;">状态</span></strong></span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="133"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;">未知</span><o:p></o:p></span></p></td></tr><tr><td valign="top" style="border-right-width: 1pt;border-color: rgb(221, 221, 221);border-bottom-width: 1pt;border-left-width: 1pt;border-top-width: initial;border-top-style: none;padding: 3pt 6pt 1.5pt;" width="169"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">漏洞细节</span></strong><o:p></o:p></span></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="127"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="font-size: 11pt;line-height: 17.6px;color: rgb(0, 122, 170);">未知</span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;" width="148"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><strong><span style="font-size: 11pt;line-height: 17.6px;">在野利用</span></strong></span><o:p></o:p></p></td><td valign="top" style="border-top: none rgb(221, 221, 221);border-left: none rgb(221, 221, 221);border-bottom-width: 1pt;border-bottom-color: rgb(221, 221, 221);border-right-width: 1pt;border-right-color: rgb(221, 221, 221);padding: 3pt 6pt 1.5pt;word-break: break-all;" width="133"><p style="text-align:left;margin: 6pt 0cm;font-size: 10.5pt;font-family: DengXian;line-height: 16.8px;"><span style="color: rgb(0, 122, 170);"><span style="color: rgb(0, 122, 170);font-size: 11pt;line-height: 17.6px;">未知</span></span><span style="background-color: rgb(255, 255, 255);color: rgb(91, 91, 91);font-family: mp-quote, -apple-system-font, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;letter-spacing: 1.8px;text-indent: 2em;"></span></p></td></tr></tbody></table>  
  
  
**影响版本**  
  
  
Apusic应用服务器 < V9.0 SP8  
  
  
  
**利用条件**  
  
  
  
无需任何利用条件  
  
**漏洞复现**  
  
目前赛博昆仑CERT已确认漏洞原理，复现截图如下：  
  
  
弹出计算器作为证明![](https://mmbiz.qpic.cn/sz_mmbiz_png/iaZ7t7b9DodtZJa9KdljftEWn3cV1hcaP0EaBk9OBIOLY4XxmlTgE0G1sQhSJhqW1fqorBQl9yR7ctyU2JtW9aQ/640?wx_fmt=png&from=appmsg "")  
  
  
**防护措施**  
  
- **修复措施**  
  
目前，官方已发布修复建议，建议受影响的用户尽快升级至安全版本。  
  
下载地址：  
https://www.apusic.com/view-477-113.html  
  
  
  
**技术咨询**  
  
赛博昆仑支持对用户提供轻量级的检测规则或热补方式，可提供定制化服务适  
配多种产品及规则，帮助用户进行漏洞检测和修复。  
  
赛博昆仑CERT已开启年订阅服务，付费客户(可申请试用)将获取更多技术详情，并支持适配客户的需求。  
  
联系邮箱：<EMAIL>  
  
公众号：赛博昆仑CERT  
  
**参考链接**  
  
https://www.apusic.com/view-477-113.html  
  
**时间线**  
  
   
  
   
  
2023年  
12月07日，官方发布通告  
  
 2023年12月19日，赛博昆仑CERT公众号发布漏洞风险通告  
  
  
  
  
  
  
  
  
  
  
