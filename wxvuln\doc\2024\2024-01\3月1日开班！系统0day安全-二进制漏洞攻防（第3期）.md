#  3月1日开班！系统0day安全-二进制漏洞攻防（第3期）   
小雪  看雪学苑   2024-01-28 17:59  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnRPB1ibpDvze0koiaBGKX52P0fTOaf9pfH2kMF4YSYK45sNQ6oSMBvSRQ/640?wx_fmt=gif&from=appmsg "")  
  
好消息!好消息!  
  
系统0day安全-二进制漏洞攻防（第3期）火热开课啦！  
  
  
想要深入了解二进制漏洞攻防的知识和实践经验吗？想要掌握模糊测试、AFL原****  
理、ASAN原理、网络协议漏洞挖掘、Linux内核漏洞挖掘、AOSP漏洞挖掘以及CodeQL代码审计等多个方面的技能吗？  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8GstpDu7iaH5qk61bgepbXF8Om4x234w2syfz6qbvPuPImNKDyC7crud4wEUK0jqh45AgIic7EQjTHA/640?wx_fmt=png&from=appmsg "")  
  
**预售期间享75折**  
  
课程较难，需具备二进制安全基础，预估时长80小时  
  
  
本课程涵盖了二进制漏洞攻防领域的多个方面，为学员提供了全面的学习资源。**课程结合实际案例进行讲解，帮助学员更好地理解和掌握二进制漏洞攻防的实际应用。**  
而且，课程强调实践操作，教授如何使用模糊测试工具、改造模糊测试工具、从零实现模糊测试工具等，提升学员的动手能力和实际操作经验。不仅如此，通过学习本课程，还能够**掌握二进制漏洞的挖掘和利用技术，提高企业的安全防护能力。**  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnUcHMibFDHib4j8ib1vbp74WUZU5ia96iaLbfDUH6AVVb3VSYbRgUgFDj6Ag/640?wx_fmt=png&from=appmsg "")  
  
**购课须知（*满10人开班）**  
  
1、预售日期：2023年12月21日-2024年2月29日（预售期75折优惠）  
  
2、开课日期：2024年3月1日，半年内可学完  
  
3、仅支持企业、机构等团体购买：个人用户不接收报名  
  
4、课程咨询：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKn0vvXlfpoeJLicZRicaADVx5ibcXcibqOSFmMReIXDEzYVVYKorPb1aVqyw/640?wx_fmt=gif&from=appmsg "")  
  
万先生  
  
*注意  
  
（1）咨询课程请联系助教，我们将全程为您服务  
  
（2）购买课程后，请及时联系助教签订保密协议  
  
**0****1**  
  
**课程简介**  
  
  
  
**《系统0day安全-二进制漏洞攻防（第3期）》**  
  
本课程旨在为学员提供深入的二进制安全知识和实践经验，课程内容涵盖了从基本的漏洞挖掘技术到高级的漏洞利用方法，涉及模糊测试、AFL原理、ASAN原理、网络协议漏洞挖掘、Linux内核漏洞挖掘、AOSP漏洞挖掘以及CodeQL代码审计等多个方面。**课程通过理论讲解、实践操作和实战案例分析，帮助学员全面掌握二进制漏洞攻防的相关技能。**  
  
  
随着信息技术的快速发展，企业的信息系统和网络环境变得越来越复杂，同时也面临着越来越多的安全威胁。二进制漏洞是黑客常用的攻击手段之一，对企业的信息安全构成了严重威胁。通过学习这门课程，学员能够**掌握二进制漏洞的挖掘和利用技术，提高企业的安全防护能力。**  
  
  
  
**课程收获**  
  
  
  
- 掌握二进制漏洞挖掘和利用的基本原理和方法，包括模糊测试、AFL原理、ASAN原理等；  
  
  
  
- 熟练使用模糊测试工具进行漏洞挖掘，如AFL等，以及如何改造和定制模糊测试工具；  
  
  
  
- 了解网络协议漏洞挖掘实战，包括Web服务器漏洞挖掘、Nginx Fuzz等；  
  
  
  
  
- 掌握Linux内核漏洞挖掘实战技巧，如syzkaller的使用和内核Fuzz模型设想；  
  
  
  
- 学会使用CodeQL进行代码审计和漏洞挖掘，提高代码安全审查能力；  
  
  
  
- 熟悉AOSP漏洞挖掘实战，包括Android安全基础入门、CVE案例分析等；  
  
  
  
- 学会从零实现模糊测试工具，提升自定义Fuzz框架的能力；  
  
  
  
- 掌握跨平台模糊测试框架LibAFL的使用方法，如安卓Frida_mode Fuzz、跨平台嵌入式设备等。  
  
  
  
  
  
**课程亮点**  
  
  
  
**1、课程内容涵盖了二进制漏洞攻防领域的多个方面，**  
包括模糊测试、AFL原理、ASAN原理、网络协议漏洞挖掘、Linux内核漏洞挖掘、AOSP漏洞挖掘以及CodeQL代码审计等，为学员提供了全面的学习资源。  
  
  
**2、课程结合实际案例进行讲解，**  
如网络协议漏洞挖掘实战、Linux内核漏洞挖掘实战和AOSP漏洞挖掘实战等，帮助学员更好地理解和掌握二进制漏洞攻防的实际应用。  
  
  
**3、课程强调实践操作，**  
教授如何使用模糊测试工具、改造模糊测试工具、从零实现模糊测试工具等，提升学员的动手能力和实际操作经验。  
  
  
**教学安排**  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8GstpDu7iaH5qk61bgepbXF8A6bibvtiaBSibvBq4rCVibiagrX6LPl6zO7dVQk5eXZjXH6DGibQDw6tG1cg/640?wx_fmt=png&from=appmsg "")  
  
### 录播课表第一章 漏洞挖掘技术总览  
- 1.1 准备阶段  
  
- 1.2 课程介绍  
  
### 第二章 模糊测试介绍  
- 模糊测试介绍  
  
### 第三章 AFL原理  
- 3.1 路径信息反馈  
  
- 3.2 forkserver  
  
- 3.3 样本变异  
  
- 3.4 一些思考  
  
### 第四章 模糊测试工具基础使用  
- 4.1 AFL环境搭建  
  
- 4.2 AFL使用  
  
- 4.3 AFL字典  
  
- 4.4 AFL持续化Fuzz  
  
- 4.5 AFL闭源Fuzz  
  
- 4.6 AFL拓展使用  
  
### 第五章 ASAN原理解析  
- 5.1 开源asan  
  
- 5.2 闭源asan  
  
### 第六章 改造模糊测试工具  
- 6.1 模糊测试挖掘命令注入漏洞  
  
- 6.2 模糊测试挖掘命令注入实现  
  
### 第七章 从零实现模糊测试工具  
- 7.1 自定义fuzz框架源码讲解  
  
- 7.2 自定义fuzz框架拓展  
  
### 第八章 网络协议漏洞挖掘实战  
- 8.1 webserver与fuzz  
  
- 8.2 lighttpd-基础fuzz  
  
- 8.3 lighttpd-单一字段fuzz  
  
- 8.4 nginx-fuzz  
  
### 第九章 LibAFL(跨平台，可拓展)  
  
- 9.1 LibAFL使用  
  
  
  
-  9.2 安卓frida_mode-fuzz  
  
  
  
-  9.3 跨平台使用(no_std mode支持嵌入式设备)  
  
  
### 第十章 linux内核漏洞挖掘实战  
- 9.1 syzkaller-模板  
  
- 9.2 syzkaller使用技巧  
  
- 9.3 利用已有知识设想内核fuzz模型  
  
### 第十一章 codeql代码审计  
- 11.1 codeql-环境配置  
  
- 11.2 codeql-基础语法  
  
- 11.3 codeql-func类  
  
- 11.4 codeql-expr类  
  
- 11.5 codeql-stmt类  
  
- 11.6 codeql-conver类型  
  
- 11.7 codeql-class类  
  
- 11.8 codeql局部数据流分析  
  
- 11.9 codegl-全局数据流分析  
  
### 第十二章 课程回顾  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnwoscbYZBVBJOOvnuffgAYOVKr7Njdr965qjx7zwBSibKysHzZBUK5bA/640?wx_fmt=gif&from=appmsg "向上箭头分割线GIF动态")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8GstpDu7iaH5qk61bgepbXF8juao1y12VKMRPwVc4MybKfL5ZX0ccY2RibYKoFJ5nKFwVwOjUqayqWg/640?wx_fmt=png&from=appmsg "")  
  
  
  
**直播课表**  
  
  
-Linux内核漏洞-Linux内核动态调试  
  
-Linux内核漏洞-CVE-2021-3490漏洞利用分析-1_ebfp-hook  
  
-Linux内核漏洞-CVE-2021-3490漏洞利用分析-2  
  
-Linux内核漏洞-DirtyCow漏洞利用分析  
  
-Linux内核漏洞-DirtyCred漏洞技巧讲解  
  
-Linux内核漏洞-DirtyPipe漏洞利用分析  
  
-Linux内核漏洞-CVE-2023-2008漏洞利用分析  
  
-AOSP漏洞-android安全基础入门四大组件  
  
-AOSP漏洞-android安全基础入门进程间交互  
  
-AOSP漏洞-CVE-2023-21266漏洞利用分析(ActivityManagerServer权限校验)  
  
-AOSP漏洞-CVE-2023-21266类权限校验类漏洞挖掘  
  
-AOSP漏洞-从用户态api-service层-hal层-驱动层解析nfc系统流程-1  
  
-AOSP漏洞-从用户态api-service层-hal层-驱动层解析nfc系统流程-2  
  
-AOSP漏洞-CVE-2022-20223-Intent_Redirection漏洞利用分析  
  
-AOSP漏洞-使用codeql挖掘Intent_Redirection类漏洞  
  
-AOSP漏洞-CVE-2018-9581-信息泄露漏洞利用分析  
  
-AOSP漏洞-freetype字体渲染库Fuzz  
  
-AOSP漏洞-CVE-2023-21282(CursorWindow)漏洞分析  
  
-AOSP漏洞-使用codeql挖掘CVE-2023-21282类漏洞模型  
  
-课程回顾  
  
  
**立即学习**  
  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8F6Tpj5aHWSKhjuvAZWgVkwhmsuxbDm5e5uMtV0gnVLG9XVqg1ic1hwicU6TSGrmNJichmJyWDraS9EQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/Uia4617poZXP96fGaMPXib13V1bJ52yHq9ycD9Zv3WhiaRb2rKV6wghrNa4VyFR2wibBVNfZt3M5IuUiauQGHvxhQrA/640?wx_fmt=jpeg "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnjjdS3brxnrdAOhAgPibwaQC4bqbS626nab7fyyreRicDJlArwM9wXVvQ/640?wx_fmt=gif&from=appmsg "")  
  
**球分享**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnjjdS3brxnrdAOhAgPibwaQC4bqbS626nab7fyyreRicDJlArwM9wXVvQ/640?wx_fmt=gif&from=appmsg "")  
  
**球点赞**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnjjdS3brxnrdAOhAgPibwaQC4bqbS626nab7fyyreRicDJlArwM9wXVvQ/640?wx_fmt=gif&from=appmsg "")  
  
**球在看**  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8F9m68uPwKxMInE7peF8jKnhZIHMqb35a4NbNj7NLZSzyyAVmC8JUPDADAWERyTfqAU0Th3kT9Ppw/640?wx_fmt=gif&from=appmsg "")  
  
点击阅读原文查看更多  
  
