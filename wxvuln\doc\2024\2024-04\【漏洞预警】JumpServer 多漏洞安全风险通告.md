#  【漏洞预警】JumpServer 多漏洞安全风险通告   
原创 网星安全  网星安全   2024-04-01 19:32  
  
**01**  
  
**漏洞介绍**  
  
  
  
JumpServer 是广受欢迎的开源堡垒机，是符合 4A 规范的专业运维安全审计系统。其开源、零门槛等特性帮助大量客户实现企业服务器用户授权、运维管理、安全审计等需求。  
  
近日，网星安全团队从JumpServer官方Github监测到，有用户反馈发现JumpServer存在安全漏洞，并向JumpServer开源项目组进行上报。此次共披露4个漏洞，其中涉及2个RCE级别漏洞，漏洞详情如下。  
  
<table><tbody style="box-sizing: border-box;"><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:5.classicTable1:0" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:0.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞编号</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:0.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;color: rgb(62, 62, 62);box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">CVE-2024-29201</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:0.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞名称</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:0.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">Ansible playbook验证绕过</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:5.classicTable1:1" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:1.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞类型</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:1.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><p style="text-align: left;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">命令执行</p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:1.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞级别</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:1.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><p style="text-align: left;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">高</p></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:5.classicTable1:2" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:2.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞利用所需权限</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:2.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">普通用户</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:2.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞级别</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:2.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">低</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:5.classicTable1:3" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:3.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞介绍</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:3.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">攻击者可以绕过 JumpServer 的 Ansible 中的输入验证机制，在 Celery 容器中执行任意代码。由于 Celery 容器以 root 权限运行并具有数据库访问权限，因此攻击者可以从所有主机窃取敏感信息或操纵数据库。</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:5.classicTable1:4" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:4.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞版本</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:5.classicTable1:4.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: center;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">v3.0.0-v3.10.6</p></section></td></tr></tbody></table>  
  
  
<table><tbody style="box-sizing: border-box;"><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:7.classicTable1:0" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:0.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞编号</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:0.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;color: rgb(62, 62, 62);box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">CVE-2024-29202</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:0.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞名称</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:0.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">后台模板注入</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:7.classicTable1:1" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:1.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞类型</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:1.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">模板注入</p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:1.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞级别</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:1.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><p style="text-align: left;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">高</p></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:7.classicTable1:2" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:2.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞利用所需权限</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:2.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">普通用户</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:2.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞级别</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:2.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">低</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:7.classicTable1:3" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:3.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞介绍</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:3.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">攻击者可以利用 JumpServer 的 Ansible 中的 Jinja2 模板注入漏洞在 Celery 容器中执行任意代码。由于 Celery 容器以 root 权限运行并具有数据库访问权限，因此攻击者可以从所有主机窃取敏感信息或操纵数据库。</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:7.classicTable1:4" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:4.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞版本</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:7.classicTable1:4.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: center;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">v3.0.0-v3.10.6</p></section></td></tr></tbody></table>  
  
  
<table><tbody style="box-sizing: border-box;"><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:9.classicTable1:0" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:0.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞编号</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:0.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;color: rgb(62, 62, 62);box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">-</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:0.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞名称</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:0.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">playbook信息泄露</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:9.classicTable1:1" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:1.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞类型</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:1.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">信息泄露</p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:1.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞级别</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:1.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><p style="text-align: left;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">中</p></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:9.classicTable1:2" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:2.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞利用所需权限</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:2.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">普通用户</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:2.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞级别</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:2.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">中</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:9.classicTable1:3" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:3.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞介绍</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:3.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">如果授权攻击者设法了解其他用户的 playbook_id，则他们可以获取 playbook 文件中包含的敏感信息。这种违反保密性的行为可能会导致信息泄露和敏感数据暴露。</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:9.classicTable1:4" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:4.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞版本</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:9.classicTable1:4.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: center;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">v3.0.0-v3.10.5</p></section></td></tr></tbody></table>  
  
  
<table><tbody style="box-sizing: border-box;"><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:11.classicTable1:0" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:0.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞编号</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:0.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;color: rgb(62, 62, 62);box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">-</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:0.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞名称</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:0.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">文件管理器批量传输功能中的不安全直接对象引用 (IDOR) 漏洞</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:11.classicTable1:1" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:1.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞类型</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:1.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">文件上传</p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:1.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><p style="white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us"><strong style="box-sizing: border-box;">漏洞级别</strong></p></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:1.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><p style="text-align: left;white-space: normal;margin: 0px;padding: 0px;box-sizing: border-box;" powered-by="xiumi.us">高</p></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:11.classicTable1:2" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:2.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞利用所需权限</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:2.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">普通用户</p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:2.td@@2" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞级别</strong></p></section></td><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:2.td@@3" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="30.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">中</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:11.classicTable1:3" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:3.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞介绍</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:3.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">经过身份验证的用户可以通过操纵作业 ID 来上传恶意文件，从而利用文件管理器批量传输中的不安全直接对象引用 (IDOR) 漏洞，从而可能损害系统的完整性和安全性。</p></section></td></tr><tr opera-tn-ra-comp="_$.pages:0.layers:0.comps:11.classicTable1:4" style="box-sizing: border-box;" powered-by="xiumi.us"><td colspan="1" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:4.td@@0" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 0px 0px 0px 4px;box-sizing: border-box;" width="20.0000%"><section style="text-align: left;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;"><strong style="box-sizing: border-box;">漏洞版本</strong></p></section></td><td colspan="3" rowspan="1" opera-tn-ra-cell="_$.pages:0.layers:0.comps:11.classicTable1:4.td@@1" style="border-width: 1px;border-color: rgb(108, 47, 242);border-style: solid;padding: 4px;box-sizing: border-box;" width="80.0100%"><section style="text-align: center;box-sizing: border-box;" powered-by="xiumi.us"><p style="margin: 0px;padding: 0px;box-sizing: border-box;">v3.0.0-v3.10.5</p></section></td></tr></tbody></table>  
  
  
  
**02**  
  
**官方修复方案**  
  
“  
  
**永久修复方案**  
  
  
升级JumpServer软件至v3.10.7及以上版本。  
  
“  
  
**临时修复方案**  
  
  
关闭作业中心功能。关闭作业中心功能的具体步骤为：  
  
以管理员身份登录至JumpServer堡垒机。依次选择“系统设置”→“功能设置”→“任务中心”，在打开的页面中关闭作业中心功能。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/XShQMCwLIrBOPFT6MJu0vQeqqjyjaibzxib8nZ5y2qzicBz5IslQPmqyaCicLb9oZmszaPPJNaAibLVuMLm0cv0kicdg/640?wx_fmt=other&from=appmsg "")  
  
  
  
**03**  
  
**参考链接**  
  
https://github.com/jumpserver/jumpserver/security  
  
  
  
**04**  
  
**堡垒机防御**  
  
  
**ITDR平台**  
  
  
  
网星安全作为国内首家ITDR厂商，围绕Identity及Infrastructure为核心进行防护，打造了ITDR（身份威胁检测与响应）平台。平台涵盖主流身份基础设施及集权设施，围绕从攻击的事前加固、事中监测，事后阻断出发，产品的设计思路覆盖攻击者活动的全生命周期。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/XShQMCwLIrBOPFT6MJu0vQeqqjyjaibzxLqEfuhXGgDHBN3TPLeA2t0g69ThiaHq4WvQ5FsudPicq1gshfibYGcrBQ/640?wx_fmt=other&from=appmsg "")  
  
  
  
**ITDR平台能力-针对Jumpserver场景特有的能力**  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/XShQMCwLIrBOPFT6MJu0vQeqqjyjaibzxkLnD2Ia5S2hCMNX0q6gIq5e5oSZMBWDwfE5g9GnhxeJOTDrVSGicCGQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/XShQMCwLIrBOPFT6MJu0vQeqqjyjaibzxHxfrUqHcz86sznge7oy1D7ia4cn4RxOLptTzU8ab5uc9Go1ZyWpmOAg/640?wx_fmt=png&from=appmsg "")  
  
  
