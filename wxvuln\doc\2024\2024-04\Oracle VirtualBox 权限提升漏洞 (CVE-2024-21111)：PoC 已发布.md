#  Oracle VirtualBox 权限提升漏洞 (CVE-2024-21111)：PoC 已发布   
Naor Hodorov  独眼情报   2024-04-22 18:57  
  
安全研究员Naor Hodorov公开了一个针对Oracle VirtualBox中严重漏洞（CVE-2024-21111）的概念验证（PoC）利用。这个漏洞影响了7.0.16之前的VirtualBox版本，允许具有基本访问权限的攻击者在运行VirtualBox的Windows系统上提升他们的权限。  
  
poc  
  
https://github.com/mansk1es/CVE-2024-21111  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/KgxDGkACWnT22vU5AkB05saDbuhaQRTKb5TEk5R00k0ibFMzDZmDdc40qibHIibzOnQ9IX7vZxSsQiczIWG4ubzpGw/640?wx_fmt=other&from=appmsg "")  
  
  
该漏洞如何运行  
  
该漏洞利用了VirtualBox管理日志文件的缺陷。攻击者可以诱使VirtualBox滥用其高级系统权限来删除或移动文件。这使得攻击者能够操纵关键文件，并可能完全控制受影响的系统。  
  
CVE-2024-21111 允许具有对宿主机低级别访问权限的攻击者将其权限提升至 NT AUTHORITY\SYSTEM，这是 Windows 系统上最高级别的权限。该漏洞利用了 VirtualBox 对日志文件的处理方式，其中软件尝试通过添加序数号将 C:\ProgramData\VirtualBox 中的日志移动到备份位置。然而，由于在管理超过十个日志时存在缺陷，VirtualBox 不小心使自己暴露于符号链接攻击之下，从而导致任意文件的删除或移动。  
  
  
   
风险因素  
- 易于利用：该漏洞被认为很容易被利用，从而增加了广泛攻击的风险。  
  
- 针  
对 Windows：  
此特定漏洞仅影响运行 VirtualBox 的基于 Windows 的系统。  
  
- 整个系统被入侵：成功利用该漏洞可以使攻击者完全控制受感染的系统。  
  
修复：Oracle 的补丁  
  
https://www.oracle.com/security-alerts/cpuapr2024.html  
  
值得庆幸的是，Oracle 在其最近的重要补丁更新（2024 年 4 月）中解决了该漏洞。该补丁更新特别值得注意，因为它解决了各种 Oracle 产品（而不仅仅是 VirtualBox）的总共 441 个安全漏洞。  
  
在最新的补丁周期中，Oracle Communications 受到了最大的关注，共有 93 个补丁，约占已发布补丁总数的 21%。紧随其后的是 Oracle 融合中间件和 Oracle 金融服务应用程序，分别有 51 个和 49 个补丁。  
  
如果您在 Windows 计算机上使用 Oracle VirtualBox，则必须立即更新到版本 7.0.16 或更高版本。  
  
  
