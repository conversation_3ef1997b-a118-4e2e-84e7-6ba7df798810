#  web测试用例逻辑漏洞瞎扯系列   
原创 lidasimida  安全学习之路   2024-01-03 16:34  
  
**前提**  
  
最近公司要求写个详细的测试用例，其实网上一大堆了，我打算自己手写算了，作为笔记用了，回顾自身，写的比较简陋，笔力有限，脑容不大，写的有些简单，见谅。****  
  
**授权认证**  
  
由于自己在表格汇总的，所以直接复制表格过来了，懒得重新编辑了，毕竟写的太普遍了，大家随便看看就行了。  
  
<table><colgroup><col width="155" style="mso-width-source:userset;mso-width-alt:5273;width:116pt;"/><col width="122" style="mso-width-source:userset;mso-width-alt:4164;width:92pt;"/><col width="535" style="mso-width-source:userset;mso-width-alt:18261;width:401pt;"/></colgroup><tbody><tr height="42" style="mso-height-source:userset;height:31.25pt;"><td height="31" width="116" style="">业务场景</td><td width="92" style="">漏洞类型</td><td width="420" style="">执行步骤</td></tr><tr height="107" style="mso-height-source:userset;height:80.0pt;"><td height="80" width="116" style="">存在可枚举参数接口，例如账户和密码的登录，批量注册，短信验证码等地方</td><td width="92" style="">暴力破解</td><td width="364" style="">1、找到存在枚举参数后对功能出现影响的接口，如登录、注册、找回密码等地方。<br/>
   
  2、查看是否存在验证码，如若存在验证码则需要校验验证码是否有效，通过删除验证码参数或设置为空或null，若无法绕过验证码可对验证码进行自动识别爆破尝试，同时需要查看接口有无其他限制，如短时间内请求过多会封禁账号或者IP等限制，亦可作为防御参考。</td></tr><tr height="258" style="mso-height-source:userset;height:193.15pt;"><td height="193" width="116" style="">主要是针对含有敏感数据或敏感功能的后台接口访问进行判断是否添加授权机制认证</td><td width="92" style="">未授权访问</td><td width="420" style="word-break: break-all;">1、灰盒测试：可以直接使用提供的测试账密登录后台接口，将后台的功能进行抓包，然后使用burpsuite插件xia_yue、Autorize等方式进行删除cookie、authorization等校验授权机制的字段进行自动化重放，然后进行判断该接口是否需要鉴权。需要注意的是鉴权机制是哪个字段影响的，如存在cookie、jwt、authorization等多个校验机制情况，需使用repeater进行删除测试哪个字段才是校验授权机制。如客户能提供接口文档，还需要对接口文档加入字典进行intruder模块，删除校验机制字段来进行授权访问遍历测试，有些接口通过后台功能点击并不能完全获得。<br/>
   
  2、黑盒测试：与灰盒测试有所区别的是如何找到接口，可通过js代码审计、框架的默认目录、后缀目录字典、备份文件字典等方式去遍历可未授权访问的接口，需要注意该开发的命名规则，有些开发会加自己的命名规则为二级目录，可能后面的目录还是常见目录命名，所以爆破接口字典的时候要多爆破二级目录甚至三级目录，也可以根据开发命名规则来模糊猜解目录</td></tr><tr height="282" style="mso-height-source:userset;height:211.5pt;"><td height="211" width="116" style="">针对有授权机制才能访问的后台接口，通过绕过手段无需授权登录亦可访问，如登录、注册、忘记密码、各类交易等场景，主要是绕过授权机制。</td><td width="92" style="">认证绕过</td><td width="420" style="">1、灰盒测试：使用提供的授权认证登录后台，抓包查看网站整个授权认证交互过程进行分析。先查看请求包有无关键字，主要是除了用户和密码字段其余多出来的参数，有可能该参数的值为空或者null之类的即可绕过登录认证，具体可以看前端代码如何校验。然后查看成功登录之后返回怎么一个授权校验机制，该授权机制是否固定值，能否绕过，比如authorization:
  basic
  test绕过，或者设置为空或null，有些是referer头、ua头、sign等等header，既然是灰盒测试，那就登录之后拿到登录后的数据包然后对每个关键请求头进行删除或为空或为null等尝试，找到绕过认证的方式。若为nacos、f5、shiro、jwt等常见框架，请从网上找漏洞尝试。还有就是用户可以通过未授权访问获取授权接口，在该用户授权未过期前发送类似username=admin这种字段即可获取该用户的授权。还有金融的支付交易场景，需要重新认证，也可以尝试修改对应关键字，具体还得根据整个授权认证交互过程来分析。最后就是sso统一登录等场景，修改返回包跳转，其他情况也可以尝试修改返回包为200的进行绕过认证。<br/>
    2、黑盒测试：相对于灰盒测试来说，完全靠猜他会用什么头来校验了，得多模糊测试，可以看js代码追踪响应，也可以暴力枚举后台，只能说更费劲，通常是根据框架的历史漏洞来搞。</td></tr><tr height="216" style="height:162.0pt;"><td height="162" width="116" style="">越权有水平越权和垂直越权，主要是适用于能获得敏感数据或者使用敏感功能的接口。</td><td width="92" style="">越权</td><td width="420" style="">1、灰盒测试：使用提供的授权认证登录后台，抓包获取应用的授权认证机制，然后使用burpsuite插件xia_yue、Autorize等方式替换授权机制认证发包，这个需要理清授权机制是哪个才方便加入自动化插件，然后就可以把后台所有功能全部手工点击抓包了，这个主要是测试接口的鉴权不合理导致接口可以越权访问，还要对一些接口的参数做越权手工测试，比如个人信息页面接口仅采用username=admin来返回接口数据的，也是可以直接替换授权机制，但是这个参数得添加使用repeater重放看看是否有影响到接口的逻辑判断，若有则可以尝试越权，参数的越权前提也是该接口可让访问，所以要先把能越权访问的接口跑一遍。<br/>
   
  2、黑盒测试：个人认为黑盒与灰盒的区别还是很明显的，信息不全，可能自己只有一个低权限账号，想要越权不管是水平还是垂直都得找到接口，可审计js前端，也可以模糊猜测接口（根据开发命名规则或直接上字典），也可以在接口添加参数进行校验，主要是在接口探测发现的困难，毕竟低权限用户给予的信息不多。</td></tr><tr height="185" style="mso-height-source:userset;height:138.4pt;"><td height="138" width="116" style="">需要用到短信验证接口</td><td width="92" style="">短信轰炸</td><td width="420" style="">1、灰盒测试：使用提供的授权手机号去发送验证码，通过参数污染、变量污染、数据长度绕过、修改返回包、+、空格等方式爆破绕过，尝试多次发送验证码成功，最后还得通过接码平台或手机处验证短信能否发送成功，最好实验15-30次，有些开发是写明一天内限制5-15条短信。<br/>
   
  2、黑盒测试：主要是在于能否使用自己的手机号注册，不能注册的情况下如何使用别人的手机号尝试，一般都是先爆破存在的手机号，通常都是测试手机号，此类手机号仅作测试和登录用，不会发送短信，所以都是根据返回包返回的数据判断发送是否成功，这不一定准确，返回包是true字段，但是服务端可能还会有另外的校验并不会实际发送短信形成轰炸，只能让开发判断。</td></tr><tr height="107" style="mso-height-source:userset;height:80.0pt;"><td height="80" width="116" style="">需要用到邮箱验证接口</td><td width="92" style="">邮箱轰炸</td><td width="420" style="">1、灰盒测试：同短信轰炸差不多，绕过手段多了大小写、编码等方式，多尝试几种。<br/>
    2、黑盒测试：同短信轰炸类似。</td></tr><tr height="107" style="mso-height-source:userset;height:80.0pt;"><td height="80" width="116" style="">注册、登录等对于用户名有判断是否存在的接口，具体看网站功能设计</td><td width="92" style="">账号枚举</td><td width="420" style="">1、找到对应校验用户名的接口，然后通过抓包对比用户存在和不存在用户的返回正文区别，如出现“用户或密码错误”之类的统一认证返回，可视为通过测试。<br/>
   
  2、如出现用户名不存在、false、密码错误、true等返回，可通过枚举账号字典进一步验证，同时能获得对应存在的账户，若是灰盒测试，仅需使用客户提供的测试账户验证。</td></tr><tr height="130" style="mso-height-source:userset;height:97.15pt;"><td height="97" width="116" style="">登录点</td><td width="92" style=""><span lang="EN-US">任意用户登录</span></td><td width="420" style="">1、使用任意用户相同密码即可未授权登录后台，密码通常为默认密码，或者为null或空的情况，可使用burpsuite抓包发包测试，网站有可能仅做前端校验。</td></tr><tr height="107" style="mso-height-source:userset;height:80.0pt;"><td height="80" width="116" style="">登录点、重置密码等接口</td><td width="92" style="">任意密码重置</td><td width="420" style="">1、首次登录时存在更改默认密码，该接口未验证用户是否授权，攻击者可以利用该接口配合csrf漏洞达到任意密码重置。<br/>
    2、重置密码接口存在二重验证，即先验证登录凭证是否正确，然后会跳转到设置新密码接口，该接口可以结合csrf漏洞达到任意密码重置利用。<br/>
    3、重置密码接口经测试旧密码参数值可设置为空或null情况，或者参数可删除情况，该接口可直接重置任意用户的密码。</td></tr><tr height="107" style="mso-height-source:userset;height:80.0pt;"><td height="80" width="116" style="">个人信息处、注册等</td><td width="92" style="">任意手机号绑定</td><td width="420" style="">1、绑定手机号地方抓包，如有验证码可进行爆破测试，亦可删除验证码或设置为空或null值进行绕过，亦可尝试绑定测试手机号加默认短信验证码。</td></tr></tbody></table>  
  
