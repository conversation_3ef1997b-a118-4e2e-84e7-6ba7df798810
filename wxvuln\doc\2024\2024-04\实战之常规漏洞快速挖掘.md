#  实战之常规漏洞快速挖掘   
 迪哥讲事   2024-04-04 22:47  
  
## 前言：  
  
闲来有空，落个笔，本来是想每个漏洞都分开写专题的，凑文章数量。  
  
但是想来想去，对于如果是常规的漏洞的话，其实挖来挖去核心逻辑其实都是那些东西，所以对相关的文章又进行了一个删除。  
  
这里写一篇文章，当然也可以说是一篇关于笔者使用相关工具介绍的文章，捡过很多漏洞。  
## 正文：  
  
在笔者看来，其实针对一个网站做渗透测试也好，针对一套代码做代码审计也好。  
  
又或者说是现实中分析事情，处理问题等等也好。  
  
核心其实都是对相关的事务足够了解，然后结合自己的知识储备亦或是资源储备，时间，进行思考采取对应的模式去做一个解决（这里模式这块如果是常见的问题，可以采用一些定论，也就是老人长谈的经验去进行解决）。  
  
因此如果是相同知识储备，相同时间的情况下，难点亦或是拉开差距的面，就是在于如何去对相关的事务做了解。  
  
因此在渗透中，核心问题还是立足于如何对相关的信息做一个收集以及如何使用一些定论去批量测试。  
  
对于针对一个网站而言，在笔者看来做一个信息收集的话，其实就是了解系统架构，核心是六个点开发语言，功能，接口，参数，鉴权逻辑，敏感信息其余的东西非通解的东西亦或是没那么重要的点这里不加讨论。  
  
因此对于笔者而言，采用的模式就会去加强自己的这6块的信息获取能力以及通解常规漏洞的能力。  
### 指纹识别以及敏感信息汇总（被动）：  
  
对于指纹以及敏感信息的获取而言，笔者采用的是Sweet Potato，里面的指纹自己去整理更改完善。  
  
可自动化识别出系统的指纹以及整理可能存在的osskey泄露等等信息。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFrscLGWbReiasskxmElPQVIup4NW7QYp6bibEUqWjnL2D5zicdaIRbmO9Q/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFdjEnEmUT39YhckWPZKFFzcgURZrIZV0mpj9iaRyAX6j07rRfgLBDovA/640?wx_fmt=png&from=appmsg "")  
burp内置的指纹识别的意义就是在于可以处理好一些跳转以及多组件的问题。如有些网站会如下，对于这种网站而言常见的指纹识别工具是无法处理的，因此很多人会漏掉很多网站，导致可能存在高危漏洞而未挖掘到。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFHjNm5v5391EEP7UgU0g1XhMSyj5x7TnPD8rIM2BCJYn8v8draXJh2w/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF05pDD9lo0rIFbbOwZibuYcQtw1ibEEqNKTnGb0beia7U1MQHsLficWvJ8w/640?wx_fmt=png&from=appmsg "")  
  
### 敏感信息检测HAE（被动）：  
  
识别常见的敏感信息
老生常谈的东西HAE  
  
不在阐述![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFLyjElXWMbP1Kv7sFia162kvC5woefkO2sh1FXlOciaKsxia5XgEVPM6Cw/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFwMyyIVw9q1zCNia5pG7FjN1gHL8MBiaFa7bUZo5OdQnZoov8Rc6rOP2w/640?wx_fmt=png&from=appmsg "")  
  
###   
### SQL注入（被动）：  
  
xia_sql  
  
不在额外阐述![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF4DUvfEUCBhticRKtKtNchJpkOKR78oyDjIOxbHLIlhOjIibPjusyyy5A/640?wx_fmt=png&from=appmsg "")  
  
### 接口获取（被动）：  
  
如果是有自己开发能力的老哥，自己捏一个基于正则匹配规则来写接口获取即可。没有的话那就使用findsomething  
  
不在额外阐述。  
  
https://github.com/momosecurity/FindSomething![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFrcqJNibIDpib3htHRs1xlVRBCia0DL6ylt38HFicnQMHurZdxcml2iaeOUQ/640?wx_fmt=png&from=appmsg "")  
  
### 未授权越权挖掘（被动）：  
  
Autorize，不在阐述，网上很多了。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFDLmEPjO7BgwdiakcaX6DibzUzjB2CBZCq1l1VcXH9SDLuTY94dZssYicw/640?wx_fmt=png&from=appmsg "")  
  
### 参数获取（被动）：  
  
个人使用god_param，怎么使用看每个人理解了。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFgdk1XM4z40pQCoVp7FUBzEgxxxGrwyuB2xERic2JTC9kXnLDfCVD6Jg/640?wx_fmt=png&from=appmsg "")  
  
### 权限绕过,SSRF（被动）：  
  
autoRepeater![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF9u68YiaZUuvIXlL4clWz9uiatQYaFbyLlQibsRCcmZzt9AIjKxshCsVibg/640?wx_fmt=png&from=appmsg "")  
  
### 大杂烩（主动）：  
  
主动获取的笔者个人使用，burpbountyplus
如何自动化发觉，自己定义规则即可。  
  
可主动自动化挖掘RCE，XSS，SQL，任意文件读取，重定向，XXE等等漏洞，自己根据自己规则来即可，注意写的规则需要无害化。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFaPz0NibKcpZpSvQzC6C1ibxEWKrE6VVbianhAFGib0h8ZEzLFW2QULfQeQ/640?wx_fmt=png&from=appmsg "")  
实战的没截图，这里贴个靶场为例的，如无害规则自动化挖掘RCE,SQL。![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF3E1A53My2zibVC0ia6MGn7Kdn3sQINuAUOGE93SNrOTbO17KfmIrtr6w/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXFG5OQx6hqcEED1w9OwayHc0HemVFlDZdvibGW4Bjy4I1XXKcqnKE8nJw/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF8FE0nhicHHiaAk4eQqYLwFpcebWe0NslKcIbQgocp91fIe1d4bkh9xlQ/640?wx_fmt=png&from=appmsg "")  
![](https://mmbiz.qpic.cn/mmbiz_png/BYtyQicN4iaC7Gh7ZGOmvdIicjK80M3dfXF8uz5uOSyicPkrBrXYVicuVV0E5v6OjibC4jnnQNpvdLGjCrMKLZpEEE0Q/640?wx_fmt=png&from=appmsg "")  
具体不在阐述。  
  
点到为止，还有一些其他的通解挖掘思路，这里不在阐述写出，每个人按照自己的思路去挖即可。  
## 后文：  
  
很多东西或者问题，学习思维等等都可以找到一种通解，重要的是 你是否get到自己的方式模式，仅此而已。  
  
如果你是一个长期主义者，欢迎加入我的知识星球，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款前面有同学问我有没优惠券，这里发放100张100元的优惠券,用完今年不再发放  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj7N5nMaJbtnMPVw96ZcVbWfp6SGDicUaGZyrWOM67xP8Ot3ftyqOybMqbj1005WvMNbDJO0hOWkCaQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
  
  
