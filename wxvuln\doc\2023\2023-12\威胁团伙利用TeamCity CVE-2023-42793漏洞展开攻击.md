#  威胁团伙利用TeamCity CVE-2023-42793漏洞展开攻击   
 网络安全应急技术国家工程中心   2023-12-11 15:23  
  
自2023年10月初以来，微软已经观察到两个由朝鲜政府撑腰的威胁组织：Diamond Sleet和Onyx Sleet，他们利用CVE-2023-42793漏洞影响JetBrains TeamCity服务器多个版本。TeamCity作为一款持续集成/持续部署（CI/CD）应用软件，被众多组织用于DevOps及其他软件开发活动。  
  
在过去的行动中，Diamond Sleet及朝鲜的其他威胁组织通过渗入到软件构建环境中，成功地实施了软件供应链攻击。有鉴于此，微软认为该活动对已受影响的组织构成了极大风险。目前，JetBrains已发布了更新版来解决这个漏洞，并为无法更新到最新软件版本的用户提供了缓解措施。  
  
虽然这两个威胁组织都利用同一个漏洞，但微软观察到Diamond Sleet和Onyx Sleet在成功渗入后都各自利用了一系列独特的工具和技术。基于受这些入侵影响的受害者组织情况，微软认为威胁组织可能在伺机闯入易受攻击的服务器。  
  
这两个组织都部署了恶意软件和工具，并利用了可以持久访问受害者环境的技术。与以往观察到的政府撑腰的攻击活动一样，微软直接通知了已成为攻击目标或受到威胁的客户，并向他们提供保护其环境所需的信息。  
  
Diamond Sleet和Onyx Sleet是何方神圣？  
  
Diamond Sleet（ZINC）是朝鲜政府撑腰的威胁组织，主要从事间谍活动、窃取数据、牟取经济利益和破坏网络。这个组织通常以全球各地的媒体、IT服务和国防相关实体为目标。微软在2021年1月报告了Diamond Sleet针对安全研究人员的攻击，该组织在2022年9月将开源软件沦为武器化。2023年8月，Diamond Sleet侵入了一家德国软件供应商的软件供应链。  
  
Onyx Sleet是同样由朝鲜政府撑腰的威胁组织，主要针对韩国、美国和印度的国防和IT服务。Onyx Sleet使用了自行开发的一套强大的工具对受害者环境实现持久访问，并且不被发现，该组织经常利用N日漏洞（N-day）获得对威胁目标的初始访问权限。  
  
Diamond Sleet攻击路径1：部署ForestTiger后门  
  
在成功入侵TeamCity服务器之后，Diamond Sleet利用PowerShell从之前被该威胁组织入侵的合法基础设施下载了两个攻击载荷。这两个攻击载荷Forest64.exe和4800-84DC-063A6A41C5C存储在C:\ProgramData目录中。  
  
启动后，Forest64.exe检查是否存在一个名为4800-84DC-063A6A41C5C的文件，然后使用“uTYNkfKxHiZrx3KJ”嵌入的静态分配密钥来读取和解密该文件的内容：  
  
c:\ProgramData\Forest64.exeuTYNkfKxHiZrx3K  
  
值得关注的是，当恶意软件被调用时，这个同样的值被指定为一个参数，但是在分析过程中没有看到它被利用。同样的值和配置名称还出现在卡巴斯基Securelist报告的这个名为ForestTiger的恶意软件的历史活动中。  
  
4800-84DC-063A6A41C5C的解密内容是恶意软件的配置文件，其中含有其他参数，比如后门用于指挥和控制（C2）的基础设施。微软观察到Diamond Sleet使用了之前被该组织入侵的基础设施充当C2。  
  
微软观察到Forest64.exe随后创建了一个名为Windows TeamCity设置用户界面的计划任务，因此每次系统启动时它都会运行，使用上述引用的命令参数“uTYNkfKxHiZrx3KJ”。微软还观察到Diamond Sleet利用ForestTiger后门通过LSASS内存转储凭据。Microsoft Defender Antivirus将该恶意软件检测为ForestTiger。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXyicm5LMnibdicsrW0UAzRRw9EjdDAYmlMFcuy904XiaeMPMHTOmRmJpZfibQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图1. Diamond Sleet攻击链1使用ForestTiger后门  
  
Diamond Sleet攻击路径2：部署用于DLL搜索顺序劫持攻击的攻击载荷  
  
Diamond Sleet利用受感染服务器上的PowerShell从攻击者的基础设施下载一个恶意DLL。然后，这个恶意DLL与合法的.exe文件一起暂存在C:\ProgramData\中，以执行DLL搜索顺序劫持。微软观察到该组织结合使用了这些恶意DLL和合法的EXE文件：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXyahMHWwJmDSmURSwwzo8la8ZOUsSIzu9TBfzAgBQ2zSKnNc0MSicE31w/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
DSROLE.dll攻击链  
  
当DSROLE.dll由wsmprovhost.exe加载时，DLL启动一个线程，该线程枚举并尝试处理与该DLL在同一个执行目录中的文件。读取候选文件的前四个字节，并表示要读取的剩余缓冲区的大小。一旦读回剩余的数据，字节将被反转，以显示暂存在内存中的可执行载荷。预期的PE文件应该是特定导出名为“StartAction”的DLL。该导出的地址已被解析，然后在内存中启动。  
  
虽然DSROLE.dll的功能最终取决于它解混淆和启动的任何攻击载荷，但微软观察到该DLL被用于启动wksprt.exe，该文件与C2域进行联系。Microsoft Defender Antivirus使用家族名称RollSling检测dslole .dll。  
  
Version.dll攻击链  
  
由clip.exe加载时，Version.dll加载并解密readme.md的内容，这是与Version.dll一起从攻击者攻陷的基础设施下载的文件。文件readme.md含有用作多字节XOR密钥的数据，用于解密嵌入在Version.dll中的位置无关代码（PIC）。这个PIC加载并启动最终阶段的远程访问木马（RAT）。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXyCc5L6PO8KLgib4JPYBwmQTvKmTOG9FwqNiaicWkuNRRPFkPml2pJUXP3Q/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图2. Readme.md的组成，被Version.dll用作多字节XOR密钥  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXynd3POvibPGvgnpPUNIoLicm8ps1MLJ6YOZhtm9Xab57tabKNsaic8wBKQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图3. 运用XOR密钥暴露下一阶段的代码块  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXysVib43AxHRTQrgdXiblwr0NDO3rwpFBpab5TQgWx2icDGZcibej0gHdOvg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图4. 从代码块中提炼出嵌入式PE  
  
一旦加载到内存中，第二阶段的可执行文件将解密一个嵌入的配置文件，该配置文件含有恶意软件用于指挥和控制的几个URL。在恶意软件向回调URL传达信号后不久，微软观察到一个单独的进程iexpress.exe已创建，并与其他C2域进行联系。Microsoft Defender Antivirus使用家族名称FeedLoad检测Version.dll。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXyfrmXXR2gWickQHYPiaUPGbFLSs9Jz4MacyqaHE3fEHvpA76FVOa8KItg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图5. Diamond Sleet攻击链2使用DLL搜索顺序劫持  
  
在成功入侵后，微软观察到Diamond Sleet通过LSASS内存转储凭据。  
  
在一些情况下，微软观察到Diamond Sleet入侵同时利用了攻击路径1和路径2的工具和技术。  
  
Onyx Sleet攻击路径：用户帐户创建、系统发现和载荷部署  
  
在使用TeamCity漏洞成功侵入后，Onyx Sleet在受攻击系统上创建了一个新的用户帐户。这个名为krtbgt的帐户很可能是为了冒充合法的Windows帐户名称KRBTGT，即Kerberos票据授予票据。创建帐户后，该威胁组织通过net use将其添加到了本地管理员组：  
  
net  localgroup administrators krtbgt /add  
  
威胁组织还在受感染系统上运行几个系统发现命令，包括如下：  
  
net localgroup 'Remote Desktop Users’  
  
net localgroup Administrators  
  
cmd.exe "/c tasklist | findstr Sec"  
  
cmd.exe "/c whoami"  
  
cmd.exe "/c netstat -nabp tcp"  
  
cmd.exe "/c ipconfig /all"  
  
cmd.exe "/c systeminfo"  
  
接下来，威胁组织通过PowerShell从攻击者控制的基础设施下载一个独特的攻击载荷，从而将其部署到受感染的系统。微软观察到有独特攻击载荷的这些文件路径：  
  
•C:\Windows\Temp\temp.exe  
  
•C:\Windows\ADFS\bg\ inetmgr.exe  
  
该攻击载荷在启动后加载并解密嵌入式PE资源。然后将这个解密的攻击载荷被加载到内存中并直接启动。内部攻击载荷是一个代理工具，可帮助在受攻击的主机和攻击者控制的基础设施之间建立持久连接。Microsoft Defender Antivirus将该代理工具检测为HazyLoad。  
  
微软还观察到在这条攻击路径中利用了攻击后工具和技术：  
  
•使用攻击者控制的krtbgt帐户通过远程桌面协议（RDP）登录到受感染的设备。  
  
•停止TeamCity服务，可能是为了防止其他威胁团伙访问。  
  
•通过LSASS内存转储凭据。  
  
•部署工具来检索凭据及浏览器存储的其他数据。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o28Kr3ibcVhHeC0kSDowobfXy8oxaicDFnzH48YWiaUDsyZTfjXpjMyP5IZ6eibTPTsBRqc9be40iaGVTCA/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
图6. Onyx Sleet攻击链，用户帐户已创建  
  
建议采取的缓解措施  
  
微软建议采取以下缓解措施，以减小该威胁的影响。  
  
•采用JetBrains发布的更新版或缓解措施以解决CVE-2023-42793。  
  
•使用文末所附的攻陷指标来调查它们是否存在于自己的环境中，并评估潜在的入侵。  
  
•阻止来自IOC表中指定的IP 地址的入站流量。  
  
•使用Microsoft Defender Antivirus来防范该威胁。开启云交付的保护和自动样本提交。这些功能使用人工智能和机器学习来快速识别和阻止新的威胁和未知的威胁。  
  
•立即采取行动，解决受影响设备上的恶意活动。如果恶意代码已经启动，攻击者很可能已经完全控制了设备。立即隔离系统，并执行凭据和令牌的重置。  
  
•调查设备时间线，寻找使用其中一个受攻击帐户进行横向移动活动的迹象。检查攻击者可能投放实现凭据访问、横向移动及其他攻击活动的其他工具。  
  
•确保“安全DLL搜索模式”已设定。  
  
•打开减小攻击面的以下规则：  
  
•阻止可执行文件运行，除非它们符合流行程度、年限或受信任列表标准。  
  
攻陷指标（IOC）  
  
下表给出了我们在调查过程中观察到的IOC。我们鼓励客户调查其环境中的这些指标，并实施检测和保护机制，以识别过去的相关活动，并防止系统遭到未来攻击。  
  
相关表详见以下链接的末尾处：  
  
https://www.microsoft.com/en-us/security/blog/2023/10/18/multiple-north-korean-threat-actors-exploiting-the-teamcity-cve-2023-42793-vulnerability/  
  
**参考及来源：**  
  
https://www.microsoft.com/en-us/security/blog/2023/10/18/multiple-north-korean-threat-actors-exploiting-the-teamcity-cve-2023-42793-vulnerability/  
  
  
  
原文来源：嘶吼专业版  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
