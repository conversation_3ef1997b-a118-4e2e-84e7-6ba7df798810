#  CVE-2024-0738漏洞挖掘过程学习   
原创 fgz  AI与网安   2024-01-22 07:03  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
****  
**应粉丝要求小编将往期poc和我在网上收集的一些poc打包分享到网盘了。同时分享了大量电子书和护网常用工具，在文末免费获取。**  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
mldong DecisionModel.java ExpressionEngine 代码注入  
漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
mldo  
ng 后台管理  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNKqGkoPRQicxsyqFfpg9mLq3ocD6bt2W4VKUN2fomZG2ks3rgNUNia25Q/640?wx_fmt=png&from=appmsg "")  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
mldong是一个开源项目，是基于  
SpringBoot+Vue3快速开发平台、自研的工作流引擎。  
在com/mldong/modules/wf/engine/model/DecisionModel.java文件的ExpressionEngine函数中发现了一个代码注入漏洞  
。攻击者通过该漏洞进行的操纵可能导致代码注入，攻击可远程发起。  
  
  
开源项目地址  
```
https://gitee.com/mldong/mldong
```  
  
  
  
  
04  
  
—  
  
漏洞挖掘过程  
  
  
在项目源码中寻找危险函数eval(通过代码搜索，这通常会触发表达式注入)  
  
这段代码的主要功能是为决策节点执行自定义执行逻辑。  
com/mldong/modules/wf/engine/model/DecisionModel.java  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNAXozgoczDIeZGHJNAOmXUj3UYqaGo7G4HkCgGlCWsr0keuTNk7zxPQ/640?wx_fmt=png&from=appmsg "")  
  
通过代码跟踪，发现eval方法是基于ExpressionEngine接口实现的。/cn/hutool/extra/expression/ExpressionUtil.class  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNnGFuDbSricTNUUUFMfBbwolBcLRLRFkz8y4NX3GhicVbc3fNhL0DTR7w/640?wx_fmt=png&from=appmsg "")  
  
getEngine方法是一个静态方法，在实例化ExpresionUtil时将自动调用该方法。最后，找到配置文件，根据文件内容，可以推断eval()方法识别SPEL表达式。  
```
mldong-master\mldong-framework\mldong-base\src\main\resources\META-INF\services\cn.hutool.extra.expression.ExpressionEngine
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNs7snPuwaF8Tfbrics3niauWXOnXIsbLoW2YMbibHjdSCxJMLr5eBOo3xw/640?wx_fmt=png&from=appmsg "")  
  
**漏洞验证:**  
  
通过调用链，发现eval()方法从实例启动接口开始触发。值得注意的是，它只在执行决策模型时触发，这意味着处理过程需要有判断条件。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNIt60SM8xkxOWaLiagPg5VxvqTUjB2AicibI8TUzasF2k2ae98Iiaw90kbg/640?wx_fmt=png&from=appmsg "")  
  
  
进入前端页面，点击添加菜单。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVN5NrAicaKT72z6UlmWKKyWAM8stYrSEmpYsKxkMpWqGS36vkn6auhbpg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNZPFTTrIfLQVjg3KamSkC28m4sYrfG9WunJQR7dmsUibiau6CerIf6miag/640?wx_fmt=png&from=appmsg "")  
  
添加后，单击设计菜单，右键单击，选择离开表单。依次选择开始节点、条件判断节点和结束节点。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNeFCyDhZpDA9EG5ibLkh4nwzSTs0tgBhnWqAFqpHIMoErUibj1bzRmTjQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNW3NFRkh8KTwoXoOpkKGEZo8Kk3iamskpNn0Ra7QOXFF8ribZjpMLUUdA/640?wx_fmt=png&from=appmsg "")  
  
点击条件判断进行编辑。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNb7DGppicuUC3YEdEaKdBib6PIFQg70tPuH7DW5UPxEht23a5lXmNnaMQ/640?wx_fmt=png&from=appmsg "")  
  
Payload：  
```
T(java.lang.Runtime).getRuntime().exec('calc')
```  
  
  
单击保存  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNLWztcibGZPaLvmBSrribr1Dgp9OEtVxoPZJKNEJ31Z86mNoicQLLaaljg/640?wx_fmt=png&from=appmsg "")  
  
部署后，在流程定义菜单中启动流程。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNvL1DeCT4DDwFnFY859MSUxjVcibDibDvJcAibb4tsuGe16N0aXnibyN0CA/640?wx_fmt=png&from=appmsg "")  
  
成功触发的漏洞。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BO8P2jlYQFVsHxEcSFvLmVNRYCCSrFzibHQNZ9qfia8w9HVicibe8lbfHhYG5OdT33VG7e2lpg3rDFUZA/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
  
05  
  
—  
  
福利领取  
  
  
关注公众号，在公众号主页点发消息发送下面  
**红色字体关键字**免费领取。  
  
  
  
  
  
  
**后台发送【****POC**  
**】关键字获取POC网盘地址**  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOUpfXGibl0WekdS4ZSLBKxKicfA18V1n8QenodzeRfcic4wFEACwGg9WoA5E9S0EqLUzGhfcMDmjLBQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
**后台发送【****电子书**  
**】关键字获取学习资料网盘地址**  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOUpfXGibl0WekdS4ZSLBKxKGhLyoPnHdx2qKD03Mpg2J6y2fNDuPcBO7IUsIqeXCibBibXqBRJESkXg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
后台发送【  
**工具**  
】获取渗透工具包  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BOUpfXGibl0WekdS4ZSLBKxKwSSQPN16gQN1s18VfzLUkwBib5J1wdmd7LibSSibgMoqNCsd5TdbUuIwQ/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
  
