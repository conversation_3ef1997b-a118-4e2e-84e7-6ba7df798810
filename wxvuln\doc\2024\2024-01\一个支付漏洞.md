#  一个支付漏洞   
 迪哥讲事   2024-01-02 23:45  
  
## 正文  
  
目标为target.com  
  
在购物车中添加一些商品并尝试下订单，同时观察到一个post api调用/cart/session这个接口，其中只有一个参数,即cartID，它投6位数值，例如:cartID=123456，这里检查了请求的响应，并观察它包含所有用户详细信息，包括电话号码和电子邮件地址,这里决定暴力破解此参数，尝试暴力破解最后4位数字，发现这个参数容易收到IDOR的攻击。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj4to1Kvvb1A22bZuUB51ZXzVDDaG2g9pnWoE8FIurkLqzcudxJGAZ4zfricBAIKzokI0zmlw1eWBxA/640?wx_fmt=png&from=appmsg "")  
  
对于一些请求，观察到“此购物车ID已过期”的错误消息。根据这里的理解，服务器为用户分配了一个唯一的购物车ID，而客户试图发起支付，这个购物车ID仅在一小时内有效。这是一个高严重性问题，但我的目标是找到一个业务逻辑问题，所以这里继续测试。  
  
这里有100个积分，可以抵用100元，当使用这些积分的时候，应用程序执行以下的http请求:  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj4to1Kvvb1A22bZuUB51ZXz9xrdEfE5BhcybkF7Aiafd1nWe0Il31ia8VBH3gXQxl04lENGjtnGtGKQ/640?wx_fmt=png&from=appmsg "")  
  
为了检查响应，这里进行拦截，在响应中观察到折扣价格，将其改为零，并forward这里的响应，发现在app中修改后的订单价格为0。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj4to1Kvvb1A22bZuUB51ZXzHibElpRUvWnUBCdqBeqQBrKYyqn2t4XLUDJQqjC5KcS6BiaNTfMOKRLQ/640?wx_fmt=png&from=appmsg "")  
  
尝试下这个订单，可能需要支付实际金额。由于应用程序中的订单金额为零，为了测试它我尝试下这个订单，令人惊讶的是，订单成功了。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj4to1Kvvb1A22bZuUB51ZXz4bZicOOxvQ9QR2umuHsfOgJgVWenVQgCRmaJo5ialeJ19yZOtBiaaHaQg/640?wx_fmt=png&from=appmsg "")  
  
如果你是一个长期主义者，欢迎加入我的知识星球([优先查看这个链接，里面可能还有优惠券](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247489122&idx=1&sn=a022eae85e06e46d769c60b2f608f2b8&chksm=e8a61c01dfd195170a090bce3e27dffdc123af1ca06d196aa1c7fe623a8957755f0cc67fe004&scene=21#wechat_redirect)  
)，我们一起往前走，每日都会更新，精细化运营，微信识别二维码付费即可加入，如不满意，72 小时内可在 App 内无条件自助退款  
  
![](https://mmbiz.qpic.cn/mmbiz_png/YmmVSe19Qj5jYW8icFkojHqg2WTWTjAnvcuF7qGrj3JLz1VgSFDDMOx0DbKjsia5ibMpeISsibYJ0ib1d2glMk2hySA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
## 往期回顾  
  
  
[xss研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487130&idx=1&sn=e20bb0ee083d058c74b5a806c8a581b3&chksm=e8a604f9dfd18defaeb9306b89226dd3a5b776ce4fc194a699a317b29a95efd2098f386d7adb&scene=21#wechat_redirect)  
  
  
[SSRF研究笔记](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486912&idx=1&sn=8704ce12dedf32923c6af49f1b139470&chksm=e8a607a3dfd18eb5abc302a40da024dbd6ada779267e31c20a0fe7bbc75a5947f19ba43db9c7&scene=21#wechat_redirect)  
  
  
[dom-xss精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247488819&idx=1&sn=5141f88f3e70b9c97e63a4b68689bf6e&chksm=e8a61f50dfd1964692f93412f122087ac160b743b4532ee0c1e42a83039de62825ebbd066a1e&scene=21#wechat_redirect)  
  
  
[2022年度精选文章](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487187&idx=1&sn=622438ee6492e4c639ebd8500384ab2f&chksm=e8a604b0dfd18da6c459b4705abd520cc2259a607dd9306915d845c1965224cc117207fc6236&scene=21#wechat_redirect)  
  
  
[Nuclei权威指南-如何躺赚](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247487122&idx=1&sn=32459310408d126aa43240673b8b0846&chksm=e8a604f1dfd18de737769dd512ad4063a3da328117b8a98c4ca9bc5b48af4dcfa397c667f4e3&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试设置功能IV](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486973&idx=1&sn=6ec419db11ff93d30aa2fbc04d8dbab6&chksm=e8a6079edfd18e88f6236e237837ee0d1101489d52f2abb28532162e2937ec4612f1be52a88f&scene=21#wechat_redirect)  
  
  
[漏洞赏金猎人系列-如何测试注册功能以及相关Tips](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
[](http://mp.weixin.qq.com/s?__biz=MzIzMTIzNTM0MA==&mid=2247486764&idx=1&sn=9f78d4c937675d76fb94de20effdeb78&chksm=e8a6074fdfd18e59126990bc3fcae300cdac492b374ad3962926092aa0074c3ee0945a31aa8a&scene=21#wechat_redirect)  
  
  
