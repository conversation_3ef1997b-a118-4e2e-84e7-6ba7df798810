#  漏洞通告 | OpenSSH ProxyCommand命令执行漏洞   
原创 微步情报局  微步在线研究响应中心   2023-12-26 15:37  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMKNkm4Pg1Ed6nv0proxQLEKJ2CUCIficfAwKfClJ84puialc9eER0oaibMn1FDUpibeK1t1YvgZcLYl3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
01 漏洞概况****  
  
  
  
OpenSSH是SSH协议的开源实现，支持对所有的传输进行加密， 广泛应用于远程管理系统和安全文件传输。ProxyCommand是一个OpenSSH客户端中被广泛使用的功能，它允许指定自定义命令来代理 SSH 连接。微步漏洞团队于近日获取到OpenSSH ProxyCommand命令注入漏洞情报。由于OpenSSH的ProxyCommand功能实现时未对传入的参数进行正确的处理，从而导致在特定的场景下会触发命令注入漏洞。  
**经分析和研判，该漏洞利用难度低，但可利用场景较为固定。目前判断可能被利用的一个场景是配合Github项目进行投毒**。利用条件为在执行git clone命令的时候添加了--recurse-submodules参数。原理为Git将会初始化并更新每一个包含在工程中的子模块。这也意味着它将会访问并clone子模块中配置的URL。如果子模块的URL配置为以 ssh:// 开头的地址，系统将会发起SSH请求。如果ssh地址的Host含有恶意命令且符合受害者ProxyCommand配置的Host规则，最终将导致远程命令执行。  
**建议根据自身情况，酌情进行修复。**  
  
02 漏洞处置优先级（VPT）  
  
  
  
**综合处置优先级：**  
**中**  
****  
  
<table><tbody style="visibility: visible;"><tr style="height: 23.3pt;visibility: visible;"><td width="123" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞编号</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">微步编号</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">XVE-2023-36365</span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="143" valign="top" rowspan="6" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞评估</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">危害评级</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><strong style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(181, 15, 26);">高危</span></strong><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">漏洞类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"><span style="font-size:10.5pt;font-family:微软雅黑;font-weight:normal;font-style:normal;" data-font-family="微软雅黑">命令执行</span></span><br style="visibility: visible;"/></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">公开程度</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;">PoC已公开</span><span style="font-size: 14px;visibility: visible;"></span></p></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">利用条件</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;visibility: visible;"><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;">无权限要求</span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;visibility: visible;"><span style="font-family: 黑体;font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">交互要求</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">1-click</span></p></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;">威胁类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">远程</span></p></td></tr><tr style="height:26.0500pt;"><td width="143" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;"><strong>利用情报</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">微步已捕获攻击行为</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">暂无</span></p></td></tr></tbody></table>  
  
### 03 漏洞影响范围 产品名称1.OpenSSH2.libssh受影响版本1.OpenSSH version < 9.6p12.libssh 0.10.6 和 libssh 0.9.8影响范围千万级有无修复补丁有前往X情报社区资产测绘查看影响资产详情：https://x.threatbook.com/v5/survey?q=app%3D%22OpenSSH%22  
  
### 04 漏洞复现 05 修复方案 1、官方修复方案：厂商已发布漏洞修复程序，请前往以下地址进行更新。1.https://www.openssh.com/txt/release-9.62.https://www.libssh.org/security/advisories/CVE-2023-6004.txt2、临时修复方案：1.如非必要，禁止对不信任的主机配置代理；2.提升自身安全意识，不随意下载公开陌生Git项目，不随意点击陌生链接。06 时间线 2023.12.24 微步漏洞团队获取该漏洞相关情报 2023.12.26 微步发布报告---End---微步漏洞情报订阅服务微步提供漏洞情报订阅服务，精准、高效助力企业漏洞运营提供高价值漏洞情报，具备及时、准确、全面和可操作性，帮助企业高效应对漏洞应急与日常运营难题；可实现对高威胁漏洞提前掌握，以最快的效率解决信息差问题，缩短漏洞运营MTTR；提供漏洞完整的技术细节，更贴近用户漏洞处置的落地；将漏洞与威胁事件库、APT组织和黑产团伙攻击大数据、网络空间测绘等结合，对漏洞的实际风险进行持续动态更新。扫码在线沟通↓↓↓点此电话咨询X 漏洞奖励计划“X漏洞奖励计划”是微步X情报社区推出的一款针对未公开漏洞的奖励计划，我们鼓励白帽子提交挖掘到的0day漏洞，并给予白帽子可观的奖励。我们期望通过该计划与白帽子共同努力，提升0day防御能力，守护数字世界安全。活动详情：https://x.threatbook.com/v5/vulReward  
  
