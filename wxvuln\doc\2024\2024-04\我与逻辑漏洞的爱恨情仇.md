#  我与逻辑漏洞的爱恨情仇   
小陈  Z2O安全攻防   2024-04-25 20:46  
  
点击上方[蓝字]，关注我们  
  
  
**建议大家把公众号“Z2O安全攻防”设为星标，否则可能就看不到啦！**  
因为公众号现在只对常读和星标的公众号才能展示大图推送。操作方法：点击右上角的【...】，然后点击【设为星标】即可。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuao3T9EnGbUIqxgDhEVicCV8NbH4FiaZ3YIbpXNEr6qFicGkAelnQHKGHsVlfapMGgO3DHA68iaiac0n4Q/640?wx_fmt=png "")  
  
  
# 免责声明  
  
  
本文仅用于技术讨论与学习，利用此文所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，文章作者及本公众号团队不为此承担任何责任。  
  
# 文章正文  
  
  
# 前言：  
  
逻辑漏洞自始至终一直是一个永恒的话题，逻辑漏洞是一种设计缺陷，通常表现为设计者或开发者在思考过程中做出的特殊假设存在明显或隐含的错误，也是目前大环境下比例比较高的漏洞，在以前挖逻辑也是id换着测，但随着时间的推移，人员安全意识的增高，也逐渐出现更多新奇的手段，比如敏感字符加密处理，让你抓不到包各种验证等等，可以尝试全局代理抓包，js前端解密等绕过。  
# 总结下逻辑漏洞相关的类型  
- • 撞库，爆破账号/密码  
  
- • 登陆处验证码可绕过，登录注册等  
  
- • 无限制的短信轰炸/突破限制的短信轰炸  
  
- • 任意用户登录/任意用户注册  
  
- • 任意用户密码找回  
  
- • 支付漏洞/零元购/并发/优惠券/签约/占用商品/溢出等  
  
- • 条件竞争/并发突破限制  
  
- • 验证码可控/DDos  
  
- • 水平越权/垂直越权  
  
- • 越权删除/评论/登录/点赞/修改资料  
  
- • 非会员使用会员功能  
  
- • 前端js某些限制绕过/解密  
  
- • 其他突破限制或影响业务的功能  
  
# 正文  
  
相信各位师傅都看过很多逻辑漏洞的文章，本文也不重复叙述那些文章，只讲一些技巧型的思路，批量挖掘逻辑漏洞，希望各位师傅看完能有所收获举一反三，为了不产生不必要麻烦，故很多地方打码处理。  
  
在2019年时，利用搜索引擎发现了大量逻辑漏洞，可以说刷分很强，但是至今在网上也没看到过这种的文章，思路其实很简单，挖洞就是脑子要灵活  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgQibwgDV3QufpHianTWIP8xN15e7wjiakgTeQgVOaZfKqvNXib2nQJOONOg/640?wx_fmt=png&from=appmsg "null")  
  
  
先来一张图冲击一下视野  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgw5h5dxR78t3o4BbfcO7G3Oicv1CcA3jGym3oE0daia0uLcsdRF2kADVA/640?wx_fmt=png&from=appmsg "null")  
  
  
Ps：2019年7月的图  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgyRAkOOukZn320nl5ILiaMvDMK5dacfCM2LpslKykoJClibGibvcNEkl0w/640?wx_fmt=png&from=appmsg "null")  
  
  
Ps：现在的图当时没有刷，相对更喜欢做一些研究然后放起来，然后当兵去了，  
时隔两三年，发现被刷掉了一部分，但还是有思路的，先来看个案例  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgbHAzjW9x8cGFmoqFbYCBNSGmO5Xs16ZFNZq4pgvNBicAgiavkkFopvIA/640?wx_fmt=png&from=appmsg "null")  
  
仅举例一些关键词，自行发挥：  
```
inurl:m intitle:订单详情 inurl:id=
我的订单 订单详情
inurl:order 订单详情
inurl:wap intitle:订单详情
inurl:m intitle:订单详情
inurl:php intitle:订单详情
title:订单详情 手机号
site:.com title:订单详情
title:订单详情 身份证
```  
  
当然不止这些，你还可以根据规律去构造关键词，用google搜索结果如下（ps:你也可以使用其他搜索引擎）：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgIh3MOPA49l0WBvqwB2pibZR0FODCvqy5FbnnWmiaxmwZicNaxMZVtW5MQ/640?wx_fmt=png&from=appmsg "null")  
  
部分案例：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgkrV2mJ5hDJz8KDyWU8ricx6gxSIOziaH1mbp2VjydrP0oDT4HWhicU94A/640?wx_fmt=png&from=appmsg "null")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWg47GD83CdmscDeTXwsVubepicLvfRhgXOiaatlF0DLsGouVInkyvShn4Q/640?wx_fmt=png&from=appmsg "null")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgIPa0fScu1GyomTb9DT6Inh3Jzm5W7fDAX4cXclia9qPkdsB231QM9eg/640?wx_fmt=png&from=appmsg "null")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgzOm5Oa8IECTvZKmYxayDSic5pyiaicPbmLNE5eFgZB8HUibntiaHT1kstow/640?wx_fmt=png&from=appmsg "null")  
  
其实细心的师傅看到这里就已经可以构造自己的关键词了 比如上面第一张图 orderdetail.ac/?id=1  
  
首先id 是一个参数，orderdetai 也可以当一个参数，可得到关键词 inurl:orderdetail 订单详情  
  
这个思路其实可以一直刷，为什么这么说，因为Google会不断收录网址，即使部分厂商修复了，但互联网一直存在，不断有产商开发web程序，各种web功能上线，再收录！  
  
我们利用url中的参数去构造，当你找到了第一个，你就可以找到N个，多去观察url地址以及网站标题和网站内容特征。  
## 扩展思路1  
  
当然，这些根据市场已经不能满足于我们的需求，我们需要寻找相同的系统  
例如：http://domain/wechat/orderDetail.ac?id=887  
可以构造关键词：inurl:orderDetail intitle:订单 inurl:wechat intitle:订单  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgNOI9ZDc3KzBRWicx3DMq5lDthaanTpC5hFgszL9ZVEG78Cobn1Azciaw/640?wx_fmt=png&from=appmsg "null")  
## 扩展思路2  
  
我们同样可以利用关键词，例如参数中为order 翻译为订单我们就可以继续构造相似关键词，例如：inurl:orders intitle:订单  
## 扩展思路3  
  
我们还可以利用网站标题继续构造关键词，例如标题：管理系统inurl:order intitle:管理系统  
## 扩展思路4  
  
发现存在越权漏洞的网址，我们利用fofa等搜索引擎搜索网站特定指纹去寻找相同系统实现批量越权然后同理google搜索 inurl:domain intitle:订单详情，思维跳跃一点  
# 其他案例  
  
部分垂直越权直接进入管理员账户  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgKyicEoHNm9Oic4mUxnIGXnSWh8icQhGugiacNjeKcKcicI9icpbY3XvsXAKA/640?wx_fmt=png&from=appmsg "null")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgJYpyxtYkD91NLBotPcOBCI5kHhyk78aaCjYGh5ClEFMk3QrxfNbDrw/640?wx_fmt=png&from=appmsg "null")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgyCGKQRiczhfEwQaicfIdEYibdcoRGSb6HEXo9qsXancvJ82H18dtbdfAQ/640?wx_fmt=png&from=appmsg "null")  
# 某SRC经典案例  
  
通过搜索引擎得到漏洞url：  
https://domain/xxxxxxxx?openid=xxxx&sign=xxxxx  
  
访问后可发现手机号关键位置都有*  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgO6eHkWWLaAf2dF8eibfP50bBPM6YhxfWIiaYNyMiaibyScmo7Pxhgict0Mg/640?wx_fmt=png&from=appmsg "null")  
  
刷新看请求数据包，common接口可以看到原手机号，导致信息泄露  
  
数据包信息如下（下面泄露的手机号均为修改后的手机号）：  
```
{"resultcode" : 0,"resultinfo" : {"errMsg" : "查询成功","list" : [{"output1" : "0","output2" : "[{\"payTime\":\"1622222277\",\"payFee\":\"3000\",\"paydateformat\":\"08月02日 00:00\",\"itemName\":\"河南中国移动30元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"175*******6\",\"dealId\":\"7921022761202209021872368048\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1661919758\",\"payFee\":\"3000\",\"paydateformat\":\"08月02日 00:00\",\"itemName\":\"河南中国移动30元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"157*******1\",\"dealId\":\"7921022741202208310771116347\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1653019410\",\"payFee\":\"3000\",\"paydateformat\":\"08月02日 00:00\",\"itemName\":\"河南中国移动30元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"157*******9\",\"dealId\":\"7921020951202205208438499741\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1650012506\",\"payFee\":\"5000\",\"paydateformat\":\"08月03日 00:00\",\"itemName\":\"河南中国联通50元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"131*******1\",\"dealId\":\"7921022741202204157844385151\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1647566320\",\"payFee\":\"3000\",\"paydateformat\":\"08月03日 00:00\",\"itemName\":\"河南中国移动30元-秒充}
{\"payTime\":\"1650012506\",\"payFee\":\"5000\",\"paydateformat\":\"08月04日 00:00\",\"itemName\":\"河南中国联通50元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"131*******2\",\"dealId\":\"7921022741202204157844385545\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1687566320\",\"payFee\":\"3000\",\"paydateformat\":\"08月04日 00:00\",\"itemName\":\"河南中国移动30元-秒充}
{\"payTime\":\"1690012506\",\"payFee\":\"5000\",\"paydateformat\":\"08月05日 00:00\",\"itemName\":\"河南中国联通50元-秒充\",\"dealType\":\"1\",\"moblileNum\":\"131*******1\",\"dealId\":\"7921022741202204157844385987\",\"sellerName\":\"\",\"dealState\":\"5\"},{\"payTime\":\"1727566320\",\"payFee\":\"3000\",\"paydateformat\":\"08月05日 00:00\",\"itemName\":\"河南中国移动30元-秒充}
```  
  
构造相关语法，得到更多的订单：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuZ1hzzZSWqicmc2P49nkrNWgL7D6RhJTSf4Qtkscem6uib8VpJBDVIfa8n9DWmCrr6LDpPG6TcNexIQ/640?wx_fmt=png&from=appmsg "")  
# 结束语  
  
如上仅为百度的展示结果，google还有更多结果，希望师傅们看完可以举一反三，吸取经验的技巧，利用此思路挖SRC挣了5k，当然不止于上面的案例，上面仅举例，具体操作还要看实际情况再去利用。  
  
```
文章来源：https://xz.aliyun.com/t/12948
作者：小陈
```  
  
  
# 技术交流  
  
  
### 知识星球  
  
  
**欢迎加入知识星球****，星球致力于红蓝对抗，实战攻防，星球不定时更新内外网攻防渗透技巧，以及最新学习研究成果等。常态化更新最新安全动态。针对网络安全成员的普遍水平，为星友提供了教程、工具、POC&EXP以及各种学习笔记等等。**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuYl1eHu25UAxhOZEBXZpSmXPg6kVsggaWKZsh0ab2kh6icbbkBgOH8icuV0x2IPGGRMiaU2hNBErstcA/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuYl1eHu25UAxhOZEBXZpSmX8Pjria4EK9ib8PPUAxiaMaSqUZibdxNoqqmmVHqGwXkYdzziaZNDLOwCGQw/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKubkRgdNbBQdOZibtbt7oibUpdUIl55vlmiaibqInxXG1Z9tfo52jF8onER5R4U2mCM5RpZia6rwEHnlMAg/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKuYItiapGtLIq3gAQYGfE5nictnkFeBicm7brKdibz4Va1hRf2dKZT0IyRRXYboE1lbZ6ZquDGnzqKibGGw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/h8P1KUHOKubjMEtcW1kV7MHIvYn288NrZ9W6Qdzx0ASyZe4Kn7ttCMJN7eN8VicJG5EhicDZxDwpTI9YejV51ibAw/640?wx_fmt=png&from=appmsg "")  
  
  
### 交流群  
  
  
关注公众号回复“**加群**”，添加Z2OBot好友，自动拉你加入**Z2O安全攻防交流群(微信群)**分享更多好东西。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/h8P1KUHOKuYMO5aHRB3TbIy3xezlTAkbFzqIRfZNnicxSC23h1UmemDu9Jq38xrleA6NyoWBu1nAj0nmE6YXEHg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
### 关注我们  
  
  
  
**关注福利：**  
  
**回复“**  
**app****" 获取  app渗透和app抓包教程**  
  
**回复“**  
**渗透字典****" 获取 针对一些字典重新划分处理，收集了几个密码管理字典生成器用来扩展更多字典的仓库。**  
  
**回复“漏洞库" 获取 最新漏洞POC库(**  
**1.2W+****)******  
  
**回复“资料" 获取 网络安全、渗透测试相关资料文档**  
  
****  
点个【 在看 】，你最好看  
  
  
