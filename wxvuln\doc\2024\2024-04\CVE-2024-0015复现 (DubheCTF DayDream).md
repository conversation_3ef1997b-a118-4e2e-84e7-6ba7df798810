#  CVE-2024-0015复现 (DubheCTF DayDream)   
WoodenmanDu  看雪学苑   2024-04-05 17:59  
  
```
```  
  
##   
  
DayDream是DubheCTF的一个AndroidPwn题，当时就立了flag一定要复现这个题。  
  
## DayDream  
  
  
◆简单说一下，DayDream这个模块是关于android的屏保，下面是google的开发文档。  
> https://developer.android.com/reference/android/service/dreams/DreamService  
  
  
  
◆通过文档可以知道：我们能够写一个service继承自DreamService，实现自定义的android屏保，就像下面这样进行选择。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4BgWuoE5lhCCyxRB5wkXzgEa1FYUlVPiaftMUUDCfkxuIibVK9XMdk7iaA/640?wx_fmt=other&from=appmsg "")  
  
  
◆欸，大家可能注意到了，这个屏保选择旁边的设置按钮是什么呢？  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4QXpicsbJWUs4IaiaAoTfgHC92aTgrAxEPuFoRtoX4JhIvGPnLPTGNXAA/640?wx_fmt=other&from=appmsg "")  
  
  
◆嗷，文档说了，如果我们设置了meta-data  
，就会出现按钮，点击按钮就会跳转到我们设置的Activity。  
  
  
◆是随便跳吗？这个时候我们的漏洞就来了。  
  
## CVE-2024-0015  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4wicZew6oCgmRCKPzX9peFn46ae6fT39Bia5B0e6ybLYfRich5e7ribOVmA/640?wx_fmt=other&from=appmsg "")  
> https://android.googlesource.com/platform/frameworks/base/+/2ce1b7fd37273ea19fbbb6daeeaa6212357b9a70%5E%21/#F1  
  
  
◆这是当时比赛的hint链接，也是我们的漏洞补丁信息，其实官方已经说的很清楚了。  
> Fix vulnerability that allowed attackers to start arbitary activities  
  
  
◆漏洞的效果就是能够造成任意的activity调用。  
  
  
为什么会这样呢？  
  
  
◆上面提到了，官方提供了一个能够调用activity的设置按钮。却忘记了对被调用activity进行验证，导致就算是其他app未导出experted=false  
的activity也能够被调用执行。  
  
  
◆一边猛加补丁，一边猛删接口。![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4UzCUHmBy87F0q8bicDnAROnncbcSbnCjIiaFp8vYoXBrMibWDmyTcD6KA/640?wx_fmt=other&from=appmsg "")  
  
  
  
Then![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4wg5ENz8NrS9tYib2bhAcAuhRL27XgYyvmumlj3ucRCO0XgAicHFHyNhA/640?wx_fmt=other&from=appmsg "")  
  
  
  
◆在diff中可以看到上述注释，补丁做了一件事情，确保被调用的组件和使用DreamService的是属于同一个apk。  
  
  
◆OK，漏洞就说到这里，接下来我们去到比赛题目。  
  
  
  
```
```  
  
##   
## jadx打开，一共有三部分。  
  
  
◆MainActivity，没什么信息。  
  
  
◆FlagReceiver，提供了一个能够接收flag的广播。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4IzGdWkm7wkStPicE9C9y8TIg6SLzeZQp2SoALVOjJhDlicQOgT5sQwpw/640?wx_fmt=other&from=appmsg "")  
  
  
◆SecretActivity，提供了一个readFile能够获取flag。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4aqwrHLIpn09LbmMWZ7OYNERHOmibMkkyewW0hrJbe2U5DByzdhiab9Fg/640?wx_fmt=other&from=appmsg "")  
  
  
很明显，只要调用了SecretActivity，就可以得到flag。再结合上面对于CVE的说明，答案已经呼之欲出了。  
  
  
不过不急，我们再看看server.py，这个文件也是在给我们提示怎么做。  
  
## server.py解读  
> 由于我是环境关了以后复现，题目docker又缺文件，然后我改server.py又发现模拟器莫名跑不起来。最后索性找了个android12的模拟器敲命令，唉  
  
  
```
```  
  
  
  
流程还是很正常的，关键就是one_click  
了。  
  
  
```
```  
  
  
  
OK，接下来我们要做的事情就是写一个apk，然后调用SecretActivity了，具体怎么写可以看上面的Dream Service 官方文档。  
  
## EXP  
> 需要注意的是题目明确了包名  
  
  
  
◆DreamService  
  
  
```
```  
  
  
  
◆androidmanifest.xml声明service（需要注意的是官方说了高API要申请权限）  
  
  
```
```  
  
  
  
◆核心的activity调用  
  
  
```
```  
  
##   
## 执行效果  
> 用户名联想是历史遗留问题:(  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4BLJsP9b2cypOmCXb0cMDlCpZHaicXz6ISdoAmuY7jwNiamM6t6YVibIfQ/640?wx_fmt=other&from=appmsg "")  
> 点击阅读原文，文末可获取  
复现视频  
附件，logcat里有那么多条是因为尝试了不止一次。  
  
  
  
最后附上源码地址：  
> https://github.com/UmVfX1BvaW50/CVE-2024-0015  
  
  
到此完结，如有错漏欢迎指教。  
  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/1UG7KPNHN8FPKkFJeXrNEMw1ySq3stM4mfWkgdqe1uzrdbc7XrgeYckyD7lw4yHfngPNiaxelJrxPHyCuHm2tTA/640?wx_fmt=png&from=appmsg "")  
  
  
**看雪ID：WoodenmanDu**  
  
https://bbs.kanxue.com/user-home-963723.htm  
  
*本文为看雪论坛优秀文章，由 WoodenmanDu 原创，转载请注明来自看雪社区  
  
  
[](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458542779&idx=3&sn=ca6d34e5d9af9acd1abd46ed43224744&chksm=b18d523186fadb2759ffaa18bd8b1b403bce7673bfeeb9162f4528120ba5a76bafab88c15ebc&scene=21#wechat_redirect)  
  
  
  
**#****往期推荐**  
  
1、[iOS越狱检测app及frida过检测](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458549464&idx=1&sn=97894e1d06a52c683be3eadd252dec6b&chksm=b18d4c5286fac544c1c746da3432a0781702507bdcb0ec41809e4d36ac60358070538a173531&scene=21#wechat_redirect)  
  
  
2、[Large Bin Attack学习（_int_malloc源码细读 ）](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458549353&idx=1&sn=b6b17cdbaa6923746ab4a34eef5e0f64&chksm=b18d4ce386fac5f54f94918e43e6095553978dc00cabc217cc312890a922a29e148732ba0f28&scene=21#wechat_redirect)  
  
  
3、[CVE-2022-2588 Dirty Cred漏洞分析与复现](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458549352&idx=1&sn=c45c212b8db20154dbfa9edeaaebc593&chksm=b18d4ce286fac5f415b3697517f2c12d523f7c13d1d143b25d895a980a49a2e420d79e5a46f8&scene=21#wechat_redirect)  
  
  
4、[开发常识 | 彻底理清 CreateFile 读写权限与共享模式的关系](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458549318&idx=1&sn=c5be1f2ffb49ec2c4e229a333e433625&chksm=b18d4ccc86fac5dac6152eedc810cf064326eb22392d908842cba2ebed75b90c13cdac5a08c1&scene=21#wechat_redirect)  
  
  
5、[XAntiDenbug的检测逻辑与基本反调试](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458549131&idx=1&sn=f55b06b92058bdfdffba4fe3d4e6c18e&chksm=b18d4b0186fac21738fc3693d35b7c2cca4fcbfe6ff0bce589eb602c0789af22d3b107277b2b&scene=21#wechat_redirect)  
  
  
6、[Frida-Hook-Java层操作大全](http://mp.weixin.qq.com/s?__biz=MjM5NTc2MDYxMw==&mid=2458548995&idx=1&sn=16bdf98a92759f86335a18613007df0b&chksm=b18d4b8986fac29f9a16b5b40df15848c98f202cf4c3f817f7bcaed2dbc7261cd1fa41bf7a83&scene=21#wechat_redirect)  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/Uia4617poZXP96fGaMPXib13V1bJ52yHq9ycD9Zv3WhiaRb2rKV6wghrNa4VyFR2wibBVNfZt3M5IuUiauQGHvxhQrA/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8GJubmq65v9uBFmEJuoJD78321RiaLpp3FAylJv0nbibloCFmXdVe4wvW4ibgnCc6srNI8sGBkX14MpQ/640?wx_fmt=gif&from=appmsg "")  
  
**球分享**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8GJubmq65v9uBFmEJuoJD78321RiaLpp3FAylJv0nbibloCFmXdVe4wvW4ibgnCc6srNI8sGBkX14MpQ/640?wx_fmt=gif&from=appmsg "")  
  
**球点赞**  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8GJubmq65v9uBFmEJuoJD78321RiaLpp3FAylJv0nbibloCFmXdVe4wvW4ibgnCc6srNI8sGBkX14MpQ/640?wx_fmt=gif&from=appmsg "")  
  
**球在看**  
  
  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_gif/1UG7KPNHN8GJubmq65v9uBFmEJuoJD78txPhfvI9WpuGSCawCN8NJCgzD16Y0IwdUkaI33Qr3DpwRRuvibgRQOg/640?wx_fmt=gif&from=appmsg "")  
  
点击阅读原文查看更多  
  
