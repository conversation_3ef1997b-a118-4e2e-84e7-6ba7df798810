#  【漏洞通告】PuTTY密钥泄露漏洞CVE-2024-31497   
深瞳漏洞实验室  深信服千里目安全技术中心   2024-04-23 16:05  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua9CDwCNcfXdMEKAHhRiaujBVWvKeP9eRcGSL9IHaA3iceibApWowiaOjYsA/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞名称：**  
  
PuTTY密钥泄露漏洞(CVE-2024-31497)  
  
**组件名称：**  
  
PuTTY  
  
**影响范围：**  
  
0.68 ≤ PuTTY ＜ 0.81  
  
**漏洞类型：**  
  
密钥管理错误  
  
**利用条件：**  
  
1、用户认证：不需要用户认证  
  
2、前置条件：默认配置  
  
3、触发方式：远程  
  
**综合评价：**  
  
<综合评定利用难度>：困难。  
  
<综合评定威胁等级>：高危。  
  
**官方解决方案：**  
  
已发布  
  
  
  
  
  
**漏洞分析**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua5s8HTFXlzmgHScqjsmfdibIcySjFp9BIVIPvMmK1f9ZZVfppBeCicahA/640?wx_fmt=gif&from=appmsg "")  
  
**组件介绍**  
  
PuTTY是一个流行的终端模拟器，主要用于远程访问Unix和Unix-like系统（如Linux、BSD等）以及Windows平台。它是一个开源软件，最初由Simon Tatham在1990年代开发。PuTTY支持多种网络协议，包括SSH（Secure Shell）、Telnet、rlogin和原始套接字连接。  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua5s8HTFXlzmgHScqjsmfdibIcySjFp9BIVIPvMmK1f9ZZVfppBeCicahA/640?wx_fmt=gif&from=appmsg "")  
  
**漏洞简介**  
  
2024年4月23日，深信服深瞳漏洞实验室监测PuTTY存在密钥泄露漏洞，编号CVE-2024-31497，漏洞威胁等级：高危。  
  
  
在 PuTTY 0.68 至 0.81之前版本中，攻击者可以通过生成有偏差的 ECDSA 随机数来恢复用户的NIST P-521密钥。密钥泄露后，对手可能会对 Git 中维护的软件进行供应链攻击。另外，作为SSH服务器管理员的攻击者可以利用该漏洞窃取用户的私人密钥，然后用它对其他服务进行未经授权的访问。  
  
  
**影响范围**  
  
0.68 ≤ PuTTY ＜ 0.81  
  
其他影响的供应链组件：  
  
3.24.1 ≤ FileZilla ＜ 3.67.0  
  
5.9.5 ≤ WinSCP ＜ 6.3.3  
  
******* ≤ TortoiseGit ＜ 2.15.1  
  
1.10.0 ≤ TortoiseSVN ≤ 1.14.6  
  
  
**解决方案**  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua5s8HTFXlzmgHScqjsmfdibIcySjFp9BIVIPvMmK1f9ZZVfppBeCicahA/640?wx_fmt=gif&from=appmsg "")  
  
**如何检测组件系统版本**  
  
  
打开putty，点击About即可查看当前版本。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua5zo2Yr86haLQQ195LHtoVibrVK1fYybibxc2wwnNzFZTUMv3bQNvMPjg/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_gif/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLua5s8HTFXlzmgHScqjsmfdibIcySjFp9BIVIPvMmK1f9ZZVfppBeCicahA/640?wx_fmt=gif&from=appmsg "")  
  
**官方修复建议**  
  
  
官方已发布新版本解决该问题，请受到影响的用户尽快升级到最新版本。  
  
https://www.chiark.greenend.org.uk/~sgtatham/putty/latest.html  
  
  
**参考链接**  
  
  
https://www.chiark.greenend.org.uk/~sgtatham/putty/  
  
https://bugzilla.suse.com/show_bug.cgi?id=1222864  
  
https://tortoisegit.org  
  
https://www.chiark.greenend.org.uk/~sgtatham/putty/  
  
https://github.com/advisories/GHSA-6p4c-r453-8743  
  
https://git.tartarus.org/?h=c193fe9848f50a88a4089aac647fecc31ae96d27&p=simon/putty.git  
  
  
**时间轴**  
  
  
  
**2024/4/15**  
  
PuTTY官方发布漏洞公告。  
  
  
**2024/4/23**  
  
深瞳漏洞实验室发布漏洞通告。  
  
  
点击**阅读原文**，及时关注并登录深信服**智安全平台**，可轻松查询漏洞相关解决方案。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLuazEpH7Duq7UiankcxsIQOx37BUtticov9ruN8hI9gfq1zDsaMoA8nvUKA/640?wx_fmt=png&from=appmsg "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/w8NHw6tcQ5wsfWGvzSIGrtdQJRMstLuaFGWU8KUrXibfR1GNMFlKibMFk0srKUkF7YHo1a4CJiaTTniblrPWS7v8EA/640?wx_fmt=jpeg&from=appmsg "")  
  
  
  
  
