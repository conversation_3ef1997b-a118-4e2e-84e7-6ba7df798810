#  【风险通告】Rust存在命令注入漏洞（CVE-2024-24576）   
安恒研究院  安恒信息CERT   2024-04-11 21:10  
  
![](https://mmbiz.qpic.cn/mmbiz_png/JAzzLj4nXesCfIew4xDgxHPaichzoa958OaWgTglXPf5mic3dq7TZc3np7PMDpLQPa4pL89cQvD6FAZaN71atsbA/640?wx_fmt=png&from=appmsg&wx_ "")  
  
<table><tbody style="box-sizing:border-box;"><tr style="box-sizing:border-box;"><td colspan="4" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;background-color:rgb(69, 119, 218);box-sizing:border-box;" width="100.0000%" data-style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;background-color:rgb(69, 119, 218);box-sizing:border-box;" class="js_darkmode__0"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;color:rgb(255, 255, 255);box-sizing:border-box;margin-bottom:unset;"><p style="text-align:center;"><strong style="box-sizing:border-box;">漏洞概述</strong></p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">漏洞名称</strong></p></section></section></td><td colspan="3" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="75.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">Rust存在命令注入漏洞（CVE-2024-24576）</p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">安恒CERT评级</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;word-break:break-all;">1级</p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">CVSS3.1评分</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">10.0<br/></p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">CVE编号</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;word-break:break-all;">CVE-2024-24576</p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">CNVD编号</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">未分配</p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">CNNVD编号</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p>CNNVD-202404-1354</p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">安恒CERT编号</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p>DM-202401-003634</p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">POC情况</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">已发现</p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">EXP情况</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">未发现</p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">在野利用</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">未发现</p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">研究情况</strong></p></section></section></td><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;">分析中<br/></p></section></section></td></tr><tr style="box-sizing:border-box;"><td colspan="1" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="25.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;"><p style="text-align:left;"><strong style="box-sizing:border-box;">危害描述</strong></p></section></section></td><td colspan="3" rowspan="1" style="border-width:1px;border-color:rgb(69, 119, 218);border-style:solid;box-sizing:border-box;" width="75.0000%"><section style="margin:5px 0%;box-sizing:border-box;"><p><span style="font-size:14px;">能够控制传递给派生进程的参数的攻击者可以绕过转义，传递恶意参数，导致任意的shell命令执行。</span></p><section style="padding:0px 5px;font-size:14px;box-sizing:border-box;margin-bottom:unset;overflow:hidden;line-height:0;"><br/></section></section></td></tr></tbody></table>  
  
  
  
**漏洞信息**  
  
  
  
  
  
**漏洞描述**  
  
Rust是一种现代、多范式的编程语言，旨在提供高性能、安全性和并发性。它被广泛应用于操作系统、游戏引擎、浏览器组件和其他高要求的系统级软件开发中。  
  
  
**应急响应等级：**  
1级  
  
**漏洞类型：**  
命令注入  
  
  
**影响范围**  
  
影响  
版本：  
  
Rust < 1.77.2（Windows）  
  
  
**CVSS向量**  
  
访问途径（AV）：网络  
  
攻击复杂度（AC）：低  
  
所需权限（PR）：无需任何权限  
  
用户交互（UI）：不需要用户交互  
  
影响范围 （S）：改变  
  
机密性影响 （C）：高  
  
完整性影响 （l）：高  
  
可用性影响 （A）：高  
  
  
  
**修复方案**  
  
  
  
  
**官方修复方案：**  
  
官方已发布修复方案，受影响的用户建议更新至安全版本。  
  
https://blog.rust-lang.org/2024/04/09/Rust-1.77.2.html  
  
  
  
  
**参考资料**  
  
  
  
  
https://github.com/rust-lang/rust/security/advisories/GHSA-q455-m56c-85mh  
https://nvd.nist.gov/vuln/detail/CVE-2024-24576  
  
  
  
**技术支持**  
  
  
  
  
如有漏洞相关需求支持请联系400-6059-110获取相关能力支撑。  
  
  
