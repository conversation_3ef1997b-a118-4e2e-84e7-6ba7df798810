#  【漏洞预警】Apache Zeppelin shell 代码注入漏洞（CVE-2024-31861）   
cexlife  飓风网络安全   2024-04-15 20:11  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu00PZQ5FHk9ejibJSuv8Ckbg9AsUA2WQAUOanT9vLZsYBUiaCXBiauPjPFiclW3a5AOowMiaRicgmYmQkUEQ/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**Apache Zeppelin是一个让交互式数据分析变得可行的基于网页的开源框架,Zeppelin提供了数据分析、数据可视化等功能,官方发布更新披露CVE-2024-31861Apache Zeppelin shell代码注入漏洞,攻击者可利用Zeppelin中的shell功能执行任意命令,升级后官方默认禁止shell interpreter。**影响范围:**0.10.1<=Apache Zeppelin<0.11.1**解决建议:**1、升级至最新版本2、利用安全组设置其仅对可信地址开放**参考链接:**https://github.com/apache/zeppelin/pull/4708     https://github.com/apache/zeppelin/pull/4708/files      
  
