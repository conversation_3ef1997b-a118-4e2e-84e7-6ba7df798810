#  .NET 复现某OA 3个SQL堆叠注入漏洞   
专攻.NET安全的  dotNet安全矩阵   2024-04-13 08:30  
  
01  
  
应用介绍  
  
某宝 OA是一款企业级办公自动化系统，提供了一系列功能强大的工具，包括但不限于流程审批、文档管理、日程安排、协同办公、人事管理等，为企业提供了一个集成化的解决方案。OA 通过提高工作效率、降低运营成本、优化业务流程等方面，为企业带来了显著的商业价值。  
  
  
  
02  
  
漏洞分析  
  
参数cmdText通过获取后未做任何过滤，  
带入ExecuteQueryNoneResult方法进  
行SQL查询，因此造成注入漏洞  
，核心漏洞代码如下所示。  
```
public ReturnResult<int> ExecuteQueryNoneResult(string token, string cmdText)
{
  if (IsAuthorityCheck() == null)
  {
    return new ReturnResult<int>("token不能为空或已过期");
  }
  cmdText = getByValue("cmdText");
  if (string.IsNullOrWhiteSpace(cmdText))
  {
    return new ReturnResult<int>("cmdText参数不能为空");
  }
  return SingleBase<systemService>.Instance.ExecuteQueryNoneResult(SingleBase<userService>.Instance.GetConnectionString(token), cmdText);
}
```  
  
另外ExecuteSqlForNone、ExecuteSqlForNoneEx两个方法同样可以利用堆叠实现延时注入攻击，只是在参数上稍加变化，将payload注入的位置改成sql参数即可。  
```
public ReturnResult<int> ExecuteSqlForNone(string token, string sql, string strParameters)
{
  if (IsAuthorityCheck() == null)
  {
    return new ReturnResult<int>("token不能为空或已过期");
  }
  sql = getByValue("sql");
  strParameters = getByValue("strParameters");
  if (string.IsNullOrWhiteSpace(sql))
  {
    return new ReturnResult<int>("sql参数不能为空");
  }
  if (string.IsNullOrWhiteSpace(getByValue("strParameters")))
  {
    return new ReturnResult<int>("strParameters参数不能为空");
  }
  return SingleBase<systemService>.Instance.ExecuteSqlForNone(SingleBase<userService>.Instance.GetConnectionString(token), sql, strParameters);
}
```  
```
public ReturnResult<int> ExecuteSqlForNoneEx(string token, string sql, SqlParameter[][] detailParms)
{
  if (IsAuthorityCheck() == null)
  {
    return new ReturnResult<int>("token不能为空或已过期");
  }
  sql = getByValue("sql");
  if (string.IsNullOrWhiteSpace(sql))
  {
    return new ReturnResult<int>("sql参数不能为空");
  }
  if (string.IsNullOrWhiteSpace(getByValue("detailParms")))
  {
    return new ReturnResult<int>("detailParms参数不能为空");
  }
  try
  {
    detailParms = JsonConvert.DeserializeObject<SqlParameter[][]>(getByValue("detailParms"));
  }
  catch
  {
    return new ReturnResult<int>("detailParms参数类型应为SqlParameter[][]");
  }
  return SingleBase<systemService>.Instance.ExecuteSqlForNoneEx(SingleBase<userService>.Instance.GetConnectionString(token), sql, detailParms);
}
```  
  
03  
  
漏洞复现  
  
某个包含名为system的API接口存在注入漏洞，  
当请求为POST时触发该API运行，注入123456的MD5，再通过堆叠注入的方式实现延时注入，WAITFOR DELAY '0:0:10'--，如图所示。  
  
![](https://mmbiz.qpic.cn/mmbiz_png/NO8Q9ApS1YickusbtTvzIHcME4dN9tJOPtHfSBHDCUAONuelZnPNria4PwibLjNfIT4EHq9WNQFtxoZov893CJtZw/640?wx_fmt=png&from=appmsg "")  
  
04  
  
**欢迎加入知识库**  
  
为了更好地应对基于.NET技术栈的风险识别和未知威胁，dotNet安全矩阵星球从创建以来一直聚焦于.NET领域的安全攻防技术，定位于高质量安全攻防星球社区，也得到了许多师傅们的支持和信任，通过星球深度连接入圈的师傅们，一起推动.NET安全高质量的向前发展。  
只需199元就可以加入我们。  
  
  
  
      
目前dot.Net安全矩阵星球已成为中国.NET安全领域最知名、最活跃的技术知识库之一，从.NET Framework到.NET Core，从Web应用到PC端软件应用，无论您是初学者还是经验丰富的开发人员，都能在这里找到对应的安全指南和最佳实践。  
  
    星球汇聚了各行业安全攻防技术大咖，并且每日分享.NET安全技术干货以及交流解答各类技术等问题，社区中发布很多高质量的.NET安全资源，可以说市面上很少见，都是干货。  
  
星球文化始终认为授人以鱼不如授人以渔！  
加入星球后可以跟星主和嘉宾们一对一提问交流，20+个专题栏目涵盖了点、线、面、体等知识面，助力师傅们快速成长！  
其中主题包括.NET Tricks、漏洞分析、内存马、代码审计、预编译、反序列化、webshell免杀、命令执行、C#工具库等等。![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1YiccvW0LwqSx3grm4bgM0fz01qCxrYGBR94wibZ7sk1zIO9DzCgviab9vmUic8qmvynXhSM8LxFhGG97w/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
    我们倾力打造专刊、视频等配套学习资源，循序渐进的方式引导加深安全攻防技术提高以及岗位内推等等服务。![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1Y9XgicSeCfnDO0KyvDNdCZhG3pTSWHRekG0Wrp0FXyHO1mz9ia5uiaICjCmg5jIzx4ERLU8MjXWVSkCw/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
    我们还有一个会员专属的星球陪伴群，加入的成员可以通过在群里提出问题或参与论的方式来与其他成员交流思想和经验。  
此外还可以通过星球或者微信群私聊向我们进行提问，以获取帮助迅速解决问题。  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1YiccvW0LwqSx3grm4bgM0fz07qexJ82p5wxfXsVyzE3cc1WOVswovGicr35RthtQKpibYwibbSvicTRnjA/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/NO8Q9ApS1Y9lvf0EpBgVnMoicPtLAx2A1ls9pNaRTDZ9HLg88k7qk0Y188fdC6DHaful53ibicIFD6ib6Wl4vbaW9Q/640?wx_fmt=other&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
