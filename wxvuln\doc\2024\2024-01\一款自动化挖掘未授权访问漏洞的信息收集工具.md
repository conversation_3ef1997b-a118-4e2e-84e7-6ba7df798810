#  一款自动化挖掘未授权访问漏洞的信息收集工具   
 信安404   2024-01-07 09:00  
  
## 免责声明：  
  
由于传播、利用本公众号所提供的信息而造成的任何直接或者间接的后果及损失，均由使用者本人负责，公众号及作者不为此承担任何责任，一旦造成后果请自行承担！如有侵权烦请告知，我们会立即删除并致歉。谢谢！  
## 项目简介  
> ❝  
> Interface detection/一款自动化挖掘未授权访问漏洞的信息收集工具  
  
## 为什么要使用 IDT？  
  
当你收集到许许多多的接口时，如果一个个去访问，却是很浪费时间。但是光是知道状态码和输出url还是不够，还需要手动打开浏览器查看是否存在未授权漏洞。 IDT支持自动使用浏览器打开url，避免了重复手测打开的操作，您只需使用眼睛去观察网页是否存在可疑的密钥或者未授权漏洞即可  
### 优势  
```
支持筛选url状态码
支持自动化浏览器打开url
支持多线程处理
支持代理模式

```  
### 安装  
  
安装模块后即可使用  
```
pip install -r requirement.txt

```  
### 选项  
```
usage: IDT.py [-h] [-u FILE] [-o OUTPUT] [-sc STATUS_CODE] [-b] [-oss] [-d] [--proxy PROXY]

Open URLs and check for OSS keys.

optional arguments:
  -h, --help            show this help message and exit
  -u FILE, --file FILE  Enumerate files for urls
  -o OUTPUT, --output OUTPUT
                        Specify the output file
  -sc STATUS_CODE, --status_code STATUS_CODE
                        Filter status code
  -b, --browser         Open urls in a web browser
  -oss, --detect_oss    Detect Oss keys
  -d, --default_status_code
                        Default output status code for the current url
  --proxy PROXY         use proxies


```  
## 使用例子  
  
-u 选项指定批量处理的url文件  
```
python IDT.py -u url.txt

```  
  
默认输出200返回值状态码的url  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/JtFpsuzZS5UFMQEwRe6Iic0picNzcdgy3n9ribshcgtNqLWrneQRkQouQKVeSlKpajQ3FxdqxWUaInibq9KSNWdHuw/640?wx_fmt=png&from=appmsg "")  
使用 -b 参数，将会使用浏览器打开输出的url接口进行访问  
```
python3 IDT.py -u url.txt -b

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/JtFpsuzZS5UFMQEwRe6Iic0picNzcdgy3nM5RkBLibSENSfJOW3uvGmEkG9DzGWNCcJ7hXnfh4oNNt3B3RQun0nPw/640?wx_fmt=png&from=appmsg "")  
使用 -d 参数可以指定输出所有url的默认状态码  
```
python IDT.py -u url.txt -d 

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/JtFpsuzZS5UFMQEwRe6Iic0picNzcdgy3nQHXgdyPEicpeok8AxTUR8FGsmtYDzDVm2uu1bpLLNxm5hbrqcAzCY2A/640?wx_fmt=png&from=appmsg "")  
使用 -sc 参数可以进行筛选状态码，然后输出指定的url  
```
python IDT.py -u url.txt -sc 301 

```  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/JtFpsuzZS5UFMQEwRe6Iic0picNzcdgy3naqIPN6Yrhdfy8Ip43MVPv6TWoPYQRZRoQ3pic0RIEQMEY238a0oiaJ0Q/640?wx_fmt=png&from=appmsg "")  
  
使用 -oss参数可以枚举是否存在AK密钥  
  
添加 -o 参数可以，另存文件  
```
python IDT.py -u url.txt -o a.txt

```  
  
添加 --proxy 参数支持代理模式  
```
python IDT.py -u url.txt --proxy 127.0.0.1:10809
```  
  
  
下载连接  
  
## 公众号后台回复“240107”  
  
  
**推荐↓↓↓**  
  
  
