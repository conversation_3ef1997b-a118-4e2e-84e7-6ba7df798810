#  从漏洞管理向持续威胁暴露管理演进的5个关键要素   
 网络安全应急技术国家工程中心   2024-04-23 15:37  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176kVuOvr8HY5fVoJ3pv9vCQn9l81oV9SZAlUfOuFH701iatg4uib7LZMogggJefupc8MKicr2e92o259w/640?wx_fmt=jpeg&from=appmsg "")  
  
多年来，漏洞管理一直是网络安全领域的主要手段，旨在不断识别系统和软件中的漏洞，确定优先级，最终修复漏洞。然而随着网络安全预算紧张、远程办公的常态化，越来越复杂的威胁形势给传统漏洞管理工作提出了更加严峻的挑战。如果企业想降低重大安全事件的数量和损失，就必须创建一个能够更加全面反映组织所面临的实际安全挑战的威胁管理流程，从传统的漏洞管理向持续威胁暴露管理（CTEM）演进成为了企业数字化转型中的必然选择。  
# 传统漏洞管理的不足  
  
漏洞管理是一个识别、分析、缓解和报告软件应用系统中安全威胁的过程。漏洞管理需要定期进行，以及时评估现有的安全状况，以及漏洞管理计划是否需要优化更改。全面且执行良好的漏洞管理计划对于防范网络威胁提供了许多帮助，但也常常面临以下限制：  
  
**1、洞察力不足**  
  
传统的漏洞管理模式需要和已知的漏洞数据库进行比对，经常会错过数据库之外的活跃威胁，或者超出其能力识别范围的复杂威胁。它们还会产生大量的误报，从而使企业对关键威胁的敏感度降低。在这种不全面的威胁管理模式下，企业往往会存在一种虚假的安全感，只要漏洞扫描没有发现异常，安全运营人员就会认为一切太平。  
  
**2、管理无序**  
  
难以对所发现的威胁进行优先级排序是企业目前在漏洞管理工作中面临的最严重的挑战之一。太多企业通过扫描识别安全漏洞，然后直接进入修复阶段。在某种程度上，这种紧迫性是可以理解的。但未能有效地确定优先级可能会导致时间和资源的浪费，因为团队竞相解决的可能是那些对业务关键资产没有真正风险的漏洞。  
  
**3、缺乏连续性**  
  
企业的威胁管理工作应该是持续的，而不是偶发的。如果企业不采取持续的方法，他们将难以控制安全风险的流动并积累大量的“风险债务”。在传统的漏洞管理模式下，由于自动化能力不足，安全运营团队往往会疲于奔命，而处理不断积压的安全问题可能会使安全运营工作整体不堪重负，如何实现以自动化威胁识别为中心的持续威胁管理方法是企业持续改进安全态势的关键要求。  
# 持续威胁暴露管理的价值  
  
传统的漏洞管理只能够解决企业当前的威胁风险问题，但潜在的漏洞风险仍然存在。为了真正做到安然无恙，研究机构Gartner提出了一种主动式威胁防御新思路——持续威胁暴露管理（Continuous Threat Exposure Management，CTEM）。在这种理念中，强调了持续性的安全威胁发现、修复和缓解，鼓励安全团队采用主动的风险管理心态，而非传统模型的被动心态。  
  
Gartner认为，CTEM是一种更加务实且有效的系统化威胁管理方法论，可以有效降低组织遭到安全泄密事件的可能性。通过优先考虑高等级的潜在威胁处置，CTEM实现了不断完善的安全态势改进，将“修复和态势改进”从暴露面管理计划中分离，强调有效改进态势的处置要求。同时，CTEM计划的运作有特定的时间范围，它遵循治理、风险和合规性（GRC）的要求，可为企业长期安全管理战略的转变提供决策支撑。  
  
对于企业组织而言，将传统漏洞管理模式升级为持续威胁暴露管理（CTEM）可以获得以下收益：  
  
• 不仅限于漏洞修补：CTEM不仅识别和修补漏洞，它还能够持续监控整个网络攻击面，包括外部威胁和错误配置，从而提供组织网络安全风险态势的完整视图；  
  
• 提高威胁管理效率：人工修补漏洞不仅耗时，也更容易出错。CTEM能够帮助安全运营人员自动化处理这些任务，使安全团队能够专注于重要工作和事件响应。  
  
• 数据驱动的管理决策：CTEM使组织能够根据实时数据和威胁情报做出更优化的安全管理决策，这有助于优化资源分配，并尽量提高威胁管理工作的成效。  
  
• 增强的安全态势：通过主动识别和缓解威胁，CTEM增强了整体安全态势，使组织能够应对一系列更广泛的威胁，不仅仅是漏洞，这大大降低了新型网络攻击得逞的风险。  
  
• 实时威胁情报支持：CTEM要求整合最新的威胁情报信息，便于组织根据漏洞被利用的可能性确定其优先级，从而确保合理利用有限资源，优先处理那些最严重的安全威胁。  
# 向持续威胁暴露管理演进的关键因素  
  
尽管开展持续威胁暴露管理有很多优点，但其真正实现也并不容易，因为这需要企业安全运营团队长期投入大量时间、人员及其他资源。为了保障企业由传统的漏洞管理模式向持续威胁暴露管理的顺利演进，研究人员建议企业在此过程中重点关注以下5个因素：  
  
**1、定义明确的目标**  
  
企业应该明确定义采用CTEM计划的安全目标和期望结果，使组织网络风险管理工作与数字化发展目标协同一致。这有利于指导接下来的持续威胁暴露管理工作。  
  
**2、评估现有的数字化环境**  
  
在实施CTEM计划之前，企业应该全面了解现有的安全基础设施、工具和流程，以确定能力差距和集成需求。掌握最新的数字化资产清单是开展持续威胁管理管理计划的基础要求，但实践起来却非常困难。尤其是在当今的企业环境中，物理设备、远程终端、物联网组件、云服务、软件即服务（SaaS）和开源代码组件等大量系统和应用充斥其中，想要获取一份全面并能及时更新的数字资产清单非常困难。  
  
**3、选择合适的技术工具**  
  
企业在实施持续威胁暴露管理计划时，需要应用功能完善的威胁管理工具。这样不仅可以快速识别各种安全漏洞，还能够自动部署和安装相应补丁，从而加强系统的安全性，防止严重的安全风险。  
  
**4、制定全面的威胁管理计划**  
  
企业应该为持续监测、威胁情报集成、漏洞补救和持续优化制定一项全面的策略计划。通过全面的威胁管理策略，可帮助安全团队在实时网络攻击的压力下，有条不紊地采取适当的威胁管理和处置措施。同时，还建议企业尽快更新组织的网络安全意识培训计划，强调日常业务环境中威胁可见性和警惕性的重要性，并根据每个员工的日常反馈，不断更新优化CTEM流程与目标。  
  
**5、组建专业的威胁管理团队**  
  
开展持续威胁暴露管理涉及多个方面，从定期的渗透测试到全面的威胁暴露管理，任何一个疏漏都可能导致危害发生。因此，需要一个专业、可靠的安全团队来进行保障。在这个安全团队中，除了要配置负责威胁发现和事件处置的专业人员，还要涵盖业务部门的利益相关者，例如业务部门的风险管理员工，他们可以说明在对业务系统进行处置时，组织可能受到的影响，这样团队可以更好地制定并实施威胁暴露管理工作计划。  
  
**参考链接：**  
  
https://strobes.co/blog/the-evolving-landscape-of-security-from-vulnerability-management-to-ctem/  
  
  
  
原文来源  
：安全牛  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=other&wxfrom=5&wx_lazy=1&wx_co=1&tp=webp "")  
  
  
