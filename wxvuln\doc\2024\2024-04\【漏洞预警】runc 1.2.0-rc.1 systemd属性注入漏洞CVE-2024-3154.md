#  【漏洞预警】runc <1.2.0-rc.1 systemd属性注入漏洞CVE-2024-3154   
cexlife  飓风网络安全   2024-04-28 21:05  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu02kwzEF2skuJ5UE3Vibd7icibEOe1coCiaoGUibDMdFTicSnuicLPKKRRvaibiccYemwZrsYGRW5efaWXomFfg/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**  
CRI-O是一款开源的、用于Kubernetes系统的轻量级容器运行时环境,runc是CRI-O中用于创建和运行容器的工具,runc受影响版本中由于未对Pod注解有效过滤,具有创建Pod注解攻击者可将恶意的systemd属性(如：ExecStartPre、ExecStart、ExecReload等)注入注解中,进而在宿主系统中执行任意操作,补丁版本通过配置黑名单PotentiallyUnsafeConfigAnnotations修复此漏洞。  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu02kwzEF2skuJ5UE3Vibd7icibE0nxmTHWZOVYENevl9DOVDCdKPwjxmBA0jib3icuiarMmAExoYo24V6CEw/640?wx_fmt=png&from=appmsg "")  
**影响范围:**github.com/opencontainers/runc/libcontainer[0.0.1, 1.2.0-rc.1)**修复方案:**将组件github.com/opencontainers/runc/libcontainer升级至**1.2.0-rc.1**及以上版本**参考链接:**https://bugzilla.redhat.com/show_bug.cgi?id=2272532  
