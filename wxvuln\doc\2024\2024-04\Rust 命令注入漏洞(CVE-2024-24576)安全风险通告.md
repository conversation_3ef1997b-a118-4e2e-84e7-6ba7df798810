#  Rust 命令注入漏洞(CVE-2024-24576)安全风险通告   
 奇安信 CERT   2024-04-11 17:40  
  
●   
点击↑蓝字关注我们，获取更多安全风险通告  
  
  
<table><tbody style="outline: 0px;visibility: visible;"><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="center" rowspan="1" colspan="4" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;background-color: rgb(70, 118, 217);visibility: visible;"><p style="outline: 0px;line-height: 1.5em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 255, 255);letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">漏洞概述</span></strong><br style="outline: 0px;visibility: visible;"/></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">漏洞名称</span></strong></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;caret-color: red;letter-spacing: 0px;visibility: visible;">Rust 命令注入漏洞</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" rowspan="1" colspan="1" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">漏洞编号</strong></span></span></p></td><td valign="middle" align="left" rowspan="1" colspan="3" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">QVD-2024-13050,CVE-2024-24576</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">公开时间</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">2024-04-09</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="165" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">影响量级</span></strong></span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);font-size: 13px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">十万级</span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);font-size: 17px;caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">奇安信评级</span></strong></span></p></td><td valign="middle" align="left" width="157" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;letter-spacing: 0px;visibility: visible;"><strong style="cursor: text;color: rgb(255, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;letter-spacing: 0px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高危</span></strong></span></p></td><td valign="middle" align="left" width="165" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;letter-spacing: 0px;visibility: visible;">CVSS 3.1分数</span></strong></p></td><td valign="middle" align="left" width="98" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-size: 13px;letter-spacing: 0px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">10.0</strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" align="left" colspan="1" rowspan="1" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;">威胁类型</strong></span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="157" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">命令执行</span></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="165" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;color: rgb(0, 0, 0);caret-color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">利用可能性</span></strong></p></td><td valign="middle" align="left" colspan="1" rowspan="1" width="98" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="cursor: text;caret-color: rgb(255, 0, 0);color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;, sans-serif;visibility: visible;max-inline-size: 100%;outline: none 0px !important;"><span style="cursor: text;font-size: 13px;visibility: visible;max-inline-size: 100%;outline: none 0px !important;">高</span></strong></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">POC状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;color: rgb(0, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;"><strong style="font-family: system-ui, -apple-system, BlinkMacSystemFont, &#34;Helvetica Neue&#34;, &#34;PingFang SC&#34;, &#34;Hiragino Sans GB&#34;, &#34;Microsoft YaHei UI&#34;, &#34;Microsoft YaHei&#34;, Arial, sans-serif;font-size: 13px;letter-spacing: 0.544px;text-align: -webkit-left;text-wrap: wrap;background-color: rgb(255, 255, 255);outline: 0px;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已公开</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="165" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">在野利用状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;color: rgb(0, 0, 0);visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未发现</span></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="1" rowspan="1" align="left" width="137" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">EXP状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="157" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">未公开</span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="165" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">技术细节状态</span></strong></span></p></td><td valign="middle" colspan="1" rowspan="1" align="left" width="98" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;color: rgb(255, 0, 0);font-family: 微软雅黑, &#34;Microsoft YaHei&#34;;visibility: visible;">已公开</span></strong></span></p></td></tr><tr style="outline: 0px;visibility: visible;"><td valign="middle" colspan="4" rowspan="1" align="left" style="border-color: rgb(70, 118, 217);outline: 0px;word-break: break-all;hyphens: auto;visibility: visible;"><p style="outline: 0px;line-height: 1em;visibility: visible;"><strong style="outline: 0px;visibility: visible;"><span style="outline: 0px;font-size: 13px;visibility: visible;">危害描述：</span></strong><span style="outline: 0px;font-size: 13px;letter-spacing: 0.544px;visibility: visible;">远程攻击者利用该漏洞能够在目标系统上执行任意命令。</span></p></td></tr></tbody></table>  
  
  
**0****1**  
  
**漏洞详情**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响组件**  
  
Rust 是一种开源的系统编程语言，它专注于安全性、并发性和性能，旨在提供内存安全和线程安全的同时，保持高性能和控制力。  
  
  
**>**  
**>**  
**>**  
**>**  
  
**漏洞描述**  
  
近日，奇安信CERT监测到Rust官方发布新版本修复**Rust 命令注入漏洞(CVE-2024-24576)**。在Windows上使用Command API调用批处理文件（使用bat和cmd扩展名）时，Rust标准库没有正确地对参数进行转义。攻击者如果能够控制传递给生成的进程的参数，就可以通过绕过转义来执行任意的Shell命令。**目前该PoC已在互联网上公开**，**鉴于此漏洞影响范围较大，建议客户尽快做好自查及防护。**  
  
  
  
**02**  
  
**影响范围**  
  
**>**  
**>**  
**>**  
**>**  
  
**影响版本**  
  
Rust < 1.77.2（Windows平台）  
  
  
**>**  
**>**  
**>**  
**>**  
  
**其他受影响组件**  
  
无  
  
  
**03**  
  
**处置建议**  
  
**>**  
**>**  
**>**  
**>**  
  
**安全更新**  
  
官方已发布新版本修复漏洞，建议尽快升级到安全版本1.77.2及以上，链接如下：  
  
https://blog.rust-lang.org/2024/04/09/Rust-1.77.2.html  
  
  
**版本检测**  
  
在Windows cmd 或 Powershell中执行命令：  
  
rustc --version  
  
  
  
  
**04**  
  
**参考资料**  
  
[1]https://github.com/rust-lang/rust/security/advisories/GHSA-q455-m56c-85mh  
  
[  
1]  
https://blog.rust-lang.org/2024/04/09/Rust-1.77.2.html  
  
[  
1]  
https://blog.rust-lang.org/2024/04/09/cve-2024-24576.html  
  
  
  
  
**05**  
  
**时间线**  
  
2024年4月11日，奇安信 CERT发布安全风险通告。  
  
  
  
**06**  
  
**漏洞情报服务**  
  
奇安信ALPHA威胁分析平台已支持漏洞情报订阅服务：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs49Igdxlxc1q7BeE5iboX6paDHfXribNNG8O7s68g1vQMZTmlQo4O008nUqvn44GOxovR1kONS8HftiaQ/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/3tG2LbK7WG3tezJEzJsicLSWCGsIggLbcfk4LB5WK7pdSwMksxPOAoHuibjQpBlEId4nyIIw52n2J8N8MowYZcjA/640?tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other "")  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/EkibxOB3fs48uicEVMI80iawKxZMqAg749f5ibO8sEvaYFuVp0AlKn67fKehcP3J7H1icvjMFN2mvduFkLrciaRZDRkw/640?tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&wx_fmt=other "CERT LOGO.png")  
  
**奇安信 CERT**  
  
**致力于**  
第一时间为企业级用户提供**权威**漏洞情报和**有效**  
解决方案。  
  
  
点击↓**阅读原文**，到  
**ALPHA威胁分析平台**  
订阅更多漏洞信息。  
  
