#  漏洞通告 | Apache Struts2文件上传漏洞   
微步情报局  微步在线研究响应中心   2023-12-11 10:39  
  
![](https://mmbiz.qpic.cn/mmbiz_png/fFyp1gWjicMKNkm4Pg1Ed6nv0proxQLEKJ2CUCIficfAwKfClJ84puialc9eER0oaibMn1FDUpibeK1t1YvgZcLYl3A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
01 漏洞概况****  
  
  
  
Apache Struts2 是一个开源的 Java Web 应用程序开发框架，旨在帮助开发人员构建灵活、可维护和可扩展的企业级Web应用程序。近日，微步漏洞团队检测到Apache Struts文件上传漏洞（CVE-2023-50164）。 经过分析和研判，攻击者可利用该漏洞，  
在特定的条件下，通过污染相关上传参数导致任意文件上传，执行任意代码，建议及时修复。  
  
02 漏洞处置优先级（VPT）  
  
  
  
**综合处置优先级：**  
**高**  
****  
  
<table><tbody style="visibility: visible;"><tr style="height: 23.3pt;visibility: visible;"><td width="123" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞编号</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">微步编号</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: 1pt;border-style: solid;border-color: rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">XVE-2023-35659</span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="143" valign="top" rowspan="6" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;"><strong style="visibility: visible;">漏洞评估</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">危害评级</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><p style="visibility: visible;"><strong style="visibility: visible;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;color: rgb(181, 15, 26);">高危</span></strong><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);visibility: visible;"></span></p></td></tr><tr style="height: 23.3pt;visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;visibility: visible;">漏洞类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">文件上传</span><br/></section></td></tr><tr style="visibility: visible;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);visibility: visible;"><p style="visibility: visible;"><span style="font-size: 14px;">公开程度</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">PoC已公开</span><span style="font-size: 14px;"></span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">利用条件</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">无权限要求</span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><section style="margin: 0px;line-height: 1.6em;text-align: justify;text-indent: 0em;"><span style="font-family: 黑体;font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">交互要求</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">0-click</span></p></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">威胁类型</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">远程</span></p></td></tr><tr style="height:26.0500pt;"><td width="143" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>利用情报</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">微步已捕获攻击行为</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">暂无</span></p></td></tr><tr><td width="143" valign="top" rowspan="4" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;"><strong>影响产品</strong></span></p></td><td width="107" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">产品名称</span></p></td><td width="174" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">Apache Struts</span></section><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="mso-spacerun:&#39;yes&#39;;font-family:微软雅黑;mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;"><span style="font-family:微软雅黑;"></span></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">受影响版本</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">Struts 2.0.0 - Struts 2.3.37</span></section><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">Struts 2.5.0 - Struts 2.5.32</span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);">Struts 6.0.0 - Struts 6.3.0</span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span><section style="line-height: 1.6em;text-align: justify;margin: 0px;text-indent: 0em;"><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></section><span style="font-size: 14px;letter-spacing: 0.578px;text-decoration: rgba(0, 0, 0, 0.9);"></span></td></tr><tr><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);"><p><span style="font-size: 14px;">影响范围</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">万级</span></p></td></tr><tr style="height:26.7000pt;"><td width="175" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有无修复补丁</span></p></td><td width="197" valign="top" style="padding: 0pt 5.4pt;border-width: medium 1pt 1pt;border-style: none solid solid;border-color: currentcolor rgb(190, 190, 190) rgb(190, 190, 190);word-break: break-all;"><p><span style="font-size: 14px;">有<br/></span></p></td></tr></tbody></table>  
### 03 漏洞复现 04 修复方案 1、官方修复方案：官方已发布修复版本，建议用户参照前文影响版本，前往以下链接下载修复版本：https://struts.apache.org/download.cgi2、临时修复方案：（1）如非必要，不对外暴露未授权文件上传接口；（2）如非必要，不完全依赖框架提供的文件上传拦截器；对上传文件做二次校验；（3）使用流量防护设备如TDP对流量进行监控，及时发现恶意文件上传行为。05 微步在线产品侧支持情况  （1）微步在线威胁感知平台TDP已支持检测，规则ID为 S3100137050（2）微步在线安全情报社区X已支持资产查询，链接为：https://x.threatbook.com/v5/survey?q=app%3DStruts206 时间线 2023.12.07 厂商发布补丁，微步漏洞团队获取该漏洞相关情报2023.12.09 微步在线威胁感知平台TDP支持检测2023.12.11 微步发布报告---End---微步漏洞情报订阅服务微步提供漏洞情报订阅服务，精准、高效助力企业漏洞运营提供高价值漏洞情报，具备及时、准确、全面和可操作性，帮助企业高效应对漏洞应急与日常运营难题；可实现对高威胁漏洞提前掌握，以最快的效率解决信息差问题，缩短漏洞运营MTTR；提供漏洞完整的技术细节，更贴近用户漏洞处置的落地；将漏洞与威胁事件库、APT组织和黑产团伙攻击大数据、网络空间测绘等结合，对漏洞的实际风险进行持续动态更新。扫码在线沟通↓↓↓点此电话咨询X 漏洞奖励计划“X漏洞奖励计划”是微步X情报社区推出的一款针对未公开漏洞的奖励计划，我们鼓励白帽子提交挖掘到的0day漏洞，并给予白帽子可观的奖励。我们期望通过该计划与白帽子共同努力，提升0day防御能力，守护数字世界安全。活动详情：https://x.threatbook.com/v5/vulReward  
  
