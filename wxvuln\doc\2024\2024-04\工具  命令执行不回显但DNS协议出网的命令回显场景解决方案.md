#  工具 | 命令执行不回显但DNS协议出网的命令回显场景解决方案   
sv3nbeast  进击的HACK   2024-04-17 23:48  
  
# DnslogCmdEcho  
  
**下载地址见文末**  
  
  
命令执行不回显但DNS协议出网的命令回显场景解决方案  
  
使用：  
- python3 HexDnsEcho.py //本机执行  
  
- python3 CommandGen.py whoami //输出的命令在目标机器上执行  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/a1BOUvqnbriaQDiaMGZOWn2FXGWg3V1u7l7xcU8kFYadtaA6z2AbnibiaqgyFLx37sgJdzCNOBapLvvElLmo1Ca1IA/640?wx_fmt=png&from=appmsg "")  
  
# Linux  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/a1BOUvqnbriaQDiaMGZOWn2FXGWg3V1u7libLKjbVozUKXEV1rc5kToBLPkumeI36f1rwmic7J3rgeqVgMzJ6KVHIA/640?wx_fmt=png&from=appmsg "")  
# Windows  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/a1BOUvqnbriaQDiaMGZOWn2FXGWg3V1u7lZOvjIXVbAcfgrvR3KFsUgdTRwWs6N2TLVL1QOdqMgEuaepQHE03rpg/640?wx_fmt=png&from=appmsg "")  
  
  
  
**下载地址**  
  
**https://github.com/sv3nbeast/DnslogCmdEcho**  
  
**点击下方名片进入公众号**  
  
****  
  
****  
  
  
