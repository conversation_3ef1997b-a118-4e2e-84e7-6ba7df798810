#  二进制漏洞分析-25.华为TrustZone TA_SignTool OOB Read   
原创 haidragon  安全狗的自我修养   2023-12-17 21:04  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHL5u1UUice03iaOicUmNl5f96icPndfmZ63AGb3pvVVTDia1u8ib7710U2wib8wa7zEULTlTE2bKtKLb6Ng/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
   
  
- # 二进制漏洞分析-1.华为Security Hypervisor漏洞  
  
- # 二进制漏洞分析-2.揭示华为安全管理程序(上)  
  
- # 二进制漏洞分析-3.揭示华为安全管理程序(下)  
  
- # 二进制漏洞分析-4.华为安全监控漏洞(SMC SE 工厂检查 OOB 访问)  
  
- **二进制漏洞分析-5.华为安全监控漏洞(SMC MNTN OOB 访问)**  
  
- # 二进制漏洞分析-6.Parallels Desktop Toolgate 漏洞  
  
- # 二进制漏洞分析-7.华为TrustZone Vsim_Sw漏洞  
  
- # 二进制漏洞分析-8.Huawei TrustZone VprTa漏洞  
  
- # 二进制漏洞分析-9.华为TrustZone TEE_Weaver漏洞  
  
- **二进制漏洞分析-10.华为TrustZone TEE_SERVICE_VOICE_REC漏洞**  
  
-   
- # 二进制漏洞分析-11.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(上)  
  
- # 二进制漏洞分析-12.华为TrustZone TEE_SERVICE_MULTIDRM漏洞(下)  
  
- # 二进制漏洞分析-13.华为TrustZone TEE_SERVICE_FACE_REC漏洞(一)  
  
- # 二进制漏洞分析-14.华为TrustZone TEE_SERVICE_FACE_REC漏洞(二)  
  
- # 二进制漏洞分析-15.华为TrustZone TEE_SERVICE_FACE_REC漏洞(三)  
  
- # 二进制漏洞分析-16.华为TrustZone TEE_SERVICE_FACE_REC漏洞(四)  
  
- # 二进制漏洞分析-17.华为TrustZone Tee_Fido_Main漏洞  
  
- # 二进制漏洞分析-18.华为TrustZone TEE_EID漏洞  
  
- **二进制漏洞分析-19.华为TrustZone TCIS漏洞**  
  
- # 二进制漏洞分析-20.TrustZone Task_Phone_Novelchd漏洞(上)  
  
- # 二进制漏洞分析-20.TrustZone Task_Phone_Novelchd漏洞(下)  
  
- **二进制漏洞分析-21.华为TrustZone TALoader信息泄露**  
  
- **二进制漏洞分析-22.华为TrustZone TA_uDFingerPrint漏洞**  
  
- **二进制漏洞分析-23.华为TrustZone TA_SensorInfo漏洞**  
  
- [二进制漏洞分析-24.华为TrustZone TA_HuaweiWallet漏洞](http://mp.weixin.qq.com/s?__biz=MzkwOTE5MDY5NA==&mid=2247490626&idx=1&sn=8257610f0a8ed33e0723423c768e30bd&chksm=c13f2f0bf648a61de2785b031d4861a30a1b0264f1af980b8386aa3d694be315f8fa61cd39b4&scene=21#wechat_redirect)  
  
  
- 华为TrustZone TA_SignTool OOB Read  
  
- SignTool TA 符合 GlobalPlatform TEE 内部核心 API。因此，它从位于正常环境中的客户端应用程序接收命令。这些命令由函数处理。该 TA 实现了 16 个命令，其中包括一个漏洞。TA_InvokeCommandEntryPointCmdInitObjectWithKeys  
## OOB 访问CmdInitObjectWithKeys¶  
  
CmdInitObjectWithKeys  
用于分配由用户提供的属性集填充的瞬态对象。例如，对象可以是 RSA 密钥对，其属性是模数、公共指数和私有指数。 只需取消引用其参数，对其进行字节交换并返回即可。retrieveUint32FromBuffer  
```
```  
```
int CmdInitObjectWithKeys(void *session, unsigned int paramTypes, TEE_Param *params) {
    // [...]
    attrInfo = (uint32_t *)params[1].memref.buffer;
    attrData = (uint8_t  *)params[2].memref.buffer;
    // [...]
    i = 0;
    do {
        attributeID = retrieveUint32FromBuffer(attrInfo);
        offset = retrieveUint32FromBuffer(attrInfo + 1);
        length = retrieveUint32FromBuffer(attrInfo + 2);
        attrInfo += 3;
        TEE_InitRefAttribute(&attrs[3 * i++], attributeID, attrData + offset, length);
    }
    while (i < attrCount);
    // [...]
    object = *(TEE_ObjectHandle *)session;
    TEE_PopulateTransientObject(object, attrs, attrCount);
    // [...]
}

```  
  
对于每个属性，用户需要在第一个缓冲区中提供 3 个 dwords：attrInfo  
  
问题是 是无界的，因此可以指向任意位置。在后面的调用中，属性数据将被复制到会话中保存的对象句柄中。offsetattrData + offsetTEE_PopulateTransientObject  
## 开发¶  
### 我们的设备设置¶  
  
我们开发漏洞利用的设备是运行固件更新的P40 Pro。trustlet 二进制 MD5 校验和如下：ELS-LGRP4-OVS_11.0.0.196  
```
```  
```
HWELS:/ # md5sum /vendor/bin/9b17660b-8968-4eed-917e-dd32379bd548.sec
f47939bd8d271f0cc8bedbd02042bb28  /vendor/bin/9b17660b-8968-4eed-917e-dd32379bd548.sec

```  
  
华为TEE OS iTrustee实现了白名单机制，只允许特定的客户端应用（原生二进制文件或APK）与可信应用通信。  
  
在我们的例子中，HuaweiNfcActiveCard TA 只能被 4 个原生二进制文件和 2 个 APK 调用：  
  
身份验证机制分为 3 个部分：- 守护进程，实现 TEE 客户端 API，检查哪个原生二进制文件/APK 正在与它通信，并将该信息发送到内核驱动程序;- 内核驱动程序确保它正在与 通信，并将它收到的信息转发给 TEE OS;- TEE OS 验证客户端应用是否在 TA 的白名单中。teecdteecd  
  
由于我们不想费心在这些二进制文件之一中注入代码，因此我们选择通过修补内核驱动程序来规避身份验证，以添加模拟任何原生二进制文件/APK 的功能。  
### 任意读取¶  
  
为了实现任意读取，我们首先发送一个命令来初始化一个具有 3 个属性的 RSA 密钥对对象：模数、公共指数和私有指数。通过为模数指定 - 的偏移量，它将使用位于 的数据进行初始化。我们根据经验和过去漏洞利用的知识获得了第二个输入缓冲区的地址，并验证了该地址始终保持不变。CmdInitObjectWithKeysaddrattrDataaddrattrData  
  
接下来，我们发送一个命令，从我们刚刚创建的对象返回模数和公共指数。模量属性数据将是从 读取的数据。CmdGetPublicKeyaddr  
  
在漏洞利用中，我们通过转储 trustlet 内存来演示我们的读取原语：  
```
```  
```
adb wait-for-device shell su root sh -c 0 "/data/local/tmp/ta_signtool"
ta_base_addr = 380d000
0x0380d000: 7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 .ELF............
0x0380d010: 03 00 28 00 01 00 00 00 cc 10 00 00 34 00 00 00 ..(.........4...
0x0380d020: 88 69 00 00 00 02 00 05 34 00 20 00 05 00 28 00 .i......4. ...(.
0x0380d030: 11 00 10 00 01 00 00 00 00 00 00 00 00 00 00 00 ................

```  
## 受影响的设备¶  
  
我们已验证该漏洞是否影响了以下设备：  
  
请注意，其他型号可能已受到影响。  
## 补丁¶  
<table><thead style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><th msttexthash="4389879" msthash="60" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">名字</th><th msttexthash="4044495" msthash="61" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">严厉</th><th msttexthash="7713706" msthash="62" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">CVE漏洞</th><th msttexthash="5254223" msthash="63" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;color: rgb(54, 54, 54);text-align: inherit;border-color: rgb(219, 219, 219);border-width: 0px 0px 2px;">补丁</th></tr></thead><tbody style="box-sizing: inherit;"><tr style="box-sizing: inherit;"><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);"><span style="box-sizing: inherit;">OOB 访问</span><code style="box-sizing: inherit;-webkit-font-smoothing: auto;background-color: rgb(245, 245, 245);color: rgb(210, 78, 104);font-size: 0.875em;padding: 0.25em 0.5em;white-space-collapse: preserve;border-radius: 5px;">CmdInitObjectWithKeys</code></td><td msttexthash="4503603" msthash="65" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">危急</td><td msttexthash="15878018" msthash="66" style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">CVE-2021-40020 漏洞</td><td style="box-sizing: inherit;padding: 0.5em 0.75em;vertical-align: top;text-align: inherit;border-width: 0px;border-color: rgb(219, 219, 219);">2022 年 &lt;&gt; 月</td></tr></tbody></table>## 时间线¶  
  
  
  
- 08年2021月<>日 - 向华为PSIRT发送漏洞报告。  
  
- 16年2021月<>日 - 华为PSIRT确认该漏洞报告。  
  
- 01年2022月2022日 - 华为PSIRT表示，这些问题已在<>年<>月的更新中修复。  
  
- 从 30 年 2022 月   
19 日至 2023 年   
<> 月 <> 日 - 我们定期交换有关公告发布的信息。  
  
- 麒麟990：P40 专业版 （ELS）  
  
- /vendor/bin/atcmdserver  
（uid 0）;  
  
- system_server  
（uid 1000）;  
  
- com.huawei.systemserver  
APK;  
  
- com.huawei.cryptosms.service  
APK;  
  
- /vendor/bin/hw/vendor.huawei.hardware.huaweisigntool@1.0-service  
（uid 1000）;  
  
- /vendor/bin/hw/vendor.huawei.hardware.hwsecurity-service  
（uid 1000）。  
  
- 这attributeID;  
  
- 第二个缓冲区中的属性数据offsetattrData;  
  
- 属性数据。length  
  
-   
- ****  
  
-   
- 二进制漏洞(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEH4eXCW471pNuHpGPzUKCkbyticiayoQ5gxMtoR1AX0QS7JJ2v1Miaibv1lA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 其它课程  
  
- windows网络安全防火墙与虚拟网卡（更新完成）  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERE5qcRgQueCyt3U01ySnOUp2wOmiaFhcXibibk6kjPoUhTeftn9aOHJjO6mZIIHRCBqIZ1ok5UjibLMRA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windows文件过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmvkF91T2mwk3lSlbG5CELC5qbib3qMOgHvow2cvl6ibicVH4KguzibAQOQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- USB过滤(更新完成)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHv0vyWxLx9Sb68ssCJQwXngPmKDw2HNnvkrcle2picUraHyrTG2sSK7A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 游戏安全(更新中)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHzEAlXtdkXShqbkibJUKumsvo65lnP6lXVR7nr5hq4PmDZdTIoky8mCg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- ios逆向  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHmjrTM3epTpceRpaWpibzMicNtpMIacEWvJMLpKKkwmA97XsDia4StFr1Q/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- windbg  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibv9VNhRI73qFehic91I5dsr3Jkh6EkHIRTPGibESZicD7IeA5ocHjexHhw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- 恶意软件开发（更新中）  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERGxhrr6IpiaZuqkGWyEJWPwXqHEYPEVp3gpDB73Pg81J9TdUQic0wn4NJQdbTCIDsgC2gqT4tkEkjsg/640?wx_fmt=png&from=appmsg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
- 还有很多免费教程(限学员)  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHhezg9PuKylWLTBfCjokEHDvveGEwLYBVsps1sH6rGrSnNZtjD2pzCk4EwhH3yeVNibMMSxsW5jkg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibR2Viaxgog8I2gicVHoXJODoqtq7tTVGybA8W0rTYaAkLcp8e2ByCd1QQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERECMA4FBVNaHekaDaROKFEibDwwqQLTNPnzDQxtQUF6JjxyxDoNGsr6XoNLicwxOeYfFia0whaxu6VXA/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-   
-   
-   
- 更多详细内容添加作者微信  
  
-   
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPMJPjIWnCTP3EjrhOXhJsryIkR34mCwqetPF7aRmbhnxBbiaicS0rwu6w/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
-    
  
-    
  
- ![](https://mmbiz.qpic.cn/sz_mmbiz_png/vBZcZNVQERHYgfyicoHWcBVxH85UOBNaPZeRlpCaIfwnM0IM4vnVugkAyDFJlhe1Rkalbz0a282U9iaVU12iaEiahw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
