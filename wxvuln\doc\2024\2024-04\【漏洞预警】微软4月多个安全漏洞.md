#  【漏洞预警】微软4月多个安全漏洞   
cexlife  飓风网络安全   2024-04-10 23:11  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDZHHHHCE4mmG8lq0Ljte64ibdeK23yLvo6u2Fcib8SmxopufxZlNGTwqA/640?wx_fmt=png&from=appmsg "")  
  
**漏洞描述:**  
  
2024年4月9日，微软发布了4月安全更新，本次更新共修复了150个漏洞（不包含之前修复的Microsoft Edge 和Mariner漏洞）,漏洞类型包括特权提升漏洞、安全功能绕过漏洞、远程代码执行漏洞、信息泄露漏洞、拒绝服务漏洞和欺骗漏洞等。本次安全更新中包含2个被积极利用的0 day漏洞:**CVE-2024-26234：Proxy Driver欺骗漏洞**该漏洞的CVSS评分为6.7，与Sophos X-Ops 发现的有效Microsoft硬件发行商证书签名的恶意驱动程序活动相关，该驱动程序被用来部署恶意后门，目前该漏洞已发现被利用并已公开披露。**CVE-2024-29988：SmartScreen Prompt安全功能绕过漏洞**该漏洞的CVSS评分为8.8，威胁者可以向目标用户发送特制文件，并诱导用户使用请求不显示UI 的启动器应用程序来启动恶意文件，可能在文件打开时绕过Microsoft Defender Smartscreen 提示，在目标系统上执行恶意代码。目前微软官方并未将该漏洞标记为已被利用，但该漏洞可能存在在野利用。本次安全更新中评级为严重的3个漏洞包括：**CVE-2024-29053：Microsoft Defender for IoT 远程代码执行漏洞**Microsoft Defender for IoT中存在路径遍历漏洞，该漏洞的CVSS评分为8.8，有权访问文件上传功能的经过身份验证的威胁者可以通过将恶意文件上传到服务器上的敏感位置来利用该路径遍历漏洞，成功利用可能导致远程代码执行。**CVE-2024-21323：Microsoft Defender for IoT 远程代码执行漏洞**Microsoft Defender for IoT中存在路径遍历漏洞，该漏洞的CVSS评分为8.8，经过身份验证并获得启动更新过程所需的权限的威胁者可向Defender for IoT 传感器发送 tar 文件来利用该漏洞。提取过程完成后，威胁者就可以发送未签名的更新包，并覆盖他们选择的任何文件。**CVE-2024-21322：Microsoft Defender for IoT 远程代码执行漏洞**Microsoft Defender for IoT中存在命令注入漏洞，该漏洞的CVSS评分为7.2，具有Web 应用程序的管理权限的威胁者可利用该漏洞导致远程代码执行。除CVE-2024-29988外，微软的可利用性评估中其他“被利用的可能性较高”的漏洞还包括：CVE-2024-26209：Microsoft 本地安全机构子系统服务中存在信息泄露漏洞，成功利用该漏洞可能导致泄露未初始化的内存。CVE-2024-26218：Windows 内核中存在提权漏洞，成功利用该漏洞可以获得SYSTEM权限。CVE-2024-26211：Windows 远程访问连接管理器中存在特权提升漏洞，成功利用该漏洞可以获得SYSTEM权限。CVE-2024-26230和CVE-2024-26239：Windows Telephony Server 中存在特权提升漏洞，成功利用该漏洞可以获得SYSTEM权限。CVE-2024-29056：Windows 身份验证中存在特权提升漏洞，成功利用该漏洞的威胁者可以查看某些敏感信息。CVE-2024-26241：Win32k中存在提权漏洞，成功利用该漏洞可以获得SYSTEM权限。CVE-2024-28921和CVE-2024-28903：安全启动中存在安全功能绕过漏洞，成功利用这些漏洞可能导致威胁者绕过安全启动。CVE-2024-26158：Microsoft Install Service特权提升漏洞CVE-2024-26212：DHCP Server Service拒绝服务漏洞CVE-2024-26256：libarchive 远程代码执行漏洞**本次更新中其他值得关注的漏洞还包括但不限于:****CVE-2024-26245:Windows SMB存在特权提升漏洞**，成功利用该漏洞可以获得SYSTEM权限。**CVE-2024-20670:Outlook for Windows存在欺骗漏洞，**威胁者可向受害者发送恶意URL并诱导受害者执行该URL，如通过发送特制的电子邮件，从而导致受害者与威胁者控制的不受信任位置建立连接，从而将受害者的 Net-NTLMv2 哈希泄露到不受信任的网络，然后威胁者可以将其中继到另一个服务并以受害者身份进行身份验证。  
  
**Microsoft SharePoint零日漏洞（暂无CVE）**:研究人员在SharePoint中发现了两种逃避渗透检测的新技术,允许用户绕过审计日志,避免在外泄文件时触发下载事件。  
  
微软4月更新涉及的完整漏洞列表如下：  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDFOHvicV44P3ttwliaZIib6alAQWcOgtLwibvyDGicq8Z1pyMNgSo3evyQeA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDrb0lyPJ0hD4eq9cpubJYv3RicS0lmxTxT53Zp24adhoibGrmT2KVLOog/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDnlZD2INyYNsAH5ZG500yUZPXKibGsicBNHhQPxEBmibicZfjKhybPoLbDA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDNibBuUFpvPWRwic7t79zKibjDia8ROm10Skx4Nkujh6xqh1GKfZ4CIS2LA/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/ibhQpAia4xu03EzOI7S2jFFuOaZ72eHxDDBSc8AUo1SibqjcD5yGKDKOTARyvBxAQRgxYn2icQlNXrehm3GgEsYKkQ/640?wx_fmt=png&from=appmsg "")  
  
**影响范围:**受影响的产品/功能/服务/组件包括:Windows BitLockerWindows Secure BootMicrosoft Office OutlookWindows Remote Procedure CallAzure Private 5G CoreWindows KernelMicrosoft Defender for IoT.NET and Visual StudioAzure Compute GalleryWindows Authentication MethodsMicrosoft Install ServiceWindows DWM Core LibraryWindows Routing and Remote Access Service (RRAS)Windows KerberosAzure MigrateWindows DHCP ServerWindows Remote Access Connection ManagerWindows Message QueuingWindows Local Security Authority Subsystem Service (LSASS)Microsoft WDAC OLE DB provider for SQLMicrosoft Brokering File SystemMicrosoft WDAC ODBC DriverWindows File Server Resource Management ServiceWindows HTTP.sysWindows Mobile HotspotRole: DNS ServerWindows Distributed File System (DFS)Windows Cryptographic ServicesWindows Proxy DriverWindows Update StackWindows Defender Credential GuardWindows Win32K - ICOMPWindows Telephony ServerWindows USB Print DriverMicrosoft Office SharePointWindows Internet Connection Sharing (ICS)Windows Virtual Machine BusWindows Compressed FolderMicrosoft Office ExcelSQL ServerAzure ArcMicrosoft Edge (Chromium-based)Windows StorageAzure AI SearchRole: Windows Hyper-VInternet Shortcut FilesAzure MonitorMicrosoft Azure Kubernetes ServiceAzure SDKAzure**安全措施:**升级版本目前微软已发布相关安全更新,建议受影响的用户尽快修复**（一） Windows Update自动更新**Microsoft Update默认启用，当系统检测到可用更新时，将会自动下载更新并在下一次启动时安装。也可选择通过以下步骤手动进行更新：1、点击“开始菜单”或按Windows快捷键，点击进入“设置”2、选择“更新和安全”，进入“Windows更新”（Windows 8、Windows 8.1、Windows Server 2012以及Windows Server 2012 R2可通过控制面板进入“Windows更新”，具体步骤为“控制面板”->“系统和安全”->“Windows更新”）3、选择“检查更新”，等待系统自动检查并下载可用更新。4、更新完成后重启计算机，可通过进入“Windows更新”->“查看更新历史记录”查看是否成功安装了更新。对于没有成功安装的更新，可以点击该更新名称进入微软官方更新描述链接，点击最新的SSU名称并在新链接中点击“Microsoft 更新目录”，然后在新链接中选择适用于目标系统的补丁进行下载并安装。**（二） 手动安装更新**Microsoft官方下载相应补丁进行更新2024年4月安全更新下载链接：https://msrc.microsoft.com/update-guide/releaseNote/2024-Apr  
  
