#  雷神众测漏洞周报2023.10.30-2023.11.06   
原创 雷神众测  雷神众测   2023-11-06 17:19  
  
摘要  
  
  
以下内容，均摘自于互联网，由于传播，利用此文所提供的信息而造成的任何直接或间接的后果和损失，均由使用者本人负责，雷神众测以及文章作者不承担任何责任。雷神众测拥有该文章的修改和解释权。如欲转载或传播此文章，必须保证此文章的副本，包括版权声明等全部内容。声明雷神众测允许，不得任意修改或增减此文章内容，不得以任何方式将其用于商业目的。  
  
  
目录  
  
**1.Google Android代码执行漏洞**  
  
**2.亿赛通电子文档安全管理系统（CDG）存在文件上传漏洞**  
  
**3.F5 BIG-IP Edge Client for Windows和macOS安全绕过漏洞**  
  
**4.Atlassian Confluence身份认证绕过漏洞**  
  
  
漏洞详情  
  
**1.Google Android代码执行漏洞**  
  
  
漏洞介绍：  
  
Google Android是美国谷歌（Google）公司的一套以Linux为基础的开源操作系统。  
  
  
漏洞危害：  
  
Google Android存在代码执行漏洞，该漏洞源于lpp_tran.h的TRANSPOSER_SETTINGS模块边界检查不正确，攻击者可利用此漏洞在系统上执行任意代码。  
  
  
漏洞编号：  
  
CVE-2023-21282  
  
  
影响范围：  
  
Google Android 11.0  
  
Google Android 12.0  
  
Google Android 13.0  
  
Google Android 12.1  
  
  
修复方案：  
  
及时测试并升级到最新版本或升级版本  
  
  
来源：CNVD  
  
**2.亿赛通电子文档安全管理系统（CDG）存在文件上传漏洞**  
  
  
漏洞介绍：  
  
北京亿赛通科技发展有限责任公司是一家经营范围：一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广等的公司。  
  
  
漏洞危害：  
  
亿赛通电子文档安全管理系统（CDG）存在文件上传漏洞，攻击者可利用该漏洞获取服务器控制权。  
  
  
影响范围：  
  
北京亿赛通科技发展有限责任公司 亿赛通电子文档安全管理系统  
  
  
修复方案：  
  
及时测试并升级到最新版本或升级版本  
  
  
来源：CNVD  
  
  
**3.F5 BIG-IP Edge Client for Windows和macOS安全绕过漏洞**  
  
  
漏洞介绍：  
  
F5 BIG-IP是美国F5公司的一款集成了网络流量管理、应用程序安全管理、负载均衡等功能的应用交付平台。  
  
  
漏洞危害：  
  
F5 BIG-IP Edge Client for Windows和macOS存在安全绕过漏洞，该漏洞源于证书验证不正确。攻击者可利用此漏洞冒充BIG-IP APM系统。  
  
  
漏洞编号：  
  
CVE-2023-24461  
  
  
影响范围：  
  
F5 BIG-IP APM Clients >=7.2.2,<=7.2.4  
  
F5 BIG-IP APM >=17.0.0，<=17.1.0  
  
F5 BIG-IP APM >=16.1.0，<=16.1.3  
  
F5 BIG-IP APM >=14.1.0，<=14.1.5  
  
F5 BIG-IP APM >=13.1.0，<=13.1.5  
  
F5 BIG-IP APM >=15.1.0，<=15.1.8  
  
  
修复方案：  
  
及时测试并升级到最新版本或升级版本  
  
  
来源：CNVD  
  
**4.Atlassian Confluence身份认证绕过漏洞**  
  
  
漏洞介绍：  
  
Atlassian Confluence是一个团队工作区，它的主要功能是创建、收集和协同处理任何项目或创意。它是一个知识与协作的融合平台，为团队成员提供一个协作环境，可以齐心协力，各擅其能，协同地编写文档和管理项目。  
  
  
漏洞危害：  
  
该漏洞存在于Atlassian Confluence Data Center & Server 中，是一个身份认证绕过漏洞。由于子组件Struts2继承关系处理不当，攻击者在未授权的情况下利用该漏洞，构造恶意数据进行认证绕过，获取服务器最高权限，进而接管服务器。  
  
  
漏洞编号：  
  
CVE-2023-22518  
  
  
影响范围：  
  
Atlassian:Confluence 8.3.0 - 8.3.4   
  
Atlassian:Confluence 8.4.0 - 8.4.4   
  
Atlassian:Confluence 8.5.0 - 8.5.3   
  
Atlassian:Confluence 8.6.0 - 8.6.1   
  
Atlassian:Confluence < 7.19.16  
  
  
修复方案：  
  
及时测试并升级到最新版本或升级版本  
  
  
来源：360CERT  
  
  
  
  
  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/HxO8NorP4JVPFpnaicobfaZBErTgaSO7TnsMU3lEdtTuk9hZNIYbVmXErATh74JTvkaGdKiaJkzLiaLjw4HPclBRQ/640?wx_fmt=jpeg "")  
  
专注渗透测试技术  
  
全球最新网络攻击技术  
  
  
**END**  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/HxO8NorP4JVPFpnaicobfaZBErTgaSO7Tz8fw6BrJKJibFLfFl0hee6kKnNgx47ibsMMqa1tNTVMGtupkoMrYhYqA/640?wx_fmt=jpeg "")  
  
  
  
