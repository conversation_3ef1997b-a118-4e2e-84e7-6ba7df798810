#  高流量即高漏洞：浏览标题党网站可能遭受攻击风险   
 网络安全应急技术国家工程中心   2023-11-16 15:17  
  
![](https://mmbiz.qpic.cn/mmbiz_png/GoUrACT176nFsh4QCPWDf5OzHMfwWM2lHE037ibicfvatgGMxULHmngbHfpzJRySUpSKFoI1Yxj9CyFVtP0ZKbJQ/640?wx_fmt=png&from=appmsg "")  
  
自2023年8月底以来，研究人员观察到专门用于标题党和广告内容的受攻击服务器显著增加。但为什么这样的网站对攻击者来说如此有吸引力呢？主要因为这些网站的设计是为了接触到大量潜在的受害者。此外，标题党网站经常使用过时或未修复的软件，这使得它们很容易受到攻击。  
  
本文足以让你了解标题党文章的危险性。文中讨论了标题党网站如何增加流量以获得广告收入，研究人员回顾了一种基于其网络流量特征，来检测易受攻击标题党网站的策略。最后，本文还阐述了基于CVE-2023-3169漏洞的标题党网站数量激增的趋势分析。  
# 标题党网站和广告流量  
  
标题党旨在让读者点击一个可疑的网络内容链接。专门从事标题党内容的网站存在的唯一目的是产生广告收入，因此，标题党网站的网页包含大量攻击性广告。  
  
点击诱饵需要大量的浏览量来产生广告收入，所以这些网站通常使用以下三种策略来增加流量。  
  
常青的话题；  
  
内容发现平台；  
  
生成人工智能(AI)工具；  
# 常青的话题  
  
增加流量的一个策略是关注常青话题。常青话题指的是与特定时间或地点无关的话题，人们一直觉得它们很有趣。例如，金融和卫生被认为是常青的话题。图1和图2显示了来自标题党网站的两个示例页面：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/wpkib3J60o29mOTOTXogpyJpe4AesKytvhsYS3T3gFaLHpicZ0e1gzln5cBVdPBhgQkohpvMSO5reCbqSnSt2GzA/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
金融主题标题党文章的例子  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/wpkib3J60o29mOTOTXogpyJpe4AesKytvsvkdfBaiaOvB3pD1ZU0opGJJQKicoj4gia1hD3Zia55JwrEE1tvRadfJZg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
健康主题标题党文章的例子  
# 内容发现平台  
  
由于标题党内容本身是通过广告传播的，许多标题党网站还依靠第二种策略来增加流量：内容发现平台。  
  
新闻机构和其他内容提供商使用内容发现平台来创收，标题党提供商经常使用这些服务来为他们自己的内容增加流量。  
  
内容发现平台经常使用技术来伪装广告。其中一种方法被称为原生广告，此方法将广告内容配置为与其所在网站的外观相似，浏览者很难区分网站的原创内容和广告内容。下图显示了出现在新闻网站上的原生广告示例：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o29mOTOTXogpyJpe4AesKytvHfkNIN6IbEncIr8CP4R5iboH0ZxYVtFLWamPLtouTcPpxNH9SMruy1A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
原生广告出现在新闻网站的例子  
  
在上图中，研究人员添加了一个红色箭头，指向hxxps://gofindyou[.]com/health/what-causes-plaque-psoriasis-heres-what-doctors-need-you-to-know的标题党内容，快速检查发现这个网站至少运行了一个过时的软件。来自网页的HTML代码表明它使用了用于Yoast SEO的WordPress插件，如下图所示：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/wpkib3J60o29mOTOTXogpyJpe4AesKytvLzXOX9UCEgiab5JXMkZfJvAhcRYpZnu0n0uiaYia0v9R6eIuUqAtbicKzA/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
一个标题党页面显示Yoast SEO插件v20.8的HTML代码  
  
显示Yoast SEO插件20.8版本的HTML最初发布于2023年5月23日。上图所示的网页是在2023年10月27日提供的，当时Yoast SEO插件的最新版本是21.4，这个插件已经过时。  
  
研究人员经常发现带有过时软件或插件的标题党网站。这种特殊情况不意味着存在任何特定的漏洞，但过时的软件可能比完全打过补丁的版本更容易受到攻击。  
# 生成人工智能工具  
  
标题党作者的最新策略是使用Jasper和AIPRM等生成式人工智能工具，这些工具提供了一种简单的方法来生成seo优化的内容，以增加网站流量。  
  
攻击者经常滥用、利用或破坏合法产品以达到恶意目的，但这并不一定意味着滥用合法产品的缺陷或质量问题。  
  
一个示例是在 hxxps://delhiproduct[.]info/top-24-earn-money-with-paid-online-surveys 上用ChatGPT写的一篇文章，在本例中，该网站是一个运行MonsterInsights插件（版本8.1.0）的过时版本的WordPress网站，如下图所示：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/wpkib3J60o29mOTOTXogpyJpe4AesKytvnvqHoxMft0EqNmjOLa9DCPbduBsKdwsnAKat3mzHetpQMy8jibIWCbA/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
delhipproductinfo[.]com的HTML显示了过时的MonsterInsights插件  
  
截至2023年10月3日，MonsterInsights插件的最新版本是8.20.1，这意味着8.1.0版本至少已经过时两年了。该插件的8.1.0版本也容易受到存储跨站攻击。  
# 查找易受攻击的网站  
  
要攻击任何网站，攻击者必须知道该网站的web服务器使用的web堆栈。这些数据包括操作系统、web内容管理软件(CMS)以及任何相关的插件和主题。  
  
攻击者使用web堆栈数据来确定服务器是否运行任何过时的软件或应用程序，有了这些信息，攻击者可以很容易地找到公开的漏洞和利用来破坏网站。  
  
研究人员如何确定服务器的web堆栈？研究人员可以通过网站的URL模式、HTML内容和功能来发现这些信息，网页的外观和感觉也可以提供线索。  
  
接下来介绍一些指标，这些指标可以显示网站的部分web堆栈。  
  
/wp-content/或/wp-includes/ ：URL或网页的HTML代码中的任何一个字符串都表示关联的网站可能使用WordPress。  
  
wp-content/themes/Newspaper/style.css?ver=11.4.1 ：在网页的HTML代码中，此字符串表示网站使用tagDiv的WordPress报纸主题，报纸版本为11.4.1。  
  
!-- This site uses the Google Analytics by MonsterInsights plugin v8.1.0-Using Analytics tracking - https://www.monsterinsights[.]com/ --：网页HTML代码中的这条评论表明该网站使用了WordPress的MonsterInsights插件。插件信息的注释在大多数情况下都是准确的。  
  
在确认利用CVE-2023-3169时，前两种技术可能会有所帮助。  
# 利用CVE-2023-3169  
  
2023年9月11日，MITRE发布了CVE-2023-3169，该漏洞在与WordPress的Composer插件一起使用时会影响tagDiv的Newspaper和Newsmag主题，已经有数千个WordPress网站因该漏洞而被攻击。  
  
Unit 42团队成员监控Palo Alto Networks的遥测恶意活动，这些数据包括网页中HTML代码的指示符及其关联的URL。根据这些数据，我们通过存在恶意脚本和其他指标来确认一个受攻击的网站。  
  
之前的研究表明，利用CVE-2023-3169后，通过Balada注入器攻击了数千个易受攻击的网站。根据这项研究，受此攻击的网站从以下位置生成加载恶意内容的页面：  
  
hxxps://stay[.]decentralappps[.]com/src/page.js  
  
研究人员的数据证实了这一发现，分析发现与CVE-2023-3169相关的受攻击WordPress网站在2023年8月底开始激增。  
  
在两个月的时间里，研究人员发现了大约10300个被攻击的WordPress网站。下面图表说明了研究人员检测到的这个峰值。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o29mOTOTXogpyJpe4AesKytvSZiaaa79yvHM9jibgAgs0SfsGvZbibY058LdSf9ucM1Wqc2eSpT8BurZQ/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
研究人员通过CVE-2023-3169窃取的WordPress网站数据  
  
如下图所示，这些受攻击的网站中有很大一部分是标题党或广告网站。调查发现，标题党和广告占检测30%以上，在这30%的网站中，至少80%的网站使用了tagDiv的Newspaper主题，另外6%的网站使用了tagDiv的Newsmag主题。  
# 注入脚本示例  
  
下图显示了2023年10月初的一个示例，其中一个受攻击网站将恶意脚本注入到网页中，注入的脚本以黄色突出显示。  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o29mOTOTXogpyJpe4AesKytv2LGnBicgsrQpLyaUhJ5OR0wBtRRjTG8YJpicjicDfh6pmTWMqUHGyUibpg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
在WordPress网站的网页上注入脚本的示例  
  
这个混淆的脚本使用表示ASCII字符的十进制值，将这些数字转换为ASCII文本会显示恶意脚本，如下图所示：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o29mOTOTXogpyJpe4AesKytv4d2OkQUNZ6rsbh74B5gPFia96n9VvE62W90Ltj08gKYrLLSNIRodfkg/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
从上图中突出显示的文本解码的恶意脚本  
  
上图中解码的脚本包含相同的hxxps://stay[.] decentralapps [.]com/src/page.js URL，在之前的报告中提到了使用Balada注入器利用CVE-2023-3169的活动。  
# 标题党和广告网站的趋势  
  
研究人员使用Cortex Xpanse和其他工具跟踪分析了漏洞趋势。除了CVE-2023-3169之外，研究人员还跟踪受其他漏洞影响的网站。  
  
在2023年9月15日至22日的样本分析中，研究人员监控了1600个随机选择的WordPress网站的数据集，有用户试图访问受攻击的网站。  
  
结果显示，与其他类别相比，流量党和广告网站的受害比例接近三比一。下图显示了研究人员每周检测的平均值：  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/wpkib3J60o29mOTOTXogpyJpe4AesKytvSyE6lSKkndJgfY4kTrUNkMZGHG79dcMoVI4q8FoIcSl6bKIuIEuicsw/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
检测受攻击WordPress网站  
  
无论是来自CVE-2023-3169还是其他漏洞，与其他类别相比，受攻击的标题党和广告网站的数量都始终更高。  
# 总结  
  
当研究人员通过分析被攻击网站的指标时，通过与其他类别相比，研究人员依旧看到大量被攻击的标题党和广告网站。  
  
这些网站通常使用过时或未修复的软件，有可能触及大量受害者，使其成为攻击者的诱人目标。因此，标题党文章本身就存在风险，读者应该意识到这种风险，并相应地调整他们的浏览习惯。  
  
**参考及来源：**  
  
https://unit42.paloaltonetworks.com/dangers-of-clickbait-sites/  
  
  
  
原文来源：嘶吼专业版  
  
“投稿联系方式：010-82992251   <EMAIL>”  
  
![](https://mmbiz.qpic.cn/mmbiz_jpg/GoUrACT176n1NvL0JsVSB8lNDX2FCGZjW0HGfDVnFao65ic4fx6Rv4qylYEAbia4AU3V2Zz801UlicBcLeZ6gS6tg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1 "")  
  
  
