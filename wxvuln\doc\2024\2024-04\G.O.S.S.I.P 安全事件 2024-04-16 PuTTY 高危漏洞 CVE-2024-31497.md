#  G.O.S.S.I.P 安全事件 2024-04-16 PuTTY 高危漏洞 CVE-2024-31497   
原创 G.O.S.S.I.P  安全研究GoSSIP   2024-04-16 22:17  
  
![](https://mmbiz.qpic.cn/sz_mmbiz_png/uicdfzKrO21EamsVrj5aCOBw9iczBOia3QXFQduykMeyAXibhicFJWcy0IC8ibI2IaIweYyuibpyiblZ5uYJR8vQM5wgVg/640?wx_fmt=png&from=appmsg "")  
  
来自Ruhr University Bochum的  
  
Fab  
ian Bäumer和Marcus Brinkmann在PuTTY（版本 v0.68-0.80）中发现了一个高危的安全漏洞，具体情况是PuTTY的私钥生成算法在产生一个特定的私钥类型及长度——521-bit ECDSA——的时候，生成了开头9个bit是全零，然后才是随机内容的私钥。你看到这里估计能猜出来点什么（9+512=521），对，PuTTY原始的私钥生成算法是2001年设计的，而后来的标准（RFC 6979）是2013年才发布的，原始的算法大概是先获取一个特定的SHA-512的摘要值，然后执行一个 mod q 操作，这个q是一个512比特的数，因此在私钥长度小于512比特的时候没问题，但是对521-bit ECDSA的私钥就完蛋了！  
  
安全威胁：在使用这种有问题的私钥（进行签名）的情况下，攻击者在获取约 60 个相同私钥生成的有效 ECDSA 签名后，可以恢复密钥。我们会在后续给出一个实际的例子，欢迎大家关注。  
  
  
